#!/usr/bin/env python3
"""
Simple model evaluation script.

This script loads a trained ExecutionBCActorPolicy model, iterates through a
PaxiniTactileRobot dataset, runs the policy in inference mode and computes the
Mean-Absolute-Error (MAE) between the predicted action of the first time-step
and the ground-truth action provided in the dataset.

It intentionally avoids the heavy TrajectoryAnalyzer pipeline to offer a
minimal, transparent sanity-check.

Usage
-----
python simple_eval.py \
    --model_path outputs/execution_bc_actor/....pth \
    --dataset_dir ./data/test \
    --seq_length 15 \
    --hand_type right \
    --batch_size 32
"""
import argparse
import sys
from pathlib import Path

import numpy as np
import torch

# Import dataset clerk and helper from the existing codebase
from px_janus_learnsim.dataprocess import PaxiniTactileRobotClerk
from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
    _process_expert_batch_for_imitation,
)

# -----------------------------------------------------------------------------
# Helper
# -----------------------------------------------------------------------------


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Simple model evaluator")
    parser.add_argument(
        "--model_path", type=str, required=True, help="Path to .pth model file"
    )
    parser.add_argument(
        "--dataset_dir",
        type=str,
        required=True,
        help="Directory containing HDF5 episodes",
    )
    parser.add_argument(
        "--seq_length",
        type=int,
        default=15,
        help="Sequence length to evaluate (default: 15)",
    )
    parser.add_argument(
        "--hand_type",
        type=str,
        default="right",
        choices=["left", "right"],
        help="Target hand type used during training",
    )
    parser.add_argument(
        "--batch_size", type=int, default=32, help="DataLoader batch size (default: 32)"
    )
    parser.add_argument(
        "--device", type=str, default="auto", help="cuda | cpu | auto (default: auto)"
    )
    parser.add_argument(
        "--evaluate_all_timesteps",
        action="store_true",
        help="If set, evaluate every timestep instead of only first frame",
    )
    parser.add_argument(
        "--metric",
        type=str,
        default="both",
        choices=["norm", "phys", "both"],
        help="Which metric space to report: normalized (norm), physical (phys) or both (default both)",
    )
    parser.add_argument(
        "--auto_stats",
        action="store_true",
        help="Ignore saved scaling stats and compute min/max from dataset for a self-contained sanity-check",
    )
    return parser.parse_args()


def main() -> None:
    args = parse_args()

    # -------------------------------------------------------------------------
    # Resolve device
    # -------------------------------------------------------------------------
    if args.device == "auto":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(args.device)
    print(f"[INFO] Using device: {device}")

    # -------------------------------------------------------------------------
    # Load policy (ExecutionBCActorPolicy object stored via torch.save)
    # -------------------------------------------------------------------------
    model_path = Path(args.model_path)
    if not model_path.exists():
        print(f"[ERROR] Model file not found: {model_path}")
        sys.exit(1)

    print(f"[INFO] Loading model from {model_path} ...")
    policy = torch.load(str(model_path), map_location=device)
    policy.eval()
    # 让自定义策略也进入推理模式（某些类需显式调用）
    if hasattr(policy, "set_training_mode"):
        policy.set_training_mode(False)

    # -------------------------------------------------------------------------
    # Prepare dataset DataLoader
    # -------------------------------------------------------------------------
    clerk = PaxiniTactileRobotClerk()
    data_cfg = {
        "database_dir": args.dataset_dir,
        "batch_size_train": args.batch_size,
        "batch_size_val": args.batch_size,  # not used
        "seq_length": args.seq_length,
        "camera_names": None,
        "tactile_names": None,
        "force_regenerate_stats": False,
    }

    train_loader, _, _, _ = clerk.load_data(data_cfg)
    if train_loader is None:
        print("[ERROR] Failed to load dataset via PaxiniTactileRobotClerk")
        sys.exit(1)

    # -------------------------------------------------------------------------
    # Iterate and compute MAE / component metrics
    # -------------------------------------------------------------------------
    total_mae = 0.0
    total_samples = 0

    # Accumulators for normalized space
    comp_joint_mae_norm = 0.0
    comp_pos_mae_norm = 0.0
    comp_rot_mae_norm = 0.0
    total_mae_norm = 0.0

    # Accumulators for physical space (after unscale)
    comp_joint_mae_phys = 0.0
    comp_pos_mae_phys = 0.0
    comp_rot_mae_phys = 0.0
    total_mae_phys = 0.0

    JOINT_SLICE = slice(0, 16)  # 16 joint angles
    POS_SLICE = slice(16, 19)  # 3-D translation (first 3 of 9-d pose)
    ROT_SLICE = slice(19, 25)  # 6-D rotation representation (next 6)

    # Optional: load action scaling processor for unscaling to physical domain
    from px_janus_learnsim.utils.action_scaling_utils import (
        load_separated_scaling_processor,
        auto_find_separated_scaling_stats,
    )

    processor = None
    if not args.auto_stats:
        scaling_stats_path = auto_find_separated_scaling_stats(
            str(model_path), dataset_dir=args.dataset_dir
        )
        if scaling_stats_path is not None:
            try:
                processor = load_separated_scaling_processor(
                    scaling_stats_path, enable_clip=False
                )
                print(f"[INFO] Loaded scaling stats from {scaling_stats_path}")
            except Exception as e:
                print(
                    f"[WARN] Failed to load scaling stats: {e}. Proceeding without unscaling."
                )
                processor = None
        else:
            print(
                "[WARN] No scaling stats file found – MAE will be computed in normalized domain."
            )

    # ---------------------------------------------------------------------
    # Auto-compute min/max stats from dataset if requested (sanity-check mode)
    # ---------------------------------------------------------------------
    if args.auto_stats:
        print("[INFO] Computing min/max statistics from dataset for auto-stats mode …")
        action_mins = []
        action_maxs = []
        for batch in train_loader:
            _, act_np, _, _, _ = _process_expert_batch_for_imitation(
                batch, device, seq_length=args.seq_length, target_hand=args.hand_type
            )
            if args.evaluate_all_timesteps:
                acts_flat = act_np.reshape(-1, act_np.shape[-1])
            else:
                acts_flat = act_np[:, 0, :]
            action_mins.append(acts_flat.min(axis=0))
            action_maxs.append(acts_flat.max(axis=0))
        min_vec = np.min(np.stack(action_mins, axis=0), axis=0)
        max_vec = np.max(np.stack(action_maxs, axis=0), axis=0)
        range_vec = max_vec - min_vec
        print("[INFO]   min range :=", range_vec.min())

        # Detect constantly-zero (or constant) dimensions
        const_dims = np.where(range_vec < 1e-8)[0]
        if const_dims.size > 0:
            print(
                f"[WARN] Found {const_dims.size} constant dimensions (range≈0): {const_dims.tolist()}"
            )
            # Replace zero range with small epsilon to avoid NaN in scaling
            range_vec[const_dims] = 1e-6

        # Define simple min-max scaling lambdas (to/from [-1,1])
        def _scale_minmax(x: torch.Tensor) -> torch.Tensor:
            rng = torch.from_numpy(range_vec).to(x.device).float()
            return 2.0 * (x - torch.from_numpy(min_vec).to(x.device)) / rng - 1.0

        def _unscale_minmax(x_norm: torch.Tensor) -> torch.Tensor:
            rng = torch.from_numpy(range_vec).to(x_norm.device).float()
            return 0.5 * (x_norm + 1.0) * rng + torch.from_numpy(min_vec).to(
                x_norm.device
            )

        class _DummyProcessor:
            def scale_actions(self, x):
                return _scale_minmax(x)

            def unscale_actions(self, x):
                return _unscale_minmax(x), "auto_minmax"

        processor = _DummyProcessor()
        print("[INFO] Auto-stats processor ready (min-max)")

    for batch in train_loader:
        # Convert batch -> numpy (obs [B,T,34], act [B,T,25])
        obs_np, act_np, _, _, _ = _process_expert_batch_for_imitation(
            batch, device, seq_length=args.seq_length, target_hand=args.hand_type
        )

        if args.evaluate_all_timesteps:
            obs_flat = obs_np.reshape(-1, obs_np.shape[-1])  # [B*T, obs_dim]
            gt_flat = act_np.reshape(-1, act_np.shape[-1])  # [B*T, act_dim]
        else:
            obs_flat = obs_np[:, 0, :]
            gt_flat = act_np[:, 0, :]

        obs_tensor_raw = torch.from_numpy(obs_flat).float().to(device)

        # Normalize observations if processor available (to match training domain)
        if processor is not None:
            try:
                obs_tensor = processor.normalize_observations(obs_tensor_raw)
            except Exception:
                # Fallback: if separated mode stats missing, use raw obs
                obs_tensor = obs_tensor_raw
        else:
            obs_tensor = obs_tensor_raw

        gt_actions_tensor = torch.from_numpy(gt_flat).float().to(device)

        # Model prediction (ExecutionBCActorPolicy.predict returns np array or tensor)
        with torch.no_grad():
            pred_result = policy.predict(obs_tensor, deterministic=True)
            if isinstance(pred_result, tuple):
                pred_actions = pred_result[0]
            else:
                pred_actions = pred_result

            # Convert to torch tensor on target device
            if isinstance(pred_actions, np.ndarray):
                pred_actions_tensor = torch.from_numpy(pred_actions).float().to(device)
            elif torch.is_tensor(pred_actions):
                pred_actions_tensor = pred_actions.to(device).float()
            else:
                raise TypeError(f"Unsupported prediction type: {type(pred_actions)}")

            # If model outputs a sequence [B, num_queries, action_dim], take first query to
            # align with ground-truth which uses timestep 0 only (unless --evaluate_all_timesteps).
            if pred_actions_tensor.dim() == 3:
                pred_actions_tensor = pred_actions_tensor[:, 0, :]

            # At this point, `pred_actions_tensor` is in normalized domain (model's training output),
            # while `gt_actions_tensor` is still in physical domain from dataset.
            # We'll:
            #   1. Convert GT to normalized for Norm metrics.
            #   2. Convert predictions to physical for Phys metrics.

        # --- Normalized-space metrics --- #
        if processor is not None:
            pred_norm = pred_actions_tensor  # already normalized
            gt_norm = processor.scale_actions(gt_actions_tensor)  # convert GT
            diff_norm = torch.abs(pred_norm - gt_norm)
        else:
            # Without processor we assume dataset already provides normalized targets.
            diff_norm = torch.abs(pred_actions_tensor - gt_actions_tensor)
        batch_mae_norm = torch.mean(diff_norm, dim=1)
        total_mae_norm += batch_mae_norm.sum().item()
        comp_joint_mae_norm += diff_norm[:, JOINT_SLICE].mean(dim=1).sum().item()
        comp_pos_mae_norm += diff_norm[:, POS_SLICE].mean(dim=1).sum().item()
        comp_rot_mae_norm += diff_norm[:, ROT_SLICE].mean(dim=1).sum().item()

        # --- Physical-space metrics (if requested) --- #
        if args.metric in ["phys", "both"] and processor is not None:
            # Convert predictions to physical domain; GT already physical
            pred_phys, _ = processor.unscale_actions(pred_actions_tensor)
            gt_phys = gt_actions_tensor
            diff_phys = torch.abs(pred_phys - gt_phys)
            batch_mae_phys = torch.mean(diff_phys, dim=1)
            total_mae_phys += batch_mae_phys.sum().item()
            comp_joint_mae_phys += diff_phys[:, JOINT_SLICE].mean(dim=1).sum().item()
            comp_pos_mae_phys += diff_phys[:, POS_SLICE].mean(dim=1).sum().item()
            comp_rot_mae_phys += diff_phys[:, ROT_SLICE].mean(dim=1).sum().item()

        total_samples += batch_mae_norm.numel()

    if total_samples == 0:
        print("[ERROR] No samples processed – check dataset path/seq_length")
        sys.exit(1)

    print("================ Evaluation Result ================")
    print(f"Samples evaluated    : {total_samples}")

    # ----- Normalized domain -----
    if args.metric in ["norm", "both"]:
        avg_mae_norm = total_mae_norm / total_samples
        joint_mae_norm = comp_joint_mae_norm / total_samples
        pos_mae_norm = comp_pos_mae_norm / total_samples
        rot_mae_norm = comp_rot_mae_norm / total_samples
        print("[Norm  (scaled) ]")
        print(f"  Avg MAE            : {avg_mae_norm:.6f}")
        print(f"    Joint  (16) MAE  : {joint_mae_norm:.6f}")
        print(f"    Position(3) MAE  : {pos_mae_norm:.6f}")
        print(f"    Rotation(6) MAE  : {rot_mae_norm:.6f}")

    # ----- Physical domain -----
    if args.metric in ["phys", "both"]:
        if processor is None:
            print("[Phys (unscaled)] : N/A  (no scaling stats found)")
        else:
            avg_mae_phys = total_mae_phys / total_samples
            joint_mae_phys = comp_joint_mae_phys / total_samples
            pos_mae_phys = comp_pos_mae_phys / total_samples
            rot_mae_phys = comp_rot_mae_phys / total_samples
            print("[Phys (unscaled)]")
            print(f"  Avg MAE            : {avg_mae_phys:.6f}")
            print(f"    Joint  (16) MAE  : {joint_mae_phys:.6f}")
            print(f"    Position(3) MAE  : {pos_mae_phys:.6f}")
            print(f"    Rotation(6) MAE  : {rot_mae_phys:.6f}")

    print("===================================================")


if __name__ == "__main__":
    main()
