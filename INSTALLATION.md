# PX-Tactrix 安装指南

## 📋 系统要求

- **Python**: 3.12+
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **GPU**: NVIDIA GPU with CUDA support (推荐)
- **内存**: 16GB+ RAM
- **存储**: 10GB+ 可用空间

## 🚀 快速安装

### 方法1: 使用uv (推荐)

```bash
# 1. 安装uv (如果还没有安装)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 克隆项目
git clone <your-repo-url>
cd px_tactrix

# 3. 创建虚拟环境并安装依赖
uv sync

# 4. 激活环境
source .venv/bin/activate

# 5. 验证安装
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
```

### 方法2: 使用pip

```bash
# 1. 创建虚拟环境
python3.12 -m venv px_tactrix_env
source px_tactrix_env/bin/activate

# 2. 安装项目
pip install -e .

# 或者从锁定文件安装
pip install -r requirements.txt  # 需要先生成此文件
```

## 🔧 可选依赖安装

### 开发工具
```bash
uv sync --extra dev
```

### Isaac Sim支持
```bash
uv sync --extra isaac
```

### 完整安装
```bash
uv sync --extra full
```


## 📊 数据准备

1. **专家数据**: 将HDF5格式的专家数据放在 `data/` 目录下
2. **配置文件**: 检查 `conf/config.yaml` 中的路径设置
3. **模型权重**: 预训练模型放在 `outputs/` 目录下

## 🐛 常见问题

### CUDA相关问题
```bash
# 检查CUDA版本
nvidia-smi

# 安装对应的PyTorch版本
uv add torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### Isaac Sim问题
```bash
# 确保Isaac Sim环境变量设置正确
export ISAAC_SIM_PATH="/path/to/isaac_sim"
export PYTHONPATH="${ISAAC_SIM_PATH}/kit/python/lib:${PYTHONPATH}"
```

### 内存不足
```bash
# 减少batch size
# 修改 conf/config.yaml 中的 batch_size
```

## 📞 获取帮助

- **Issues**: [GitHub Issues](https://github.com/yourusername/px_tactrix/issues)
- **文档**: [Documentation](./docs/)
- **示例**: [Examples](./examples/)
