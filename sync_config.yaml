# 🚀 项目同步配置文件
# 版本: 1.0
# 创建时间: 2025-06-25

# 项目配置
projects:
  px_tactrix:
    local_path: "/home/<USER>/workspace/px_tactrix/"
    remote_path: "/root/workspace/px_tactrix/"
    exclude_file: "rsync_exclude.txt"
    description: "PX Tactrix - 机器人触觉学习项目"

  px_LearningSim_Janus:
    local_path: "/home/<USER>/workspace/px_LearningSim_Janus/"
    remote_path: "/root/workspace/px_LearningSim_Janus/"
    exclude_file: "rsync_exclude.txt"
    description: "PX Learning Simulation Janus - 通用机器人学习仿真框架"

# 服务器配置 (可选，命令行参数会覆盖这些设置)
remote_host: "************"
remote_user: "root"

# 配置元信息
version: "1.0"
created_at: "2025-06-25T14:50:00"

# 可选的全局设置
settings:
  # 默认排除文件
  default_exclude_file: "rsync_exclude.txt"

  # 同步选项
  sync_options:
    delete: true # 是否删除远端多余文件
    compress: true # 是否压缩传输
    verbose: true # 是否显示详细信息

  # 历史记录设置
  history:
    max_records: 100 # 最大记录数

  # 通知设置 (未来功能)
  notifications:
    enabled: false
    email: ""
    slack_webhook: ""
