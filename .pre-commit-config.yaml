repos:

# - repo: https://github.com/astral-sh/ruff-pre-commit
#   rev: "v0.4.2"
#   hooks:
#   - id: ruff
#     args: [ "--extend-select", "I", "--fix", "--show-fixes", "--exclude", "/example" ]

# - repo: https://github.com/codespell-project/codespell
#   rev: v2.1.0
#   hooks:
#   - id: codespell

- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.6.0
  hooks:
  - id: check-added-large-files
  - id: check-case-conflict
  - id: check-merge-conflict
  - id: debug-statements
  - id: end-of-file-fixer
  - id: mixed-line-ending
  - id: requirements-txt-fixer
  - id: trailing-whitespace

- repo: https://github.com/psf/black
  rev: 24.4.2
  hooks:
  - id: black

- repo: https://github.com/commitizen-tools/commitizen
  rev: v3.20.0
  hooks:
  - id: commitizen
    stages: [ commit-msg ]
