[project]
name = "px-tactrix"
version = "0.1.0"
description = "Hierachical VTLA (Vision - Tactile - Language - Action) for tactile-enhanced robotic manipulation"
readme = "README.md"
requires-python = ">=3.12"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
]
license = {text = ""}
keywords = ["robotics", "machine-learning", "tactile", "manipulation", "reinforcement-learning"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    # Core ML/DL dependencies
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "numpy>=1.24.0",
    "scipy>=1.10.0",

    # Data handling
    "h5py>=3.8.0",
    "pandas>=2.0.0",



    # Visualization
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.14.0",

    # Configuration management
    "hydra",
    "omegaconf",
    "pyyaml>=6.0",

    # Training utilities
    "tqdm>=4.65.0",
    "wandb>=0.15.0",
    "tensorboard>=2.13.0",

    # Scientific computing
    "einops>=0.6.0",
    "scikit-learn>=1.3.0",

    # Utilities
    "colorlog>=6.7.0",
    "termcolor>=2.3.0",
    "rich>=13.0.0",

    # RL and Imitation Learning
    "stable-baselines3>=2.0.0",
    "imitation>=1.0.0",
    "gymnasium>=0.28.0",

    # Experiment tracking and logging
    "swanlab>=0.3.0",

    # CLI and table formatting
    "prettytable>=3.8.0",

    # Isaac Lab (如果需要)
    # "isaaclab>=1.0.0",  # 取消注释如果需要

    # Isaac Sim (optional, comment out if not available)
    # "isaacsim>=2023.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.4.0",
    "pre-commit>=3.3.0",
]

# Isaac Sim dependencies - 注释掉不可用的包
isaac = [
    # Isaac Sim specific dependencies
    # 注意：这些包需要从NVIDIA官方渠道安装
    # "omni-isaac-sim>=2023.1.0",
    # "omni-isaac-core>=2023.1.0",
]

# 移除full依赖中的isaac部分
# full = [
#     "px-tactrix[dev,isaac]",
# ]

[project.scripts]
px-tactrix-train = "main:main"
px-tactrix-eval = "eval_model_vis:main"
px-tactrix-data-vis = "scripts.visualize_dataset_distribution:main"

[project.urls]
Homepage = "https://gitlab.paxini.cloud/px_robot_learning/px_tactrix"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["train", "eval", "conf"]

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.pytest.ini_options]
testpaths = ["test"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
