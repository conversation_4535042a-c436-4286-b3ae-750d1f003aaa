# 🤖 灵枢 - 多维触觉AI仿生灵巧手系统 (px_tactrix)

<p align="center">
  <img src="./logo_tactrix.jpeg"  alt="灵枢系统标志">
</p>

<p align="center">
  <a href="#核心特性">✨ 核心特性</a> •
  <a href="#系统架构">🏗️ 系统架构</a> •
  <a href="#快速开始">🚀 快速开始</a> •
  <a href="#技术实现">🛠️ 技术实现</a> •
  <a href="#应用场景">🌐 应用场景</a> •
  <a href="#文档">📚 文档</a> •
  <a href="#贡献指南">👥 贡献指南</a>
</p>

## ✨ 核心特性

**行业领先的多模态感知系统**
- 1956维高密度触觉阵列 + 5+2摄像头视觉融合感知
- 亚毫米级6D位姿实时估计（精度±0.2mm）
- 动态接触力觉建模（0-50N连续测量）

**仿生运动控制系统**
- 13自由度模块化灵巧手设计
- 毫秒级动态阻抗控制（1000Hz刷新率）
- 多关节协同运动规划算法

**智能决策架构**
```mermaid
graph TD
    A[多模态感知] --> B{决策中心}
    B --> C[抓取策略生成]
    B --> D[操作轨迹规划]
    C --> E[动态力控执行]
    D --> E
    E --> F[环境状态更新]
    F --> A
```

## 🏗️ 详细系统架构

灵枢系统采用分层架构，融合了多种感知模态和先进的学习算法，以实现灵巧的操作能力。整体流程包括感知与状态表示、决策层抓取姿态选择、执行层动作生成，并通过行为克隆（BC）和强化学习（RL）进行持续优化和自适应。

### 感知与状态表示

系统首先整合来自不同来源的数据，通过场景理解模块生成统一的状态表示，作为后续决策和执行的基础。

```mermaid
flowchart LR
    subgraph 数据源
        A[语言指令 ]
        B[视觉输入]
        C[本体状态]
        D[触觉信号]
    end

    subgraph 场景理解模块
        AA[LLM 语义解析器]
        BB[物体表示模块]
        CC[场景理解融合]
    end

    E[状态表示
任务+物体+场景]

    A --> AA
    B --> BB
    C --> BB
    D --> BB
    AA --> CC
    BB --> CC
    CC --> E

```
说明：
- **语言指令** 由 `LLM 语义解析器` 处理，提取任务意图。
- **视觉输入**、**本体状态** 和 **触觉信号** 被送入 `物体表示模块`，结合物理属性生成场景和物体的几何与物理表示。
- 各模块信息在 `场景理解融合` 步骤中整合，最终形成结构化的 `状态表示`，包含任务目标、物体属性和场景上下文。


### 决策层：抓取姿态选择

决策层接收状态表示，利用不同的策略生成器和评估网络来选择最优的抓取姿态。

```mermaid
flowchart TD
    A[状态表示 ] --> 决策层[抓取姿态生成与选择]

    subgraph 决策层
        direction LR
        B1[IBS 生成器] --> B4
        B2[DexGraspNet
数据增强/先验] --> B4{候选抓取姿态集合}
        B3[RL 策略探索] --> B4
    end

    subgraph 评估与优化
        direction LR
        B4 --> C[决策层 Critic 网络
 评估候选姿态价值]
        D[接触感知与建模引擎 NCF
预测接触效果] --> C
    end

    C --> E[选定的目标抓取姿态]

```
说明：
- `状态表示` 输入到决策层。
- 通过 `IBS 生成器`、利用 `DexGraspNet` 的先验知识或进行 `RL 策略探索`，生成一系列 `候选抓取姿态`。
- `决策层 Critic 网络` 负责评估这些候选姿态的优劣，可能结合 `NCF 引擎` 对接触效果的预测。
- 最终输出 `选定的目标抓取姿态` 给执行层。


### 执行层与行为克隆

执行层负责将决策层选定的目标姿态转化为具体的机器人动作序列，并通过行为克隆进行学习。

```mermaid
flowchart TD
    A[选定的目标抓取姿态] --> C
    B[当前机器人状态 + 可用传感器反馈] --> C[执行层状态表示]

    subgraph 执行与控制
        C --> D[Actor 网络]
        D --> E[下一序列动作]
        E --> F[执行控制器]
        F --> G[执行级控制指令]
    end

    G --> H[环境交互 / 物理执行]
    H --> I[新的传感器反馈 ]
    I --> B

    subgraph 学习回路
        J[专家演示数据] --> K{BC 模仿学习}
        D -- 输出策略 --> K
        K -- 更新梯度 --> D
    end
```
说明：
- `目标抓取姿态` 与实时的 `机器人状态` 和 `传感器反馈` 结合，形成 `执行层状态表示`。
- `Actor 网络` (策略网络) 根据当前状态决定下一步（或一系列）`动作`。
- `执行控制器` 将这些高级动作转换为底层的 `执行级控制指令`。
- 机器人 `执行` 指令并与环境交互，产生 `新的传感器反馈`，更新状态。
- `BC 模仿学习` 利用 `专家演示数据` 对 `Actor 网络` 进行监督训练，使其模仿专家行为。


### 强化学习与自适应

强化学习提供了一个在线自适应和优化的框架，使得系统能够通过与环境的持续交互来改进其决策和执行策略。

```mermaid
flowchart TD
    subgraph "执行层RL - 抓取微调循环"
        A[接收姿态] --> B[构建初始状态]
        B --> C[初始控制 Actor]
        C --> D[执行与感知]
        D --> E[更新状态]
        E --> F[RL 策略优化 微调 Actor/Critic]
        F --> G[环境]
        G -.-> D
    end
```
说明：
- RL 循环始于接收 `决策层选择的抓取姿态`。
- 系统 `构建初始状态`，`Actor 网络` 生成接近目标的 `初始控制指令`。
- 在 `强化学习环境` (模拟或真实) 中 `执行` 指令并 `感知` 结果，特别是收集接触时的传感器数据。
- `状态更新` 模块将新的传感器数据融入 `执行层状态表示`。
- `控制适应` 阶段是 RL 的核心：
    - `接触感知与建模引擎 (NCF)` 可能用于预测此次交互的物理后果或特性。
    - `执行层 Critic 网络` 基于更新后的状态、实际执行的动作以及 NCF 的预测（可选）来评估该状态动作对的价值 (Q 值)。
    - `Actor 网络` 和 `Critic 网络` 根据评估结果和奖励信号进行更新（例如，使用 Actor-Critic 算法）。
- 系统进入下一个 `循环迭代`，持续优化策略。


## 🚀 快速开始

### 硬件要求
- NVIDIA Jetson AGX Orin 64GB
- Intel RealSense D455 深度相机
- 定制触觉传感阵列（v2.4+）

### 安装依赖
```bash
conda create -n tactrix python=3.10
conda activate tactrix
pip install -r requirements.txt
```


### 执行层BC（行为克隆）训练
```bash
python tora_learning_train_sb3_hierarchical.py \
         --headless --dataset_dir ./simulated_data \
         --execution_epochs 5 \
         --training_phase execution_bc_train --seq_length 15
```

### 模型评估
```bash
python evaluate_models.py \
         --model_path outputs/execution_bc_actor/2025-05-26/17-40-51/Columniform-Speakerphone-X6EH/execution_bc_policy.pth \
         --model_type bc \
         --eval_episodes 50 \
         --detailed --num_envs 4
```

### 数据可视化与回放

系统提供了强大的数据可视化工具`vis_hdf5.py`，支持机器人运动轨迹和触觉数据的实时回放。

#### 基本播放命令
```bash
# 播放单个HDF5文件
python vis_hdf5.py --data_source ./simulated_data/demo_data.h5 --hand_type left

# 播放目录中所有h5文件（按序播放）
python vis_hdf5.py --data_source ./simulated_data/ --hand_type left --loop_count 3

# 无限循环播放
python vis_hdf5.py --data_source ./simulated_data/demo_data.h5 --hand_type left --loop_count -1
```

#### 高级参数配置
```bash
# 指定相机视角
python vis_hdf5.py --data_source ./simulated_data/demo_data.h5 \
                   --hand_type left \
                   --camera_pos -1.0 -1.0 10.5 \
                   --camera_target -0.5 0.0 10.18

# 关闭触觉可视化（提升性能）
python vis_hdf5.py --data_source ./simulated_data/demo_data.h5 \
                   --hand_type left \
                   --close_tactile_vis

# 无头模式播放
python vis_hdf5.py --data_source ./simulated_data/demo_data.h5 \
                   --hand_type left \
                   --headless
```

#### 参数说明
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--data_source` | str | 必需 | 数据源路径（文件或目录） |
| `--hand_type` | str | left | 手部模型（left/right/paxini） |
| `--object_type` | str | bottle | 物体模型（bottle/box/sphere/ketchup） |
| `--loop_count` | int | 1 | 循环次数（-1为无限循环） |
| `--camera_pos` | float[3] | 自动计算 | 相机位置 [x y z] |
| `--camera_target` | float[3] | 自动计算 | 相机目标点 [x y z] |
| `--close_tactile_vis` | flag | False | 关闭触觉数据可视化 |

#### 支持的数据格式
- **HDF5格式** (`.h5/.hdf5`): 支持1140维触觉数据可视化
- **CSV格式** (`.csv`): 兼容旧版数据格式
- **目录播放**: 自动按文件名排序播放所有h5文件

### 部署(PlaceHolder)
```bash

```

## 🛠️ 技术实现

### 视触觉融合模块
```python
class MultiModalFusion(nn.Module):
    def __init__(self):
        super().__init__()
        # 点云特征提取
        self.pointnet = PointNetSeg()
        # 触觉时序编码
        self.tactile_lstm = nn.LSTM(16, 64)
        # 跨模态注意力
        self.cross_attn = CrossAttention(256, 128)

    def forward(self, point_cloud, tactile_seq):
        pc_feat = self.pointnet(point_cloud)
        tact_feat, _ = self.tactile_lstm(tactile_seq)
        fused = self.cross_attn(pc_feat, tact_feat)
        return fused
```

### 动态力控算法
$$\tau = J^T(K_p\Delta x + K_d\Delta \dot{x}) + \hat{f}_{ext}$$
- $\tau$: 关节力矩向量
- $J$: 雅可比矩阵
- $K_p/K_d$: 阻抗参数
- $\hat{f}_{ext}$: 估计外力

## 🌐 应用场景

**工业装配场景**
```mermaid
gantt
    title 精密装配任务流水线
    dateFormat  HH:mm
    section 抓取阶段
    零件定位           :a1, 00:00, 2m
    自适应抓取         :a2, after a1, 3m
    section 装配阶段
    孔轴对齐          :a3, after a2, 3m
    柔顺插入          :a4, after a3, 2m
    力矩检测          :a5, after a4, 1m
```

## 📚 文档体系
```mermaid
graph TD
    A[核心API手册] --> B[硬件接口规范]
    A --> C[算法白皮书]
    B --> D[传感器集成指南]
    C --> E[案例教学]
    D --> F[故障排查手册]
```

## ❓ 常见问题

**Q1: 如何校准触觉传感器阵列？**
通过专用夹具执行自动校准流程：
```bash
python -m tactrix.calibration --mode auto
```

**Q2: 点云重建精度不达标怎么办？**
检查：
1. 深度相机固件版本（需≥2.4.8）
2. 环境光照条件（推荐500-1000lux）
3. 标定板完整性

## 👥 贡献指南
欢迎通过以下方式参与贡献：
- 提交[GitLab Issue](https://gitlab.paxini.cloud/tactrix)
- 加入[开发者讨论组](https://paxini.cloud/slack)
- 参与[技术路线讨论](https://gitlab.paxini.cloud/tactrix/-/wikis)

## 📬 联系我们
