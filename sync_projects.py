#!/usr/bin/env python3
"""
🚀 项目同步脚本
================

功能：
1. 推送本地项目到远端服务器
2. 拉取远端训练结果到本地
3. 支持多项目同步
4. 智能排除不必要的文件
5. 支持SSH密码输入

作者：AI Assistant
"""

import os
import sys
import subprocess
import logging
import argparse
import getpass
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional


# ANSI颜色代码
class Colors:
    # 基础颜色
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"

    # 亮色
    BRIGHT_BLACK = "\033[90m"
    BRIGHT_RED = "\033[91m"
    BRIGHT_GREEN = "\033[92m"
    BRIGHT_YELLOW = "\033[93m"
    BRIGHT_BLUE = "\033[94m"
    BRIGHT_MAGENTA = "\033[95m"
    BRIGHT_CYAN = "\033[96m"
    BRIGHT_WHITE = "\033[97m"

    # 背景色
    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"

    # 样式
    RESET = "\033[0m"
    BOLD = "\033[1m"
    DIM = "\033[2m"
    ITALIC = "\033[3m"
    UNDERLINE = "\033[4m"
    BLINK = "\033[5m"
    REVERSE = "\033[7m"
    STRIKETHROUGH = "\033[9m"


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""

    # 日志级别对应的颜色和emoji
    LEVEL_COLORS = {
        "DEBUG": Colors.BRIGHT_BLACK,
        "INFO": Colors.BRIGHT_CYAN,
        "WARNING": Colors.BRIGHT_YELLOW,
        "ERROR": Colors.BRIGHT_RED,
        "CRITICAL": Colors.RED + Colors.BG_WHITE + Colors.BOLD,
    }

    LEVEL_EMOJIS = {
        "DEBUG": "🔍",
        "INFO": "💡",
        "WARNING": "⚠️",
        "ERROR": "❌",
        "CRITICAL": "💥",
    }

    def __init__(self, fmt=None, datefmt=None):
        super().__init__()
        self.fmt = fmt or "[%(asctime)s][%(levelname)s] %(message)s"
        self.datefmt = datefmt or "%H:%M:%S"

    def format(self, record):
        # 获取日志级别的颜色和emoji
        level_color = self.LEVEL_COLORS.get(record.levelname, Colors.WHITE)
        level_emoji = self.LEVEL_EMOJIS.get(record.levelname, "📝")

        # 时间颜色（灰色）
        time_color = Colors.BRIGHT_BLACK

        # 重置颜色
        reset = Colors.RESET

        # 构建彩色格式
        colored_fmt = (
            f"{time_color}[%(asctime)s]{reset}"
            f"{level_color}[{level_emoji} %(levelname)s]{reset} "
            f"%(message)s"
        )

        # 创建临时格式化器
        formatter = logging.Formatter(colored_fmt, self.datefmt)
        return formatter.format(record)


# 配置彩色日志
def setup_colored_logging():
    """设置彩色日志"""
    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # 清除现有的handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 创建控制台handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 设置彩色格式化器
    colored_formatter = ColoredFormatter()
    console_handler.setFormatter(colored_formatter)

    # 添加handler到logger
    logger.addHandler(console_handler)

    return logger


# 设置彩色日志
logger = setup_colored_logging()


# 自定义日志函数，添加更多emoji
def log_rocket(message):
    """火箭日志 - 用于启动操作"""
    logger.info(f"🚀 {message}")


def log_success(message):
    """成功日志"""
    logger.info(f"✅ {message}")


def log_warning(message):
    """警告日志"""
    logger.warning(f"⚠️  {message}")


def log_error(message):
    """错误日志"""
    logger.error(f"❌ {message}")


def log_sync(message):
    """同步日志"""
    logger.info(f"🔄 {message}")


def log_download(message):
    """下载日志"""
    logger.info(f"📥 {message}")


def log_upload(message):
    """上传日志"""
    logger.info(f"📤 {message}")


def log_status(message):
    """状态日志"""
    logger.info(f"📊 {message}")


def log_celebration(message):
    """庆祝日志"""
    logger.info(f"🎉 {message}")


def log_folder(message):
    """文件夹日志"""
    logger.info(f"📂 {message}")


def log_file(message):
    """文件日志"""
    logger.info(f"📄 {message}")


def log_network(message):
    """网络日志"""
    logger.info(f"🌐 {message}")


class ProjectSyncer:
    """通用项目同步器"""

    def __init__(
        self,
        remote_host: str,
        remote_user: str = "root",
        ssh_password: str = None,
        config_file: str = None,
    ):
        self.remote_host = remote_host
        self.remote_user = remote_user
        self.remote_address = f"{remote_user}@{remote_host}"
        self.ssh_password = ssh_password

        # 同步历史记录文件
        self.history_file = os.path.expanduser("~/.px_sync_history.json")

        # 项目配置 - 从配置文件或默认配置加载
        self.projects = {}
        self.config_file = config_file or "sync_config.yaml"
        self._load_projects_config()

        # 检查sshpass是否可用
        self.sshpass_available = self._check_sshpass()
        if ssh_password and not self.sshpass_available:
            log_warning("sshpass未安装，无法自动输入密码。请考虑配置SSH密钥认证。")

    def _load_projects_config(self):
        """加载项目配置"""
        config_loaded = False

        # 尝试从配置文件加载
        if os.path.exists(self.config_file):
            try:
                import yaml

                with open(self.config_file, "r", encoding="utf-8") as f:
                    config = yaml.safe_load(f)
                    if "projects" in config:
                        self.projects = config["projects"]
                        log_file(f"从配置文件加载项目: {self.config_file}")
                        config_loaded = True
            except ImportError:
                log_warning("PyYAML未安装，无法读取YAML配置文件")
            except Exception as e:
                log_warning(f"加载配置文件失败: {e}")

        # 如果没有配置文件，使用默认配置（向后兼容）
        if not config_loaded:
            log_warning(f"配置文件 {self.config_file} 不存在，使用默认配置")
            self.projects = self._get_default_projects()

    def _get_default_projects(self) -> dict:
        """获取默认项目配置（向后兼容）"""
        return {
            "px_tactrix": {
                "local_path": "/home/<USER>/workspace/px_tactrix/",
                "remote_path": "/root/workspace/px_tactrix/",
                "exclude_file": "rsync_exclude.txt",
                "description": "PX Tactrix项目",
            },
            "px_LearningSim_Janus": {
                "local_path": "/home/<USER>/workspace/px_LearningSim_Janus/",
                "remote_path": "/root/workspace/px_LearningSim_Janus/",
                "exclude_file": "rsync_exclude.txt",
                "description": "PX Learning Simulation Janus项目",
            },
        }

    def add_project(
        self,
        name: str,
        local_path: str,
        remote_path: str,
        exclude_file: str = None,
        description: str = None,
        auto_create: bool = False,
    ):
        """动态添加项目"""
        # 检查项目是否已存在
        if name in self.projects:
            log_warning(f"项目 '{name}' 已存在，将会覆盖现有配置")

        # 标准化路径
        local_path = os.path.abspath(os.path.expanduser(local_path))
        if not local_path.endswith("/"):
            local_path += "/"
        if not remote_path.endswith("/"):
            remote_path += "/"

        # 验证本地路径
        local_exists = os.path.exists(local_path)
        if not local_exists:
            log_warning(f"本地路径不存在: {local_path}")

            if auto_create:
                try:
                    os.makedirs(local_path, exist_ok=True)
                    log_success(f"已创建本地目录: {local_path}")
                    local_exists = True
                except Exception as e:
                    log_error(f"创建本地目录失败: {e}")
                    return False
            else:
                log_folder("提示:")
                logger.info(f"  📂 可以手动创建目录: mkdir -p {local_path}")
                logger.info(f"  🤖 或使用 --auto-create 参数自动创建")

                # 询问用户是否继续
                try:
                    choice = input("是否继续添加项目? (y/N): ").strip().lower()
                    if choice not in ["y", "yes", "是"]:
                        log_warning("取消添加项目")
                        return False
                except KeyboardInterrupt:
                    log_warning("取消添加项目")
                    return False

        # 验证排除文件
        if exclude_file:
            exclude_file = os.path.abspath(os.path.expanduser(exclude_file))
            if not os.path.exists(exclude_file):
                log_warning(f"排除文件不存在: {exclude_file}")
                logger.info("  📄 将使用默认排除规则")

        # 添加项目配置
        self.projects[name] = {
            "local_path": local_path,
            "remote_path": remote_path,
            "exclude_file": exclude_file,
            "description": description or f"项目 {name}",
        }

        # 显示项目信息
        log_success(f"成功添加项目: {name}")
        logger.info(f"  📦 项目名称: {name}")
        logger.info(f"  📝 描述: {description or f'项目 {name}'}")
        logger.info(f"  📂 本地路径: {local_path} {'✅' if local_exists else '❌'}")
        logger.info(f"  🌐 远端路径: {remote_path}")
        if exclude_file:
            exclude_exists = os.path.exists(exclude_file)
            logger.info(
                f"  📄 排除文件: {exclude_file} {'✅' if exclude_exists else '❌'}"
            )

        # 提示保存配置
        logger.info(f"  💡 使用 --action save-config 保存配置到文件")

        return True

    def remove_project(self, name: str):
        """移除项目"""
        if name in self.projects:
            del self.projects[name]
            log_success(f"移除项目: {name}")
        else:
            log_warning(f"项目不存在: {name}")

    def list_projects(self):
        """列出所有项目"""
        if not self.projects:
            log_warning("没有配置任何项目")
            return

        log_status("已配置的项目:")
        for name, config in self.projects.items():
            logger.info(f"   📦 {name}")
            logger.info(f"     📝 描述: {config.get('description', '无描述')}")
            logger.info(f"     📂 本地: {config['local_path']}")
            logger.info(f"     🌐 远端: {config['remote_path']}")
            if config.get("exclude_file"):
                logger.info(f"     📄 排除: {config['exclude_file']}")
            print()

    def save_config(self, config_file: str = None):
        """保存当前配置到文件"""
        config_file = config_file or self.config_file
        try:
            import yaml

            config = {
                "projects": self.projects,
                "remote_host": self.remote_host,
                "remote_user": self.remote_user,
                "created_at": datetime.now().isoformat(),
                "version": "1.0",
            }

            with open(config_file, "w", encoding="utf-8") as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

            log_success(f"配置已保存到: {config_file}")
        except ImportError:
            log_error("PyYAML未安装，无法保存YAML配置文件")
        except Exception as e:
            log_error(f"保存配置文件失败: {e}")

    def _check_sshpass(self) -> bool:
        """检查sshpass是否可用"""
        try:
            subprocess.run(["sshpass", "-V"], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False

    def _get_ssh_cmd_prefix(self) -> List[str]:
        """获取SSH命令前缀（包含密码处理）"""
        if self.ssh_password and self.sshpass_available:
            return ["sshpass", "-p", self.ssh_password]
        return []

    def run_rsync(
        self,
        source: str,
        dest: str,
        exclude_file: Optional[str] = None,
        extra_options: List[str] = None,
    ) -> bool:
        """执行rsync命令"""
        cmd = ["rsync", "-avz", "--progress"]

        # 添加SSH选项（如果有密码）
        if self.ssh_password and self.sshpass_available:
            # 正确的rsync SSH选项格式
            ssh_cmd = f"sshpass -p '{self.ssh_password}' ssh"
            cmd.extend(["-e", ssh_cmd])

        # 添加额外选项
        if extra_options:
            cmd.extend(extra_options)

        # 硬编码排除同步工具文件（额外保护）
        sync_tool_excludes = [
            "sync_projects.py",
            "sync_projects.sh",
            "sync_config.yaml",
            "sync_config.yml",
            "rsync_exclude.txt",
            ".px_sync_history.json",
        ]

        for exclude in sync_tool_excludes:
            cmd.extend(["--exclude", exclude])

        # 添加排除文件
        if exclude_file and os.path.exists(exclude_file):
            cmd.extend(["--exclude-from", exclude_file])
            log_file(f"使用排除文件: {exclude_file}")
        else:
            log_warning(f"排除文件不存在: {exclude_file}，使用默认排除规则")
            # 基本的默认排除规则
            basic_excludes = ["__pycache__/", "*.pyc", ".venv/", "dist/", ".git/"]
            for exclude in basic_excludes:
                cmd.extend(["--exclude", exclude])

        cmd.extend([source, dest])

        log_network(f"执行命令: {' '.join(cmd)}")

        try:
            result = subprocess.run(cmd, check=True, capture_output=False)
            return True
        except subprocess.CalledProcessError as e:
            log_error(f"rsync失败: {e}")
            return False
        except KeyboardInterrupt:
            log_warning("用户中断操作")
            return False

    def run_ssh_command(self, command: str) -> bool:
        """执行SSH命令"""
        ssh_prefix = self._get_ssh_cmd_prefix()
        cmd = ssh_prefix + ["ssh", self.remote_address, command]

        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            return True
        except subprocess.CalledProcessError as e:
            log_error(f"SSH命令失败: {e}")
            return False

    def push_project(self, project_name: str) -> bool:
        """推送项目到远端"""
        if project_name not in self.projects:
            log_error(f"未知项目: {project_name}")
            return False

        start_time = datetime.now()
        project = self.projects[project_name]
        local_path = project["local_path"]
        remote_path = f"{self.remote_address}:{project['remote_path']}"
        exclude_file = project.get("exclude_file")

        log_upload(f"推送项目 {project_name} 到远端...")
        logger.info(f"   📂 本地路径: {local_path}")
        logger.info(f"   🌐 远端路径: {remote_path}")

        # 检查本地路径是否存在
        if not os.path.exists(local_path):
            log_error(f"本地路径不存在: {local_path}")
            self._save_history("push", project_name, False, {"error": "本地路径不存在"})
            return False

        # 创建远端目录
        remote_dir = project["remote_path"]
        log_folder(f"创建远端目录: {remote_dir}")
        self.run_ssh_command(f"mkdir -p {remote_dir}")

        # 执行同步
        success = self.run_rsync(
            source=local_path,
            dest=remote_path,
            exclude_file=exclude_file,
            extra_options=["--delete"],
        )

        # 计算耗时
        duration = (datetime.now() - start_time).total_seconds()

        # 保存历史记录
        details = {
            "duration": duration,
            "local_path": local_path,
            "remote_path": remote_path,
        }

        if success:
            log_success(f"项目 {project_name} 推送成功")
            logger.info(f"   ⏱️  耗时: {duration:.1f}秒")
        else:
            log_error(f"项目 {project_name} 推送失败")
            details["error"] = "推送失败"

        self._save_history("push", project_name, success, details)
        return success

    def pull_outputs(self, project_name: str = "px_tactrix") -> bool:
        """拉取远端训练结果到本地"""
        if project_name not in self.projects:
            log_error(f"未知项目: {project_name}")
            return False

        start_time = datetime.now()
        project = self.projects[project_name]
        remote_outputs = f"{self.remote_address}:{project['remote_path']}outputs/"
        local_outputs = os.path.join(project["local_path"], "outputs/")

        log_download(f"拉取 {project_name} 的训练结果...")
        logger.info(f"   🌐 远端路径: {remote_outputs}")
        logger.info(f"   📂 本地路径: {local_outputs}")

        # 创建本地outputs目录
        os.makedirs(local_outputs, exist_ok=True)

        # 检查远端outputs目录是否存在
        if not self.run_ssh_command(f"test -d {project['remote_path']}outputs"):
            log_warning(f"远端outputs目录不存在: {project['remote_path']}outputs")
            self._save_history("pull", project_name, True, {"note": "远端无训练结果"})
            return True  # 不算错误，可能还没有训练结果

        # 拉取训练结果（不删除本地多余文件，保留本地训练结果）
        # outputs目录结构：execution_bc_actor/YYYY-MM-DD/HH-MM-SS/RandomName/
        success = self.run_rsync(
            source=remote_outputs,
            dest=local_outputs,
            exclude_file=None,  # 不排除训练结果文件
            extra_options=[],  # 不使用--delete，保留本地文件
        )

        # 计算耗时和统计信息
        duration = (datetime.now() - start_time).total_seconds()
        model_count = 0

        details = {
            "duration": duration,
            "remote_path": remote_outputs,
            "local_path": local_outputs,
        }

        if success:
            log_success(f"{project_name} 训练结果拉取成功")
            logger.info(f"   ⏱️  耗时: {duration:.1f}秒")

            # 显示拉取的内容
            try:
                result = subprocess.run(
                    ["find", local_outputs, "-name", "*.pth", "-o", "-name", "*.pt"],
                    capture_output=True,
                    text=True,
                )
                if result.stdout.strip():
                    model_files = result.stdout.strip().split("\n")
                    model_count = len(model_files)
                    log_file(f"拉取的模型文件: {model_count} 个")
                    for line in model_files[:5]:  # 只显示前5个
                        logger.info(f"  📄 {line}")
                    if len(model_files) > 5:
                        logger.info(f"  ... 还有 {len(model_files) - 5} 个文件")

                    details["model_files"] = model_count
            except:
                pass
        else:
            log_error(f"{project_name} 训练结果拉取失败")
            details["error"] = "拉取失败"

        self._save_history("pull", project_name, success, details)
        return success

    def sync_all_projects(self) -> bool:
        """同步所有项目"""
        log_sync("开始同步所有项目...")

        all_success = True

        # 推送所有项目
        for project_name in self.projects:
            success = self.push_project(project_name)
            all_success = all_success and success
            print()  # 空行分隔

        # 拉取训练结果
        success = self.pull_outputs("px_tactrix")
        all_success = all_success and success

        if all_success:
            log_celebration("所有项目同步完成！")
        else:
            log_error("部分项目同步失败")

        return all_success

    def get_remote_project_info(self, remote_path: str) -> dict:
        """获取远端项目信息"""
        info = {
            "exists": False,
            "last_modified": "未知",
            "size": "未知",
            "outputs_size": "未知",
            "model_count": 0,
        }

        try:
            # 检查远端路径是否存在
            if not self.run_ssh_command(f"test -d {remote_path}"):
                return info

            info["exists"] = True

            # 获取最后修改时间 - 找到最新修改的文件
            ssh_prefix = self._get_ssh_cmd_prefix()
            cmd = ssh_prefix + [
                "ssh",
                self.remote_address,
                f"find {remote_path} -type f -printf '%T@ %p\\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f1",
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                timestamp = float(result.stdout.strip())
                info["last_modified"] = datetime.fromtimestamp(timestamp).strftime(
                    "%Y-%m-%d %H:%M:%S"
                )

            # 获取项目大小
            cmd = ssh_prefix + [
                "ssh",
                self.remote_address,
                f"du -sh {remote_path} 2>/dev/null | cut -f1",
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                info["size"] = result.stdout.strip()

            # 获取outputs目录信息
            outputs_path = f"{remote_path}outputs"
            if self.run_ssh_command(f"test -d {outputs_path}"):
                # outputs目录大小
                cmd = ssh_prefix + [
                    "ssh",
                    self.remote_address,
                    f"du -sh {outputs_path} 2>/dev/null | cut -f1",
                ]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)
                if result.returncode == 0 and result.stdout.strip():
                    info["outputs_size"] = result.stdout.strip()

                # 模型文件数量
                cmd = ssh_prefix + [
                    "ssh",
                    self.remote_address,
                    f"find {outputs_path} -name '*.pth' -o -name '*.pt' 2>/dev/null | wc -l",
                ]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=20)
                if result.returncode == 0 and result.stdout.strip():
                    info["model_count"] = int(result.stdout.strip())

        except Exception as e:
            log_warning(f"获取远端项目信息失败: {e}")

        return info

    def show_status(self):
        """显示同步状态"""
        log_status("项目同步状态:")
        logger.info(f"   🌐 远端服务器: {self.remote_address}")
        logger.info(f"   🔐 SSH密码认证: {'✅' if self.ssh_password else '❌'}")
        logger.info(f"   🛠️  sshpass可用: {'✅' if self.sshpass_available else '❌'}")
        logger.info(f"   🕐 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        for name, project in self.projects.items():
            local_exists = os.path.exists(project["local_path"])
            exclude_exists = os.path.exists(project.get("exclude_file", ""))

            # 获取本地项目大小
            local_size = "未知"
            local_model_count = 0
            local_outputs_size = "未知"

            if local_exists:
                try:
                    result = subprocess.run(
                        ["du", "-sh", project["local_path"]],
                        capture_output=True,
                        text=True,
                        timeout=10,
                    )
                    if result.returncode == 0:
                        local_size = result.stdout.split()[0]
                except:
                    pass

                # 本地outputs信息
                local_outputs_path = os.path.join(project["local_path"], "outputs")
                if os.path.exists(local_outputs_path):
                    try:
                        # 本地模型文件数量
                        model_files = subprocess.run(
                            [
                                "find",
                                local_outputs_path,
                                "-name",
                                "*.pth",
                                "-o",
                                "-name",
                                "*.pt",
                            ],
                            capture_output=True,
                            text=True,
                        )
                        local_model_count = (
                            len(model_files.stdout.strip().split("\n"))
                            if model_files.stdout.strip()
                            else 0
                        )

                        # 本地outputs目录大小
                        outputs_size = subprocess.run(
                            ["du", "-sh", local_outputs_path],
                            capture_output=True,
                            text=True,
                            timeout=5,
                        )
                        if outputs_size.returncode == 0:
                            local_outputs_size = outputs_size.stdout.split()[0]
                    except:
                        pass

            # 获取远端项目信息
            log_network(f"获取远端项目 {name} 信息...")
            remote_info = self.get_remote_project_info(project["remote_path"])

            logger.info(f"   📦 {name}:")
            logger.info(
                f"     📂 本地路径: {project['local_path']} {'✅' if local_exists else '❌'}"
            )
            logger.info(
                f"     🌐 远端路径: {project['remote_path']} {'✅' if remote_info['exists'] else '❌'}"
            )
            logger.info(
                f"     📄 排除文件: {project.get('exclude_file')} {'✅' if exclude_exists else '❌'}"
            )

            # 项目大小对比
            logger.info(
                f"     📊 项目大小: 本地 {local_size} | 远端 {remote_info['size']}"
            )

            # 最后更新时间（远端）
            logger.info(f"     🕐 远端最后更新: {remote_info['last_modified']}")

            # 训练结果对比
            if local_model_count > 0 or remote_info["model_count"] > 0:
                logger.info(
                    f"     🤖 模型文件: 本地 {local_model_count} 个 | 远端 {remote_info['model_count']} 个"
                )
                logger.info(
                    f"     💾 训练结果: 本地 {local_outputs_size} | 远端 {remote_info['outputs_size']}"
                )

            print()  # 空行分隔项目

    def _load_history(self) -> dict:
        """加载同步历史"""
        try:
            if os.path.exists(self.history_file):
                import json

                with open(self.history_file, "r", encoding="utf-8") as f:
                    return json.load(f)
        except:
            pass
        return {}

    def _save_history(
        self, action: str, project: str, success: bool, details: dict = None
    ):
        """保存同步历史"""
        try:
            import json

            history = self._load_history()

            if "records" not in history:
                history["records"] = []

            record = {
                "timestamp": datetime.now().isoformat(),
                "action": action,
                "project": project,
                "success": success,
                "remote_host": self.remote_host,
                "details": details or {},
            }

            history["records"].append(record)

            # 只保留最近100条记录
            history["records"] = history["records"][-100:]

            with open(self.history_file, "w", encoding="utf-8") as f:
                json.dump(history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            log_warning(f"保存历史记录失败: {e}")

    def show_history(self, limit: int = 10):
        """显示同步历史"""
        history = self._load_history()
        records = history.get("records", [])

        if not records:
            log_warning("暂无同步历史记录")
            return

        log_status(f"最近 {min(limit, len(records))} 次同步记录:")

        for record in records[-limit:]:
            timestamp = datetime.fromisoformat(record["timestamp"]).strftime(
                "%m-%d %H:%M:%S"
            )
            action_emoji = {"push": "📤", "pull": "📥", "sync": "🔄"}.get(
                record["action"], "📝"
            )

            status_emoji = "✅" if record["success"] else "❌"

            logger.info(
                f"   {action_emoji} {timestamp} {record['action'].upper()} {record['project']} {status_emoji}"
            )

            if record.get("details"):
                details = record["details"]
                if "files_transferred" in details:
                    logger.info(f"      📄 传输文件: {details['files_transferred']}")
                if "bytes_transferred" in details:
                    logger.info(f"      📊 传输大小: {details['bytes_transferred']}")
                if "duration" in details:
                    logger.info(f"      ⏱️  耗时: {details['duration']:.1f}秒")


def main():
    parser = argparse.ArgumentParser(description="🚀 通用项目同步脚本")
    parser.add_argument("--host", default="************", help="远端服务器地址")
    parser.add_argument("--user", default="root", help="远端用户名")
    parser.add_argument(
        "--password", default="!Paxini123", help="SSH密码（不推荐，建议使用SSH密钥）"
    )
    parser.add_argument("--config", help="配置文件路径 (默认: sync_config.yaml)")
    parser.add_argument(
        "--action",
        choices=[
            "push",
            "pull",
            "sync",
            "status",
            "history",
            "list",
            "add",
            "remove",
            "save-config",
        ],
        default="sync",
        help="执行的操作",
    )
    parser.add_argument("--project", help="指定项目名称")
    parser.add_argument("--limit", type=int, default=10, help="历史记录显示数量")

    # 添加项目的参数
    parser.add_argument("--local-path", help="本地项目路径")
    parser.add_argument("--remote-path", help="远端项目路径")
    parser.add_argument("--exclude-file", help="排除文件路径")
    parser.add_argument("--description", help="项目描述")
    parser.add_argument("--auto-create", action="store_true", help="自动创建本地目录")

    args = parser.parse_args()

    # 获取SSH密码
    ssh_password = args.password
    if not ssh_password and args.action not in ["list", "save-config"]:
        try:
            ssh_password = getpass.getpass("🔐 请输入SSH密码 (回车跳过): ")
            if not ssh_password.strip():
                ssh_password = None
        except KeyboardInterrupt:
            log_warning("操作取消")
            sys.exit(0)

    # 创建同步器
    syncer = ProjectSyncer(args.host, args.user, ssh_password, args.config)

    try:
        if args.action == "list":
            syncer.list_projects()
        elif args.action == "add":
            if not args.project or not args.local_path or not args.remote_path:
                log_error("添加项目需要指定 --project, --local-path, --remote-path")
                sys.exit(1)
            syncer.add_project(
                args.project,
                args.local_path,
                args.remote_path,
                args.exclude_file,
                args.description,
                args.auto_create,
            )
        elif args.action == "remove":
            if not args.project:
                log_error("移除项目需要指定 --project")
                sys.exit(1)
            syncer.remove_project(args.project)
        elif args.action == "save-config":
            syncer.save_config(args.config)
        elif args.action == "status":
            syncer.show_status()
        elif args.action == "history":
            syncer.show_history(args.limit)
        elif args.action == "push":
            if args.project:
                if args.project not in syncer.projects:
                    log_error(
                        f"项目 '{args.project}' 不存在。使用 --action list 查看可用项目"
                    )
                    sys.exit(1)
                syncer.push_project(args.project)
            else:
                for project in syncer.projects:
                    syncer.push_project(project)
        elif args.action == "pull":
            project_name = (
                args.project or list(syncer.projects.keys())[0]
                if syncer.projects
                else None
            )
            if not project_name:
                log_error("没有可用的项目")
                sys.exit(1)
            if project_name not in syncer.projects:
                log_error(
                    f"项目 '{project_name}' 不存在。使用 --action list 查看可用项目"
                )
                sys.exit(1)
            syncer.pull_outputs(project_name)
        elif args.action == "sync":
            syncer.sync_all_projects()

    except KeyboardInterrupt:
        log_warning("操作被用户中断")
        sys.exit(1)
    except Exception as e:
        log_error(f"执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
