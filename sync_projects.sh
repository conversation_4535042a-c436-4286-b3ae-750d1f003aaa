#!/bin/bash
# 🚀 项目同步脚本 (Bash版本)
# =============================

set -e  # 遇到错误立即退出

# 配置
REMOTE_HOST="************"
REMOTE_USER="root"
REMOTE_ADDRESS="${REMOTE_USER}@${REMOTE_HOST}"

# 项目路径配置
PX_TACTRIX_LOCAL="/home/<USER>/workspace/px_tactrix/"
PX_TACTRIX_REMOTE="/root/workspace/px_tactrix/"

PX_JANUS_LOCAL="/home/<USER>/workspace/px_LearningSim_Janus/"
PX_JANUS_REMOTE="/root/workspace/px_LearningSim_Janus/"

# SSH密码（可选）
SSH_PASSWORD=""

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查sshpass是否可用
check_sshpass() {
    if command -v sshpass >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 获取SSH命令前缀
get_ssh_prefix() {
    if [ -n "$SSH_PASSWORD" ] && check_sshpass; then
        echo "sshpass -p $SSH_PASSWORD"
    else
        echo ""
    fi
}

# 获取rsync SSH选项
get_rsync_ssh_option() {
    if [ -n "$SSH_PASSWORD" ] && check_sshpass; then
        echo "sshpass -p '$SSH_PASSWORD' ssh"
    else
        echo ""
    fi
}

# 执行SSH命令
run_ssh_command() {
    local command="$1"
    local ssh_prefix=$(get_ssh_prefix)

    if [ -n "$ssh_prefix" ]; then
        $ssh_prefix ssh "$REMOTE_ADDRESS" "$command"
    else
        ssh "$REMOTE_ADDRESS" "$command"
    fi
}

# 推送项目函数
push_project() {
    local project_name="$1"
    local local_path="$2"
    local remote_path="$3"
    local exclude_file="$4"

    log_info "🚀 推送项目 ${project_name} 到远端..."
    log_info "   本地路径: ${local_path}"
    log_info "   远端路径: ${REMOTE_ADDRESS}:${remote_path}"

    # 检查本地路径
    if [ ! -d "$local_path" ]; then
        log_error "本地路径不存在: $local_path"
        return 1
    fi

    # 创建远端目录
    log_info "创建远端目录: $remote_path"
    run_ssh_command "mkdir -p $remote_path"

    # 构建rsync命令
    local rsync_cmd="rsync -avz --progress --delete"

    # 添加SSH选项
    local ssh_option=$(get_rsync_ssh_option)
    if [ -n "$ssh_option" ]; then
        rsync_cmd="$rsync_cmd -e '$ssh_option'"
    fi

    # 添加排除文件
    if [ -n "$exclude_file" ] && [ -f "$exclude_file" ]; then
        rsync_cmd="$rsync_cmd --exclude-from=$exclude_file"
        log_info "使用排除文件: $exclude_file"
    else
        log_warning "排除文件不存在: $exclude_file，使用默认排除规则"
        # 基本的默认排除规则
        rsync_cmd="$rsync_cmd --exclude=__pycache__/ --exclude=*.pyc --exclude=.venv/ --exclude=dist/ --exclude=.git/"
    fi

    rsync_cmd="$rsync_cmd $local_path ${REMOTE_ADDRESS}:$remote_path"

    log_info "执行命令: $rsync_cmd"

    if eval "$rsync_cmd"; then
        log_success "项目 ${project_name} 推送成功"
        return 0
    else
        log_error "项目 ${project_name} 推送失败"
        return 1
    fi
}

# 拉取训练结果函数
pull_outputs() {
    local project_name="$1"
    local remote_path="$2"
    local local_path="$3"

    log_info "📥 拉取 ${project_name} 的训练结果..."

    local remote_outputs="${REMOTE_ADDRESS}:${remote_path}outputs/"
    local local_outputs="${local_path}outputs/"

    log_info "   远端路径: $remote_outputs"
    log_info "   本地路径: $local_outputs"

    # 创建本地outputs目录
    mkdir -p "$local_outputs"

    # 检查远端outputs目录是否存在
    if ! run_ssh_command "test -d ${remote_path}outputs"; then
        log_warning "远端outputs目录不存在: ${remote_path}outputs"
        return 0  # 不算错误
    fi

    # 构建rsync命令
    local rsync_cmd="rsync -avz --progress"

    # 添加SSH选项
    local ssh_option=$(get_rsync_ssh_option)
    if [ -n "$ssh_option" ]; then
        rsync_cmd="$rsync_cmd -e '$ssh_option'"
    fi

    rsync_cmd="$rsync_cmd $remote_outputs $local_outputs"

    # 拉取训练结果（不使用--delete，保留本地文件）
    if eval "$rsync_cmd"; then
        log_success "${project_name} 训练结果拉取成功"

        # 显示拉取的模型文件
        local model_files=$(find "$local_outputs" -name "*.pth" -o -name "*.pt" 2>/dev/null | head -5)
        if [ -n "$model_files" ]; then
            log_info "拉取的模型文件:"
            echo "$model_files" | while read -r file; do
                log_info "  $file"
            done
        fi
        return 0
    else
        log_error "${project_name} 训练结果拉取失败"
        return 1
    fi
}

# 提示输入SSH密码
prompt_password() {
    if [ -z "$SSH_PASSWORD" ]; then
        echo -n "请输入SSH密码 (回车跳过): "
        read -s SSH_PASSWORD
        echo

        if [ -n "$SSH_PASSWORD" ] && ! check_sshpass; then
            log_warning "sshpass未安装，无法自动输入密码。请考虑配置SSH密钥认证。"
            log_info "安装sshpass: sudo apt-get install sshpass"
            SSH_PASSWORD=""
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo "🚀 项目同步脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  push-tactrix    推送px_tactrix项目"
    echo "  push-janus      推送px_LearningSim_Janus项目"
    echo "  push-all        推送所有项目"
    echo "  pull-outputs    拉取训练结果"
    echo "  sync            完整同步（推送所有项目+拉取结果）"
    echo "  status          显示项目状态"
    echo "  help            显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  SSH_PASSWORD    SSH密码（不推荐，建议使用SSH密钥）"
    echo ""
    echo "示例:"
    echo "  $0 sync                    # 完整同步"
    echo "  $0 push-tactrix           # 只推送px_tactrix"
    echo "  $0 pull-outputs           # 只拉取训练结果"
    echo "  SSH_PASSWORD=xxx $0 sync  # 使用密码同步"
}

# 显示项目状态
show_status() {
    log_info "📊 项目同步状态:"
    log_info "   远端服务器: $REMOTE_ADDRESS"

    if [ -n "$SSH_PASSWORD" ]; then
        log_info "   SSH密码认证: ✅"
    else
        log_info "   SSH密码认证: ❌"
    fi

    if check_sshpass; then
        log_info "   sshpass可用: ✅"
    else
        log_info "   sshpass可用: ❌"
    fi

    echo ""

    # px_tactrix状态
    if [ -d "$PX_TACTRIX_LOCAL" ]; then
        local_status="✅"
    else
        local_status="❌"
    fi

    if [ -f "rsync_exclude.txt" ]; then
        exclude_status="✅"
    else
        exclude_status="❌"
    fi

    log_info "   px_tactrix:"
    log_info "     本地路径: $PX_TACTRIX_LOCAL $local_status"
    log_info "     远端路径: $PX_TACTRIX_REMOTE"
    log_info "     排除文件: rsync_exclude.txt $exclude_status"
    echo ""

    # px_LearningSim_Janus状态
    if [ -d "$PX_JANUS_LOCAL" ]; then
        local_status="✅"
    else
        local_status="❌"
    fi

    log_info "   px_LearningSim_Janus:"
    log_info "     本地路径: $PX_JANUS_LOCAL $local_status"
    log_info "     远端路径: $PX_JANUS_REMOTE"
    log_info "     排除文件: rsync_exclude.txt $exclude_status"
}

# 主函数
main() {
    local action="${1:-sync}"

    # 如果需要密码且没有提供，则提示输入
    if [[ "$action" != "help" && "$action" != "status" ]]; then
        prompt_password
    fi

    case "$action" in
        "push-tactrix")
            push_project "px_tactrix" "$PX_TACTRIX_LOCAL" "$PX_TACTRIX_REMOTE" "rsync_exclude.txt"
            ;;
        "push-janus")
            push_project "px_LearningSim_Janus" "$PX_JANUS_LOCAL" "$PX_JANUS_REMOTE" "rsync_exclude.txt"
            ;;
        "push-all")
            log_info "🔄 推送所有项目..."
            push_project "px_tactrix" "$PX_TACTRIX_LOCAL" "$PX_TACTRIX_REMOTE" "rsync_exclude.txt"
            echo ""
            push_project "px_LearningSim_Janus" "$PX_JANUS_LOCAL" "$PX_JANUS_REMOTE" "rsync_exclude.txt"
            ;;
        "pull-outputs")
            pull_outputs "px_tactrix" "$PX_TACTRIX_REMOTE" "$PX_TACTRIX_LOCAL"
            ;;
        "sync")
            log_info "🔄 开始完整同步..."
            echo ""
            push_project "px_tactrix" "$PX_TACTRIX_LOCAL" "$PX_TACTRIX_REMOTE" "rsync_exclude.txt"
            echo ""
            push_project "px_LearningSim_Janus" "$PX_JANUS_LOCAL" "$PX_JANUS_REMOTE" "rsync_exclude.txt"
            echo ""
            pull_outputs "px_tactrix" "$PX_TACTRIX_REMOTE" "$PX_TACTRIX_LOCAL"
            echo ""
            log_success "🎉 完整同步完成！"
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 捕获Ctrl+C
trap 'log_warning "\n⚠️  操作被用户中断"; exit 1' INT

# 执行主函数
main "$@"
