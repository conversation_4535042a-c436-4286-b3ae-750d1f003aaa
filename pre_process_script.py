"""pre_process_script.py
============================================================
批量 *原位* 坐标变换脚
------------------------------------------------------------
• 逐个复制 `.h5/.hdf5` 专家数据文件到新目录。
• 对文件中指定数据集路径执行「世界坐标系 → 物体坐标系」数值转换，
  其他层级与 dtype 均保持不变。

用法示例：
    python pre_process_script.py  ./raw_h5_dir   ./converted_h5_dir

如需修改转换逻辑，请实现 `world_to_object()`。
============================================================
"""

# --- 必要标准库 & 第三方库 ---

from __future__ import annotations

import argparse
import sys
import os
from pathlib import Path

import h5py
import numpy as np

# ------------------------- Optional torch backend ----------------------------
try:
    import torch  # noqa: WPS433
except ImportError:  # pragma: no cover
    torch = None

_USE_TORCH_BACKEND = False  # 控制开关，由 CLI --use_torch 设置

import random
from concurrent.futures import ProcessPoolExecutor, as_completed

try:
    from tqdm.auto import tqdm
except ImportError:  # pragma: no cover
    # fallback dummy tqdm if not installed
    def tqdm(iterable=None, *args, **kwargs):
        return iterable if iterable is not None else lambda x: x


import logging

# -----------------------------------------------------------------------------
# Logging setup (configured later in __main__)
# -----------------------------------------------------------------------------

logger = logging.getLogger("preprocess")

import matplotlib.pyplot as plt

# ------------------------------------------------------------
# 配置：需要做坐标变换的 HDF5 数据集路径
# ------------------------------------------------------------

# --- 默认数据集路径，可通过 CLI 覆盖 ------------------------------------

DEFAULT_OBJ_PATH = "dataset/observation/state/obj1/data"
DEFAULT_HANDPOSE_PATHS = [
    # action 手部动作指令
    "dataset/action/lefthand/handpose/data",
    "dataset/action/righthand/handpose/data",
    # observation 观测到的手部状态
    "dataset/observation/state/lefthand/handpose/data",
    "dataset/observation/state/righthand/handpose/data",
]

# ------------------------------------------------------------------------

# ------------------------------------------------------------
# world_to_object —— 根据项目坐标系实现
# ------------------------------------------------------------


def _6d_to_matrix(rot6d: np.ndarray) -> np.ndarray:
    """6D 旋转表示 → 3×3 矩阵 (批量, NumPy 实现).

    实现基于 Gram-Schmidt 正交化，与项目中 `rot6d_to_matrix_gram_schmidt` 一致，
    但完全依赖 NumPy 以避免 `torch` 对 GPU/CuDNN 的要求。
    """
    # 保证形状为 (N, 6)
    rot6d = rot6d.reshape(-1, 6)
    a1 = rot6d[:, 0:3]
    a2 = rot6d[:, 3:6]

    # 第一列向量单位化
    b1 = a1 / (np.linalg.norm(a1, axis=1, keepdims=True) + 1e-8)

    # Gram-Schmidt 正交化得到第二列向量
    dot_prod = np.sum(b1 * a2, axis=1, keepdims=True)
    b2 = a2 - dot_prod * b1
    b2 = b2 / (np.linalg.norm(b2, axis=1, keepdims=True) + 1e-8)

    # 第三列为叉乘
    b3 = np.cross(b1, b2)

    # 拼接成旋转矩阵 (N,3,3)，列向量为 b1, b2, b3
    R = np.stack([b1, b2, b3], axis=-1)
    return R


def _matrix_to_6d(mat: np.ndarray) -> np.ndarray:
    """3×3 旋转矩阵 → 6D 表示 (批量, NumPy 实现)."""
    mat = mat.reshape(-1, 3, 3)
    # 取前两列向量
    rot6d = np.concatenate([mat[:, :, 0], mat[:, :, 1]], axis=1)  # (N, 6)
    return rot6d


# ------------------------- Torch backend 实现 ---------------------------------


def _6d_to_matrix_torch(rot6d: "torch.Tensor") -> "torch.Tensor":  # type: ignore
    rot6d = rot6d.reshape(-1, 6)
    a1 = rot6d[:, 0:3]
    a2 = rot6d[:, 3:6]

    b1 = a1 / (a1.norm(dim=1, keepdim=True) + 1e-8)
    dot_prod = (b1 * a2).sum(dim=1, keepdim=True)
    b2 = a2 - dot_prod * b1
    b2 = b2 / (b2.norm(dim=1, keepdim=True) + 1e-8)
    b3 = torch.cross(b1, b2, dim=1)
    return torch.stack([b1, b2, b3], dim=-1)  # (N,3,3)


def _matrix_to_6d_torch(mat: "torch.Tensor") -> "torch.Tensor":  # type: ignore
    mat = mat.reshape(-1, 3, 3)
    return torch.cat([mat[:, :, 0], mat[:, :, 1]], dim=1)


def world_to_object_torch(
    handpose: np.ndarray, obj_pose: np.ndarray
) -> np.ndarray:  # noqa: D401
    if torch is None:
        raise RuntimeError("Torch backend requested but torch not installed")

    if handpose.shape[-1] != 9 or obj_pose.shape[-1] != 9:
        raise ValueError("handpose / obj_pose 维度必须为 9")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    hp = torch.from_numpy(handpose).to(device, dtype=torch.float32)
    op = torch.from_numpy(obj_pose).to(device, dtype=torch.float32)

    pos_h, rot6_h = hp[:, :3], hp[:, 3:9]
    pos_o, rot6_o = op[:, :3], op[:, 3:9]

    R_h = _6d_to_matrix_torch(rot6_h)
    R_o = _6d_to_matrix_torch(rot6_o)

    R_rel = torch.einsum("bij,bjk->bik", R_o.transpose(1, 2), R_h)
    rot6_rel = _matrix_to_6d_torch(R_rel)

    pos_rel = torch.einsum("bij,bj->bi", R_o.transpose(1, 2), pos_h - pos_o)

    rel = torch.cat([pos_rel, rot6_rel], dim=1).cpu().numpy().astype(handpose.dtype)
    return rel


def world_to_object(
    handpose: np.ndarray,
    obj_pose: np.ndarray,
) -> np.ndarray:  # noqa: D401
    """示例实现：尝试调用项目自带的坐标变换工具。

    如果导入失败或形状不匹配，则回退为原样返回。
    你可在此处替换为任意自定义的 NumPy 变换逻辑。
    """

    global _USE_TORCH_BACKEND

    if _USE_TORCH_BACKEND:
        try:
            return world_to_object_torch(handpose, obj_pose)
        except Exception as exc:
            print(f"[world_to_object_torch] 回退到 NumPy: {exc}")

    try:
        if handpose.shape[-1] != 9 or obj_pose.shape[-1] != 9:
            raise ValueError("handpose / obj_pose 维度必须为 9")

        pos_h = handpose[:, :3]
        rot6_h = handpose[:, 3:9]

        pos_o = obj_pose[:, :3]
        rot6_o = obj_pose[:, 3:9]

        # 转为矩阵
        R_h = _6d_to_matrix(rot6_h)
        R_o = _6d_to_matrix(rot6_o)

        # 相对旋转: R_rel = R_o^T * R_h
        R_rel = np.einsum("bij,bjk->bik", R_o.transpose(0, 2, 1), R_h)
        rot6_rel = _matrix_to_6d(R_rel)

        # 相对位置: pos_rel = R_o^T * (p_h - p_o)
        pos_rel = np.einsum("bij,bj->bi", R_o.transpose(0, 2, 1), pos_h - pos_o)

        return np.concatenate([pos_rel, rot6_rel], axis=-1)

    except Exception as exc:  # noqa: BLE001
        print(f"[world_to_object] ⚠️  转换失败，直接返回原数组: {exc}")
        return handpose


# ----------------------------------------------------------------------------
# 文件验证和 h5py 处理单文件
# ----------------------------------------------------------------------------


def _is_valid_hdf5_file(file_path: Path) -> bool:
    """检查HDF5文件是否有效且可读"""
    try:
        with h5py.File(file_path, "r") as f:
            # 尝试访问文件的基本信息来验证完整性
            _ = f.keys()
            return True
    except Exception as e:
        logger.warning(f"文件 {file_path.name} 可能已损坏: {e}")
        return False


def _comprehensive_validation(
    world_pos: np.ndarray,
    world_rot: np.ndarray,
    obj_pos: np.ndarray,
    obj_rot: np.ndarray,
    rel_pos: np.ndarray,
    rel_rot: np.ndarray,
    out_dir: Path,
    filename_prefix: str,
    hand_name: str = "手部",
):
    """全面的3D可视化和数值验证"""

    # 1. 3D位置散点图
    fig = plt.figure(figsize=(16, 8))

    # 3D散点图
    ax1 = fig.add_subplot(2, 4, 1, projection="3d")
    ax1.scatter(
        world_pos[:, 0],
        world_pos[:, 1],
        world_pos[:, 2],
        c="blue",
        s=20,
        alpha=0.6,
        label=f"{hand_name} (World)",
    )
    ax1.scatter(
        obj_pos[:, 0],
        obj_pos[:, 1],
        obj_pos[:, 2],
        c="green",
        s=30,
        alpha=0.8,
        label="Object (World)",
    )
    ax1.set_title(f"{hand_name} - World Coordinate System")
    ax1.set_xlabel("X")
    ax1.set_ylabel("Y")
    ax1.set_zlabel("Z")
    ax1.legend()

    ax2 = fig.add_subplot(2, 4, 2, projection="3d")
    ax2.scatter(
        rel_pos[:, 0],
        rel_pos[:, 1],
        rel_pos[:, 2],
        c="red",
        s=20,
        alpha=0.6,
        label=f"{hand_name} (Object Frame)",
    )
    ax2.scatter([0], [0], [0], c="green", s=100, marker="*", label="Object (Origin)")
    ax2.set_title(f"{hand_name} - Object Coordinate System")
    ax2.set_xlabel("X")
    ax2.set_ylabel("Y")
    ax2.set_zlabel("Z")
    ax2.legend()

    # 2. 距离分析
    ax3 = fig.add_subplot(2, 4, 3)
    world_distances = np.linalg.norm(world_pos - obj_pos, axis=1)
    rel_distances = np.linalg.norm(rel_pos, axis=1)

    # 使用相同的bins范围，避免重叠
    dist_min = min(world_distances.min(), rel_distances.min())
    dist_max = max(world_distances.max(), rel_distances.max())
    bins = np.linspace(dist_min, dist_max, 20)

    ax3.hist(
        world_distances,
        bins=bins,
        alpha=0.6,
        label="World distances",
        color="blue",
        edgecolor="blue",
    )
    ax3.hist(
        rel_distances,
        bins=bins,
        alpha=0.6,
        label="Object distances",
        color="red",
        edgecolor="red",
        histtype="step",
        linewidth=2,
    )
    ax3.set_title(f"{hand_name} - Object Distance Distribution")
    ax3.set_xlabel("Distance")
    ax3.set_ylabel("Count")
    ax3.legend()

    # 添加距离差异检查
    dist_diff = np.abs(world_distances - rel_distances)
    if np.max(dist_diff) > 1e-6:
        ax3.text(
            0.02,
            0.98,
            f"Max distance diff: {np.max(dist_diff):.6f}",
            transform=ax3.transAxes,
            verticalalignment="top",
            bbox=dict(boxstyle="round", facecolor="yellow", alpha=0.8),
        )

    # 3. 坐标分量对比
    ax4 = fig.add_subplot(2, 4, 4)
    components = ["X", "Y", "Z"]
    world_std = np.std(world_pos, axis=0)
    rel_std = np.std(rel_pos, axis=0)

    x = np.arange(len(components))
    width = 0.35
    ax4.bar(x - width / 2, world_std, width, label="World std", color="blue", alpha=0.7)
    ax4.bar(x + width / 2, rel_std, width, label="Object std", color="red", alpha=0.7)
    ax4.set_title(f"{hand_name} - Standard Deviation by Axis")
    ax4.set_xlabel("Axis")
    ax4.set_ylabel("Standard Deviation")
    ax4.set_xticks(x)
    ax4.set_xticklabels(components)
    ax4.legend()

    # 4. 旋转角度分析（转换为实际角度）
    ax5 = fig.add_subplot(2, 4, 5)
    try:
        # 将6D旋转转换为旋转矩阵，然后计算旋转角度
        R_world = _6d_to_matrix(world_rot)
        R_rel = _6d_to_matrix(rel_rot)

        # 计算旋转角度（通过矩阵的迹）
        def rotation_angle_from_matrix(R):
            # 旋转角度 = arccos((tr(R) - 1) / 2)
            trace = np.trace(R, axis1=1, axis2=2)
            # 限制trace的范围以避免数值误差
            trace = np.clip(trace, -1, 3)
            angles = np.arccos(np.clip((trace - 1) / 2, -1, 1))
            return np.degrees(angles)  # 转换为度数

        world_angles = rotation_angle_from_matrix(R_world)
        rel_angles = rotation_angle_from_matrix(R_rel)

        # 绘制角度分布直方图
        bins = np.linspace(0, 180, 20)
        ax5.hist(
            world_angles,
            bins=bins,
            alpha=0.6,
            label="World angles",
            color="blue",
            density=True,
        )
        ax5.hist(
            rel_angles,
            bins=bins,
            alpha=0.6,
            label="Object angles",
            color="red",
            density=True,
            histtype="step",
            linewidth=2,
        )
        ax5.set_title(f"{hand_name} - Rotation Angle Distribution")
        ax5.set_xlabel("Rotation Angle (degrees)")
        ax5.set_ylabel("Density")
        ax5.legend()

        # 添加统计信息
        ax5.text(
            0.02,
            0.98,
            f"World: μ={world_angles.mean():.1f}°, σ={world_angles.std():.1f}°\nObject: μ={rel_angles.mean():.1f}°, σ={rel_angles.std():.1f}°",
            transform=ax5.transAxes,
            verticalalignment="top",
            bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
        )
    except Exception as e:
        ax5.text(
            0.5,
            0.5,
            f"Rotation analysis failed: {str(e)}",
            ha="center",
            va="center",
            transform=ax5.transAxes,
        )

    # 5. 旋转6D分量分析
    ax6 = fig.add_subplot(2, 4, 6)
    try:
        # 分析6D旋转向量的各个分量
        rot_components = ["R1", "R2", "R3", "R4", "R5", "R6"]
        world_rot_std = np.std(world_rot, axis=0)
        rel_rot_std = np.std(rel_rot, axis=0)

        x = np.arange(len(rot_components))
        width = 0.35
        ax6.bar(
            x - width / 2,
            world_rot_std,
            width,
            label="World rot std",
            color="blue",
            alpha=0.7,
        )
        ax6.bar(
            x + width / 2,
            rel_rot_std,
            width,
            label="Object rot std",
            color="red",
            alpha=0.7,
        )
        ax6.set_title(f"{hand_name} - Rotation 6D Components")
        ax6.set_xlabel("6D Rotation Component")
        ax6.set_ylabel("Standard Deviation")
        ax6.set_xticks(x)
        ax6.set_xticklabels(rot_components)
        ax6.legend()

        # 添加数值标签
        for i, (w_std, r_std) in enumerate(zip(world_rot_std, rel_rot_std)):
            ax6.text(
                i - width / 2,
                w_std + 0.01,
                f"{w_std:.3f}",
                ha="center",
                va="bottom",
                fontsize=8,
            )
            ax6.text(
                i + width / 2,
                r_std + 0.01,
                f"{r_std:.3f}",
                ha="center",
                va="bottom",
                fontsize=8,
            )
    except Exception as e:
        ax6.text(
            0.5,
            0.5,
            f"Rotation component analysis failed: {str(e)}",
            ha="center",
            va="center",
            transform=ax6.transAxes,
        )

    # 6. 数值统计表
    ax7 = fig.add_subplot(2, 4, 7)
    ax7.axis("off")

    stats_text = f"""
{hand_name} VALIDATION SUMMARY:

Position Statistics:
• World coordinate range:
  X: [{world_pos[:, 0].min():.3f}, {world_pos[:, 0].max():.3f}]
  Y: [{world_pos[:, 1].min():.3f}, {world_pos[:, 1].max():.3f}]
  Z: [{world_pos[:, 2].min():.3f}, {world_pos[:, 2].max():.3f}]

• Object coordinate range:
  X: [{rel_pos[:, 0].min():.3f}, {rel_pos[:, 0].max():.3f}]
  Y: [{rel_pos[:, 1].min():.3f}, {rel_pos[:, 1].max():.3f}]
  Z: [{rel_pos[:, 2].min():.3f}, {rel_pos[:, 2].max():.3f}]

Distance Statistics:
• Avg {hand_name}-object distance (world): {np.mean(world_distances):.3f}m
• Avg {hand_name}-object distance (object): {np.mean(rel_distances):.3f}m
• Distance std ratio: {np.std(rel_distances)/np.std(world_distances):.2f}

Concentration Metrics:
• World coordinate std: {np.mean(world_std):.3f}
• Object coordinate std: {np.mean(rel_std):.3f}
• Concentration improvement: {(np.mean(world_std)/np.mean(rel_std)):.2f}x
    """

    ax7.text(
        0.05,
        0.95,
        stats_text,
        transform=ax7.transAxes,
        fontsize=9,
        verticalalignment="top",
        fontfamily="monospace",
    )

    # 7. 旋转轴分析
    ax8 = fig.add_subplot(2, 4, 8)
    try:
        # 从旋转矩阵中提取旋转轴
        R_world = _6d_to_matrix(world_rot)
        R_rel = _6d_to_matrix(rel_rot)

        def extract_rotation_axis(R):
            """从旋转矩阵中提取旋转轴"""
            axes = []
            for i in range(len(R)):
                # 计算旋转轴 (通过特征向量)
                w, v = np.linalg.eig(R[i])
                # 找到特征值为1的特征向量（旋转轴）
                real_eigenvals = np.real(w)
                axis_idx = np.argmin(np.abs(real_eigenvals - 1))
                axis = np.real(v[:, axis_idx])
                axes.append(axis)
            return np.array(axes)

        world_axes = extract_rotation_axis(R_world)
        rel_axes = extract_rotation_axis(R_rel)

        # 绘制旋转轴的方向分布（只显示前50个样本避免过于密集）
        n_samples = min(50, len(world_axes))
        indices = np.linspace(0, len(world_axes) - 1, n_samples, dtype=int)

        ax8 = fig.add_subplot(2, 4, 8, projection="3d")
        ax8.quiver(
            0,
            0,
            0,
            world_axes[indices, 0],
            world_axes[indices, 1],
            world_axes[indices, 2],
            color="blue",
            alpha=0.6,
            length=0.1,
            normalize=True,
            label="World axes",
        )
        ax8.quiver(
            0,
            0,
            0,
            rel_axes[indices, 0],
            rel_axes[indices, 1],
            rel_axes[indices, 2],
            color="red",
            alpha=0.6,
            length=0.1,
            normalize=True,
            label="Object axes",
        )
        ax8.set_title(f"{hand_name} - Rotation Axes")
        ax8.set_xlabel("X")
        ax8.set_ylabel("Y")
        ax8.set_zlabel("Z")
        ax8.legend()

        # 设置坐标轴范围
        ax8.set_xlim([-1, 1])
        ax8.set_ylim([-1, 1])
        ax8.set_zlim([-1, 1])

    except Exception as e:
        ax8 = fig.add_subplot(2, 4, 8)
        ax8.text(
            0.5,
            0.5,
            f"Rotation axis analysis failed: {str(e)}",
            ha="center",
            va="center",
            transform=ax8.transAxes,
        )
        ax8.axis("off")

    plt.tight_layout(pad=1.5)
    plt.savefig(
        out_dir / f"{filename_prefix}_comprehensive.png", dpi=100, bbox_inches="tight"
    )
    plt.close(fig)  # 明确关闭特定的图形对象
    plt.clf()  # 清除当前图形
    plt.cla()  # 清除当前轴

    # 返回验证结果
    validation_results = {
        "world_std_mean": np.mean(world_std),
        "object_std_mean": np.mean(rel_std),
        "concentration_improvement": (
            np.mean(world_std) / np.mean(rel_std)
            if np.mean(rel_std) > 0
            else float("inf")
        ),
        "world_distance_mean": np.mean(world_distances),
        "object_distance_mean": np.mean(rel_distances),
        "distance_std_ratio": (
            np.std(rel_distances) / np.std(world_distances)
            if np.std(world_distances) > 0
            else float("inf")
        ),
    }

    return validation_results


def _save_hand_visualizations(
    world_pos: np.ndarray,
    world_rot: np.ndarray,
    obj_pos: np.ndarray,
    obj_rot: np.ndarray,
    rel_pos: np.ndarray,
    rel_rot: np.ndarray,
    out_dir: Path,
    hand_name: str,
):
    """生成汇总大图 (2×3 子图) 并额外用坐标系三轴直观展示旋转。"""

    import matplotlib.pyplot as plt
    from matplotlib.lines import Line2D

    # 输出目录
    hand_folder = "left_hand" if "left" in hand_name.lower() else "right_hand"
    save_dir = Path(out_dir) / hand_folder
    os.makedirs(save_dir, exist_ok=True)

    fontsize = 9

    # 预先计算常用量
    world_distances = np.linalg.norm(world_pos - obj_pos, axis=1)
    rel_distances = np.linalg.norm(rel_pos, axis=1)

    # 旋转矩阵 & 角度
    def _6d_to_matrix(rot6d: np.ndarray) -> np.ndarray:
        rot6d = rot6d.reshape(-1, 6)
        a1 = rot6d[:, 0:3]
        a2 = rot6d[:, 3:6]
        b1 = a1 / (np.linalg.norm(a1, axis=1, keepdims=True) + 1e-8)
        dot_prod = np.sum(b1 * a2, axis=1, keepdims=True)
        b2 = a2 - dot_prod * b1
        b2 = b2 / (np.linalg.norm(b2, axis=1, keepdims=True) + 1e-8)
        b3 = np.cross(b1, b2)
        return np.stack([b1, b2, b3], axis=-1)

    R_world = _6d_to_matrix(world_rot)
    R_rel = _6d_to_matrix(rel_rot)
    R_obj = _6d_to_matrix(obj_rot)

    def rotation_angle_from_matrix(R: np.ndarray) -> np.ndarray:
        trace = np.trace(R, axis1=1, axis2=2)
        trace = np.clip(trace, -1, 3)
        return np.degrees(np.arccos(np.clip((trace - 1) / 2, -1, 1)))

    world_angles = rotation_angle_from_matrix(R_world)
    rel_angles = rotation_angle_from_matrix(R_rel)

    # 创建大图
    fig = plt.figure(figsize=(18, 10))
    gs = fig.add_gridspec(2, 3, wspace=0.3, hspace=0.25)

    # (0,0) 世界坐标散点
    ax_ws = fig.add_subplot(gs[0, 0], projection="3d")
    ax_ws.scatter(
        world_pos[:, 0],
        world_pos[:, 1],
        world_pos[:, 2],
        c="tab:blue",
        s=8,
        alpha=0.6,
        label=hand_name,
    )
    ax_ws.scatter(
        obj_pos[:, 0],
        obj_pos[:, 1],
        obj_pos[:, 2],
        c="tab:green",
        s=18,
        alpha=0.9,
        label="Object",
    )
    ax_ws.set_title(f"{hand_name} – World Frame", fontsize=fontsize + 1)
    ax_ws.set_xlabel("X", fontsize=fontsize)
    ax_ws.set_ylabel("Y", fontsize=fontsize)
    ax_ws.set_zlabel("Z", fontsize=fontsize)
    ax_ws.legend(fontsize=fontsize - 1)

    # (0,1) 物体坐标散点
    ax_os = fig.add_subplot(gs[0, 1], projection="3d")
    ax_os.scatter(
        rel_pos[:, 0],
        rel_pos[:, 1],
        rel_pos[:, 2],
        c="tab:red",
        s=8,
        alpha=0.6,
        label=hand_name,
    )
    ax_os.scatter(
        [0], [0], [0], c="tab:green", s=40, marker="*", label="Object @ Origin"
    )
    ax_os.set_title(f"{hand_name} – Object Frame", fontsize=fontsize + 1)
    ax_os.set_xlabel("X", fontsize=fontsize)
    ax_os.set_ylabel("Y", fontsize=fontsize)
    ax_os.set_zlabel("Z", fontsize=fontsize)
    ax_os.legend(fontsize=fontsize - 1)

    # (0,2) 坐标系直观展示（世界坐标下）
    ax_cf = fig.add_subplot(gs[0, 2], projection="3d")

    # 取首样本做示意
    o_pos = obj_pos[0]
    h_pos = world_pos[0]
    R_o = R_obj[0]
    R_h = R_world[0]

    axis_len = np.median(world_distances) * 0.3 if len(world_distances) > 0 else 0.1

    colors = ["r", "g", "b"]  # XYZ

    for i in range(3):
        ax_cf.plot(
            [o_pos[0], o_pos[0] + R_o[0, i] * axis_len],
            [o_pos[1], o_pos[1] + R_o[1, i] * axis_len],
            [o_pos[2], o_pos[2] + R_o[2, i] * axis_len],
            color=colors[i],
            linewidth=2,
        )

    for i in range(3):
        ax_cf.plot(
            [h_pos[0], h_pos[0] + R_h[0, i] * axis_len],
            [h_pos[1], h_pos[1] + R_h[1, i] * axis_len],
            [h_pos[2], h_pos[2] + R_h[2, i] * axis_len],
            color=colors[i],
            linewidth=2,
            linestyle="--",
        )

    ax_cf.scatter(o_pos[0], o_pos[1], o_pos[2], c="tab:green", s=40, marker="o")
    ax_cf.scatter(h_pos[0], h_pos[1], h_pos[2], c="tab:blue", s=40, marker="^")

    ax_cf.set_title("Object (solid) vs Hand (dashed) Frames", fontsize=fontsize + 1)
    ax_cf.set_xlabel("X", fontsize=fontsize)
    ax_cf.set_ylabel("Y", fontsize=fontsize)
    ax_cf.set_zlabel("Z", fontsize=fontsize)

    legend_elems = [
        Line2D([0], [0], color="k", lw=2, label="Object axes"),
        Line2D([0], [0], color="k", lw=2, linestyle="--", label="Hand axes"),
    ]
    ax_cf.legend(handles=legend_elems, loc="upper left", fontsize=fontsize - 1)

    # (1,0) 距离直方图
    ax_dh = fig.add_subplot(gs[1, 0])
    bins = np.linspace(
        min(world_distances.min(), rel_distances.min()),
        max(world_distances.max(), rel_distances.max()),
        20,
    )
    ax_dh.hist(
        world_distances,
        bins=bins,
        alpha=0.6,
        label="World",
        color="tab:blue",
        edgecolor="tab:blue",
    )
    ax_dh.hist(
        rel_distances,
        bins=bins,
        alpha=0.6,
        label="Object",
        color="tab:red",
        edgecolor="tab:red",
        histtype="step",
        linewidth=1.5,
    )
    ax_dh.set_title("Hand – Object Distance", fontsize=fontsize + 1)
    ax_dh.set_xlabel("Distance", fontsize=fontsize)
    ax_dh.set_ylabel("Count", fontsize=fontsize)
    ax_dh.legend(fontsize=fontsize - 1)

    # (1,1) 旋转角度直方图
    ax_ra = fig.add_subplot(gs[1, 1])
    ang_bins = np.linspace(0, 180, 20)
    ax_ra.hist(
        world_angles,
        bins=ang_bins,
        alpha=0.65,
        label="World",
        color="tab:blue",
        density=True,
    )
    ax_ra.hist(
        rel_angles,
        bins=ang_bins,
        alpha=0.65,
        label="Object",
        color="tab:red",
        density=True,
        histtype="step",
        linewidth=1.5,
    )
    ax_ra.set_title("Rotation Angle Distribution", fontsize=fontsize + 1)
    ax_ra.set_xlabel("Angle (deg)", fontsize=fontsize)
    ax_ra.set_ylabel("Density", fontsize=fontsize)
    ax_ra.legend(fontsize=fontsize - 1)

    # (1,2) 6-D 旋转分量 Std
    ax_rs = fig.add_subplot(gs[1, 2])
    rot_components = [f"R{i+1}" for i in range(6)]
    world_rot_std = np.std(world_rot, axis=0)
    rel_rot_std = np.std(rel_rot, axis=0)
    x = np.arange(6)
    width = 0.35
    ax_rs.bar(
        x - width / 2, world_rot_std, width, label="World", color="tab:blue", alpha=0.8
    )
    ax_rs.bar(
        x + width / 2, rel_rot_std, width, label="Object", color="tab:red", alpha=0.8
    )
    ax_rs.set_xticks(x)
    ax_rs.set_xticklabels(rot_components)
    ax_rs.set_ylabel("Std", fontsize=fontsize)
    ax_rs.set_title("6-D Rotation Std", fontsize=fontsize + 1)
    ax_rs.legend(fontsize=fontsize - 1)

    # 保存并关闭
    fig.suptitle(f"{hand_name} – Summary", fontsize=fontsize + 3)
    fig.savefig(save_dir / "summary.png", dpi=110, bbox_inches="tight")
    plt.close(fig)

    # 彻底清理，防止未关闭的句柄阻塞脚本退出
    plt.close("all")


def _process_single_h5(
    src_path: Path,
    dst_path: Path,
    *,
    obj_path: str,
    handpose_paths: list[str],
    verify: bool = False,
    num_samples: int = 200,
    verbose: bool = False,
    chunk_size: int = 0,
) -> None:
    """复制单个 .h5 文件并在目标文件里就地修改数据。"""

    if dst_path.exists():
        dst_path.unlink()  # 避免 h5py 报错

    try:
        with h5py.File(src_path, "r") as src, h5py.File(dst_path, "w") as dst:
            # 复制顶层对象（data、attrs等）
            for key in src:
                src.copy(key, dst, name=key)
            # 复制文件级别属性
            for attr_key, attr_val in src.attrs.items():
                dst.attrs[attr_key] = attr_val

            # 读取基准物体位姿（从原始文件读取，避免使用已修改的数据）
            obj_np = src[obj_path][...] if obj_path in src else None

            # 对指定路径逐一处理
            paths_to_convert = handpose_paths + [obj_path]
            for dset_path in paths_to_convert:
                if dset_path not in dst:
                    if verbose:
                        print(f"[WARN] 数据集缺失: {dset_path} (文件 {src_path.name})")
                    continue

                dset = dst[dset_path]

                total_frames = dset.shape[0]
                if chunk_size <= 0 or total_frames <= chunk_size:
                    # -------- 原始整段处理 --------
                    original_np = dset[...]

                    if dset_path == obj_path:
                        transformed_np = np.zeros_like(original_np)
                        if transformed_np.shape[-1] == 9:
                            identity_6d = np.array(
                                [1.0, 0.0, 0.0, 0.0, 1.0, 0.0], dtype=original_np.dtype
                            )
                            transformed_np[:, 3:9] = identity_6d
                        else:
                            raise ValueError("Object pose expected 9D (3 pos + 6 rot)")
                    else:
                        if obj_np is None:
                            raise ValueError(
                                f"找不到基准数据集 {obj_path}，无法进行坐标转换"
                            )

                        obj_first = obj_np[0]
                        obj_ref = np.broadcast_to(obj_first, original_np.shape)
                        transformed_np = world_to_object(original_np, obj_ref)

                    dset[...] = transformed_np.astype(dset.dtype)

                else:
                    # -------- 分块处理 --------
                    if verbose:
                        print(
                            f"   ↻ 分块处理 {dset_path} (chunk={chunk_size}, total={total_frames})"
                        )

                    iteration = range(0, total_frames, chunk_size)
                    if verbose:
                        iteration = tqdm(
                            iteration, desc=f"{dset_path} chunks", unit="chunk"
                        )

                    for start_idx in iteration:
                        end_idx = min(start_idx + chunk_size, total_frames)
                        slc = slice(start_idx, end_idx)
                        original_chunk = dset[slc]

                        if dset_path == obj_path:
                            transformed_chunk = np.zeros_like(original_chunk)
                            if transformed_chunk.shape[-1] == 9:
                                identity_6d = np.array(
                                    [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
                                    dtype=original_chunk.dtype,
                                )
                                transformed_chunk[:, 3:9] = identity_6d
                            else:
                                raise ValueError(
                                    "Object pose expected 9D (3 pos + 6 rot)"
                                )
                        else:
                            obj_first = obj_np[0]
                            obj_ref = np.broadcast_to(obj_first, original_chunk.shape)
                            transformed_chunk = world_to_object(original_chunk, obj_ref)

                        dset[slc] = transformed_chunk.astype(dset.dtype)

        print(f"✅ {src_path.name} → {dst_path.name} 处理完成")

    except Exception as e:
        # 出现错误时删除目标文件，避免留下损坏文件
        if dst_path.exists():
            dst_path.unlink()
        print(f"❌ 处理 {src_path.name} 失败: {e}")
        raise

    # ----------------- 验证 / 可视化 (在文件关闭后进行) -----------------
    if verify:
        if verbose:
            print("   🔍 开始验证转换效果...")

        try:
            # 重新打开原始文件和转换后文件进行对比
            with h5py.File(src_path, "r") as src_f, h5py.File(dst_path, "r") as dst_f:
                # 检查物体数据是否存在
                if obj_path not in src_f:
                    if verbose:
                        print(f"   ⚠️ 原始文件中找不到物体数据: {obj_path}")
                    return

                obj_world = src_f[obj_path][...]  # 原始世界坐标

                # 分别验证左手和右手
                validation_results = None
                for i, hp_path in enumerate(handpose_paths):
                    if hp_path not in src_f or hp_path not in dst_f:
                        if verbose:
                            print(f"   ⚠️ 跳过缺失的数据集: {hp_path}")
                        continue

                    hand_name = (
                        "Left Hand"
                        if "lefthand" in hp_path
                        else "Right Hand" if "righthand" in hp_path else f"Hand {i+1}"
                    )

                    if verbose:
                        print(f"   📊 验证 {hand_name}: {hp_path}")

                    # 读取转换前后的数据
                    hp_world = src_f[hp_path][...]  # 原始世界坐标
                    hp_obj = dst_f[hp_path][...]  # 转换后物体坐标

                    # 随机采样
                    total_samples = len(hp_world)
                    sample_size = min(num_samples, total_samples)
                    sample_idx = random.sample(range(total_samples), sample_size)

                    world_pos_sample = hp_world[sample_idx, :3]
                    rel_pos_sample = hp_obj[sample_idx, :3]
                    obj_pos_sample = obj_world[sample_idx, :3]

                    # 计算距离统计
                    world_distances = np.linalg.norm(
                        world_pos_sample - obj_pos_sample, axis=1
                    )
                    obj_distances = np.linalg.norm(rel_pos_sample, axis=1)  # 物体在原点

                    if verbose:
                        print(f"     📈 采样数量: {sample_size}/{total_samples}")
                        print(
                            f"     📍 世界坐标范围: X[{world_pos_sample[:, 0].min():.3f}, {world_pos_sample[:, 0].max():.3f}]"
                        )
                        print(
                            f"     📍 物体坐标范围: X[{rel_pos_sample[:, 0].min():.3f}, {rel_pos_sample[:, 0].max():.3f}]"
                        )
                        print(
                            f"     📏 平均距离: 世界={world_distances.mean():.3f}m, 物体={obj_distances.mean():.3f}m"
                        )
                        print(
                            f"     📊 位置标准差: 世界={np.std(world_pos_sample, axis=0)}, 物体={np.std(rel_pos_sample, axis=0)}"
                        )

                        # 判断是否为有效操作手
                        if (
                            world_distances.mean() < 1.0
                        ):  # 距离物体小于1米认为是有效操作手
                            print(
                                f"     ✅ {hand_name} 是有效操作手 (平均距离 {world_distances.mean():.3f}m)"
                            )
                        else:
                            print(
                                f"     ⚠️ {hand_name} 可能是填充数据 (平均距离 {world_distances.mean():.3f}m)"
                            )

                    # 为每个有效的手部数据都生成可视化图
                    world_rot_sample = hp_world[sample_idx, 3:9]
                    rel_rot_sample = hp_obj[sample_idx, 3:9]
                    obj_rot_sample = obj_world[sample_idx, 3:9]
                    _save_hand_visualizations(
                        world_pos_sample,
                        world_rot_sample,
                        obj_pos_sample,
                        obj_rot_sample,
                        rel_pos_sample,
                        rel_rot_sample,
                        dst_path.parent,
                        hand_name,
                    )

                    # 仅在第一次循环时记录验证结果，用于后续汇总打印
                    if validation_results is None:
                        validation_results = {
                            "concentration_improvement": 1.0
                        }  # dummy for print

                if validation_results is not None:
                    print("   🔍 综合验证图已保存")

                    # 验证结果判断
                    if validation_results["concentration_improvement"] > 1.0:
                        print(
                            f"   ✅ 转换效果良好: 集中度提升 {validation_results['concentration_improvement']:.2f}x"
                        )
                    else:
                        print(
                            f"   ⚠️ 转换效果异常: 集中度变化 {validation_results['concentration_improvement']:.2f}x"
                        )
                        print(
                            "      可能原因: 1) 坐标系定义错误 2) 旋转变换方向错误 3) 数据本身问题"
                        )
                else:
                    print("   ⚠️ 未找到有效的手部数据进行验证")

        except Exception as e:
            print(f"   ❌ 验证过程失败: {e}")
            if verbose:
                import traceback

                traceback.print_exc()


# ----------------------------------------------------------------------------
# 主入口调整：处理目录/单文件
# ----------------------------------------------------------------------------


def preprocess_hdf5_dir(
    input_path: Path,
    output_dir: Path,
    *,
    obj_path: str,
    handpose_paths: list[str],
    verify: bool = False,
    num_samples: int = 200,
    verbose: bool = False,
    overwrite: bool = False,
    chunk_size: int = 0,
    workers: int = 1,
    use_torch: bool = False,
) -> None:
    """遍历输入路径（文件或目录）并批量处理。"""

    if input_path.is_file():
        files = [input_path]
    else:
        # 递归搜索，以支持多层目录结构
        files = list(input_path.rglob("*.h5")) + list(input_path.rglob("*.hdf5"))
        files.sort()

    if not files:
        raise FileNotFoundError(
            f"未找到任何 .h5/.hdf5 文件于 {input_path}，请确认路径和扩展名是否正确"
        )

    # 验证文件完整性（除非跳过）
    if not args.skip_validation:
        valid_files = []
        corrupted_files = []

        print(f"🔍 验证 {len(files)} 个HDF5文件的完整性...")
        for file_path in tqdm(files, desc="Validating files", unit="file"):
            if _is_valid_hdf5_file(file_path):
                valid_files.append(file_path)
            else:
                corrupted_files.append(file_path)

        if corrupted_files:
            print(f"⚠️  发现 {len(corrupted_files)} 个损坏的文件:")
            for corrupted_file in corrupted_files:
                print(f"   ❌ {corrupted_file.name}")
            print(f"✅ 将处理 {len(valid_files)} 个有效文件")

        if not valid_files:
            raise FileNotFoundError("没有找到任何有效的HDF5文件")

        files = valid_files
    else:
        print("⚠️  跳过文件完整性验证")

    output_dir.mkdir(parents=True, exist_ok=True)

    def _make_param_tuple(src_path: Path) -> tuple:
        return (
            str(src_path),
            str(output_dir / src_path.name),
            obj_path,
            handpose_paths,
            verify,
            num_samples,
            verbose,
            chunk_size,
            overwrite,
        )

    param_list = [_make_param_tuple(f) for f in files]

    def _process_worker(args_tuple):
        (
            src_s,
            dst_s,
            obj_path_w,
            handpose_paths_w,
            verify_w,
            num_samples_w,
            verbose_w,
            chunk_size_w,
            overwrite_w,
        ) = args_tuple

        src_p = Path(src_s)
        dst_p = Path(dst_s)

        if dst_p.exists() and not overwrite_w:
            print(f"   ⚠️ 目标文件已存在，跳过: {dst_p}")
            return

        try:
            _process_single_h5(
                src_p,
                dst_p,
                obj_path=obj_path_w,
                handpose_paths=handpose_paths_w,
                verify=verify_w,
                num_samples=num_samples_w,
                verbose=verbose_w,
                chunk_size=chunk_size_w,
            )
        except Exception as e:
            # Log the error but don't crash the entire process
            print(f"   ❌ 处理 {src_p.name} 失败: {e}")
            # Clean up any partial output file
            if dst_p.exists():
                try:
                    dst_p.unlink()
                except:
                    pass
            # Re-raise the exception to be handled by the caller
            raise

    if workers <= 1:
        failed_files = []
        for args_tuple in tqdm(param_list, desc="Files", unit="file"):
            try:
                _process_worker(args_tuple)
            except Exception as e:
                failed_files.append((args_tuple[0], str(e)))
                continue

        if failed_files:
            print(f"\n⚠️  处理失败的文件 ({len(failed_files)}/{len(param_list)}):")
            for failed_file, error in failed_files:
                print(f"   ❌ {Path(failed_file).name}: {error}")
            print(
                f"\n✅ 成功处理: {len(param_list) - len(failed_files)}/{len(param_list)} 个文件"
            )
        else:
            print(f"\n✅ 所有 {len(param_list)} 个文件处理成功")
    else:
        with ProcessPoolExecutor(max_workers=workers) as executor:
            future_to_param = {
                executor.submit(_process_worker, p): p for p in param_list
            }
            failed_files = []
            with tqdm(total=len(param_list), desc="Files", unit="file") as pbar:
                for future in as_completed(future_to_param):
                    try:
                        future.result()  # This will raise any exception from the worker
                    except Exception as e:
                        param = future_to_param[future]
                        failed_files.append((param[0], str(e)))
                    pbar.update(1)

            if failed_files:
                print(f"\n⚠️  处理失败的文件 ({len(failed_files)}/{len(param_list)}):")
                for failed_file, error in failed_files:
                    print(f"   ❌ {Path(failed_file).name}: {error}")
                print(
                    f"\n✅ 成功处理: {len(param_list) - len(failed_files)}/{len(param_list)} 个文件"
                )
            else:
                print(f"\n✅ 所有 {len(param_list)} 个文件处理成功")


# ----------------------------------------------------------------------------
# CLI 更新
# ----------------------------------------------------------------------------
if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="批量将 HDF5 专家数据从世界坐标转换到物体坐标系，保持文件结构不变。",
    )
    parser.add_argument("input", type=str, help="原始 .h5 文件或目录")
    parser.add_argument("output", type=str, help="输出目录")
    parser.add_argument("--verify", action="store_true", help="启用简单可视化验证")
    parser.add_argument("--num_samples", type=int, default=200, help="验证散点采样数量")
    parser.add_argument(
        "--obj_path",
        type=str,
        default=DEFAULT_OBJ_PATH,
        help="HDF5 内物体位姿数据集路径",
    )
    parser.add_argument(
        "--handpose",
        action="append",
        dest="handpose_paths",
        default=None,
        help="需要转换的 HandPose 数据集路径，可重复指定",
    )
    parser.add_argument("--verbose", action="store_true", help="打印调试信息")

    # 新增 CLI 参数（Phase 1 优化）
    parser.add_argument("--workers", type=int, default=1, help="并行处理进程数 (预留)")
    parser.add_argument(
        "--chunk", type=int, default=0, help="分块大小 (帧数)，0 表示整段处理 (预留)"
    )
    parser.add_argument(
        "--use_torch", action="store_true", help="启用 torch 后端 (需要安装 torch)"
    )
    parser.add_argument(
        "--overwrite", action="store_true", help="如目标文件已存在则覆盖，否则跳过"
    )
    parser.add_argument(
        "--log_level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="日志级别",
    )
    parser.add_argument(
        "--skip_validation",
        action="store_true",
        help="跳过文件完整性验证（不推荐，除非确定所有文件都有效）",
    )

    args = parser.parse_args()

    # -------------------------- Logging 配置 ---------------------------
    logging.basicConfig(
        level=getattr(logging, args.log_level.upper(), logging.INFO),
        format="%(asctime)s [%(levelname)s] %(message)s",
    )

    # 若启用 verbose，则强制 DEBUG 级别
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    # 将全局 print 重定向到 logger.info，保持向后兼容
    import builtins as _builtins  # noqa: WPS433

    _builtins.print = logger.info  # type: ignore

    # torch backend flag
    _USE_TORCH_BACKEND = args.use_torch and torch is not None
    if args.use_torch and torch is None:
        logger.warning("--use_torch 指定但未安装 torch，已回退到 NumPy")

    input_path = Path(args.input).expanduser().resolve()
    output_dir = Path(args.output).expanduser().resolve()

    handpose_paths = (
        args.handpose_paths if args.handpose_paths else DEFAULT_HANDPOSE_PATHS
    )

    preprocess_hdf5_dir(
        input_path,
        output_dir,
        obj_path=args.obj_path,
        handpose_paths=handpose_paths,
        verify=args.verify,
        num_samples=args.num_samples,
        verbose=args.verbose,
        overwrite=args.overwrite,
        chunk_size=args.chunk,
        workers=args.workers,
        use_torch=_USE_TORCH_BACKEND,
    )

    print("[ALL DONE] 所有文件处理完成 ✔")

    # 清理matplotlib资源
    import matplotlib.pyplot as plt

    plt.close("all")  # 关闭所有图形窗口

    # 显式退出
    import sys

    sys.exit(0)
