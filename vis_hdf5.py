# 基础库导入
import os
import logging
import json
import argparse
import random
import math
import pandas as pd
import signal
import sys
import threading
import time
from px_janus_learnsim.config.paths import ASSET_PATH
from tqdm import tqdm
from typing import List


# 设置日志配置
def setup_logging():
    """设置日志配置"""
    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 生成日志文件名，包含时间戳
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"vis_hdf5_{timestamp}.log")

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)  # 设置最低日志级别

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)  # 控制台显示INFO及以上级别
    console_format = logging.Formatter(
        "%(asctime)s [%(levelname)s] %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
    )
    console_handler.setFormatter(console_format)

    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, encoding="utf-8")
    file_handler.setLevel(logging.DEBUG)  # 文件记录DEBUG及以上级别
    file_format = logging.Formatter(
        "%(asctime)s [%(levelname)s] [%(name)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    file_handler.setFormatter(file_format)

    # 添加处理器到根日志记录器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

    # 设置一些第三方库的日志级别
    logging.getLogger("isaaclab").setLevel(logging.INFO)
    logging.getLogger("omni").setLevel(logging.WARNING)
    logging.getLogger("carb").setLevel(logging.WARNING)

    return root_logger


# 初始化日志
logger = setup_logging()
logger.info("日志系统初始化完成")

# Isaac Sim基础导入
from isaaclab.app import AppLauncher

# 创建参数解析器
parser = argparse.ArgumentParser(description="机器人状态可视化")
parser.add_argument(
    "--data_source", type=str, required=True, help="数据源路径（CSV或HDF5文件）"
)
parser.add_argument(
    "--data_type",
    type=str,
    choices=["csv", "hdf5"],
    help="数据类型：csv或hdf5（可选，默认根据文件后缀自动判断）",
)
parser.add_argument("--num_envs", type=int, default=1, help="环境数量")
parser.add_argument(
    "--hand_type",
    type=str,
    choices=["left", "right", "paxini"],
    default="left",
    help="手部模型类型：left（左手）, right（右手）, paxini（整机）",
)
parser.add_argument(
    "--object_type",
    type=str,
    choices=["bottle", "box", "sphere", "ketchup"],
    default="bottle",
    help="物体模型类型：bottle（瓶子）, ketchup（番茄酱）",
)
parser.add_argument(
    "--loop_count",
    type=int,
    default=1,
    help="仿真循环次数，-1表示无限循环",
)
parser.add_argument(
    "--camera_pos",
    type=float,
    nargs=3,
    help="相机位置 [x, y, z], 如果不指定则自动计算",
)
parser.add_argument(
    "--camera_target",
    type=float,
    nargs=3,
    help="相机目标点 [x, y, z], 如果不指定则自动计算",
)

parser.add_argument(
    "--close_tactile_vis",
    action="store_true",
    help="是否可视化触觉数据",
)
AppLauncher.add_app_launcher_args(parser)
args = parser.parse_args()

# 全局变量
simulation_app_instance = None
_should_exit = False  # 全局退出标志
_exit_lock = threading.Lock()  # 用于同步退出操作
_exit_timeout = 5.0  # 退出超时时间(秒)


def setup_signal_handlers():
    """设置信号处理器"""

    def signal_handler(signum, frame):
        global _should_exit
        with _exit_lock:
            if _should_exit:  # 如果已经在退出过程中,直接返回
                return
            _should_exit = True

        logger.info("\n检测到中断信号，正在停止仿真...")
        # 不再直接调用sys.exit(),而是设置标志让主线程处理退出

    # 注册SIGINT和SIGTERM信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def close_simulation_app(app, timeout=_exit_timeout):
    """安全地关闭simulation_app,带超时机制"""
    if app is None or not app.is_running():
        return

    logger.info("正在关闭Isaac Sim应用...")
    start_time = time.time()

    try:
        # 在新线程中执行close操作
        def close_thread():
            try:
                app.close()
            except Exception as e:
                logger.error(f"关闭Isaac Sim应用时出错: {str(e)}")

        close_thread = threading.Thread(target=close_thread)
        close_thread.daemon = True  # 设置为守护线程
        close_thread.start()

        # 等待关闭完成或超时
        close_thread.join(timeout)
        if close_thread.is_alive():
            logger.warning(f"关闭Isaac Sim应用超时({timeout}秒),强制退出")
            # 如果超时,强制退出
            os._exit(1)
        else:
            logger.info("Isaac Sim应用已关闭")

    except Exception as e:
        logger.error(f"关闭Isaac Sim应用时出错: {str(e)}")
        # 如果发生异常,强制退出
        os._exit(1)


# 在创建app_launcher后设置全局变量
app_launcher = AppLauncher(args)
simulation_app = app_launcher.app
simulation_app_instance = simulation_app  # 保存到全局变量

# 设置信号处理器
setup_signal_handlers()

# 其他库导入
import h5py
import torch
import numpy as np
import matplotlib

# 尝试使用适合的后端，如果Qt不可用则回退到默认后端
try:
    matplotlib.use("Qt5Agg")  # 优先尝试Qt后端
except ImportError:
    try:
        matplotlib.use("TkAgg")  # 回退到Tk后端
    except ImportError:
        # 使用默认后端，通常是Agg
        matplotlib.use("Agg")
import matplotlib.pyplot as plt

# 设置字体以支持中文显示或禁用中文字符警告
plt.rcParams["font.sans-serif"] = ["DejaVu Sans", "SimHei", "Arial Unicode MS"]
plt.rcParams["axes.unicode_minus"] = False
from typing import Dict, List, Tuple, Optional, Union, Any
from scipy.spatial.transform import Rotation as R

# Isaac Sim相关导入
import isaaclab.sim as sim_utils
from isaaclab.sim import (
    SimulationContext,
    SimulationCfg,
    GroundPlaneCfg,
    DomeLightCfg,
    PinholeCameraCfg,
)
from isaaclab.sensors import CameraCfg
from isaaclab.assets import ArticulationCfg, AssetBaseCfg, RigidObjectCfg
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.utils import configclass
from isaaclab.managers import SceneEntityCfg
from isaaclab.utils.math import quat_from_matrix

# 项目相关导入
from px_janus_learnsim.dataprocess.PaxiniTactileRobot.PaxiniTactileRobot_clerk import (
    PaxiniTactileRobotDataset,
)
from px_janus_learnsim.robot.DexH13.DexH13_left import DexH13_left_CFG
from px_janus_learnsim.robot.DexH13.DexH13_right import DexH13_right_CFG


# 添加模型配置映射
HAND_MODEL_CONFIGS = {
    "left": DexH13_left_CFG,
    "right": DexH13_right_CFG,
}

# 添加关节配置映射
HAND_JOINT_CONFIGS = {
    "left": {
        "mzcb": 0.00,
        "mzjdxz": 0.00,
        "mzzdxz": 0.00,
        "mzyd": 0.00,
        "szcb": 0.0,
        "szjdxz": 0.0,
        "szzdxz": 0.0,
        "szydxz": 0.0,
        "zzcb": 0.0,
        "zzjdxz": 0.0,
        "zzzdxz": 0.0,
        "zzydxz": 0.0,
        "wmzcb": 0.0,
        "wmzjdxz": 0.0,
        "wmzzdxz": 0.0,
        "wmzydxz": 0.0,
    },
    "right": {
        "mzcb": 0.00,
        "mzjdxz": 0.00,
        "mzzd": 0.00,  # 注意：右手使用mzzd而不是mzzdxz
        "mzydxz": 0.00,  # 注意：右手使用mzydxz而不是mzyd
        "szcb": 0.0,
        "szjdxz": 0.0,
        "szzdxz": 0.0,
        "szydxz": 0.0,
        "zzcb": 0.0,
        "zzjdxz": 0.0,
        "zzzdxz": 0.0,
        "zzydxz": 0.0,
        "wmzcb": 0.0,
        "wmzjdxz": 0.0,
        "wmzzdxz": 0.0,
        "wmzydxz": 0.0,
    },
}

OBJECT_MODEL_CONFIGS = {
    "bottle": {
        "usd_path": f"{ASSET_PATH}/yeyedenongchangfanqiejiang.usda",
        "scale": np.array([0.001, 0.001, 0.001]),
        "mass": 0.0001,
        "init_pos": (-0.455, 0.005, 10.18),
        "collision_enabled": False,
        "rigid_body_enabled": True,  # 添加刚体属性
        "kinematic_enabled": True,  # 添加运动学属性
    },
    "ketchup": {
        "usd_path": f"{ASSET_PATH}/005_tomato_soup_can.usd",
        "scale": np.array([1.0, 1.0, 1.0]),
        "mass": 0.001,
        "init_pos": (-0.5, 0.0, 0.1),
        "collision_enabled": False,
        "rigid_body_enabled": True,  # 添加刚体属性
        "kinematic_enabled": True,  # 添加运动学属性
    },
}


@configclass
class RobotSceneCfg(InteractiveSceneCfg):
    """可配置的机器人场景类，支持自定义手部和物体模型"""

    def __init__(
        self,
        hand_cfg: ArticulationCfg,
        object_cfg: Optional[RigidObjectCfg] = None,
        num_envs: int = 1,
        env_spacing: float = 2.0,
        camera_pos: Tuple[float, float, float] = (-1, 0.1, 10.18),
        camera_target: Tuple[float, float, float] = (0.0, 0.0, 0.0),
        **kwargs,
    ):
        super().__init__(num_envs=num_envs, env_spacing=env_spacing, **kwargs)

        # 地面
        self.ground = AssetBaseCfg(
            prim_path="/World/defaultGroundPlane", spawn=GroundPlaneCfg()
        )

        # 灯光
        self.dome_light = AssetBaseCfg(
            prim_path="/World/Light",
            spawn=DomeLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75)),
        )

        # 手部模型
        self.hand = hand_cfg

        # 物体模型（可选）
        if object_cfg is not None:
            self.object = object_cfg

        # 相机
        self.camera = CameraCfg(
            prim_path="{ENV_REGEX_NS}/Camera",
            update_period=0.0,
            height=480,
            width=640,
            data_types=[
                "rgb",
                "distance_to_image_plane",
                "semantic_segmentation",
                "instance_id_segmentation_fast",
            ],
            spawn=PinholeCameraCfg(
                focal_length=15.0,
                focus_distance=400.0,
                horizontal_aperture=20.955,
            ),
            offset=CameraCfg.OffsetCfg(
                pos=list(camera_pos),
                convention="world",
            ),
            depth_clipping_behavior="min",
        )


class RobotStateVisualizer:
    """用于加载和可视化机器人状态数据的类"""

    def __init__(
        self,
        data_source: str,
        data_type: str = "hdf5",
        hand_type: str = "right",
        hand_cfg: Optional[ArticulationCfg] = None,
        object_cfg: Optional[RigidObjectCfg] = None,
        seq_length: int = 1,
        stride: int = 1,
        parse_pointcloud: bool = True,
        simulation_app: Optional[Any] = None,
        camera_pos: Optional[Tuple[float, float, float]] = None,  # 添加相机位置参数
        camera_target: Optional[
            Tuple[float, float, float]
        ] = None,  # 添加相机目标点参数
        close_tactile_vis: bool = False,
    ):
        """
        初始化数据加载器

        参数:
        - data_source: 数据源路径（HDF5文件或CSV文件）
        - data_type: 数据类型，'hdf5'或'csv'
        - hand_type: 手部类型，'left'或'right'
        - hand_cfg: 手部模型配置
        - object_cfg: 物体模型配置
        - seq_length: 序列长度
        - stride: 采样步长
        - parse_pointcloud: 是否解析点云数据
        - simulation_app: Isaac Sim应用实例（可选）
        - camera_pos: 相机位置，如果为None则自动计算
        - camera_target: 相机目标点，如果为None则自动计算
        - close_tactile_vis: 是否关闭触觉可视化
        """
        self.data_source = data_source
        self.data_type = data_type.lower()
        self.hand_type = hand_type.lower()
        self.hand_cfg = hand_cfg
        self.object_cfg = object_cfg
        self.seq_length = seq_length
        self.stride = stride
        self.parse_pointcloud = parse_pointcloud
        self.simulation_app = simulation_app
        self.camera_pos = camera_pos
        self.camera_target = camera_target
        self.logger = logging.getLogger(__name__)

        # 初始化close_tactile_vis，必须在_load_data()之前设置
        self.close_tactile_vis = close_tactile_vis
        if self.close_tactile_vis:
            self.logger.info("关闭触觉数据可视化")
        else:
            self.logger.info("打开触觉数据可视化")

        # 验证手部类型
        if self.hand_type not in ["left", "right"]:
            raise ValueError(f"不支持的手部类型: {hand_type}，仅支持'left'或'right'")

        # 验证数据源
        if not os.path.exists(data_source):
            raise FileNotFoundError(f"数据源 {data_source} 不存在")

        # 初始化多文件播放相关属性
        self.current_file_index = 0
        self.h5_files = []
        self.total_files = 1

        # 如果是目录，获取所有h5文件
        if os.path.isdir(data_source):
            self.h5_files = self._get_h5_files_from_directory(data_source)
            self.total_files = len(self.h5_files)
            if self.total_files == 0:
                raise FileNotFoundError(f"目录 {data_source} 中未找到.h5文件")
            self.logger.info(f"检测到目录模式，找到 {self.total_files} 个.h5文件")
        else:
            # 单文件模式
            self.h5_files = [data_source]
            self.total_files = 1

        # 初始化数据
        self.data = None
        self.dataset = None
        self._load_data()

        # 初始化仿真环境
        self.sim = None
        self.scene = None
        if simulation_app is not None:
            self._init_simulation()

        self._should_stop = False
        self.simulation_app = simulation_app
        self._exit_lock = threading.Lock()  # 添加线程锁

        # 初始化触觉数据可视化相关属性
        if not self.close_tactile_vis:
            # 添加新的属性用于触觉数据可视化
            self.hand_sketch = None
            self._tactile_mapping = None
            self._tactile_data_cache = None  # 添加触觉数据缓存

            # 初始化时加载背景图和创建映射
            self._load_hand_sketch()
            self._tactile_mapping = self._create_tactile_mapping()

            # 预处理触觉数据以提高性能
            self._preprocess_tactile_data()
        else:
            self.hand_sketch = None
            self._tactile_mapping = None
            self._tactile_data_cache = None  # 添加触觉数据缓存

    def _get_h5_files_from_directory(self, directory: str) -> List[str]:
        """获取目录中所有的h5文件并按文件名排序

        参数:
        - directory: 目录路径

        返回:
        - 排序后的h5文件完整路径列表
        """
        try:
            # 获取目录中所有.h5文件
            h5_files = []
            for filename in os.listdir(directory):
                if filename.lower().endswith((".h5", ".hdf5")):
                    full_path = os.path.join(directory, filename)
                    h5_files.append(full_path)

            # 按文件名排序
            h5_files.sort()

            self.logger.info(f"在目录 {directory} 中找到 {len(h5_files)} 个h5文件")
            for i, file_path in enumerate(h5_files):
                self.logger.info(f"  {i+1}. {os.path.basename(file_path)}")

            return h5_files

        except Exception as e:
            self.logger.error(f"获取目录中h5文件时出错: {str(e)}")
            return []

    def _load_data(self):
        """加载数据"""
        try:
            self.logger.info(f"开始加载数据: {self.data_source}")
            with tqdm(total=2, desc="数据加载进度", unit="步骤") as pbar:
                if self.data_type == "hdf5":
                    self._load_hdf5_data(pbar)
                elif self.data_type == "csv":
                    self._load_csv_data(pbar)
                else:
                    raise ValueError(f"不支持的数据类型: {self.data_type}")
                pbar.update(1)  # 更新总体进度
            self.logger.info(f"成功加载数据: {self.data_source}")
        except Exception as e:
            self.logger.error(f"加载数据失败: {str(e)}")
            raise

    def _load_hdf5_data(self, pbar: Optional[tqdm] = None):
        """加载HDF5格式数据"""
        try:
            # 获取当前要加载的文件路径
            current_file = self.h5_files[self.current_file_index]

            # 显示数据集加载进度
            with tqdm(
                total=1,
                desc=f"加载HDF5数据集 ({self.current_file_index+1}/{self.total_files})",
                unit="数据集",
                leave=False,
            ) as dataset_pbar:
                self.dataset = PaxiniTactileRobotDataset(
                    current_file,
                    seq_length=self.seq_length,
                    stride=self.stride,
                    parse_pointcloud=self.parse_pointcloud,
                )
                dataset_pbar.update(1)

            # 显示数据获取进度
            with tqdm(
                total=1, desc="获取数据序列", unit="序列", leave=False
            ) as data_pbar:
                # 直接打开HDF5文件读取数据
                with h5py.File(current_file, "r") as f:
                    converted_data = {}

                    # 读取左手数据
                    if "dataset/observation/state/lefthand" in f:
                        left_hand = f["dataset/observation/state/lefthand"]
                        if "joints/data" in left_hand:
                            converted_data["left_joint_angles"] = left_hand[
                                "joints/data"
                            ][:].tolist()
                        if "handpose/data" in left_hand:
                            hand_pose = left_hand["handpose/data"][:]
                            converted_data["left_hand_pos_xyz"] = hand_pose[
                                :, :3
                            ].tolist()
                            converted_data["left_hand_rot_q_xyzw"] = hand_pose[
                                :, 3:
                            ].tolist()
                        if "tactile/data" in left_hand:
                            converted_data["left_tactile_fenli"] = left_hand[
                                "tactile/data"
                            ][:].tolist()

                    # 读取右手数据
                    if "dataset/observation/state/righthand" in f:
                        right_hand = f["dataset/observation/state/righthand"]
                        if "joints/data" in right_hand:
                            converted_data["right_joint_angles"] = right_hand[
                                "joints/data"
                            ][:].tolist()
                        if "handpose/data" in right_hand:
                            hand_pose = right_hand["handpose/data"][:]
                            converted_data["right_hand_pos_xyz"] = hand_pose[
                                :, :3
                            ].tolist()
                            converted_data["right_hand_rot_q_xyzw"] = hand_pose[
                                :, 3:
                            ].tolist()
                        if "tactile/data" in right_hand:
                            converted_data["right_tactile_fenli"] = right_hand[
                                "tactile/data"
                            ][:].tolist()

                    # 读取物体数据
                    if "dataset/observation/state/obj1/data" in f:
                        obj1_data = f["dataset/observation/state/obj1/data"][:]
                        converted_data["obj_pos_xyz"] = obj1_data[:, :3].tolist()
                        converted_data["obj_rot_q_xyzw"] = obj1_data[:, 3:9].tolist()

                    if "dataset/observation/state/obj2/data" in f:
                        obj2_data = f["dataset/observation/state/obj2/data"][:]
                        converted_data["obj2_pos_xyz"] = obj2_data[:, :3].tolist()
                        converted_data["obj2_rot_q_xyzw"] = obj2_data[:, 3:9].tolist()

                # 为了保持向后兼容性,如果只指定了一只手,则使用原来的键名
                if self.hand_type in ["left", "right"]:
                    prefix = f"{self.hand_type}_"
                    for key in list(converted_data.keys()):
                        if key.startswith(prefix):
                            new_key = key[len(prefix) :]
                            converted_data[new_key] = converted_data.pop(key)

                self.data = converted_data
                data_pbar.update(1)

                # 记录数据信息
                loaded_hands = []
                for hand_type in ["left", "right"]:
                    prefix = f"{hand_type}_"
                    hand_fields = [
                        k for k in converted_data.keys() if k.startswith(prefix)
                    ]
                    if hand_fields:
                        loaded_hands.append(hand_type)

                if loaded_hands:
                    self.logger.info(
                        f"成功加载手部数据: {', '.join(loaded_hands)}，包含字段: {', '.join(converted_data.keys())}"
                    )
                else:
                    self.logger.info(
                        f"成功加载数据，包含字段: {', '.join(converted_data.keys())}"
                    )

                # 记录数据序列长度
                if "joint_angles" in converted_data:
                    self.logger.info(
                        f"数据序列长度: {len(converted_data['joint_angles'])}"
                    )
                elif "left_joint_angles" in converted_data:
                    self.logger.info(
                        f"左手数据序列长度: {len(converted_data['left_joint_angles'])}"
                    )
                elif "right_joint_angles" in converted_data:
                    self.logger.info(
                        f"右手数据序列长度: {len(converted_data['right_joint_angles'])}"
                    )
                else:
                    self.logger.info("数据序列长度: 未知")
                if self.close_tactile_vis:
                    # 检查触觉数据维度并在必要时生成模拟数据
                    tactile_data_info = []
                    should_generate_sim_tactile = False
                    tactile_keys_to_replace = []

                    for key in [
                        "tactile_fenli",
                        "left_tactile_fenli",
                        "right_tactile_fenli",
                    ]:
                        if key in converted_data and converted_data[key]:
                            try:
                                first_frame = np.array(converted_data[key][0])
                                tactile_data_info.append(f"{key}: {first_frame.shape}")

                                # 检查是否兼容1140维格式
                                if first_frame.shape == (1140, 3):
                                    # 数据已经是正确格式
                                    pass
                                elif first_frame.shape == (12, 120, 3):
                                    self.logger.warning(
                                        f"{key}维度为(12, 120, 3)，与期望的(1140, 3)不匹配，将生成模拟数据替代"
                                    )
                                    tactile_keys_to_replace.append(key)
                                    should_generate_sim_tactile = True
                                else:
                                    self.logger.warning(
                                        f"{key}维度为{first_frame.shape}，不支持触觉可视化，将生成模拟数据替代"
                                    )
                                    tactile_keys_to_replace.append(key)
                                    should_generate_sim_tactile = True
                            except Exception as e:
                                tactile_data_info.append(f"{key}: 无法解析")
                                self.logger.warning(
                                    f"{key}无法解析，将生成模拟数据替代"
                                )
                                tactile_keys_to_replace.append(key)
                                should_generate_sim_tactile = True

                    if tactile_data_info:
                        self.logger.info(
                            f"原始触觉数据维度: {', '.join(tactile_data_info)}"
                        )

                    # 如果需要生成模拟触觉数据
                    if should_generate_sim_tactile:
                        # 确定帧数 - 从任何可用的数据源获取
                        num_frames = 0
                        for key in [
                            "joint_angles",
                            "left_joint_angles",
                            "right_joint_angles",
                        ]:
                            if key in converted_data and converted_data[key]:
                                num_frames = len(converted_data[key])
                                break

                        if num_frames == 0:
                            num_frames = 100  # 默认值
                            self.logger.warning("无法确定帧数，使用默认值100")

                        # 生成模拟触觉数据
                        sim_tactile_data = self._generate_sim_tactile_data(num_frames)

                        # 替换不兼容的触觉数据
                        for key in tactile_keys_to_replace:
                            converted_data[key] = sim_tactile_data
                            self.logger.info(f"已将{key}替换为模拟的1140维触觉数据")

                        self.logger.info(
                            f"成功生成并替换了{len(tactile_keys_to_replace)}个触觉数据字段"
                        )

                    # 记录最终的触觉数据状态
                    compatible_data = []
                    for key in [
                        "tactile_fenli",
                        "left_tactile_fenli",
                        "right_tactile_fenli",
                    ]:
                        if key in converted_data and converted_data[key]:
                            try:
                                first_frame = np.array(converted_data[key][0])
                                if first_frame.shape == (1140, 3):
                                    compatible_data.append(key)
                            except:
                                pass

                    if compatible_data:
                        self.logger.info(
                            f"最终兼容1140维触觉可视化的数据: {', '.join(compatible_data)}"
                        )
                    else:
                        self.logger.info("未发现兼容1140维触觉可视化的数据")

            if pbar is not None:
                pbar.update(1)  # 更新总体进度
        except Exception as e:
            self.logger.error(f"加载HDF5数据失败: {str(e)}")
            raise

    def _load_csv_data(self, pbar: Optional[tqdm] = None):
        """加载CSV格式数据"""
        try:
            # 显示CSV读取进度
            with tqdm(
                total=1, desc="读取CSV文件", unit="文件", leave=False
            ) as read_pbar:
                data = pd.read_csv(self.data_source)
                read_pbar.update(1)

            # 显示数据转换进度
            total_columns = len(data.columns)
            with tqdm(
                total=total_columns, desc="转换数据格式", unit="列", leave=False
            ) as convert_pbar:
                # 转换触觉数据
                if "tactile_heli" in data.columns:
                    data["tactile_heli"] = data["tactile_heli"].apply(lambda x: eval(x))
                    convert_pbar.update(1)
                if "tactile_fenli" in data.columns:
                    data["tactile_fenli"] = data["tactile_fenli"].apply(
                        lambda x: eval(x)
                    )
                    convert_pbar.update(1)

                # 转换关节角度
                if "joint_angles" in data.columns:
                    data["joint_angles"] = data["joint_angles"].apply(
                        lambda x: [math.radians(angle) for angle in eval(x)]
                    )
                    convert_pbar.update(1)

                # 转换物体位置
                if "obj_pos_xyz" in data.columns:
                    data["obj_pos_xyz"] = data["obj_pos_xyz"].apply(
                        lambda lst: [x / 1000 for x in eval(lst)]
                    )
                    convert_pbar.update(1)

                # 转换物体旋转
                if "obj_rot_q_xyzw" in data.columns:
                    data["obj_rot_q_xyzw"] = data["obj_rot_q_xyzw"].apply(
                        lambda x: eval(x)[-1:] + eval(x)[:-1]  # xyzw -> wxyz
                    )
                    convert_pbar.update(1)

                # 转换手部位置
                if "hand_pos_xyz" in data.columns:
                    data["hand_pos_xyz"] = data["hand_pos_xyz"].apply(
                        lambda lst: [x / 1000 for x in eval(lst)]
                    )
                    convert_pbar.update(1)

                # 转换手部旋转
                if "hand_rot_q_xyzw" in data.columns:
                    data["hand_rot_q_xyzw"] = data["hand_rot_q_xyzw"].apply(
                        lambda x: eval(x)[-1:] + eval(x)[:-1]  # xyzw -> wxyz
                    )
                    convert_pbar.update(1)

                # 更新剩余列数
                convert_pbar.update(total_columns - convert_pbar.n)

            self.data = data
            if pbar is not None:
                pbar.update(1)  # 更新总体进度

        except Exception as e:
            self.logger.error(f"加载CSV数据失败: {str(e)}")
            self.logger.error("详细错误信息:", exc_info=True)
            # 创建一个新的异常，包含原始异常的信息
            raise RuntimeError(f"加载CSV数据失败: {str(e)}") from e

    def _calculate_camera_view(self) -> Tuple[List[float], List[float]]:
        """计算合适的相机视角

        分析手部和物体的位置数据,计算合适的相机位置和目标点。

        返回:
        - camera_pos: 相机位置 [x, y, z]
        - camera_target: 相机目标点 [x, y, z]
        """
        try:
            # 收集所有位置数据
            positions = []

            # 添加手部位置数据
            if "hand_pos_xyz" in self.data:
                positions.extend(self.data["hand_pos_xyz"])

            # 添加物体位置数据
            if "obj_pos_xyz" in self.data:
                positions.extend(self.data["obj_pos_xyz"])

            if not positions:
                self.logger.warning("未找到位置数据,使用默认相机视角")
                return [-0.5, -1.0, 10.5], [-0.5, 0.0, 10.18]

            # 转换为numpy数组
            positions = np.array(positions)

            # 计算中心点
            center = np.mean(positions, axis=0)

            # 计算数据分布范围
            min_pos = np.min(positions, axis=0)
            max_pos = np.max(positions, axis=0)
            range_size = max_pos - min_pos

            # 计算相机位置
            # 在y轴方向偏移一定距离,确保能看到整个场景
            camera_offset = max(range_size[1] * 2, 1.0)  # 至少偏移1.0单位
            camera_pos = [
                center[0],  # x坐标与中心点对齐
                center[1] - camera_offset,  # y坐标向后偏移
                center[2] + range_size[2] * 0.5,  # z坐标略高于中心点
            ]

            # 相机目标点就是中心点
            camera_target = center.tolist()

            self.logger.info(
                f"计算得到相机视角: 位置={camera_pos}, 目标点={camera_target}"
            )
            return camera_pos, camera_target

        except Exception as e:
            self.logger.error(f"计算相机视角时出错: {str(e)}")
            # 发生错误时返回默认视角
            return [-0.5, -1.0, 10.5], [-0.5, 0.0, 10.18]

    def _init_simulation(self):
        """初始化仿真环境"""
        if self.simulation_app is None:
            raise ValueError("未提供simulation_app实例")

        # 配置仿真参数
        sim_cfg = SimulationCfg(
            dt=0.1,
            render_interval=2,
            physics_material=sim_utils.RigidBodyMaterialCfg(
                friction_combine_mode="min",
                restitution_combine_mode="min",
                static_friction=0.0,  # 降低摩擦
                dynamic_friction=0.0,  # 降低摩擦
                restitution=0.0,  # 无弹性
            ),
            gravity=[0.0, 0.0, 0.0],  # 禁用全局重力
        )
        self.sim = SimulationContext(sim_cfg)
        self.logger.info("仿真环境配置完成")
        self.logger.info(f"全局重力设置: {sim_cfg.gravity}")
        self.logger.info(
            f"物理材质设置: friction={sim_cfg.physics_material.static_friction}, restitution={sim_cfg.physics_material.restitution}"
        )

        # 计算或使用指定的相机视角
        if self.camera_pos is None or self.camera_target is None:
            camera_pos, camera_target = self._calculate_camera_view()
        else:
            camera_pos = list(self.camera_pos)
            camera_target = list(self.camera_target)

        # 设置相机视角
        self.sim.set_camera_view(eye=camera_pos, target=camera_target)
        self.logger.info(f"相机视角设置完成: 位置={camera_pos}, 目标点={camera_target}")

        # 创建场景配置
        self.logger.info("开始创建场景配置...")
        try:
            scene_cfg = RobotSceneCfg(
                hand_cfg=self.hand_cfg,
                object_cfg=self.object_cfg,
                num_envs=1,
                env_spacing=2.0,
                camera_pos=camera_pos,  # 使用计算得到的相机位置
                camera_target=camera_target,  # 使用计算得到的相机目标点
            )
            self.logger.info("场景配置创建完成")
        except Exception as e:
            self.logger.error(f"创建场景配置时出错: {str(e)}")
            raise

        # 创建场景
        self.logger.info("开始创建交互式场景...")
        try:
            self.scene = InteractiveScene(scene_cfg)
            self.logger.info("交互式场景创建完成")
        except Exception as e:
            self.logger.error(f"创建交互式场景时出错: {str(e)}")
            self.logger.error("这可能是由于以下原因导致的:")
            self.logger.error("1. Isaac Sim环境未正确初始化")
            self.logger.error("2. 手部或物体模型配置有误")
            self.logger.error("3. USD文件路径不正确")
            self.logger.error("4. GPU内存不足")
            raise

        # 重置仿真
        self.logger.info("开始重置仿真...")
        try:
            self.sim.reset()
            self.logger.info("仿真重置完成")
        except Exception as e:
            self.logger.error(f"重置仿真时出错: {str(e)}")
            raise

        self.logger.info("仿真环境初始化完成")

    def get_data(self) -> Union[pd.DataFrame, Dict]:
        """获取加载的数据"""
        return self.data

    def _normalize_force_data(
        self, data: np.ndarray, force_max: Optional[float] = None
    ) -> np.ndarray:
        """使用固定的归一化参数对力数据进行归一化

        参数:
        - data: 力数据数组
        - force_max: 最大力值，如果为None则使用数据中的最大值

        返回:
        - 归一化后的数据
        """
        if force_max is None:
            # 计算全局最大最小值,添加一个小的偏移量
            force_max = np.max(np.abs(data)) + 1e-6

        # 使用对数归一化,保持符号
        normalized = np.zeros_like(data)
        for i in range(3):  # xyz三个通道
            channel = data[..., i]
            # 保持符号的对数归一化
            sign = np.sign(channel)
            abs_values = np.abs(channel)
            # 添加一个小的偏移量避免log(0)
            log_values = np.log1p(abs_values)
            # 归一化到[0,1]范围
            normalized[..., i] = (log_values / np.log1p(force_max)) * sign
            # 将范围从[-1,1]映射到[0,1]
            normalized[..., i] = (normalized[..., i] + 1) / 2
        return normalized

    def _process_tactile_data(self, tactile_data: List) -> Tuple[np.ndarray, float]:
        """处理触觉数据，计算归一化参数

        参数:
        - tactile_data: 触觉数据列表

        返回:
        - 处理后的数据
        - 最大力值
        """
        # 计算整个数据序列的统计特性
        all_forces = []
        for idx in range(len(tactile_data)):
            forces = np.array(tactile_data[idx]).reshape(-1, 3)
            all_forces.append(forces)
        all_forces = np.concatenate(all_forces, axis=0)

        # 计算全局最大最小值
        force_max = np.max(np.abs(all_forces)) + 1e-6

        return all_forces, force_max

    def _reshape_tactile_data(self, xyz_forces: np.ndarray) -> np.ndarray:
        """重塑触觉数据为可视化格式

        参数:
        - xyz_forces: 原始触觉数据

        返回:
        - 重塑后的数据
        """
        try:
            # 记录输入数据的形状
            self.logger.debug(f"输入触觉数据形状: {xyz_forces.shape}")

            # 计算每个手指的数据点数量
            total_points = len(xyz_forces)
            points_per_finger = total_points // 4  # 假设数据平均分配给4个手指

            # 分离各个手指的数据
            finger_data = np.array_split(xyz_forces, 4)
            index, mid, ring, thumb = finger_data

            # 记录每个手指的数据形状
            self.logger.debug(
                f"手指数据形状 - 拇指: {thumb.shape}, 食指: {index.shape}, 中指: {mid.shape}, 无名指: {ring.shape}"
            )

            # 计算每个手指的各个部分的数据点数量
            # 假设每个手指的数据按比例分配：y部分60%，z部分30%，j部分10%
            y_ratio, z_ratio, j_ratio = 0.6, 0.3, 0.1

            # 计算每个部分的数据点数量
            y_points = int(points_per_finger * y_ratio)
            z_points = int(points_per_finger * z_ratio)
            j_points = points_per_finger - y_points - z_points

            # 确保每个部分至少有一个数据点
            y_points = max(1, y_points)
            z_points = max(1, z_points)
            j_points = max(1, j_points)

            # 调整数据点数量以确保能被12整除（用于reshape）
            y_points = (y_points // 12) * 12
            z_points = (z_points // 6) * 6
            j_points = (j_points // 12) * 12

            # 记录每个部分的数据点数量
            self.logger.debug(
                f"每个手指的数据点分配 - y部分: {y_points}, z部分: {z_points}, j部分: {j_points}"
            )

            # 为每个手指创建填充数组
            def create_padding(shape):
                return np.zeros((shape[0], 6, 3))

            # 处理每个手指的数据
            def process_finger_data(finger_data, y_points, z_points, j_points):
                # 确保数据点数量不超过实际数据
                y_points = min(y_points, len(finger_data))
                z_points = min(z_points, len(finger_data) - y_points)
                j_points = min(j_points, len(finger_data) - y_points - z_points)

                # 分割数据
                y_data = finger_data[:y_points]
                z_data = finger_data[y_points : y_points + z_points]
                j_data = finger_data[
                    y_points + z_points : y_points + z_points + j_points
                ]

                # 计算reshape后的维度
                y_rows = y_points // 12
                z_rows = z_points // 6
                j_rows = j_points // 12

                # reshape数据
                y_reshaped = (
                    y_data.reshape(y_rows, 12, 3)
                    if y_rows > 0
                    else np.zeros((1, 12, 3))
                )
                z_reshaped = (
                    z_data.reshape(z_rows, 6, 3) if z_rows > 0 else np.zeros((1, 6, 3))
                )
                j_reshaped = (
                    j_data.reshape(j_rows, 12, 3)
                    if j_rows > 0
                    else np.zeros((1, 12, 3))
                )

                # 创建填充数组
                padding = create_padding(y_reshaped.shape)

                # 确保所有数组具有相同的第一维大小
                max_rows = max(
                    y_reshaped.shape[0],
                    z_reshaped.shape[0],
                    j_reshaped.shape[0],
                    padding.shape[0],
                )

                # 填充数组到相同大小
                y_padded = np.pad(
                    y_reshaped, ((0, max_rows - y_reshaped.shape[0]), (0, 0), (0, 0))
                )
                z_padded = np.pad(
                    z_reshaped, ((0, max_rows - z_reshaped.shape[0]), (0, 0), (0, 0))
                )
                j_padded = np.pad(
                    j_reshaped, ((0, max_rows - j_reshaped.shape[0]), (0, 0), (0, 0))
                )
                padding_padded = np.pad(
                    padding, ((0, max_rows - padding.shape[0]), (0, 0), (0, 0))
                )

                return np.concatenate([y_padded, z_padded, padding_padded], axis=1)

            # 处理每个手指的数据
            thumb_all = process_finger_data(thumb, y_points, z_points, j_points)
            index_all = process_finger_data(index, y_points, z_points, j_points)
            mid_all = process_finger_data(mid, y_points, z_points, j_points)
            ring_all = process_finger_data(ring, y_points, z_points, j_points)

            # 组合所有手指的数据
            image = np.concatenate([thumb_all, index_all, mid_all, ring_all], axis=0)
            rgb_image = np.transpose(image, (1, 0, 2))

            self.logger.debug(f"最终图像形状: {rgb_image.shape}")
            return rgb_image

        except Exception as e:
            self.logger.error(f"处理触觉数据时出错: {str(e)}")
            self.logger.error(f"输入数据形状: {xyz_forces.shape}")
            # 返回一个默认的空图像
            return np.zeros((30, 30, 3))

    def run_simulation(self, loop_count: int = 1):
        """运行仿真回放"""
        if self.sim is None or self.scene is None:
            raise ValueError("仿真环境未初始化")

        if self.simulation_app is None:
            raise ValueError("未提供simulation_app实例")

        import carb
        import matplotlib.pyplot as plt
        from scipy.spatial.transform import Rotation as R

        # 重置停止标志
        self._should_stop = False

        # 修改信号处理函数
        def signal_handler(signum, frame):
            with self._exit_lock:
                if self._should_stop:  # 如果已经在停止过程中,直接返回
                    return
                self._should_stop = True

            self.logger.info("\n检测到中断信号，正在停止仿真...")
            plt.close("all")  # 关闭所有matplotlib图形

        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        try:
            # 获取手部和物体对象
            robot = self.scene["hand"]
            obj = self.scene["object"]

            # 获取总帧数
            total_frames = len(self.data["joint_angles"])
            current_loop = 0

            # 设置仿真步长
            sim_dt = 0.1  # 固定步长，与real2sim_left_hand.py保持一致

            # 初始化机器人状态
            root_state = robot.data.default_root_state.clone()
            root_state[:, :3] += self.scene.env_origins
            robot.write_root_state_to_sim(root_state)

            # 在循环外创建触觉可视化窗口（如果有触觉数据） - 修复内存泄漏
            tactile_fig, tactile_ax = None, None
            if "tactile_fenli" in self.data:
                try:
                    first_tactile_data = np.array(self.data["tactile_fenli"][0])
                    tactile_fig, tactile_ax = self._visualize_tactile_on_hand(
                        first_tactile_data, 0
                    )
                    if tactile_fig is not None:
                        plt.show(block=False)
                        self.logger.info(
                            "触觉可视化窗口已创建，将在整个仿真过程中复用此窗口"
                        )
                except Exception as e:
                    self.logger.error(f"初始化触觉可视化窗口时出错: {str(e)}")

            # 重置场景
            self.scene.reset()
            self.logger.info("开始仿真回放...")
            if loop_count == -1:
                self.logger.info("设置为无限循环模式，按Ctrl+C可停止仿真")

            # 预分配一些常用的tensor，避免重复创建
            device = self.sim.device

            # 初始化tensor池以优化性能
            self._optimize_tensor_usage(device)

            while (
                self.simulation_app.is_running()
                and (loop_count == -1 or current_loop < loop_count)
                and not self._should_stop
            ):
                # 重置场景和机器人
                self.scene.reset()
                robot.reset()

                # 创建进度条
                loop_desc = (
                    "无限循环"
                    if loop_count == -1
                    else f"循环 {current_loop + 1}/{loop_count}"
                )

                # 添加文件信息到进度条描述
                if self.total_files > 1:
                    current_file_name = os.path.basename(
                        self.h5_files[self.current_file_index]
                    )
                    file_desc = f"文件: {current_file_name} ({self.current_file_index+1}/{self.total_files})"
                    full_desc = f"播放轨迹 ({loop_desc}) - {file_desc}"
                else:
                    full_desc = f"播放轨迹 ({loop_desc})"

                pbar = tqdm(
                    total=total_frames,
                    desc=full_desc,
                    unit="帧",
                    leave=True,
                )

                sim_time = 0.0  # 添加仿真时间计数器

                # 播放每一帧
                for idx in range(total_frames):
                    # 检查是否需要停止
                    if self._should_stop:
                        break

                    # 更新进度条
                    pbar.update(1)
                    pbar.set_postfix(
                        {
                            "frame": f"{idx+1}/{total_frames}",
                            "speed": f"{1/sim_dt:.1f} fps",
                        }
                    )

                    # 更新手部位姿
                    # 1. 读取当前帧的关节角度和手部位姿数据
                    finger_angles = self.data["joint_angles"][idx]  # 读取关节角度

                    # 2. 读取手部位置和旋转数据
                    hand_pos = self.data["hand_pos_xyz"][idx]  # 位置数据 [x,y,z]

                    hand_rot = self.data["hand_rot_q_xyzw"][
                        idx
                    ]  # 旋转数据 [r11,r12,r21,r22,r31,r32]

                    # 3. 将旋转矩阵前两列转换为完整的旋转矩阵
                    rot_matrix = np.zeros((3, 3))
                    rot_matrix[:, 0] = hand_rot[:3]  # 第一列 [r11,r21,r31]
                    rot_matrix[:, 1] = hand_rot[3:6]  # 第二列 [r12,r22,r32]
                    # 计算第三列（叉积）
                    rot_matrix[:, 2] = np.cross(rot_matrix[:, 0], rot_matrix[:, 1])
                    # 正交化
                    rot_matrix[:, 2] = rot_matrix[:, 2] / np.linalg.norm(
                        rot_matrix[:, 2]
                    )
                    rot_matrix[:, 0] = rot_matrix[:, 0] / np.linalg.norm(
                        rot_matrix[:, 0]
                    )
                    rot_matrix[:, 1] = np.cross(rot_matrix[:, 2], rot_matrix[:, 0])

                    # 🚀 优化：使用tensor池减少内存分配
                    # 4. 使用预分配的tensor，优化tensor创建
                    rot_matrix_tensor = self._get_reusable_tensor(
                        "rotation_3x3", rot_matrix, device
                    )
                    # 使用quat_from_matrix转换为wxyz格式的四元数
                    quat = quat_from_matrix(rot_matrix_tensor)  # 返回wxyz格式

                    # 5. 组合成7维位姿数据 [x,y,z,qw,qx,qy,qz]
                    hand_real_pos = np.concatenate([hand_pos, quat.cpu().numpy()])

                    # 6. 🚀 优化：复用预分配的pose tensor
                    hand_pose_tensor = self._get_reusable_tensor(
                        "pose_7d", np.array([hand_real_pos]), device
                    )
                    robot.write_root_pose_to_sim(root_pose=hand_pose_tensor)

                    # 更新物体位姿（如果存在）
                    if "obj_pos_xyz" in self.data and "obj_rot_q_xyzw" in self.data:
                        try:
                            # 获取物体位置和旋转数据
                            obj_pos = self.data["obj_pos_xyz"][idx]
                            obj_rot = self.data["obj_rot_q_xyzw"][idx]

                            # 将旋转矩阵前两列转换为完整的旋转矩阵
                            obj_rot_matrix = np.zeros((3, 3))
                            obj_rot_matrix[:, 0] = obj_rot[:3]
                            obj_rot_matrix[:, 1] = obj_rot[3:6]
                            obj_rot_matrix[:, 2] = np.cross(
                                obj_rot_matrix[:, 0], obj_rot_matrix[:, 1]
                            )
                            # 正交化
                            obj_rot_matrix[:, 2] = obj_rot_matrix[
                                :, 2
                            ] / np.linalg.norm(obj_rot_matrix[:, 2])
                            obj_rot_matrix[:, 0] = obj_rot_matrix[
                                :, 0
                            ] / np.linalg.norm(obj_rot_matrix[:, 0])
                            obj_rot_matrix[:, 1] = np.cross(
                                obj_rot_matrix[:, 2], obj_rot_matrix[:, 0]
                            )

                            # 🚀 优化：复用tensor池
                            obj_rot_matrix_tensor = self._get_reusable_tensor(
                                "rotation_3x3", obj_rot_matrix, device
                            )
                            obj_quat = quat_from_matrix(obj_rot_matrix_tensor)

                            # 🚀 优化：组合物体位姿数据
                            obj_real_pos = np.concatenate(
                                [obj_pos, obj_quat.cpu().numpy()]
                            )
                            obj_pose_tensor = self._get_reusable_tensor(
                                "pose_7d",
                                np.array([obj_real_pos]),
                                device=device,
                            )
                            # 使用quat_from_matrix转换为wxyz格式的四元数
                            obj_quat = quat_from_matrix(
                                obj_rot_matrix_tensor
                            )  # 返回wxyz格式

                            # 组合成7维位姿数据 [x,y,z,qw,qx,qy,qz]
                            obj_new_pose = np.concatenate(
                                [obj_pos, obj_quat.cpu().numpy()]
                            )

                            # 更新物体位姿
                            obj_pose_tensor = torch.tensor(
                                np.array([obj_new_pose]),
                                dtype=torch.float32,
                                device=device,
                            )
                            obj.write_root_pose_to_sim(root_pose=obj_pose_tensor)
                        except Exception as e:
                            self.logger.error(
                                f"更新物体位姿时出错 (帧 {idx}): {str(e)}"
                            )

                    # 更新关节角度
                    new_finger_angles = np.array(finger_angles).reshape(4, 4).T
                    trans_angles = (
                        np.concatenate(
                            [new_finger_angles[:, 3:], new_finger_angles[:, :3]], axis=1
                        )
                        .reshape(1, 16)
                        .tolist()[0]
                    )
                    robot.set_joint_position_target(
                        torch.tensor(trans_angles, device=device)
                    )

                    # 更新仿真
                    self.scene.write_data_to_sim()
                    self.sim.step()
                    sim_time += sim_dt  # 更新仿真时间
                    self.scene.update(sim_time)  # 使用累计的仿真时间

                    # 更新触觉数据可视化 - 复用现有窗口
                    if (
                        "tactile_fenli" in self.data
                        and tactile_fig is not None
                        and tactile_ax is not None
                    ):
                        try:
                            # 获取当前帧的触觉数据
                            tactile_data = np.array(self.data["tactile_fenli"][idx])

                            # 使用现有的窗口更新触觉可视化
                            updated_fig, updated_ax = self._visualize_tactile_on_hand(
                                tactile_data, idx, tactile_fig, tactile_ax
                            )

                            if updated_fig is not None:
                                # 刷新显示（非阻塞模式）
                                plt.draw()
                                plt.pause(0.001)  # 很短的暂停以更新显示

                        except Exception as e:
                            self.logger.error(
                                f"更新触觉可视化时出错 (帧 {idx}): {str(e)}"
                            )

                    # 处理Qt事件，确保UI响应
                    if hasattr(self.simulation_app, "process_events"):
                        self.simulation_app.process_events()

                    # 每10帧清理一次GPU缓存，避免积累过多
                    if idx % 10 == 0 and torch.cuda.is_available():
                        torch.cuda.empty_cache()

                # 完成一轮播放
                pbar.close()

                # 记录内存使用情况
                self._log_memory_usage(f"循环{current_loop+1}结束")

                # 每次循环结束时的轻量级清理
                # 1. 清理GPU资源（但不要太频繁）
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                # 2. 清理Qt事件循环
                if hasattr(self.simulation_app, "process_events"):
                    self.simulation_app.process_events()

                # 3. 重置场景和仿真环境
                if hasattr(self, "scene") and self.scene is not None:
                    try:
                        self.scene.reset()
                    except Exception as e:
                        self.logger.error(f"重置场景时出错: {str(e)}")

                if hasattr(self, "sim") and self.sim is not None:
                    try:
                        self.sim.reset()
                    except Exception as e:
                        self.logger.error(f"重置仿真环境时出错: {str(e)}")

                # 4. 重置机器人状态
                robot.reset()

                # 检查是否需要加载下一个文件（目录模式下）
                if (
                    self.total_files > 1
                    and self.current_file_index + 1 < self.total_files
                ):
                    self.logger.info(f"当前文件播放完成，准备加载下一个文件...")
                    if self._load_next_file():
                        # 重新获取总帧数
                        total_frames = len(self.data["joint_angles"])
                        self.logger.info(
                            f"成功切换到下一个文件，新文件包含 {total_frames} 帧"
                        )
                        # 重新创建触觉可视化窗口（如果需要）
                        if "tactile_fenli" in self.data and tactile_fig is not None:
                            try:
                                first_tactile_data = np.array(
                                    self.data["tactile_fenli"][0]
                                )
                                tactile_fig, tactile_ax = (
                                    self._visualize_tactile_on_hand(
                                        first_tactile_data, 0, tactile_fig, tactile_ax
                                    )
                                )
                            except Exception as e:
                                self.logger.error(f"更新触觉可视化窗口时出错: {str(e)}")
                        continue  # 继续播放新文件，不增加loop计数
                    else:
                        self.logger.error("加载下一个文件失败，停止播放")
                        break

                # 5. 记录循环完成信息
                if self._should_stop:
                    break
                current_loop += 1
                if loop_count != -1:
                    self.logger.info(f"完成循环 {current_loop}/{loop_count}")
                else:
                    self.logger.info(f"完成第 {current_loop} 轮循环，继续下一轮...")

        except KeyboardInterrupt:
            self.logger.info("\n检测到用户中断，正在停止仿真...")
        except Exception as e:
            self.logger.error(f"仿真过程中出错: {str(e)}")
            raise
        finally:
            # 确保资源被正确释放 - 全局资源清理
            # 关闭触觉可视化窗口
            if tactile_fig is not None:
                plt.close(tactile_fig)

            plt.close("all")  # 关闭所有matplotlib图形

            # 清理GPU资源
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                # 强制GPU同步，确保所有操作完成
                torch.cuda.synchronize()

            # 清理Qt事件循环
            if hasattr(self.simulation_app, "process_events"):
                self.simulation_app.process_events()

            # 重置场景和仿真环境
            if hasattr(self, "scene") and self.scene is not None:
                try:
                    self.scene.reset()
                except Exception as e:
                    self.logger.error(f"重置场景时出错: {str(e)}")

            if hasattr(self, "sim") and self.sim is not None:
                try:
                    self.sim.reset()
                except Exception as e:
                    self.logger.error(f"重置仿真环境时出错: {str(e)}")

            self.logger.info("仿真回放结束")

    def visualize_tactile_data(
        self, data: Optional[Dict] = None
    ) -> Tuple[plt.Figure, plt.Axes]:
        """可视化触觉数据

        参数:
        - data: 要可视化的数据，如果为None则使用当前加载的数据

        返回:
        - 图形对象和坐标轴对象
        """
        if data is None:
            data = self.data

        if "tactile_fenli" not in data:
            raise ValueError("数据中不包含触觉数据")

        # 处理触觉数据
        all_forces, force_max = self._process_tactile_data(data["tactile_fenli"])

        # 创建图形
        fig, ax = plt.subplots()

        # 获取第一帧数据
        first_frame = data["tactile_fenli"][0]
        xyz_forces = np.array(first_frame).reshape(-1, 3)
        rgb_image = self._reshape_tactile_data(xyz_forces)
        rgb_image = self._normalize_force_data(rgb_image, force_max)

        # 显示图像
        img = ax.imshow(rgb_image)
        plt.colorbar(img, ax=ax)

        return fig, ax

    def close(self):
        """关闭数据加载器和仿真环境"""
        try:
            if self.dataset is not None:
                self.dataset.close()

            # 重置场景和仿真环境
            if hasattr(self, "scene") and self.scene is not None:
                try:
                    self.scene.reset()
                except Exception as e:
                    self.logger.error(f"关闭时重置场景出错: {str(e)}")

            if hasattr(self, "sim") and self.sim is not None:
                try:
                    self.sim.reset()
                except Exception as e:
                    self.logger.error(f"关闭时重置仿真环境出错: {str(e)}")

            # 不再在这里关闭simulation_app,由主线程统一处理

        except Exception as e:
            self.logger.error(f"关闭资源时出错: {str(e)}")

    def _load_hand_sketch(self):
        """加载手部轮廓图像"""
        try:
            # 尝试加载手部轮廓图像
            sketch_path = os.path.join(os.path.dirname(__file__), "hand_skatch.png")
            if not os.path.exists(sketch_path):
                self.logger.warning(f"手部轮廓图像不存在: {sketch_path}")
                return

            # 使用matplotlib加载图像
            self.hand_sketch = plt.imread(sketch_path)
            self.logger.info(f"成功加载手部轮廓图像: {sketch_path}")

            # 记录图像信息
            self.logger.debug(f"图像形状: {self.hand_sketch.shape}")

        except Exception as e:
            self.logger.error(f"加载手部轮廓图像时出错: {str(e)}")
            self.hand_sketch = None

    def _create_tactile_mapping(self):
        """创建触觉传感器位置映射

        返回:
        - 包含每个手指和段位的触觉传感器位置映射的字典
        """
        if self.hand_sketch is None:
            self.logger.warning("未加载手部轮廓图像，无法创建触觉映射")
            return None

        try:
            # 获取图像尺寸
            height, width = self.hand_sketch.shape[:2]

            # 定义手指区域（基于图像坐标，需要根据实际图像调整）
            finger_regions = {
                "thumb": {
                    "proximal": {"x": (0.10, 0.30), "y": (0.50, 0.75)},  # 拇指近段
                    "distal": {"x": (0.02, 0.22), "y": (0.25, 0.45)},  # 拇指远段
                },
                "index": {
                    "proximal": {"x": (0.2, 0.4), "y": (0.6, 0.85)},  # 食指近段
                    "middle": {"x": (0.2, 0.4), "y": (0.4, 0.6)},  # 食指中段
                    "distal": {"x": (0.2, 0.4), "y": (0.15, 0.4)},  # 食指远段
                },
                "middle": {
                    "proximal": {"x": (0.55, 0.7), "y": (0.7, 0.95)},  # 中指近段
                    "middle": {"x": (0.5, 0.65), "y": (0.4, 0.6)},  # 中指中段
                    "distal": {"x": (0.45, 0.6), "y": (0.15, 0.4)},  # 中指远段
                },
                "ring": {
                    "proximal": {"x": (0.7, 0.85), "y": (0.7, 0.95)},  # 无名指近段
                    "middle": {"x": (0.65, 0.8), "y": (0.4, 0.6)},  # 无名指中段
                    "distal": {"x": (0.6, 0.75), "y": (0.1, 0.4)},  # 无名指远段
                },
            }

            # 创建映射字典
            mapping = {}

            # 为每个手指创建映射
            for finger, segments in finger_regions.items():
                mapping[finger] = {}
                for segment, region in segments.items():
                    # 根据段位类型生成不同数量的点
                    if segment == "proximal" or segment == "distal":
                        n_points = 120  # 近段和远段都是120个点
                    else:  # middle
                        n_points = 60  # 中间段位60个点

                    # 生成点映射
                    points = self._create_segment_points(
                        region["x"], region["y"], n_points, width, height
                    )
                    mapping[finger][segment] = points

            self.logger.info("成功创建触觉传感器位置映射")
            return mapping

        except Exception as e:
            self.logger.error(f"创建触觉映射时出错: {str(e)}")
            return None

    def _create_segment_points(self, x_range, y_range, n_points, width, height):
        """为指定区域创建点映射

        参数:
        - x_range: x坐标范围 (min, max)，相对值
        - y_range: y坐标范围 (min, max)，相对值
        - n_points: 需要生成的点数量
        - width: 图像宽度
        - height: 图像高度

        返回:
        - 点坐标列表 [(x1, y1), (x2, y2), ...]
        """
        # 将相对坐标转换为像素坐标
        x_min = int(x_range[0] * width)
        x_max = int(x_range[1] * width)
        y_min = int(y_range[0] * height)
        y_max = int(y_range[1] * height)

        # 计算网格尺寸
        grid_size = int(np.ceil(np.sqrt(n_points)))

        # 在区域内均匀分布点
        x_points = np.linspace(x_min, x_max, grid_size)
        y_points = np.linspace(y_min, y_max, grid_size)

        # 创建网格点
        xx, yy = np.meshgrid(x_points, y_points)
        points = list(zip(xx.flatten(), yy.flatten()))

        # 如果点数不够，随机添加点
        while len(points) < n_points:
            x = np.random.uniform(x_min, x_max)
            y = np.random.uniform(y_min, y_max)
            points.append((x, y))

        # 如果点数太多，随机选择需要的点数
        if len(points) > n_points:
            points = random.sample(points, n_points)

        return points

    def _convert_1140_tactile_to_dict(self, tactile_data):
        """将1140维触觉数据转换为字典格式

        参数:
        - tactile_data: (1140, 3)格式的触觉数据数组

        返回:
        - 字典格式的触觉数据，按手指和段位组织
        """
        try:
            # 确保数据是numpy数组
            tactile_data = np.array(tactile_data)

            if tactile_data.shape[0] != 1140 or tactile_data.shape[1] != 3:
                # 静默返回None，不输出错误日志
                return None

            # 创建输出字典
            result = {}
            current_idx = 0

            # 拇指数据 (240点：近段120 + 远段120)
            result["thumb"] = {}
            result["thumb"]["proximal"] = tactile_data[current_idx : current_idx + 120]
            current_idx += 120
            result["thumb"]["distal"] = tactile_data[current_idx : current_idx + 120]
            current_idx += 120

            # 其余三指数据 (每指300点：近段120 + 中段60 + 远段120)
            finger_names = ["index", "middle", "ring"]
            for finger_name in finger_names:
                result[finger_name] = {}
                # 近段 (120点)
                result[finger_name]["proximal"] = tactile_data[
                    current_idx : current_idx + 120
                ]
                current_idx += 120
                # 中段 (60点)
                result[finger_name]["middle"] = tactile_data[
                    current_idx : current_idx + 60
                ]
                current_idx += 60
                # 远段 (120点)
                result[finger_name]["distal"] = tactile_data[
                    current_idx : current_idx + 120
                ]
                current_idx += 120

            return result

        except Exception as e:
            # 静默返回None，不输出错误日志
            return None

    def _visualize_tactile_on_hand(self, tactile_data, frame_idx, fig=None, ax=None):
        """在手部轮廓上可视化触觉数据

        参数:
        - tactile_data: 当前帧的触觉数据，可以是(1140, 3)的数组或字典格式
        - frame_idx: 当前帧索引
        - fig: 可选的现有图形对象，如果提供则复用
        - ax: 可选的现有轴对象，如果提供则复用

        返回:
        - fig, ax: matplotlib图形对象
        """
        try:
            # 检查输入数据
            if tactile_data is None:
                self.logger.warning("触觉数据为空，无法可视化")
                return None, None

            # 检查手部轮廓图
            if self.hand_sketch is None:
                self.logger.warning("手部轮廓图未加载，无法可视化")
                return None, None

            # 检查触觉映射
            if self._tactile_mapping is None:
                self.logger.warning("触觉映射未创建，无法可视化")
                return None, None

            # 如果没有提供图形对象，创建新的
            if fig is None or ax is None:
                fig, ax = plt.subplots(figsize=(10, 12))
            else:
                # 清除现有内容
                ax.clear()

            # 显示手部轮廓
            ax.imshow(self.hand_sketch, cmap="gray")

            # 如果输入是numpy数组，转换为字典格式
            if isinstance(tactile_data, (np.ndarray, list)):
                tactile_data = self._convert_1140_tactile_to_dict(tactile_data)
                if tactile_data is None:
                    # 静默返回，数据维度不匹配
                    return None, None

            # 为每个手指的每个段位绘制触觉数据
            scatter_plots = []
            for finger in ["thumb", "index", "middle", "ring"]:
                if finger not in tactile_data or finger not in self._tactile_mapping:
                    self.logger.warning(f"缺少手指数据或映射: {finger}")
                    continue

                for segment in self._tactile_mapping[finger].keys():
                    if segment not in tactile_data[finger]:
                        self.logger.warning(f"缺少段位数据: {finger}.{segment}")
                        continue

                    points = self._tactile_mapping[finger][segment]
                    forces = tactile_data[finger][segment]

                    # 确保数据维度匹配
                    if len(points) != len(forces):
                        self.logger.warning(
                            f"数据维度不匹配: {finger}.{segment} - 点数: {len(points)}, 力数据: {len(forces)}"
                        )
                        continue

                    # 计算力的大小
                    force_magnitudes = np.linalg.norm(forces, axis=1)

                    # 使用散点图显示触觉数据
                    scatter = ax.scatter(
                        [p[0] for p in points],
                        [p[1] for p in points],
                        c=force_magnitudes,
                        cmap="viridis",
                        alpha=0.7,
                        s=30,
                        vmin=0,
                        vmax=(
                            np.max(force_magnitudes) if len(force_magnitudes) > 0 else 1
                        ),
                    )
                    scatter_plots.append(scatter)

            # 设置标题和坐标轴（使用英文避免字体问题）
            ax.set_title(f"Tactile Data Visualization - Frame {frame_idx}", fontsize=14)
            ax.axis("off")

            # 添加颜色条（只在没有现有颜色条时添加）
            if scatter_plots and not hasattr(ax, "_colorbar"):
                cbar = fig.colorbar(
                    scatter_plots[-1], ax=ax, label="Force Magnitude (N)", shrink=0.6
                )
                ax._colorbar = cbar  # 标记已添加颜色条

            # 调整布局
            plt.tight_layout()

            return fig, ax

        except Exception as e:
            self.logger.error(f"可视化触觉数据时出错: {str(e)}")
            return None, None

    def _generate_sim_tactile_data(self, num_frames: int) -> List:
        """生成模拟的1140维触觉数据

        参数:
        - num_frames: 需要生成的帧数

        返回:
        - 包含模拟触觉数据的列表，每帧为(1140, 3)的数组
        """
        self.logger.info(f"生成{num_frames}帧的模拟1140维触觉数据")

        tactile_data = []
        for frame in range(num_frames):
            # 为每帧生成1140个传感器点的3维力向量
            frame_data = np.zeros((1140, 3))

            # 为每个手指生成不同的力分布
            finger_regions = [
                (0, 240),  # 拇指：240点
                (240, 540),  # 食指：300点
                (540, 840),  # 中指：300点
                (840, 1140),  # 无名指：300点
            ]

            for i, (start, end) in enumerate(finger_regions):
                # 生成基础力向量
                base_force = np.random.uniform(0, 5, end - start)
                # 添加时间变化
                time_factor = np.sin(frame * 0.1 + i * np.pi / 2) * 0.5 + 0.5

                # 生成3维力向量
                frame_data[start:end, 0] = (
                    np.random.uniform(-0.5, 0.5, end - start) * base_force * time_factor
                )  # x分量
                frame_data[start:end, 1] = (
                    np.random.uniform(-0.5, 0.5, end - start) * base_force * time_factor
                )  # y分量
                frame_data[start:end, 2] = base_force * time_factor  # z分量（主要力）

            tactile_data.append(frame_data.tolist())

        self.logger.info(f"模拟触觉数据生成完成: {num_frames}帧, 每帧维度(1140, 3)")
        return tactile_data

    def _preprocess_tactile_data(self):
        """预处理触觉数据，避免运行时重复计算"""
        try:
            if "tactile_fenli" in self.data and self.data["tactile_fenli"]:
                self.logger.info("开始预处理触觉数据...")
                all_forces, force_max = self._process_tactile_data(
                    self.data["tactile_fenli"]
                )
                self._tactile_data_cache = {
                    "all_forces": all_forces,
                    "force_max": force_max,
                    "processed": True,
                }
                self.logger.info("触觉数据预处理完成")
            else:
                self._tactile_data_cache = {"processed": False}
        except Exception as e:
            self.logger.error(f"预处理触觉数据时出错: {str(e)}")
            self._tactile_data_cache = {"processed": False}

    def _optimize_tensor_usage(self, device):
        """优化tensor使用，预分配常用tensor以减少内存分配开销 - 扩展版本"""
        try:
            # 🚀 扩展：预分配更多常用的tensor大小
            self._tensor_pool = {
                "pose_7d": torch.zeros((1, 7), dtype=torch.float32, device=device),
                "rotation_3x3": torch.zeros((3, 3), dtype=torch.float32, device=device),
                "joint_16d": torch.zeros(16, dtype=torch.float32, device=device),
                # 新增：更多预分配tensor
                "position_3d": torch.zeros(3, dtype=torch.float32, device=device),
                "quaternion_4d": torch.zeros(4, dtype=torch.float32, device=device),
                "rotation_matrix_flat": torch.zeros(
                    9, dtype=torch.float32, device=device
                ),
                "temp_array": torch.zeros(
                    100, dtype=torch.float32, device=device
                ),  # 通用临时数组
            }

            # 🚀 新增：内存使用统计
            self._tensor_pool_stats = {"hits": 0, "misses": 0, "memory_saved_mb": 0.0}

            self.logger.info(
                f"扩展tensor池已初始化，包含{len(self._tensor_pool)}种tensor类型"
            )
            self.logger.debug("Tensor pool initialized for performance optimization")
        except Exception as e:
            self.logger.error(f"初始化tensor池时出错: {str(e)}")
            self._tensor_pool = {}
            self._tensor_pool_stats = {"hits": 0, "misses": 0, "memory_saved_mb": 0.0}

    def _get_reusable_tensor(self, key, data, device):
        """获取可复用的tensor，减少内存分配 - 增强版本"""
        try:
            if hasattr(self, "_tensor_pool") and key in self._tensor_pool:
                tensor = self._tensor_pool[key]

                # 🚀 优化：检查tensor形状是否匹配
                if isinstance(data, np.ndarray):
                    data_shape = data.shape
                    if tensor.shape == data_shape or (
                        len(data_shape) == 1 and tensor.numel() >= data.size
                    ):
                        if tensor.shape != data_shape:
                            # 重新调整形状以匹配数据
                            tensor = tensor.view(data_shape)
                        tensor.copy_(torch.from_numpy(data).to(device))
                    else:
                        # 形状不匹配，创建新tensor
                        tensor = torch.from_numpy(data).to(
                            dtype=torch.float32, device=device
                        )
                else:
                    data_tensor = torch.tensor(data, device=device, dtype=torch.float32)
                    if tensor.shape == data_tensor.shape:
                        tensor.copy_(data_tensor)
                    else:
                        tensor = data_tensor

                # 🚀 统计命中次数
                if hasattr(self, "_tensor_pool_stats"):
                    self._tensor_pool_stats["hits"] += 1
                    # 估算节省的内存（简单估算）
                    self._tensor_pool_stats["memory_saved_mb"] += (
                        tensor.numel() * 4 / 1024 / 1024
                    )

                return tensor
            else:
                # 🚀 统计未命中次数
                if hasattr(self, "_tensor_pool_stats"):
                    self._tensor_pool_stats["misses"] += 1

                # 回退到正常创建
                if isinstance(data, np.ndarray):
                    return torch.from_numpy(data).to(dtype=torch.float32, device=device)
                else:
                    return torch.tensor(data, dtype=torch.float32, device=device)
        except Exception as e:
            self.logger.debug(f"Tensor复用失败: {str(e)}")
            # 如果复用失败，回退到正常创建
            if isinstance(data, np.ndarray):
                return torch.from_numpy(data).to(dtype=torch.float32, device=device)
            else:
                return torch.tensor(data, dtype=torch.float32, device=device)

    def _log_memory_usage(self, context=""):
        """记录当前内存使用情况，用于调试"""
        try:
            import psutil
            import gc

            # Python内存使用
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024

            # GPU内存使用（如果可用）
            gpu_memory_str = ""
            if torch.cuda.is_available():
                gpu_memory = torch.cuda.memory_allocated() / 1024 / 1024
                gpu_max_memory = torch.cuda.max_memory_allocated() / 1024 / 1024
                gpu_memory_str = (
                    f", GPU: {gpu_memory:.1f}MB (max: {gpu_max_memory:.1f}MB)"
                )

            # 垃圾收集统计
            gc_stats = gc.get_stats()
            total_objects = sum(stat["collections"] for stat in gc_stats)

            self.logger.debug(
                f"内存使用情况 {context}: RAM: {memory_mb:.1f}MB{gpu_memory_str}, "
                f"GC collections: {total_objects}"
            )

        except Exception as e:
            self.logger.debug(f"无法获取内存使用情况: {str(e)}")

    def _load_next_file(self) -> bool:
        """加载下一个h5文件

        返回:
        - 是否成功加载下一个文件
        """
        if self.current_file_index + 1 >= self.total_files:
            self.logger.info("已经是最后一个文件，无法加载下一个文件")
            return False

        try:
            # 移动到下一个文件
            self.current_file_index += 1
            current_file = self.h5_files[self.current_file_index]

            self.logger.info(
                f"正在加载下一个文件: {os.path.basename(current_file)} ({self.current_file_index+1}/{self.total_files})"
            )

            # 重新加载数据
            self._load_hdf5_data()

            # 重新预处理触觉数据
            if not self.close_tactile_vis:
                self._preprocess_tactile_data()

            self.logger.info(f"成功加载文件: {os.path.basename(current_file)}")
            return True

        except Exception as e:
            self.logger.error(f"加载下一个文件时出错: {str(e)}")
            return False


def _get_data_type_from_path(data_source: str) -> str:
    """根据文件路径自动判断数据类型

    参数:
    - data_source: 数据源文件路径或目录路径

    返回:
    - 数据类型：'csv'或'hdf5'

    异常:
    - ValueError: 当文件类型不支持时抛出
    """
    if not os.path.exists(data_source):
        raise FileNotFoundError(f"数据源文件不存在: {data_source}")

    # 如果是目录，检查是否包含.h5文件
    if os.path.isdir(data_source):
        h5_files = [
            f for f in os.listdir(data_source) if f.lower().endswith((".h5", ".hdf5"))
        ]
        if h5_files:
            return "hdf5"
        else:
            raise ValueError(f"目录 {data_source} 中未找到.h5或.hdf5文件")

    # 获取文件后缀
    _, ext = os.path.splitext(data_source.lower())

    # 根据后缀判断类型
    if ext == ".csv":
        return "csv"
    elif ext in [".h5", ".hdf5"]:
        return "hdf5"
    else:
        raise ValueError(f"不支持的文件类型: {ext}，仅支持.csv和.h5/.hdf5文件")


def _generate_sim_data(original_path: str, hand_type: str) -> str:
    """生成包含1140维触觉数据的模拟HDF5文件

    参数:
    - original_path: 原始数据路径（用于生成新文件名）
    - hand_type: 手部类型

    返回:
    - 生成的模拟数据文件路径
    """
    import os
    import tempfile

    # 生成临时文件路径
    base_name = os.path.splitext(os.path.basename(original_path))[0]
    sim_file_path = f"sim_data_{base_name}_{hand_type}_1140d.h5"

    logger.info(f"开始生成模拟数据文件: {sim_file_path}")

    try:
        with h5py.File(sim_file_path, "w") as f:
            # 创建数据集结构
            num_frames = 100

            # 创建左手/右手数据组
            hand_group_name = f"{hand_type}hand"
            dataset_group = f.create_group("dataset")
            obs_group = dataset_group.create_group("observation")
            state_group = obs_group.create_group("state")
            hand_group = state_group.create_group(hand_group_name)

            # 生成关节角度数据 (100, 16)
            joint_data = np.random.uniform(-1, 1, (num_frames, 16))
            joints_group = hand_group.create_group("joints")
            joints_group.create_dataset("data", data=joint_data)

            # 生成手部位姿数据 (100, 6) - [x,y,z, r11,r12,r21,r22,r31,r32]
            hand_pos = np.tile([-0.5, 0.0, 10.18], (num_frames, 1))  # 位置
            hand_rot = np.tile(
                [1.0, 0.0, 0.0, 1.0, 0.0, 0.0], (num_frames, 1)
            )  # 旋转矩阵前两列
            hand_pose = np.concatenate([hand_pos, hand_rot], axis=1)
            handpose_group = hand_group.create_group("handpose")
            handpose_group.create_dataset("data", data=hand_pose)

            # 生成1140维触觉数据 (100, 1140, 3)
            tactile_data = []
            for frame in range(num_frames):
                # 为每帧生成1140个传感器点的3维力向量
                frame_data = np.zeros((1140, 3))

                # 为每个手指生成不同的力分布
                finger_regions = [
                    (0, 240),  # 拇指：240点
                    (240, 540),  # 食指：300点
                    (540, 840),  # 中指：300点
                    (840, 1140),  # 无名指：300点
                ]

                for i, (start, end) in enumerate(finger_regions):
                    # 生成基础力向量
                    base_force = np.random.uniform(0, 5, end - start)
                    # 添加时间变化
                    time_factor = np.sin(frame * 0.1 + i * np.pi / 2) * 0.5 + 0.5

                    # 生成3维力向量
                    frame_data[start:end, 0] = (
                        np.random.uniform(-0.5, 0.5, end - start)
                        * base_force
                        * time_factor
                    )  # x分量
                    frame_data[start:end, 1] = (
                        np.random.uniform(-0.5, 0.5, end - start)
                        * base_force
                        * time_factor
                    )  # y分量
                    frame_data[start:end, 2] = (
                        base_force * time_factor
                    )  # z分量（主要力）

                tactile_data.append(frame_data)

            tactile_data = np.array(tactile_data)
            tactile_group = hand_group.create_group("tactile")
            tactile_group.create_dataset("data", data=tactile_data)

            # 生成物体数据 (100, 9) - [x,y,z, r11,r12,r21,r22,r31,r32]
            obj_pos = np.tile([-0.5, 0.0, 10.18], (num_frames, 1))
            obj_rot = np.tile([1.0, 0.0, 0.0, 1.0, 0.0, 0.0], (num_frames, 1))
            obj_data = np.concatenate([obj_pos, obj_rot], axis=1)

            obj1_group = state_group.create_group("obj1")
            obj1_group.create_dataset("data", data=obj_data)

        logger.info(f"模拟数据生成完成: {sim_file_path}")
        logger.info(f"数据包含: {num_frames}帧, 触觉数据维度: (1140, 3)")

        return sim_file_path

    except Exception as e:
        logger.error(f"生成模拟数据失败: {str(e)}")
        raise


def main(
    data_source: str,
    data_type: Optional[str] = None,
    num_envs: int = 1,
    hand_type: str = "left",
    object_type: str = "bottle",
    loop_count: int = 1,  # 添加loop_count参数，-1表示无限循环
    camera_pos: Optional[Tuple[float, float, float]] = None,  # 添加相机位置参数
    camera_target: Optional[Tuple[float, float, float]] = None,  # 添加相机目标点参数
    close_tactile_vis: bool = False,
):
    """主函数"""
    global simulation_app_instance, _should_exit

    logger.info("=" * 50)
    logger.info("开始初始化可视化器")
    logger.info(f"参数：data_source={data_source}")
    logger.info(f"参数：data_type={data_type}")
    logger.info(f"参数：hand_type={hand_type}")
    logger.info(f"参数：object_type={object_type}")
    logger.info(f"参数：loop_count={loop_count if loop_count != -1 else '无限循环'}")
    logger.info("=" * 50)

    try:
        # 如果没有指定数据类型，则自动判断
        if data_type is None:
            try:
                data_type = _get_data_type_from_path(data_source)
                logger.info(f"自动判断数据类型为：{data_type}")
            except (FileNotFoundError, ValueError) as e:
                logger.error(f"数据类型判断失败: {str(e)}")
                return

        # 获取手部模型配置
        if hand_type not in HAND_MODEL_CONFIGS:
            raise ValueError(f"不支持的手部模型类型: {hand_type}")
        hand_cfg_class = HAND_MODEL_CONFIGS[hand_type]
        logger.info(f"使用手部模型: {hand_type}")

        # 获取对应的关节配置
        if hand_type not in HAND_JOINT_CONFIGS:
            raise ValueError(f"未找到手部类型 {hand_type} 的关节配置")
        joint_config = HAND_JOINT_CONFIGS[hand_type]
        logger.info(f"使用关节配置: {hand_type}")

        # 配置手部模型
        hand_cfg = hand_cfg_class.replace(
            prim_path="{ENV_REGEX_NS}/Robot",
            init_state=ArticulationCfg.InitialStateCfg(
                pos=(-0.5, 0.0, 10.18),  # 调整初始位置
                rot=(0.0, 0.0, -0.7071, 0.7071),
                joint_pos=joint_config,
                joint_vel={".*": 1.0},
            ),
        )
        logger.info("手部模型配置完成")

        # 获取物体模型配置
        if object_type not in OBJECT_MODEL_CONFIGS:
            raise ValueError(f"不支持的物体模型类型: {object_type}")
        obj_config = OBJECT_MODEL_CONFIGS[object_type]
        logger.info(f"使用物体模型: {object_type}")
        logger.info(f"物体模型路径: {obj_config['usd_path']}")
        logger.info(f"物体模型配置: scale={obj_config['scale']}")
        logger.info(f"物体模型配置: mass={obj_config['mass']}")
        logger.info(f"物体模型配置: init_pos={obj_config['init_pos']}")
        logger.info(
            f"物体模型配置: collision_enabled={obj_config.get('collision_enabled')}"
        )
        logger.info(
            f"物体模型配置: rigid_body_enabled={obj_config.get('rigid_body_enabled')}"
        )
        logger.info(
            f"物体模型配置: kinematic_enabled={obj_config.get('kinematic_enabled')}"
        )

        # 配置物体模型
        collision_props = sim_utils.CollisionPropertiesCfg(
            collision_enabled=obj_config.get("collision_enabled", False),
            contact_offset=0.0,  # 设置接触偏移为0
            rest_offset=0.0,  # 设置静止偏移为0
        )

        rigid_props = sim_utils.RigidBodyPropertiesCfg(
            kinematic_enabled=obj_config.get("kinematic_enabled", True),
            rigid_body_enabled=obj_config.get("rigid_body_enabled", True),
            disable_gravity=True,  # 禁用重力
            retain_accelerations=False,  # 不保留加速度
            linear_damping=0.0,  # 线性阻尼
            angular_damping=0.0,  # 角阻尼
        )

        object_cfg = RigidObjectCfg(
            prim_path=f"{{ENV_REGEX_NS}}/{object_type}",
            spawn=sim_utils.UsdFileCfg(
                usd_path=obj_config["usd_path"],
                scale=obj_config["scale"],
                mass_props=sim_utils.MassPropertiesCfg(mass=obj_config["mass"]),
                collision_props=collision_props,
                rigid_props=rigid_props,
            ),
            init_state=RigidObjectCfg.InitialStateCfg(
                pos=(-0.5, 0.0, 10.18),  # 调整初始位置与手部一致
            ),
        )
        logger.info("物体模型配置完成")
        logger.info(
            "碰撞属性配置：" + str(object_cfg.spawn.collision_props.collision_enabled)
        )
        logger.info(
            "刚体属性配置：" + str(object_cfg.spawn.rigid_props.kinematic_enabled)
        )
        logger.info(
            "刚体启用状态：" + str(object_cfg.spawn.rigid_props.rigid_body_enabled)
        )
        logger.info(
            "重力禁用状态：" + str(object_cfg.spawn.rigid_props.disable_gravity)
        )

        # 创建可视化器
        logger.info("开始创建可视化器实例...")
        visualizer = RobotStateVisualizer(
            data_source=data_source,
            data_type=data_type,
            hand_type=hand_type,
            hand_cfg=hand_cfg,
            object_cfg=object_cfg,
            simulation_app=simulation_app,
            camera_pos=camera_pos,  # 传递相机位置参数
            camera_target=camera_target,  # 传递相机目标点参数
            close_tactile_vis=close_tactile_vis,
        )
        logger.info("可视化器实例创建完成")

        # 运行仿真
        logger.info("开始运行仿真...")
        visualizer.run_simulation(loop_count=loop_count)
        logger.info("仿真运行完成")

    except KeyboardInterrupt:
        logger.info("\n检测到用户中断，正在停止程序...")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}", exc_info=True)
        raise
    finally:
        # 确保资源被正确释放
        logger.info("开始清理资源...")
        if "visualizer" in locals():
            try:
                visualizer.close()
            except Exception as e:
                logger.error(f"关闭可视化器时出错: {str(e)}")

        # 确保simulation_app被正确关闭
        if simulation_app_instance is not None and simulation_app_instance.is_running():
            close_simulation_app(simulation_app_instance)

        logger.info("资源清理完成")
        logger.info("=" * 50)


if __name__ == "__main__":
    try:
        # 解析命令行参数
        args = parser.parse_args()

        # 运行主函数
        main(
            data_source=args.data_source,
            data_type=args.data_type,
            num_envs=args.num_envs,
            hand_type=args.hand_type,
            object_type=args.object_type,
            loop_count=args.loop_count,
            camera_pos=args.camera_pos,  # 传递相机位置参数
            camera_target=args.camera_target,  # 传递相机目标点参数
            close_tactile_vis=args.close_tactile_vis,
        )
    except KeyboardInterrupt:
        logger.info("\n检测到用户中断，正在停止程序...")
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}", exc_info=True)
    finally:
        # 检查是否需要退出
        if _should_exit:
            # 确保simulation_app被正确关闭
            if (
                simulation_app_instance is not None
                and simulation_app_instance.is_running()
            ):
                close_simulation_app(simulation_app_instance)
        logger.info("=" * 50)
