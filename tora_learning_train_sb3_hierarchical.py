# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""脚本用于使用层次化学习架构训练Tora机器人/DexH13灵巧手。

利用层次化学习模型，整合决策层和执行层进行端到端训练。
"""

# """首先启动Isaac Sim模拟器。""" # This comment might be slightly out of place now

import os
import sys
import time
import copy
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import torch
import torch.nn as nn
import numpy as np
from omegaconf import DictConfig, OmegaConf
import hydra
from tqdm import tqdm


# 添加统一日志配置导入 - 但不立即执行disable_duplicate_logging
from px_janus_learnsim.utils.logger import (
    setup_logger,
    smart_disable_duplicate_logging,
    check_colorlog_status,
)

# 不立即禁用重复日志，等待Hydra配置完成
# disable_duplicate_logging()

import warnings
import math
import traceback
from hydra.core.hydra_config import HydraConfig
from hydra.core.global_hydra import GlobalHydra
import tempfile
import yaml
from typing import List, Tuple
import argparse  # argparse is back for minimal AppLauncher setup
import random
from datetime import datetime
import torch.utils.data
from copy import deepcopy

# 移动这些导入到这里
from stable_baselines3.common.utils import (
    get_device,
    set_random_seed,
    obs_as_tensor,
)
from imitation.algorithms import bc
from imitation.data import types as imitation_types
from imitation.util.util import save_policy
from imitation.util import (
    logger as imitation_logger,
)  # Added for SwanLab integration

from stable_baselines3.common import logger as sb3_base_logger  # Added import

from isaaclab.app import AppLauncher  # AppLauncher import must be early

import swanlab

# --- Early Argparse for AppLauncher ---
ap_parser = argparse.ArgumentParser(description="Isaac Sim App Launcher Args")
AppLauncher.add_app_launcher_args(ap_parser)  # Add AppLauncher's own args
ap_parser.add_argument(
    "--video_early",
    action="store_true",
    help="Early flag for video to enable cameras for AppLauncher",
)
# 🔧 添加debug参数
ap_parser.add_argument(
    "--debug",
    action="store_true",
    help="Enable debug logging (shows detailed batch information and model outputs)",
)
app_launcher_known_args, remaining_argv = ap_parser.parse_known_args()

# Handle specific logic like enable_cameras based on early video flag
if (
    hasattr(app_launcher_known_args, "video_early")
    and app_launcher_known_args.video_early
):
    if hasattr(app_launcher_known_args, "enable_cameras"):
        app_launcher_known_args.enable_cameras = True
    else:
        setattr(app_launcher_known_args, "enable_cameras", True)

# Initialize AppLauncher globally and early
app_launcher_instance = AppLauncher(app_launcher_known_args)
simulation_app = app_launcher_instance.app  # This makes isaacsim.core available

# Pass remaining args to Hydra
sys.argv = [sys.argv[0]] + remaining_argv

# 现在使用setup_logger，但保持Hydra兼容性
logger = setup_logger("tora_learning", preserve_hydra_config=True)

# 🔧 根据命令行参数设置debug级别
if hasattr(app_launcher_known_args, "debug") and app_launcher_known_args.debug:
    # 设置所有相关logger为DEBUG级别
    logging.getLogger("tora_learning").setLevel(logging.DEBUG)
    logging.getLogger("px_janus_learnsim.learning.trainers.conditional_bc").setLevel(
        logging.DEBUG
    )
    logging.getLogger("action_scaling_utils").setLevel(logging.DEBUG)
    logger.info("🔍 Debug模式已启用 - 将显示详细的训练信息")
else:
    logger.info("ℹ️  使用INFO日志级别 - 如需详细信息请使用 --debug 参数")

# 将包含IsaacSim_direct_task_table_set_env.py的目录添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 统一环境注册
from env_registry import register_all_environments

register_all_environments()

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
SRC_DIR = os.path.abspath(os.path.join(SCRIPT_DIR, "../../.."))
if SRC_DIR not in sys.path:
    sys.path.append(SRC_DIR)

SWANLAB_INITIALIZED = (
    False  # Added for SwanLab integration, ensure this is False initially
)


class ConstantSchedulePicklable:
    def __init__(self, value: float):
        self.value = value

    def __call__(self, progress_remaining: float) -> float:
        return self.value


from px_janus_learnsim.utils.name_generator import generate_human_readable_run_name

from px_janus_learnsim.learning.models.reward_model import ReconstructionRewardModel

# Register the resolver with OmegaConf
# This must be done before @hydra.main is encountered
OmegaConf.register_new_resolver("generate_run_name", generate_human_readable_run_name)


# Register model type resolver for directory organization
def get_model_type(training_phase: str) -> str:
    """根据训练阶段返回对应的模型类型，用于目录组织"""
    model_type_mapping = {
        # Execution Layer Training
        "execution_bc_actor_train": "execution_bc_actor",
        "execution_bc_critic_train": "execution_bc_critic",
        "execution_joint_rl": "execution_rl_actor_critic",
        # Decision Layer Training
        "decision_bc_train": "decision_bc_scoring",
        "decision_rl_train": "decision_rl_policy",
        # Reward Model Training
        "reward_model_pretrain": "reward_model",
        # Full System Training
        "joint_rl": "hierarchical_rl_full_system",
        # Legacy/Backward Compatibility
        "execution_bc_train": "execution_bc_actor",  # 向后兼容
    }
    return model_type_mapping.get(training_phase, "unknown_model_type")


OmegaConf.register_new_resolver("get_model_type", get_model_type)


@hydra.main(config_path="conf", config_name="config", version_base=None)
def main(cfg: DictConfig):
    # Moved imports and definitions
    import gymnasium as gym
    from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
        HierarchicalBCPolicy,
        ExecutionRLPolicy,
        ExecutionBCActorPolicy,
        HierarchicalHybridSAC,
        DecisionBCScoringPolicy,
        _process_expert_batch_for_imitation,
    )
    from stable_baselines3.common.callbacks import (
        CheckpointCallback,
        EvalCallback,
        CallbackList,
    )
    from stable_baselines3.common.vec_env import VecNormalize, DummyVecEnv
    from isaaclab.utils.dict import print_dict
    from isaaclab.utils.io import dump_pickle, dump_yaml
    from isaaclab_rl.sb3 import Sb3VecEnvWrapper
    from stable_baselines3.common.utils import (
        get_device,
        set_random_seed,
        obs_as_tensor,
    )
    from imitation.algorithms import bc
    from imitation.data import types as imitation_types
    from imitation.util.util import save_policy
    from imitation.util import (
        logger as imitation_logger,
    )  # Added for SwanLab integration

    def convert_dataloader_to_transitions(
        dataloader: torch.utils.data.DataLoader,
        device: torch.device,
        seq_length: int,
    ) -> Tuple[imitation_types.Transitions, imitation_types.Transitions]:
        """
        Converts data from a DataLoader (yielding batches processed by
        _process_expert_batch_for_imitation) into imitation.types.Transitions object.

        Args:
            dataloader: DataLoader yielding batches compatible with the helper function.
            device: The torch device used in the helper function.
            seq_length: The sequence length used in the helper function.

        Returns:
            An imitation_types.Transitions object containing all expert data.
        """
        logger_convert = logging.getLogger(
            "tora_learning.convert_dataloader"
        )  # Use a specific sub-logger
        obs_list, acts_list, infos_list, next_obs_list, dones_list = [], [], [], [], []
        target_pose_list = []
        logger_convert.info(
            "Converting DataLoader data to imitation.types.Transitions..."
        )

        batch_num = 0
        for batch in tqdm(dataloader, desc="Converting expert data"):
            batch_num += 1
            try:
                # ✅ 使用坐标转换包装器而不是原始处理函数
                try:
                    from px_janus_learnsim.utils.coordinate_transform_wrapper import (
                        process_expert_batch_with_transforms,
                    )

                    obs_np, acts_np, next_obs_np, dones_np, target_pose_np = (
                        process_expert_batch_with_transforms(
                            batch,
                            device,
                            seq_length,
                            target_hand=cfg.env.target_hand,
                            enable_coord_transform=True,
                            enable_6d_rotation_fix=False,
                        )
                    )
                except Exception as e:  # 🔧 修正：捕获所有异常，不只是ImportError
                    logger_convert.warning(
                        f"坐标转换包装器执行失败: {e}，使用原始处理函数"
                    )
                    obs_np, acts_np, next_obs_np, dones_np, target_pose_np = (
                        _process_expert_batch_for_imitation(
                            batch, device, seq_length, target_hand=cfg.env.target_hand
                        )
                    )

                obs_list.append(obs_np)
                acts_list.append(acts_np)
                next_obs_list.append(next_obs_np)
                dones_list.append(dones_np)
                infos_list.append([{} for _ in range(len(obs_np))])  # Basic info dict
                target_pose_list.append(target_pose_np)

            except ValueError as e:
                logger_convert.error(
                    f"Error processing batch {batch_num} for Transitions: {e}. Skipping batch."
                )
                continue
            except Exception as e:
                logger_convert.error(
                    f"Unexpected error converting batch {batch_num}: {e}", exc_info=True
                )
                continue

        logger_convert.info(f"Total batches processed: {batch_num}")

        if not obs_list:
            raise ValueError("No data successfully converted from DataLoader.")

        # 展平序列数据到样本级别
        np_obs_all = np.concatenate(obs_list, axis=0).reshape(-1, obs_list[0].shape[-1])
        np_acts_all = np.concatenate(acts_list, axis=0).reshape(
            -1, acts_list[0].shape[-1]
        )
        np_next_obs_all = np.concatenate(next_obs_list, axis=0).reshape(
            -1, next_obs_list[0].shape[-1]
        )
        np_dones_all = np.concatenate(dones_list, axis=0).flatten()

        # 扩展target_pose以匹配展平后的样本数
        target_poses_concatenated = np.concatenate(
            target_pose_list, axis=0
        )  # (528, 50)
        seq_length = obs_list[0].shape[1]  # 获取序列长度
        np_target_pose_all = np.repeat(
            target_poses_concatenated, seq_length, axis=0
        )  # (7920, 50)

        # 扩展infos以匹配展平后的样本数
        flat_infos_list = [info for sublist in infos_list for info in sublist]
        total_samples = np_obs_all.shape[0]
        np_infos_all = np.array([{} for _ in range(total_samples)], dtype=object)
        total_processed_samples = len(np_obs_all)

        logger_convert.info(
            f"Conversion complete. Successfully processed and converted {total_processed_samples} samples."
        )
        logger_convert.info(f"  - Observations shape: {np_obs_all.shape}")
        logger_convert.info(f"  - Actions shape: {np_acts_all.shape}")
        logger_convert.info(f"  - Target Poses shape: {np_target_pose_all.shape}")

        execution_transitions = imitation_types.Transitions(
            obs=np_obs_all,
            acts=np_acts_all,
            infos=np_infos_all,
            next_obs=np_next_obs_all,
            dones=np_dones_all,
        )
        decision_transitions = imitation_types.Transitions(
            obs=np_obs_all,
            acts=np_target_pose_all,
            infos=np_infos_all,
            next_obs=np_next_obs_all,
            dones=np_dones_all,
        )
        return execution_transitions, decision_transitions

    class SwanlabBCLogger(imitation_logger.HierarchicalLogger):
        _target_summary_provider_logger: sb3_base_logger.Logger = (
            None  # For type hinting
        )

        def __init__(self, *args, **kwargs):
            logger.info("SwanlabBCLogger.__init__: Entered")
            temp_dir_for_default_logger = None
            if "folder" not in kwargs or kwargs["folder"] is None:
                # Create a unique temporary directory for the default logger if no folder is specified
                # This is to avoid conflicts if multiple SwanlabBCLoggers are instantiated without explicit folders
                temp_dir_for_default_logger = tempfile.mkdtemp(
                    prefix="swanlab_bc_logger_default_"
                )
                actual_folder = temp_dir_for_default_logger
                logger.info(
                    f"SwanlabBCLogger.__init__: No folder specified, created temporary folder for default_logger: {actual_folder}"
                )
            else:
                actual_folder = kwargs["folder"]

            actual_format_strs = kwargs.get(
                "format_strs", ["stdout"]
            )  # Keep it minimal for the internal default

            # Create the default_logger that HierarchicalLogger's super().__init__ expects
            # This mimics part of imitation_logger.configure()
            sb3_output_formats = [
                imitation_logger.make_output_format(f, str(actual_folder))
                for f in actual_format_strs
                if f != "wandb"
            ]

            internal_default_sb3_logger = sb3_base_logger.Logger(
                folder=str(actual_folder), output_formats=sb3_output_formats
            )
            super().__init__(
                default_logger=internal_default_sb3_logger,
                format_strs=actual_format_strs,
            )
            logger.info(
                f"SwanlabBCLogger.__init__: super().__init__ called with default_logger path {actual_folder}"
            )
            self._target_summary_provider_logger = self.default_logger
            logger.info(
                f"SwanlabBCLogger.__init__: Set _target_summary_provider_logger to self.default_logger (type: {type(self._target_summary_provider_logger)})"
            )

            self._swanlab_actual_batch_count = 0
            self._epoch_losses = []  # For accumulating losses over a full epoch
            self._internal_epoch_count = (
                0  # For epoch summary step if not directly passed
            )
            self._interval_losses = (
                []
            )  # For accumulating losses over a log interval (between dump calls)

        def record(self, key: str, value: any):  # type: ignore[override]
            logger.debug(
                f"SwanlabBCLogger.record: Received key: '{key}', value: '{value}' (type: {type(value)})"
            )

            # General logging for all "bc/" prefixed keys to SwanLab
            if SWANLAB_INITIALIZED and key.startswith("bc/"):
                log_val_for_swanlab = None
                try:
                    if isinstance(value, torch.Tensor):
                        log_val_for_swanlab = value.item()
                    elif isinstance(value, (float, int)):
                        log_val_for_swanlab = float(value)
                    else:
                        logger.debug(
                            f"SwanlabBCLogger.record: Value for key '{key}' (type: {type(value)}) is not directly convertible to float for SwanLab. Skipping SwanLab log for this specific key."
                        )

                    if log_val_for_swanlab is not None:
                        swanlab.log(
                            {key: log_val_for_swanlab},
                            step=self._swanlab_actual_batch_count,
                        )
                        logger.debug(
                            f"SwanlabBCLogger.record: Logged to SwanLab '{key}': {log_val_for_swanlab:.4f} at step {self._swanlab_actual_batch_count}"
                        )

                except Exception as e_swanlab_log:
                    logger.error(
                        f"SwanlabBCLogger.record: Error logging key '{key}' to SwanLab: {e_swanlab_log}",
                        exc_info=True,
                    )

            # Specific handling for "bc/loss" for aggregations and primary step counting
            if key == "bc/loss":
                loss_for_aggregation = None
                # Extract loss_value for aggregation lists, even if SwanLab wasn't initialized or bc/ prefix logging failed
                try:
                    if isinstance(value, torch.Tensor):
                        loss_for_aggregation = value.item()
                    elif isinstance(value, (float, int)):
                        loss_for_aggregation = float(value)
                    # If not convertible, loss_for_aggregation remains None, and won't be added to lists.
                except Exception as e_extract_loss:
                    logger.warning(
                        f"SwanlabBCLogger.record: Could not extract float from value for 'bc/loss' for aggregation. Key: {key}, Value: {value}, Error: {e_extract_loss}"
                    )

                if loss_for_aggregation is not None:
                    self._epoch_losses.append(loss_for_aggregation)
                    self._interval_losses.append(loss_for_aggregation)
                if SWANLAB_INITIALIZED:
                    self._swanlab_actual_batch_count += 1
            super().record(key, value)

        def record_mean(self, key: str, value: any, exclude: any = None):  # type: ignore[override]
            logger.info(
                f"SwanlabBCLogger.record_mean: Received key: '{key}', value: '{value}'"
            )
            # Potentially log to SwanLab here if needed, or just pass to super
            super().record_mean(key, value, exclude)

        def dump(self, step: int = 0):
            super().dump(step)

            if SWANLAB_INITIALIZED:
                if self._interval_losses:
                    self._interval_losses = []  # Reset for the next interval

            #     logger.info(
            #         f"SwanlabBCLogger.dump: interval_summary/loss_mean logging to SwanLab is disabled. SwanLab step for this dump was {step}."
            #     )
            # else:
            #     logger.info(
            #         f"SwanlabBCLogger.dump: SWANLAB_INITIALIZED is False. Skipping SwanLab logging for swanlab_step {step}."
            #     )

        def log_epoch_summary(self):
            """Calculates and logs the average loss for the completed epoch."""
            global SWANLAB_INITIALIZED
            if not SWANLAB_INITIALIZED:
                return

            if self._epoch_losses:
                epoch_avg_loss = sum(self._epoch_losses) / len(self._epoch_losses)
                # swanlab.log(
                #     {"epoch/loss_mean": epoch_avg_loss}, step=self._internal_epoch_count
                # )
                # logger.info(
                #     f"SwanLab: Logged epoch {self._internal_epoch_count} summary - Avg Loss: {epoch_avg_loss:.4f}"
                # )

            self._epoch_losses = []  # Reset for the next epoch
            self._internal_epoch_count += 1  # Increment for the next epoch summary

    logger.info("Hydra main function started. AppLauncher already initialized.")
    logger.info(
        f"AppLauncher was configured with (subset of args): {app_launcher_known_args}"
    )
    # Corrected f-string with triple quotes for multi-line YAML
    logger.info(
        f"""Hydra effective configuration:
    {OmegaConf.to_yaml(cfg)}"""
    )

    output_dir = hydra.core.hydra_config.HydraConfig.get().runtime.output_dir
    logger.info(f"All outputs will be saved to: {output_dir}")

    env = None  # Initialize env to None for the finally block
    global SWANLAB_INITIALIZED  # Ensure we affect the global flag
    SWANLAB_INITIALIZED = False  # Default to False

    try:
        # --- 智能日志配置检查和设置 ---
        print("🔧 正在初始化日志配置...")
        # 检查当前的colorlog状态
        check_colorlog_status()

        # 智能处理重复日志问题，同时保持colorlog
        smart_disable_duplicate_logging()
        print("✅ 日志配置完成")
        # --- End 智能日志配置 ---

        # --- SwanLab Initialization ---
        if cfg.logging.get("use_swanlab", False):
            try:
                logger.info("Initializing SwanLab...")
                # Get the run name (it's already generated by Hydra processing the config)
                current_run_name = cfg.logging.current_run_id
                output_dir = (
                    hydra.core.hydra_config.HydraConfig.get().runtime.output_dir
                )
                logger.info(f"Hydra output directory: {output_dir}")
                logger.info(f"Shared Run ID for SwanLab & Hydra: {current_run_name}")

                swanlab.init(
                    project=cfg.logging.get(
                        "swanlab_project_name", "px_tactrix_project"
                    ),
                    workspace=cfg.logging.get("swanlab_workspace", None),
                    entity=cfg.logging.get("swanlab_entity", None),
                    experiment_name=current_run_name,
                    description=cfg.logging.get(
                        "experiment_description",
                        "Behavioral Cloning training run for px_tactrix execution layer.",
                    ),
                    config=OmegaConf.to_container(
                        cfg, resolve=True, throw_on_missing=True
                    ),
                    logdir=str(Path(output_dir) / "swanlog"),
                    mode="cloud",
                )
                SWANLAB_INITIALIZED = True
                logger.info(
                    f"SwanLab initialized for project: {cfg.logging.get('swanlab_project_name', 'px_tactrix_project')}, experiment: {current_run_name}"
                )
            except Exception as e:
                logger.error(f"Failed to initialize SwanLab: {e}", exc_info=True)
                SWANLAB_INITIALIZED = False  # Ensure it's false on failure
        # --- End SwanLab Initialization ---

        # --- Seed Handling --- #
        actual_seed = cfg.app.seed
        if cfg.app.seed == -1:
            actual_seed = random.randint(0, 10000)
            logger.info(f"Generated random seed: {actual_seed}")
        set_random_seed(actual_seed)
        logger.info(f"Using seed: {actual_seed}")
        # --- End Seed Handling --- #

        # 使用统一的配置管理
        from env_config_utils import (
            get_environment_config,
            apply_training_config_overrides,
            save_evaluation_config,
        )

        # 动态导入环境配置，根据config.yaml中的env.name
        env_name = cfg.env.name
        EnvCfg, env_cfg = get_environment_config(env_name, cfg)

        logger.info(f"Using environment: {env_name} with config: {EnvCfg.__name__}")

        # 应用训练配置覆盖（统一逻辑）
        env_cfg = apply_training_config_overrides(env_cfg, cfg)

        print(
            f"[INFO] 环境配置: frames_to_stack={env_cfg.num_frames_to_stack}, base_obs={env_cfg.base_observation_space}, total_obs={env_cfg.observation_space}"
        )

        # 设置其他环境参数
        env_cfg.scene.num_envs = (
            cfg.env.num_envs if cfg.env.num_envs is not None else env_cfg.scene.num_envs
        )
        env_cfg.seed = actual_seed
        env_cfg.sim.device = (
            cfg.app.device if cfg.app.device is not None else env_cfg.sim.device
        )
        env_cfg_save_path = os.path.join(output_dir, "env_config_applied.yaml")
        dump_yaml(env_cfg_save_path, env_cfg)
        logger.info(f"Saved applied {type(env_cfg).__name__} to {env_cfg_save_path}")

        # 🆕 保存完全解析的配置（包含命令行覆盖和引用解析）
        try:
            # 解析所有配置引用（如${env.seq_length}）为实际数值
            resolved_cfg = OmegaConf.to_container(
                cfg, resolve=True, throw_on_missing=True
            )

            # 保存解析后的配置
            resolved_config_path = os.path.join(output_dir, "resolved_config.yaml")
            with open(resolved_config_path, "w") as f:
                yaml.dump(resolved_cfg, f, default_flow_style=False, indent=2)
            logger.info(f"保存完全解析的配置到: {resolved_config_path}")

            # 同时保存原始配置（用于调试对比）
            original_config_path = os.path.join(output_dir, "original_config.yaml")
            OmegaConf.save(config=cfg, f=original_config_path)
            logger.info(f"保存原始配置到: {original_config_path}")

            # 🔍 打印关键解析信息
            logger.info("🔍 关键配置解析结果:")
            logger.info(f"  - env.seq_length: {resolved_cfg['env']['seq_length']}")
            if "policy_kwargs" in resolved_cfg.get("agent", {}):
                logger.info(
                    f"  - agent.policy_kwargs.num_queries: {resolved_cfg['agent']['policy_kwargs'].get('num_queries', 'N/A')}"
                )
            if "execution_policy" in resolved_cfg.get("agent", {}):
                exec_policy = resolved_cfg["agent"]["execution_policy"]
                if "policy_kwargs" in exec_policy:
                    logger.info(
                        f"  - agent.execution_policy.policy_kwargs.num_queries: {exec_policy['policy_kwargs'].get('num_queries', 'N/A')}"
                    )

        except Exception as e:
            logger.error(f"保存解析配置失败: {e}")
            # 降级保存原始配置
            OmegaConf.save(
                config=cfg, f=os.path.join(output_dir, "effective_config.yaml")
            )
            logger.info(f"降级保存原始配置到 effective_config.yaml")

        logger.info(f"Creating main environment: {cfg.env.name}")
        env = gym.make(
            cfg.env.name,
            cfg=env_cfg,
            render_mode="rgb_array" if cfg.video.record else None,
        )
        if cfg.video.record:
            video_save_dir = os.path.join(output_dir, "videos", "train")
            os.makedirs(video_save_dir, exist_ok=True)
            video_kwargs = {
                "video_folder": video_save_dir,
                "step_trigger": lambda step: step % cfg.video.interval == 0,
                "video_length": cfg.video.length,
                "disable_logger": True,
            }
            print("[INFO] 在训练期间录制视频。")
            print_dict(video_kwargs, nesting=4)
            env = gym.wrappers.RecordVideo(env, **video_kwargs)
        env = Sb3VecEnvWrapper(env)
        logger.info("Main environment created and wrapped.")

        # --- Training Phase Logic --- #
        if (
            cfg.training.training_phase == "decision_bc_train"
            or cfg.training.training_phase == "execution_bc_train"
            or cfg.training.training_phase == "execution_bc_actor_train"
        ):
            logger.info(
                "\033[91mStarting BC Pretraining Phase using imitation library\033[0m"
            )
            expert_path = cfg.training.dataset_dir
            device_str = cfg.app.get("device", "auto")
            device = get_device(device_str)
            device_str_for_model = str(device)  # 确保是字符串格式
            batch_size_load = cfg.training.get(
                "batch_size_load", cfg.training.batch_size
            )
            try:
                train_dataloader, val_dataloader, stats, is_sim = (
                    HierarchicalHybridSAC.load_data(
                        expert_path,
                        batch_size_train=batch_size_load,
                        batch_size_val=max(1, batch_size_load // 2),
                        seq_length=cfg.env.seq_length,
                    )
                )
                if train_dataloader is None:
                    raise ValueError("Failed to load training data.")
                logger.info(f"Expert data loaded for BC. Stats: {stats}, Sim: {is_sim}")
            except Exception as e:
                logger.error(f"Error loading expert data for BC: {e}", exc_info=True)
                env.close()
                return

            env_observation_space = env.observation_space
            env_action_space = env.action_space

            if cfg.training.training_phase == "decision_bc_train":
                logger.info(
                    "\033[91mStarting Custom Decision Layer BC Training ... [Using DecisionBCScoringPolicy] \033[0m"
                )

                # 🔄 Decision BC需要转换数据格式
                logger.info("为Decision BC训练转换数据格式...")
                execution_transitions, decision_transitions = (
                    convert_dataloader_to_transitions(
                        train_dataloader, device, cfg.env.seq_length
                    )
                )
                num_candidates = cfg.agent.decision_policy.get("num_candidates", 10)
                num_epochs = cfg.training.decision_epochs
                current_lr = cfg.agent.decision_policy.get(
                    "learning_rate", cfg.agent.learning_rate
                )

                save_dir = os.path.join(output_dir, "decision_models")
                os.makedirs(save_dir, exist_ok=True)
                decision_policy_save_path = os.path.join(
                    save_dir, "decision_bc_scoring_policy.pth"
                )

                logger.info(
                    f"Decision BC Parameters: num_candidates={num_candidates}, lr={current_lr}, epochs={num_epochs}"
                )
                logger.info(
                    f"Saving decision policy model to: {decision_policy_save_path}"
                )

                decision_policy_kwargs = OmegaConf.to_container(
                    cfg.agent.decision_policy.get("policy_kwargs", {}), resolve=True
                )
                # Ensure base policy kwargs are also available if not directly under decision_policy.policy_kwargs
                base_policy_kwargs = OmegaConf.to_container(
                    cfg.agent.get("policy_kwargs", {}), resolve=True
                )
                # Merge them, specific decision_policy_kwargs take precedence
                merged_decision_kwargs = {
                    **base_policy_kwargs,
                    **decision_policy_kwargs,
                }
                # Ensure num_queries and tactile_shape are correctly set if not in config
                merged_decision_kwargs.setdefault("num_queries", cfg.env.seq_length)
                # Set tactile_shape parameter with correct default value matching SharedBaseEncoders
                merged_decision_kwargs.setdefault("tactile_shape", (16, 1))

                try:
                    decision_policy = DecisionBCScoringPolicy(
                        observation_space=env_observation_space,
                        action_space=env_action_space,  # Placeholder for BasePolicy
                        lr_schedule=ConstantSchedulePicklable(current_lr),
                        **merged_decision_kwargs,
                    ).to(device)
                    logger.info("DecisionBCScoringPolicy instantiated successfully.")
                    optimizer = decision_policy.optimizer
                    criterion = torch.nn.CrossEntropyLoss()
                    logger.info(
                        f"Optimizer ({type(optimizer).__name__}) and Criterion ({type(criterion).__name__}) are set up."
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to instantiate DecisionBCScoringPolicy: {e}",
                        exc_info=True,
                    )
                    env.close()
                    return

                from px_janus_learnsim.learning.models.bc_utils import (
                    process_imitation_batch,
                    dummy_pose_generator,
                )

                batch_size_train_decision = cfg.training.get(
                    "decision_batch_size", cfg.training.batch_size
                )

                for epoch in range(num_epochs):
                    epoch_loss = 0.0
                    num_batches = 0
                    total_correct = 0
                    total_samples = 0
                    progress_bar = tqdm(
                        train_dataloader,
                        desc=f"Epoch {epoch+1}/{num_epochs}",
                        leave=False,
                    )
                    for batch_idx, batch_data in enumerate(progress_bar):
                        optimizer.zero_grad()
                        try:
                            obs_dict, p_expert_batch, _, _, _ = process_imitation_batch(
                                batch_data, device
                            )
                            current_batch_size = p_expert_batch.shape[0]
                            P_candidates_batch = dummy_pose_generator(
                                current_batch_size, num_candidates - 1, 18, device
                            )
                            P_combined_batch = torch.cat(
                                [p_expert_batch.unsqueeze(1), P_candidates_batch], dim=1
                            )
                            target_indices = torch.zeros(
                                current_batch_size, dtype=torch.long, device=device
                            )
                            scores = decision_policy.evaluate_scores(
                                obs_dict, P_combined_batch
                            )
                            loss = criterion(scores, target_indices)
                            loss.backward()
                            optimizer.step()
                            epoch_loss += loss.item()
                            num_batches += 1
                            predicted_indices = torch.argmax(scores, dim=1)
                            correct_in_batch = (
                                (predicted_indices == target_indices).sum().item()
                            )
                            total_correct += correct_in_batch
                            total_samples += current_batch_size
                            running_accuracy = (
                                total_correct / total_samples
                                if total_samples > 0
                                else 0
                            )
                            progress_bar.set_postfix(
                                {
                                    "batch_loss": f"{loss.item():.4f}",
                                    "epoch_acc": f"{running_accuracy:.3f}",
                                }
                            )
                        except Exception as e:
                            logger.error(
                                f"Error during training batch (Epoch {epoch+1}, Batch {batch_idx}): {e}",
                                exc_info=True,
                            )
                            continue
                    avg_epoch_loss = epoch_loss / num_batches if num_batches > 0 else 0
                    epoch_accuracy = (
                        total_correct / total_samples if total_samples > 0 else 0
                    )
                    logger.info(
                        f"Epoch {epoch+1}/{num_epochs} completed. Avg Loss: {avg_epoch_loss:.6f}, Accuracy: {epoch_accuracy:.4f}"
                    )

                logger.info("Decision BC training finished. Saving policy model...")
                try:
                    torch.save(decision_policy.state_dict(), decision_policy_save_path)
                    logger.info(
                        f"Decision policy model saved to {decision_policy_save_path}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to save Decision BC policy state_dict: {e}",
                        exc_info=True,
                    )
                logger.info("Ending decision_bc_train phase.")
                env.close()
                return

            elif (
                cfg.training.training_phase == "execution_bc_train"
                or cfg.training.training_phase == "execution_bc_actor_train"
            ):
                logger.info("\033[91mStarting Execution Layer BC Training ...\033[0m")
                current_lr = cfg.agent.execution_policy.get(
                    "learning_rate", cfg.agent.learning_rate
                )
                num_epochs = cfg.training.execution_epochs
                batch_size_train_execution = cfg.training.get(
                    "execution_batch_size", cfg.training.batch_size
                )

                exec_policy_kwargs = OmegaConf.to_container(
                    cfg.agent.execution_policy.get("policy_kwargs", {}), resolve=True
                )
                base_policy_kwargs = OmegaConf.to_container(
                    cfg.agent.get("policy_kwargs", {}), resolve=True
                )
                merged_exec_kwargs = {**base_policy_kwargs, **exec_policy_kwargs}
                merged_exec_kwargs.setdefault(
                    "num_queries", cfg.env.seq_length
                )  # Ensure num_queries
                # Set tactile_shape parameter with correct default value matching SharedBaseEncoders
                merged_exec_kwargs.setdefault("tactile_shape", (16, 1))

                # Debug: print the final merged_exec_kwargs
                logger.info(f"Final merged_exec_kwargs: {merged_exec_kwargs}")

                # 检查是否需要启用单手模式并调整动作空间
                enable_single_hand = merged_exec_kwargs.get("enable_single_hand", False)
                single_hand_config = merged_exec_kwargs.get("single_hand_config", {})
                if (
                    enable_single_hand
                    and cfg.env.name != "Isaac-SingleHand-DexH13-Direct-v0"
                ):
                    logger.info("🔧 单手训练模式已启用，修改环境动作空间从50维到25维")

                    # 为单手训练创建修改的动作空间
                    import gymnasium as gym

                    original_action_space = env_action_space
                    env_action_space = gym.spaces.Box(
                        low=-1.0,
                        high=1.0,
                        shape=(25,),  # 单手25维动作空间
                        dtype=np.float32,
                    )
                    logger.info(
                        f"动作空间已修改: {original_action_space.shape} -> {env_action_space.shape}"
                    )

                try:
                    execution_bc_policy = ExecutionBCActorPolicy(
                        observation_space=env_observation_space,
                        action_space=env_action_space,
                        lr_schedule=ConstantSchedulePicklable(current_lr),
                        policy_kwargs=merged_exec_kwargs,
                    ).to(device)
                    logger.info(
                        "ExecutionBCActorPolicy instantiated successfully for Execution BC."
                    )

                    # 重新创建优化器以支持权重衰减（如果配置了）
                    weight_decay = single_hand_config.get("weight_decay", 0.0)
                    if weight_decay > 0:
                        logger.info(f"使用权重衰减: {weight_decay}")
                        execution_bc_policy.optimizer = torch.optim.AdamW(
                            execution_bc_policy.parameters(),
                            lr=current_lr,
                            weight_decay=weight_decay,
                        )
                        logger.info("优化器已更新为AdamW with权重衰减")

                except Exception as e:
                    logger.error(
                        f"Failed to instantiate ExecutionBCActorPolicy for Execution BC: {e}",
                        exc_info=True,
                    )
                    env.close()
                    return

                custom_execution_logger = None
                bc_console_log_interval = cfg.logging.get(
                    "bc_log_interval", 500
                )  # Default to 500 if not in config

                if SWANLAB_INITIALIZED:
                    logger.info(
                        "SwanLab is initialized. Creating SwanlabBCLogger for Execution BC."
                    )
                    custom_execution_logger = SwanlabBCLogger()
                # --- End SwanLab Logger for Execution BC ---

                # --- Define on_epoch_end callback for BC training ---
                def on_epoch_end_callback():  # Removed epoch_num parameter
                    if custom_execution_logger:
                        custom_execution_logger.log_epoch_summary()  # Called without arguments

                # --- End on_epoch_end callback ---

                # --- Create conditional BC trainer that supports target poses ---

                # Check if we should enable conditional training
                enable_conditional_training = (
                    merged_exec_kwargs.get("conditional_pose_dim", 0) > 0
                )

                # 🎯 策略选择日志
                strategy_name = "条件BC" if enable_conditional_training else "传统BC"
                logger.info(f"🎯 选择的训练策略: {strategy_name}")
                logger.info(
                    f"Conditional training enabled: {enable_conditional_training}"
                )

                if enable_conditional_training:
                    logger.info("📈 直接使用dataloader")
                else:
                    logger.info("⚠️ 建议使用条件BC训练")

                # 单手训练变量在前面已经定义，这里直接使用
                logger.info(f"Single hand training enabled: {enable_single_hand}")
                if enable_single_hand:
                    logger.info(f"Single hand config: {single_hand_config}")

                if enable_conditional_training:
                    # 使用新的ConditionalBCTrainer进行条件BC训练
                    from px_janus_learnsim.learning.trainers import ConditionalBCTrainer

                    logger.info("🚀 使用条件BC训练模式")

                    # 确定模型保存路径
                    execution_bc_model_path = os.path.join(
                        output_dir, "execution_bc_policy.pth"
                    )

                    # 创建条件BC训练器
                    conditional_trainer = ConditionalBCTrainer(
                        policy=execution_bc_policy,
                        dataloader=train_dataloader,
                        optimizer=execution_bc_policy.optimizer,
                        device=device,
                        custom_logger=(
                            custom_execution_logger
                            if merged_exec_kwargs.get("custom_logger", True)
                            else None
                        ),
                        enable_single_hand=enable_single_hand,
                        single_hand_config={
                            **single_hand_config,
                            # 添加物体中心坐标系配置到single_hand_config中
                            "use_object_centric": merged_exec_kwargs.get(
                                "use_object_centric", False
                            ),
                            "enable_pose_randomization": merged_exec_kwargs.get(
                                "enable_pose_randomization", False
                            ),
                            "randomization_range": merged_exec_kwargs.get(
                                "randomization_range", 0.5
                            ),
                        },
                        # 添加动作缩放配置
                        enable_action_scaling=merged_exec_kwargs.get(
                            "enable_action_scaling", True
                        ),
                        # 添加调试模式参数，从命令行获取
                        debug=(
                            hasattr(app_launcher_known_args, "debug")
                            and app_launcher_known_args.debug
                        ),
                        obs_normalization=merged_exec_kwargs.get(
                            "obs_normalization", "standard"
                        ),
                        action_scaling=merged_exec_kwargs.get(
                            "action_scaling", "minmax"
                        ),
                        action_clip_range=tuple(
                            merged_exec_kwargs.get("action_clip_range", [-1.0, 1.0])
                        ),
                        scaling_stats_path=merged_exec_kwargs.get(
                            "scaling_stats_path", None
                        ),  # 保留配置文件中的路径（如果有）
                        model_save_path=execution_bc_model_path,  # 新增：传入模型保存路径
                        enable_separated_normalization=merged_exec_kwargs.get(
                            "enable_separated_normalization", True
                        ),
                        target_pose_normalization=merged_exec_kwargs.get(
                            "target_pose_normalization", "standard"
                        ),
                    )

                    # 调试信息：打印最终的single_hand_config
                    final_single_hand_config = {
                        **single_hand_config,
                        "use_object_centric": merged_exec_kwargs.get(
                            "use_object_centric", False
                        ),
                        "enable_pose_randomization": merged_exec_kwargs.get(
                            "enable_pose_randomization", False
                        ),
                        "randomization_range": merged_exec_kwargs.get(
                            "randomization_range", 0.5
                        ),
                    }
                    logger.info(
                        f"🔧 最终single_hand_config: {final_single_hand_config}"
                    )
                    logger.info(
                        f"🎯 物体中心坐标系启用状态: {final_single_hand_config.get('use_object_centric', False)}"
                    )

                    logger.info(f"开始条件BC训练: {num_epochs}轮")

                    # 执行训练
                    training_stats = conditional_trainer.train(
                        n_epochs=num_epochs,
                        log_interval=bc_console_log_interval,
                        on_epoch_end=on_epoch_end_callback,
                    )

                    logger.info(f"条件BC训练完成!")
                    logger.info(
                        f"训练统计: 总轮次={training_stats['total_epochs']}, "
                        f"总批次={training_stats['total_batches']}, "
                        f"平均损失={training_stats['average_loss']:.6f}"
                    )

                else:
                    # Use standard BC training (original code)
                    logger.info("🔄 使用传统BC训练模式，正在转换数据格式...")
                    logger.info("⚠️  注意：传统BC需要额外的数据转换步骤，性能相对较慢")

                    # 确定模型保存路径（传统BC也需要）
                    execution_bc_model_path = os.path.join(
                        output_dir, "execution_bc_policy.pth"
                    )

                    # 🔄 只在传统BC路径下才执行数据转换
                    execution_transitions, decision_transitions = (
                        convert_dataloader_to_transitions(
                            train_dataloader, device, cfg.env.seq_length
                        )
                    )
                    logger.info("传统BC数据转换完成，开始训练...")

                    try:
                        rng = np.random.default_rng(actual_seed)
                        execution_bc_trainer = bc.BC(
                            observation_space=env_observation_space,
                            action_space=env_action_space,
                            policy=execution_bc_policy,
                            demonstrations=execution_transitions,
                            rng=rng,
                            batch_size=batch_size_train_execution,
                            optimizer_cls=torch.optim.AdamW,
                            optimizer_kwargs={"lr": current_lr},
                            custom_logger=custom_execution_logger,
                        )
                        logger.info(
                            "imitation.bc.BC trainer instantiated successfully for Execution BC."
                        )
                    except Exception as e:
                        logger.error(
                            f"Failed to instantiate imitation BC trainer for Execution BC: {e}",
                            exc_info=True,
                        )
                        env.close()
                        return

                    logger.info(
                        f"Starting Execution BC training loop for {num_epochs} epochs... Console logs every ~{bc_console_log_interval} batches."
                    )
                    execution_bc_trainer.train(
                        n_epochs=num_epochs,
                        log_interval=bc_console_log_interval,  # Controls console print frequency via logger.dump()
                        on_epoch_end=on_epoch_end_callback,  # For logging true epoch summaries to SwanLab
                    )
                    logger.info(
                        f"Execution BC training finished after {num_epochs} epochs."
                    )

                # execution_bc_model_path在上面创建ConditionalBCTrainer时已经定义
                logger.info(
                    f"Saving trained Execution BC policy to {execution_bc_model_path}..."
                )
                try:
                    if enable_conditional_training:
                        # 条件BC训练模式：保存conditional_trainer中的policy
                        logger.info(
                            f"Saving trained Execution BC policy to {execution_bc_model_path}..."
                        )
                        save_policy(execution_bc_policy, execution_bc_model_path)
                    else:
                        # 标准BC训练模式：保存execution_bc_trainer.policy
                        logger.info(
                            f"Saving trained Execution BC policy to {execution_bc_model_path}..."
                        )
                        save_policy(
                            execution_bc_trainer.policy, execution_bc_model_path
                        )

                    logger.info(
                        f"Execution BC Policy saved to {execution_bc_model_path}"
                    )

                    # 保存训练时的有效配置
                    try:
                        # 🆕 保存完全解析的配置
                        resolved_cfg = OmegaConf.to_container(
                            cfg, resolve=True, throw_on_missing=True
                        )
                        resolved_config_path = os.path.join(
                            output_dir, "resolved_config.yaml"
                        )
                        with open(resolved_config_path, "w") as f:
                            yaml.dump(
                                resolved_cfg, f, default_flow_style=False, indent=2
                            )
                        logger.info(
                            f"BC训练完全解析配置已保存到: {resolved_config_path}"
                        )

                        # 同时保存原始配置
                        original_config_path = os.path.join(
                            output_dir, "original_config.yaml"
                        )
                        OmegaConf.save(cfg, original_config_path)
                        logger.info(f"BC训练原始配置已保存到: {original_config_path}")

                        # 使用统一配置管理工具保存环境配置，使用解析后的数值
                        save_evaluation_config(
                            env_cfg,
                            output_dir,
                            {
                                "source": "bc_training_script",
                                "env_name": env_name,
                                "seq_length": resolved_cfg["env"][
                                    "seq_length"
                                ],  # 使用解析后的数值
                                "frames_to_stack": env_cfg.num_frames_to_stack,
                                "observation_space": env_cfg.observation_space,
                                "base_observation_space": env_cfg.base_observation_space,
                                "num_envs": env_cfg.scene.num_envs,
                                "timestamp": time.strftime("%Y-%m-%d_%H-%M-%S"),
                                # 🆕 添加更多关键参数
                                "policy_kwargs_num_queries": resolved_cfg.get(
                                    "agent", {}
                                )
                                .get("execution_policy", {})
                                .get("policy_kwargs", {})
                                .get("num_queries", None),
                                "enable_single_hand": enable_single_hand,
                                "conditional_pose_dim": merged_exec_kwargs.get(
                                    "conditional_pose_dim", 0
                                ),
                            },
                        )
                    except Exception as config_save_error:
                        logger.warning(f"保存BC配置信息失败: {config_save_error}")

                except Exception as e:
                    logger.error(
                        f"Failed to save Execution BC trained policy: {e}",
                        exc_info=True,
                    )

                logger.info("Ending execution_bc_train phase.")
                env.close()
                return

        elif cfg.training.training_phase == "reward_model_pretrain":
            logger.info(
                "\033[91mStarting Self-Supervised Reward Model Pretraining\033[0m"
            )

            # 加载数据（复用现有数据加载逻辑）
            expert_path = cfg.training.dataset_dir
            device_str = cfg.app.get("device", "auto")
            device = get_device(device_str)
            device_str_for_model = str(device)  # 确保是字符串格式

            try:
                train_dataloader, val_dataloader, stats, is_sim = (
                    HierarchicalHybridSAC.load_data(
                        expert_path,
                        batch_size_train=cfg.agent.reward_model.batch_size,
                        batch_size_val=max(1, cfg.agent.reward_model.batch_size // 2),
                        seq_length=cfg.env.seq_length,
                    )
                )
                if train_dataloader is None:
                    raise ValueError("Failed to load training data.")
                logger.info(
                    f"Expert data loaded for reward model. Stats: {stats}, Sim: {is_sim}"
                )
            except Exception as e:
                logger.error(
                    f"Error loading expert data for reward model: {e}", exc_info=True
                )
                env.close()
                return

            # 获取观察和动作空间维度
            observation_dim = env.observation_space.shape[0]
            action_dim = env.action_space.shape[0]

            # 首先处理一个批次的数据来获取实际的观察维度
            logger.info("Analyzing actual observation dimensions from expert data...")
            sample_batch = next(iter(train_dataloader))
            try:
                # ✅ 使用坐标转换包装器进行样本处理
                try:
                    from px_janus_learnsim.utils.coordinate_transform_wrapper import (
                        process_expert_batch_with_transforms,
                    )

                    sample_obs_np, _, _, _, _ = process_expert_batch_with_transforms(
                        sample_batch,
                        device,
                        cfg.env.seq_length,
                        target_hand="left",
                        enable_coord_transform=True,
                        enable_6d_rotation_fix=False,
                    )
                except Exception as e:
                    logger.warning(f"坐标转换包装器执行失败: {e}，使用原始处理函数")
                    sample_obs_np, _, _, _, _ = _process_expert_batch_for_imitation(
                        sample_batch, device, cfg.env.seq_length
                    )
                actual_observation_dim = sample_obs_np.shape[1]  # [batch_size, obs_dim]
                logger.info(f"Environment observation space: {observation_dim}")
                logger.info(
                    f"Actual processed observation dim: {actual_observation_dim}"
                )

                # 使用实际的观察维度
                input_dim_for_model = actual_observation_dim

            except Exception as e:
                logger.error(f"Failed to analyze observation dimensions: {e}")
                logger.info(
                    f"Falling back to environment observation space dimension: {observation_dim}"
                )
                input_dim_for_model = observation_dim

            # 初始化ReconstructionRewardModel
            try:
                # 处理配置参数映射
                model_kwargs = OmegaConf.to_container(
                    cfg.agent.reward_model.model_kwargs, resolve=True
                )

                # 映射参数名
                if "encoder_dim" in model_kwargs:
                    # 将encoder_dim转换为encoder_hidden_dims列表
                    encoder_dim = model_kwargs.pop("encoder_dim")
                    model_kwargs["encoder_hidden_dims"] = [
                        encoder_dim,
                        encoder_dim // 2,
                    ]
                    model_kwargs["decoder_hidden_dims"] = [
                        encoder_dim // 2,
                        encoder_dim,
                    ]

                # 映射learning_rate参数名
                if "learning_rate" in model_kwargs:
                    model_kwargs["ae_learning_rate"] = model_kwargs.pop("learning_rate")

                # 移除不被支持的参数
                unsupported_params = [
                    "weight_decay"
                ]  # ReconstructionRewardModel不支持的参数
                for param in unsupported_params:
                    if param in model_kwargs:
                        removed_value = model_kwargs.pop(param)
                        logger.info(
                            f"Removed unsupported parameter '{param}': {removed_value}"
                        )

                reward_model = ReconstructionRewardModel(
                    input_dim=input_dim_for_model,  # 使用实际的观察维度
                    device=device_str_for_model,
                    **model_kwargs,  # 从配置传入处理后的模型参数
                )
                logger.info(
                    f"ReconstructionRewardModel initialized: input_dim={input_dim_for_model}"
                )
            except Exception as e:
                logger.error(
                    f"Failed to initialize ReconstructionRewardModel: {e}",
                    exc_info=True,
                )
                env.close()
                return

            # 训练循环
            num_epochs = cfg.agent.reward_model.epochs
            best_val_loss = float("inf")

            # 创建观察数据的DataLoader用于ReconstructionRewardModel训练
            # ReconstructionRewardModel的train_reward_model期望DataLoader产生observations
            class ObservationDataset(torch.utils.data.Dataset):
                def __init__(self, dataloader, device, seq_length):
                    self.observations = []
                    logger.info(
                        "Extracting observations from expert data for reward model training..."
                    )

                    for batch_data in tqdm(dataloader, desc="Processing expert data"):
                        try:
                            # ✅ 使用坐标转换包装器进行观察数据处理
                            try:
                                print("🆕 使用坐标转换包装器进行观察数据处理")
                                from px_janus_learnsim.utils.coordinate_transform_wrapper import (
                                    process_expert_batch_with_transforms,
                                )

                                obs_np, _, _, _, _ = (
                                    process_expert_batch_with_transforms(
                                        batch_data,
                                        device,
                                        seq_length,
                                        target_hand="left",
                                        enable_coord_transform=True,
                                        enable_6d_rotation_fix=False,
                                    )
                                )
                            except Exception as e:
                                logger.warning(
                                    f"坐标转换包装器执行失败: {e}，使用原始处理函数"
                                )
                                obs_np, _, _, _, _ = (
                                    _process_expert_batch_for_imitation(
                                        batch_data, device, seq_length
                                    )
                                )
                            # 只保留observations用于autoencoder训练
                            self.observations.append(torch.from_numpy(obs_np).float())
                        except Exception as e:
                            logger.error(
                                f"Error processing batch for observations: {e}",
                                exc_info=True,
                            )
                            continue

                    # 合并所有观察数据
                    if self.observations:
                        self.observations = torch.cat(self.observations, dim=0)
                        logger.info(
                            f"Total observations for reward model training: {len(self.observations)}"
                        )
                    else:
                        raise ValueError("No observations extracted from expert data")

                def __len__(self):
                    return len(self.observations)

                def __getitem__(self, idx):
                    return self.observations[idx]

            # 创建观察数据集和DataLoader
            try:
                obs_dataset = ObservationDataset(
                    train_dataloader, device, cfg.env.seq_length
                )
                obs_dataloader = torch.utils.data.DataLoader(
                    obs_dataset,
                    batch_size=cfg.agent.reward_model.batch_size,
                    shuffle=True,
                    drop_last=False,
                )
                logger.info(
                    f"Created observation DataLoader with {len(obs_dataset)} samples"
                )
            except Exception as e:
                logger.error(
                    f"Failed to create observation dataset: {e}", exc_info=True
                )
                env.close()
                return

            # 使用ReconstructionRewardModel的train_reward_model方法
            try:
                logger.info(
                    f"Starting reward model training for {num_epochs} epochs..."
                )
                epoch_losses, final_loss = reward_model.train_reward_model(
                    expert_data_loader=obs_dataloader,
                    epochs=num_epochs,
                    learning_rate=cfg.agent.reward_model.model_kwargs.get(
                        "learning_rate", 0.001
                    ),
                )

                logger.info(
                    f"Reward model training completed. Final loss: {final_loss:.6f}"
                )

                # SwanLab记录（如果启用）
                if SWANLAB_INITIALIZED:
                    for epoch, loss in enumerate(epoch_losses):
                        swanlab.log(
                            {
                                "reward_model/train_loss": loss,
                                "reward_model/epoch": epoch,
                            },
                            step=epoch,
                        )

            except Exception as e:
                logger.error(f"Error during reward model training: {e}", exc_info=True)
                env.close()
                return

            # 保存最终模型
            final_model_path = os.path.join(output_dir, "final_reward_model.pth")
            try:
                reward_model.save(final_model_path)
                logger.info(f"Final reward model saved: {final_model_path}")
            except Exception as e:
                logger.error(f"Error saving final reward model: {e}", exc_info=True)

            # 如果指定了检查点保存间隔，也可以保存一个备份
            best_model_path = os.path.join(output_dir, "best_reward_model.pth")
            try:
                reward_model.save(best_model_path)
                logger.info(
                    f"Best reward model (same as final) saved: {best_model_path}"
                )
            except Exception as e:
                logger.error(f"Error saving best reward model: {e}", exc_info=True)

            logger.info("Reward model pretraining completed.")
            env.close()
            return

        elif cfg.training.training_phase == "joint_rl":
            logger.info("\033[91mStarting Joint RL Training Phase\033[0m")
            if cfg.training.training_mode == "bc":
                logger.error(
                    "Inconsistent arguments: training_phase='joint_rl' but training_mode='bc'"
                )
                env.close()
                return

            agent_class = HierarchicalHybridSAC
            current_env_for_agent = env

            policy_kwargs_for_sac = OmegaConf.to_container(
                cfg.agent.get("policy_kwargs", {}), resolve=True
            )
            policy_kwargs_for_sac.setdefault(
                "num_queries", cfg.env.seq_length
            )  # Ensure num_queries matches

            vec_normalize_load_path = None
            if cfg.training.get(
                "use_vec_normalize", False
            ):  # Assume a config flag for this
                if cfg.training.load_model:
                    vec_normalize_load_path = os.path.join(
                        os.path.dirname(cfg.training.load_model), "vec_normalize.pkl"
                    )
                    if os.path.exists(vec_normalize_load_path):
                        logger.info(
                            f"Loading VecNormalize state from {vec_normalize_load_path}"
                        )
                        current_env_for_agent = VecNormalize.load(
                            vec_normalize_load_path, current_env_for_agent
                        )
                    else:
                        logger.warning(
                            f"VecNormalize save file not found at {vec_normalize_load_path}, creating new."
                        )
                        current_env_for_agent = VecNormalize(
                            current_env_for_agent, gamma=cfg.agent.get("gamma", 0.99)
                        )
                else:
                    logger.info("Initializing new VecNormalize wrapper.")
                    current_env_for_agent = VecNormalize(
                        current_env_for_agent, gamma=cfg.agent.get("gamma", 0.99)
                    )
                logger.info("VecNormalize is active for the agent.")

            # --- Agent Instantiation or Loading --- #
            if cfg.training.load_model is not None:
                logger.info(f"Loading agent model from: {cfg.training.load_model}")
                agent = agent_class.load(
                    cfg.training.load_model,
                    env=current_env_for_agent,  # Pass the (potentially normalized) env
                    device=cfg.app.get("device", "auto"),
                )
                logger.info(
                    f"Agent model loaded. Using environment: {current_env_for_agent}"
                )
            else:
                logger.info("Creating new HierarchicalHybridSAC agent...")
                agent = agent_class(
                    policy=ExecutionRLPolicy,  # Pass the class, SB3 style
                    env=current_env_for_agent,
                    learning_rate=cfg.agent.learning_rate,  # Can be float or callable schedule
                    buffer_size=cfg.agent.buffer_size,
                    learning_starts=cfg.agent.learning_starts,
                    batch_size=cfg.training.batch_size,
                    tau=cfg.agent.tau,
                    gamma=cfg.agent.gamma,
                    train_freq=cfg.agent.get("train_freq", 1),  # Default if not in cfg
                    gradient_steps=cfg.agent.get(
                        "gradient_steps", 1
                    ),  # Default if not in cfg
                    # action_noise= # if applicable, from cfg
                    # replay_buffer_class= # if applicable, from cfg
                    # replay_buffer_kwargs= # if applicable, from cfg
                    optimize_memory_usage=cfg.agent.get("optimize_memory_usage", False),
                    ent_coef=cfg.agent.get("ent_coef", "auto"),
                    target_update_interval=cfg.agent.get("target_update_interval", 1),
                    target_entropy=cfg.agent.get("target_entropy", "auto"),
                    use_sde=cfg.agent.get("use_sde", False),
                    sde_sample_freq=cfg.agent.get("sde_sample_freq", -1),
                    sde_eta=cfg.agent.get("sde_eta", 0.0),
                    tensorboard_log=os.path.join(output_dir, "tensorboard_logs"),
                    policy_kwargs=policy_kwargs_for_sac,
                    verbose=cfg.agent.get("verbose", 1),
                    seed=actual_seed,
                    device=cfg.app.get("device", "auto"),
                    _init_setup_model=True,
                    # HierarchicalHybridSAC specific params from cfg.agent or cfg.training
                    expert_buffer_size=cfg.agent.get("expert_buffer_size", 100000),
                    expert_ratio=cfg.agent.get("expert_ratio", 0.1),
                    bc_weight=cfg.agent.get("bc_weight", 0.1),
                    train_mode=cfg.training.hierarchical_mode,  # e.g., joint, decision_only etc.
                    # decision_epochs & execution_epochs are for BC, not directly used by SAC agent constructor usually
                    seq_length=cfg.env.seq_length,  # Pass seq_length if HierarchicalHybridSAC uses it
                )
                logger.info("New agent created.")

                # Load Pretrained BC Execution Policy (if not loading a full agent)
                load_bc_path_key = "load_execution_bc_model_path"
                bc_policy_load_path = cfg.training.get(load_bc_path_key, None)
                if (
                    not bc_policy_load_path
                ):  # Default to checking in current output dir if not specified
                    bc_policy_load_path = os.path.join(
                        output_dir, "execution_bc_policy.pth"
                    )

                if os.path.exists(bc_policy_load_path):
                    logger.info(
                        f"Found pretrained BC execution policy at {bc_policy_load_path}. Loading weights into agent.policy.actor ..."
                    )
                    try:
                        bc_state_dict = torch.load(
                            bc_policy_load_path, map_location=device
                        )
                        if hasattr(bc_state_dict, "state_dict"):  # It's a policy object
                            bc_state_dict = bc_state_dict.state_dict()
                        elif (
                            "policy_state_dict" in bc_state_dict
                        ):  # It's a common wrapper from some save utilities
                            bc_state_dict = bc_state_dict["policy_state_dict"]
                        elif (
                            "model_state_dict" in bc_state_dict
                        ):  # Another common wrapper
                            bc_state_dict = bc_state_dict["model_state_dict"]

                        agent.policy.actor.load_state_dict(bc_state_dict, strict=False)
                        logger.info(
                            "Successfully loaded BC policy weights into agent.policy.actor (strict=False)."
                        )
                    except Exception as e:
                        logger.error(
                            f"Failed to load BC execution policy weights: {e}",
                            exc_info=True,
                        )
                        logger.warning(
                            "Proceeding with randomly initialized RL policy actor."
                        )
                else:
                    logger.warning(
                        f"No pretrained BC execution policy found at {bc_policy_load_path} (or not specified via {load_bc_path_key}). Starting RL training from scratch or with loaded agent."
                    )

            # Load Expert Data into Replay Buffer (if applicable for HierarchicalHybridSAC)
            if (
                isinstance(agent, HierarchicalHybridSAC)
                and cfg.agent.get("expert_ratio", 0) > 0
            ):
                logger.info("Loading expert data into replay buffer for RL phase...")
                try:
                    expert_data_load_batch_size = cfg.training.get(
                        "expert_data_load_batch_size", cfg.training.batch_size
                    )
                    # Re-use the common data loading logic if structure is compatible
                    expert_train_dataloader, _, _, _ = HierarchicalHybridSAC.load_data(
                        cfg.training.dataset_dir,
                        batch_size_train=expert_data_load_batch_size,
                        batch_size_val=0,  # No validation needed for buffer filling
                        seq_length=cfg.env.seq_length,
                    )
                    if expert_train_dataloader and hasattr(
                        agent, "load_expert_data_from_dataloader"
                    ):
                        added_count = agent.load_expert_data_from_dataloader(
                            expert_train_dataloader
                        )
                        logger.info(
                            f"Added {added_count} expert samples to the replay buffer."
                        )
                    elif not hasattr(agent, "load_expert_data_from_dataloader"):
                        logger.warning(
                            "Agent does not have 'load_expert_data_from_dataloader' method."
                        )
                    else:
                        logger.warning(
                            "Could not load expert dataloader for RL phase buffer filling."
                        )
                except Exception as e:
                    logger.error(
                        f"Error loading expert data into buffer for RL: {e}",
                        exc_info=True,
                    )

            # --- Evaluation Environment --- #
            logger.info("Creating evaluation environment...")
            eval_env_cfg = deepcopy(env_cfg)  # Use the fully configured env_cfg
            eval_env_cfg.scene.num_envs = 1  # Override for evaluation

            def make_eval_env():  # Closure to pass to DummyVecEnv
                eval_instance = gym.make(
                    cfg.env.name, cfg=eval_env_cfg, render_mode=None
                )
                return eval_instance

            eval_vec_env = DummyVecEnv([make_eval_env])
            if cfg.training.get("use_vec_normalize", False):
                eval_vec_env = VecNormalize(
                    eval_vec_env,
                    training=False,
                    norm_obs=True,
                    norm_reward=False,
                    gamma=cfg.agent.get("gamma", 0.99),
                )
                if isinstance(current_env_for_agent, VecNormalize):
                    # Copy running stats from the training VecNormalize instance
                    eval_vec_env.obs_rms = current_env_for_agent.obs_rms
                    eval_vec_env.ret_rms = (
                        current_env_for_agent.ret_rms
                    )  # Though norm_reward is False
                logger.info(
                    "Evaluation environment wrapped with VecNormalize (sync with training env stats)."
                )
            eval_vec_env = Sb3VecEnvWrapper(eval_vec_env)  # Wrap for SB3
            logger.info("Evaluation environment created and wrapped.")

            # --- Callbacks --- #
            logger.info("Setting up callbacks...")
            checkpoint_callback = CheckpointCallback(
                save_freq=max(
                    1, cfg.training.save_freq // current_env_for_agent.num_envs
                ),  # Adjust freq by num_envs
                save_path=output_dir,
                name_prefix="rl_model",
                save_replay_buffer=cfg.training.get(
                    "save_replay_buffer_on_checkpoint", False
                ),
                save_vecnormalize=cfg.training.get("use_vec_normalize", False),
                verbose=1,
            )
            eval_callback = EvalCallback(
                eval_vec_env,
                best_model_save_path=output_dir,
                log_path=output_dir,
                eval_freq=max(
                    1, cfg.training.eval_freq // current_env_for_agent.num_envs
                ),  # eval_freq from cfg
                n_eval_episodes=cfg.training.n_eval_episodes,
                deterministic=cfg.training.get("eval_deterministic", True),
                render=False,  # No rendering during eval callback
                callback_on_new_best=None,  # Or a custom callback
                warn=True,
            )
            callback_list = CallbackList([checkpoint_callback, eval_callback])

            # --- Train the Agent --- #
            logger.info(
                f"Starting RL training for {cfg.training.n_timesteps} timesteps..."
            )
            agent.learn(
                total_timesteps=cfg.training.n_timesteps,
                callback=callback_list,
                reset_num_timesteps=cfg.training.load_model
                is None,  # Reset if not resuming
                # tb_log_name will be set by agent using its tensorboard_log path
                log_interval=cfg.logging.get(
                    "bc_log_interval", 4
                ),  # How often to log stats (episodes for RL, batches for BC)
                # progress_bar=cfg.training.get("progress_bar", False) # SB3 SAC doesn't have progress_bar arg directly
            )

            # --- Save Final Model and Environment Stats --- #
            logger.info("RL training finished. Saving final model...")
            final_model_path = os.path.join(output_dir, "rl_model_final")
            agent.save(final_model_path)
            logger.info(f"Final agent model saved to {final_model_path}")
            if isinstance(current_env_for_agent, VecNormalize):
                vecnormalize_final_path = os.path.join(
                    output_dir, "vec_normalize_final.pkl"
                )
                current_env_for_agent.save(vecnormalize_final_path)
                logger.info(f"VecNormalize stats saved to {vecnormalize_final_path}")

            # 保存RL训练的配置
            try:
                # 🆕 保存完全解析的配置
                resolved_cfg = OmegaConf.to_container(
                    cfg, resolve=True, throw_on_missing=True
                )
                resolved_config_path = os.path.join(output_dir, "resolved_config.yaml")
                with open(resolved_config_path, "w") as f:
                    yaml.dump(resolved_cfg, f, default_flow_style=False, indent=2)
                logger.info(f"RL训练完全解析配置已保存到: {resolved_config_path}")

                # 同时保存原始配置
                original_config_path = os.path.join(output_dir, "original_config.yaml")
                OmegaConf.save(cfg, original_config_path)
                logger.info(f"RL训练原始配置已保存到: {original_config_path}")

                # 使用统一配置管理工具保存环境配置，使用解析后的数值
                save_evaluation_config(
                    env_cfg,
                    output_dir,
                    {
                        "source": "rl_training_script",
                        "env_name": env_name,
                        "seq_length": resolved_cfg["env"][
                            "seq_length"
                        ],  # 使用解析后的数值
                        "frames_to_stack": env_cfg.num_frames_to_stack,
                        "observation_space": env_cfg.observation_space,
                        "base_observation_space": env_cfg.base_observation_space,
                        "num_envs": env_cfg.scene.num_envs,
                        "timestamp": time.strftime("%Y-%m-%d_%H-%M-%S"),
                        # 🆕 添加更多关键参数
                        "policy_kwargs_num_queries": resolved_cfg.get("agent", {})
                        .get("policy_kwargs", {})
                        .get("num_queries", None),
                        "hierarchical_mode": resolved_cfg.get("training", {}).get(
                            "hierarchical_mode", None
                        ),
                    },
                )
            except Exception as config_save_error:
                logger.warning(f"保存RL配置信息失败: {config_save_error}")

            current_env_for_agent.close()  # Close train env
            eval_vec_env.close()  # Close eval env
            return  # End of RL phase

        # Fallback if training_phase is not handled by specific BC or RL blocks
        # This was the logger.info("Main function setup complete...") and the warning block.
        # If we reach here, it means the training phase wasn't one of the implemented ones.
        logger.warning(
            f"Training phase '{cfg.training.training_phase}' not fully implemented or unrecognized. Closing environment."
        )
        env.close()  # Ensure main env is closed if no phase ran to completion

    except Exception as e_main_try:
        logger.error(
            f"An unhandled exception occurred in the main try block: {e_main_try}",
            exc_info=True,
        )
        # Optionally re-raise, or just let finally execute.
    finally:
        # This block will execute regardless of how the try block exits (normal, return, or exception).
        if env is not None:  # Ensure env was actually created
            try:
                logger.info(
                    "Attempting to close main environment in `finally` block..."
                )
                env.close()  # env might be the original gym.make or Sb3VecEnvWrapper
                logger.info(
                    "Main environment close attempt finished in `finally` block."
                )
            except Exception as e_env_close_finally:
                logger.error(
                    f"Error closing main environment in `finally` block: {e_env_close_finally}",
                    exc_info=True,
                )

        if SWANLAB_INITIALIZED:
            logger.info("Finishing SwanLab run in `finally` block...")
            try:
                swanlab.finish()
                logger.info("SwanLab run finished successfully in `finally` block.")
            except Exception as e_swanlab_finish:
                logger.error(
                    f"Error finishing SwanLab run in `finally` block: {e_swanlab_finish}",
                    exc_info=True,
                )


if __name__ == "__main__":
    try:
        main()  # Call the Hydra-decorated main function
    finally:
        if simulation_app is not None:
            logger.info("Closing simulation_app in __main__ finally block...")
            simulation_app.close()
            logger.info("simulation_app closed.")
