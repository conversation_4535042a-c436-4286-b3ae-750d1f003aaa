#!/usr/bin/env python3
"""
🚀 PX Tactrix 模块化训练系统 - 主入口文件

这是一个完全重构的训练脚本，将原来1862行的单体文件拆分为清晰的模块化架构。
支持多种训练模式：BC训练、RL训练、奖励模型预训练等。

## 🎯 支持的训练阶段

### 1. 行为克隆 (Behavioral Cloning)
- **execution_bc_train**: 执行层BC训练
- **execution_bc_actor_train**: 执行层Actor策略训练
- **decision_bc_train**: 决策层BC训练

### 2. 奖励模型预训练
- **reward_model_pretrain**: 奖励模型预训练

### 3. 强化学习
- **joint_rl**: 层次化RL训练

## 📋 快速开始

### 基础用法
```bash
# BC训练
python train_model.py training.training_phase=execution_bc_train

# RL训练
python train_model.py training.training_phase=joint_rl

# 奖励模型预训练
python train_model.py training.training_phase=reward_model_pretrain
```

### 高级用法
```bash
# 指定设备和批次大小
python train_model.py training.training_phase=execution_bc_train \
    app.device=cuda:0 training.batch_size=64

# 启用视频录制
python train_model.py training.training_phase=joint_rl \
    video.record=true video.interval=1000

# 自定义输出目录
python train_model.py training.training_phase=execution_bc_train \
    hydra.run.dir=./custom_output
```

## 🔧 配置参数

### 训练配置
- `training.training_phase`: 训练阶段
- `training.batch_size`: 批次大小
- `training.execution_epochs`: BC训练轮数
- `training.n_timesteps`: RL训练步数

### 环境配置
- `env.name`: 环境名称
- `env.seq_length`: 序列长度
- `app.device`: 训练设备

### 日志配置
- `logging.use_swanlab`: 是否使用SwanLab
- `logging.bc_log_interval`: BC训练日志间隔

## 🏗️ 架构概览

```
train_model.py (主入口，85行)
├── train/cli/main.py (CLI处理)
├── train/config/manager.py (配置管理)
├── train/data/loader.py (数据加载)
├── train/core/
│   ├── environment.py (环境管理)
│   └── trainers/ (训练器模块)
│       ├── bc_trainer.py (BC训练器)
│       ├── rl_trainer.py (RL训练器)
│       ├── reward_model_trainer.py (奖励模型训练器)
│       └── factory.py (训练器工厂)
├── train/utils/ (工具函数)
└── train/integrations/ (第三方集成)
```

## 📊 相比原脚本的改进

- **代码量减少**: 从1862行减少到85行 (95%减少)
- **模块化设计**: 清晰的职责分离
- **可维护性**: 更易于理解和修改
- **可扩展性**: 轻松添加新的训练器
- **错误处理**: 统一的异常处理和资源清理

## 🐛 故障排除

### 常见问题
1. **ImportError**: 确保所有依赖已安装
2. **CUDA错误**: 检查GPU设备配置
3. **数据加载失败**: 验证数据集路径和格式

### 调试模式
```bash
python train_model.py --debug training.training_phase=execution_bc_train
```

## 📝 更多信息

- 配置文件: `conf/config.yaml`
- 环境注册: `env_registry.py`
- 日志输出: `outputs/YYYY-MM-DD/HH-MM-SS/`

---
由 PX Tactrix 团队开发 | 基于 Isaac Lab 和 Stable Baselines3
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def main():
    """主入口函数"""
    try:
        from train.cli.main import cli_main

        cli_main()
    except KeyboardInterrupt:
        print("\n⚠️  训练被用户中断")
        sys.exit(1)
    except Exception as e:
        print("=" * 80)
        print("🚨 训练脚本执行失败:")
        print("=" * 80)
        print(f"异常类型: {type(e).__name__}")
        print(f"异常信息: {str(e)}")
        print("=" * 80)
        print("详细堆栈跟踪:")
        import traceback

        traceback.print_exc()
        print("=" * 80)
        sys.exit(1)


if __name__ == "__main__":
    main()

"""
🚀 训练脚本使用指南：

基本训练模式：
===============

# 1. 执行层BC训练（推荐起点）
python train_model.py training.training_phase=execution_bc_train \\
    training.dataset_dir=/path/to/expert/data \\
    training.execution_epochs=60

# 2. 决策层BC训练
python train_model.py training.training_phase=decision_bc_train \\
    training.dataset_dir=/path/to/expert/data \\
    training.decision_epochs=30

# 3. 联合RL训练
python train_model.py training.training_phase=joint_rl \\
    training.n_timesteps=1000000 \\
    training.load_execution_bc_model_path=/path/to/bc/model.pth

# 4. 奖励模型预训练
python train_model.py training.training_phase=reward_model_pretrain \\
    agent.reward_model.epochs=100

高级选项：
==========

# 启用SwanLab实验跟踪
python train_model.py logging.use_swanlab=true \\
    logging.swanlab_project_name=my_project

# 单手训练模式
python train_model.py \\
    agent.execution_policy.policy_kwargs.enable_single_hand=true \\
    env.name=Isaac-SingleHand-DexH13-Direct-v0

# 调试模式
python train_model.py --debug

# 视频录制
python train_model.py video.record=true video.interval=1000

配置文件：
==========
所有配置都在 conf/config.yaml 中定义
支持命令行覆盖任意配置参数

输出结果：
==========
- 训练好的模型文件 (.pth)
- 完整的配置文件备份
- 训练日志和指标
- SwanLab实验记录（如果启用）
- 检查点文件（支持恢复训练）
"""
