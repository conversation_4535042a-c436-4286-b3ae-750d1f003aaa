#!/usr/bin/env python3
"""
快速查看 HDF5 文件结构的工具脚本
"""

import h5py
import sys
from pathlib import Path


def inspect_h5_structure(file_path):
    """递归打印 HDF5 文件的完整结构"""
    print(f"\n📁 文件: {file_path}")
    print("=" * 60)
    
    with h5py.File(file_path, "r") as f:
        def print_structure(name, obj):
            indent = "  " * name.count("/")
            if isinstance(obj, h5py.Dataset):
                shape = obj.shape
                dtype = obj.dtype
                print(f"{indent}📊 {name} → shape: {shape}, dtype: {dtype}")
                
                # 如果是可能的 9 维数据，标记出来
                if len(shape) >= 2 and shape[-1] == 9:
                    print(f"{indent}   ⭐ 可能的 9 维位姿数据!")
                    
            elif isinstance(obj, h5py.Group):
                print(f"{indent}📂 {name}/")
        
        print("📂 /")
        f.visititems(print_structure)


def suggest_paths(file_path):
    """建议可能的数据集路径"""
    print(f"\n🔍 分析文件: {file_path}")
    print("=" * 60)
    
    obj_candidates = []
    handpose_candidates = []
    
    with h5py.File(file_path, "r") as f:
        def analyze_dataset(name, obj):
            if isinstance(obj, h5py.Dataset):
                shape = obj.shape
                if len(shape) >= 2 and shape[-1] == 9:
                    if "obj" in name.lower() or "object" in name.lower():
                        obj_candidates.append(name)
                    elif "hand" in name.lower() or "pose" in name.lower():
                        handpose_candidates.append(name)
        
        f.visititems(analyze_dataset)
    
    print("🎯 建议的命令行参数:")
    if obj_candidates:
        print(f"   --obj_path {obj_candidates[0]}")
    if handpose_candidates:
        for hp in handpose_candidates:
            print(f"   --handpose {hp}")
    
    if obj_candidates or handpose_candidates:
        print("\n💡 完整命令示例:")
        cmd = "python pre_process_script.py ./data/test/ ./data/obj_trans --verify --verbose"
        if obj_candidates:
            cmd += f" --obj_path {obj_candidates[0]}"
        for hp in handpose_candidates:
            cmd += f" --handpose {hp}"
        print(f"   {cmd}")
    else:
        print("❌ 未找到明显的 9 维位姿数据集")


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python inspect_h5.py <h5文件路径>")
        sys.exit(1)
    
    file_path = Path(sys.argv[1])
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        sys.exit(1)
    
    inspect_h5_structure(file_path)
    suggest_paths(file_path) 