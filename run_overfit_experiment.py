#!/usr/bin/env python3
"""
过拟合实验运行脚本
使用优化配置训练模型以实现对167个样本的完全过拟合
"""

import subprocess
import sys
import time
import os
from pathlib import Path
import json
import yaml
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="[%(asctime)s] %(levelname)s: %(message)s"
)
logger = logging.getLogger(__name__)


def check_data_exists(data_path: str) -> bool:
    """检查数据是否存在"""
    path = Path(data_path)
    if not path.exists():
        logger.error(f"数据路径不存在: {data_path}")
        return False

    # 检查是否有数据文件
    data_files = (
        list(path.glob("*.hdf5")) + list(path.glob("*.h5")) + list(path.glob("*.pkl"))
    )
    if not data_files:
        logger.error(f"在 {data_path} 中未找到数据文件 (.hdf5, .h5, .pkl)")
        return False

    logger.info(f"✅ 找到 {len(data_files)} 个数据文件")
    return True


def run_overfitting_experiment(
    config_name: str = "config_overfit", data_path: str = "./simulated_data"
):
    """运行过拟合实验"""

    logger.info("🚀 开始过拟合实验")
    logger.info(f"   配置文件: {config_name}")
    logger.info(f"   数据路径: {data_path}")

    # 检查数据
    if not check_data_exists(data_path):
        logger.error("数据检查失败，请确保数据路径正确且包含有效数据文件")
        return False

    # 检查配置文件
    config_path = f"conf/{config_name}.yaml"
    if not Path(config_path).exists():
        logger.error(f"配置文件不存在: {config_path}")
        logger.info("请确保 config_overfit.yaml 文件已创建")
        return False

    # 构建训练命令 - 使用模块方式运行以解决相对导入问题
    cmd = [
        sys.executable,
        "-m",
        "train.cli.main",  # 使用模块方式运行
        f"--config-name={config_name}",
        f"training.dataset_dir={data_path}",
        "training.training_phase=execution_bc_train",
        "training.hierarchical_mode=execution_only",
        # 注意: 不使用 --multirun 标志即表示单次运行
    ]

    logger.info("🔧 训练命令:")
    logger.info(" ".join(cmd))

    # 创建输出目录
    output_dir = Path("./overfit_experiment_results")
    output_dir.mkdir(exist_ok=True)

    # 启动训练
    try:
        logger.info("📚 开始训练...")
        start_time = time.time()

        # 运行训练
        result = subprocess.run(
            cmd,
            cwd=".",
            capture_output=False,  # 让输出直接显示
            text=True,
            check=False,  # 不自动抛出异常，手动检查返回码
        )

        end_time = time.time()
        training_time = end_time - start_time

        if result.returncode == 0:
            logger.info(f"✅ 训练完成! 用时: {training_time:.1f}秒")

            # 尝试找到并显示最终的训练结果
            try:
                # 查找最新的输出目录
                outputs_dir = Path("outputs")
                if outputs_dir.exists():
                    # 找到最新的运行目录
                    run_dirs = [
                        d
                        for d in outputs_dir.rglob("*")
                        if d.is_dir() and "execution_bc" in d.name
                    ]
                    if run_dirs:
                        latest_run = max(run_dirs, key=lambda x: x.stat().st_mtime)
                        logger.info(f"📁 训练输出保存在: {latest_run}")

                        # 检查是否有训练日志
                        log_files = list(latest_run.glob("*.log")) + list(
                            latest_run.glob("**/*.log")
                        )
                        if log_files:
                            logger.info(f"📊 训练日志: {log_files[0]}")

                        # 检查是否有模型文件
                        model_files = list(latest_run.glob("*.pth")) + list(
                            latest_run.glob("**/*.pth")
                        )
                        if model_files:
                            logger.info(f"💾 模型文件: {model_files[0]}")

            except Exception as e:
                logger.warning(f"查找输出文件时出错: {e}")

            return True

        else:
            logger.error(f"❌ 训练失败! 返回码: {result.returncode}")
            logger.error("请检查上面的错误信息")
            return False

    except KeyboardInterrupt:
        logger.warning("⚠️ 训练被用户中断")
        return False
    except Exception as e:
        logger.error(f"❌ 训练过程中出现异常: {e}")
        return False


def analyze_training_results(output_dir: str = None):
    """分析训练结果"""
    logger.info("📊 分析训练结果...")

    if output_dir is None:
        # 自动查找最新的输出目录
        outputs_dir = Path("outputs")
        if not outputs_dir.exists():
            logger.error("未找到输出目录")
            return

        run_dirs = [
            d
            for d in outputs_dir.rglob("*")
            if d.is_dir()
            and any(
                keyword in d.name.lower() for keyword in ["execution_bc", "overfit"]
            )
        ]
        if not run_dirs:
            logger.error("未找到相关的训练输出目录")
            return

        output_dir = max(run_dirs, key=lambda x: x.stat().st_mtime)

    output_path = Path(output_dir)
    logger.info(f"分析目录: {output_path}")

    # 查找训练指标文件
    metrics_files = list(output_path.glob("**/metrics*.json"))
    log_files = list(output_path.glob("**/*.log"))

    if metrics_files:
        logger.info(f"📈 找到指标文件: {metrics_files[0]}")
        try:
            with open(metrics_files[0], "r") as f:
                metrics = json.load(f)

            # 简单分析
            if "train_loss" in metrics:
                logger.info(f"   最终训练损失: {metrics['train_loss']:.6f}")
            if "epoch" in metrics:
                logger.info(f"   训练轮数: {metrics['epoch']}")

        except Exception as e:
            logger.warning(f"读取指标文件时出错: {e}")

    # 提供下一步建议
    logger.info("\n🎯 下一步建议:")
    logger.info("1. 检查训练损失是否降到接近0 (< 1e-3)")
    logger.info("2. 如果损失仍然较高，尝试:")
    logger.info("   - 降低学习率: 1e-4 → 1e-5")
    logger.info("   - 增加训练轮数: 500 → 1000")
    logger.info("   - 进一步简化模型架构")
    logger.info("3. 如果训练不稳定，添加梯度裁剪")
    logger.info("4. 使用 debug_overfitting.py 进行详细诊断")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="过拟合实验运行器")
    parser.add_argument(
        "--config",
        type=str,
        default="config_overfit",
        help="配置文件名 (不含.yaml扩展名)",
    )
    parser.add_argument(
        "--data-path", type=str, default="./simulated_data", help="数据路径"
    )
    parser.add_argument(
        "--analyze-only", action="store_true", help="仅分析已有结果，不运行训练"
    )
    parser.add_argument("--output-dir", type=str, help="指定要分析的输出目录")

    args = parser.parse_args()

    if args.analyze_only:
        analyze_training_results(args.output_dir)
        return

    # 显示实验说明
    print("=" * 60)
    print("🎯 过拟合实验指南")
    print("=" * 60)
    print("目标: 使模型完全记住167个训练样本，损失降到接近0")
    print("")
    print("🔧 优化策略:")
    print("• 降低学习率到1e-4，避免梯度爆炸")
    print("• 简化模型架构，降低复杂度")
    print("• 关闭所有正则化 (dropout=0, weight_decay=0)")
    print("• 增加训练轮数到500")
    print("• 使用小批次大小(16)增加更新频率")
    print("")
    print("📊 成功标准:")
    print("• 训练损失 < 1e-3")
    print("• 损失曲线单调下降")
    print("• 梯度范数稳定")
    print("")
    print("⏱️  预计时间: 10-30分钟 (取决于硬件)")
    print("=" * 60)
    print("")

    # 询问是否继续
    try:
        user_input = input("是否开始实验? [y/N]: ").strip().lower()
        if user_input not in ["y", "yes"]:
            print("实验取消")
            return
    except KeyboardInterrupt:
        print("\n实验取消")
        return

    # 运行实验
    success = run_overfitting_experiment(args.config, args.data_path)

    if success:
        print("\n" + "=" * 60)
        print("🎉 实验完成!")
        print("=" * 60)

        # 自动分析结果
        analyze_training_results()

        print("\n📝 接下来可以:")
        print("1. 运行 python debug_overfitting.py 进行详细诊断")
        print("2. 查看输出目录中的模型文件和日志")
        print("3. 如果结果不理想，调整配置后重新运行")
    else:
        print("\n" + "=" * 60)
        print("❌ 实验失败")
        print("=" * 60)
        print("请检查错误信息并:")
        print("1. 确认数据路径正确")
        print("2. 检查配置文件语法")
        print("3. 确认环境依赖完整")


if __name__ == "__main__":
    main()
