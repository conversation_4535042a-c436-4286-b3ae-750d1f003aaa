#!/usr/bin/env python3
"""
单手控制模型评估脚本 - 模块化版本
基于eval_model_vis_bakup.py重构
"""

import argparse
import os
import json
import time
from typing import Dict
import numpy as np
import torch
from pathlib import Path
from prettytable import PrettyTable
from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
    HierarchicalHybridSAC,
)


# 终端颜色常量 - 已移至 eval.utils.colors
from eval.utils.colors import Colors


# Isaac Lab相关导入
from isaaclab.app import AppLauncher


# 数据集采样器 - 已移至 eval.data.dataset_sampler
from eval.data.dataset_sampler import DatasetSampler


# 调度器和编码器 - 已移至 eval.utils
from eval.utils.schedule_utils import ConstantSchedulePicklable
from eval.utils.json_encoder import NumpyEncoder

from eval.cli.argument_parser import ArgumentParser_vis
from px_janus_learnsim.utils.math import rot6d_to_matrix_gram_schmidt, matrix_to_rot6d
from eval.core.single_hand_evaluator import SingleHandModelEvaluator

if __name__ == "__main__":
    # 解析命令行参数
    parser = ArgumentParser_vis()
    args = parser.parse_args()

    print("🤖 单手DexH13控制模型评估器")
    print("=" * 50)
    print(f"📁 模型路径: {args.model_path}")
    print(f"🤚 手部类型: {args.hand_type}")
    print(f"📊 评估episodes: {args.episodes}")
    print(f"🎯 位置阈值: {args.position_tolerance}m")
    print(f"🔧 关节阈值: {args.joint_tolerance}rad")
    print(f"🌐 物体中心坐标系: {'启用' if args.use_object_centric else '禁用'}")
    print(f"🎬 动作选择策略: {args.action_selection_strategy}")
    print(f"🔄 动作缩放: {'启用' if args.enable_action_scaling else '禁用'}")
    if getattr(args, "enable_debug_table", False):
        interval = getattr(args, "debug_table_interval", 50)
        if interval > 0:
            print(f"🔍 详细调试表格: 启用 (每{interval}步输出一次)")
        else:
            print("🔍 详细调试表格: 禁用")
    print("=" * 50)

    # 启动Isaac Lab应用
    app_launcher = AppLauncher(args)
    simulation_app = app_launcher.app

    try:
        # 现在可以导入Isaac Lab相关模块

        # 导入单手环境

        # 导入模型相关类
        try:
            from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
                ExecutionBCActorPolicy,
            )

            print("✅ 成功导入模型类")
        except ImportError as e:
            print(f"⚠️ 警告：无法导入模型类: {e}")
            print("将使用随机动作进行基准测试")

        # 创建评估器并运行评估
        evaluator = SingleHandModelEvaluator(args)
        evaluator.run_evaluation()

    except KeyboardInterrupt:
        print("\n⚠️ 评估被用户中断")
    except Exception as e:
        print(f"❌ 评估过程中发生错误: {e}")
        import traceback

        traceback.print_exc()
    finally:
        # 清理资源
        if "evaluator" in locals() and hasattr(evaluator, "env"):
            print("🔄 关闭环境...")
            evaluator.env.close()
        print("🔄 关闭仿真应用...")
        simulation_app.close()