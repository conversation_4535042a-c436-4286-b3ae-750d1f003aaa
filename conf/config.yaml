# @package _global_

# Ensure 'defaults' section exists or add it
defaults:
- _self_ # Important: keeps other defaults if any
# Optional: disable Hydra's own log files if SwanLab handles all logging
# - override hydra/job_logging: disabled
# - override hydra/hydra_logging: disabled

# Example for structured configs (optional, can be added later):
# - agent: default_agent
# - env: default_env
# - training: default_training

# Model type for directory organization
model_type: ${get_model_type:${training.training_phase}}

# 训练阶段环境需求映射
training_phases:
  environment_requirements:
    # BC训练阶段 - 不需要环境
    execution_bc_train: false
    execution_bc_actor_train: false
    execution_bc_critic_train: false
    decision_bc_train: false
    reward_model_pretrain: false

    # RL训练阶段 - 需要环境
    execution_rl_train: true
    execution_joint_rl: true
    decision_rl_train: true
    joint_rl: true

    # 评估阶段 - 需要环境
    evaluation: true

app:
  # New app group
  headless: true # Default for headless
  device: "cuda" # MODIFIED: from "auto" to "cuda"
  # enable_cameras: true # Optional: default for AppLauncher arg if needed

  # General project settings
  seed: 42 # Default seed, can be overridden by --seed if that logic is kept or by hydra override
  task_name: "Grasp" # Corresponds to --task
  # device: "auto" # REMOVED - General device setting (moved to app group)

  # Logging and Hydra output settings
  # Hydra manages the output directory automatically.
  # The structure will be outputs/YYYY-MM-DD/HH-MM-SS/
  # We can specify a different base via hydra.run.dir or hydra.sweep.dir
logging_level: "INFO" # For the custom logger in the script

# Environment specific parameters (corresponds to env_cfg and some args_cli)
env:
  # name: "Isaac-DexH13-Direct-v0" # 统一使用DexH13环境
  name: "Isaac-SingleHand-DexH13-Direct-v0" # 单手DexH13环境
  num_envs: 4 # Default number of parallel environments
  seq_length: 15 # 匹配专家数据的实际时间序列长度
  action_type: "abs"
  obs_type: "state"
  image_height: 240 # Only relevant if obs_type includes 'rgb' or 'depth'
  image_width: 320 # Only relevant if obs_type includes 'rgb' or 'depth'
  camera_names: [ "overhead_camera", "wrist_camera" ]
  depth_scale: 1000.0
  # Agent and Policy parameters (corresponds to agent_cfg and parts of args_cli)
agent:
  # Common RL/SAC parameters (from agent_cfg)
  buffer_size: 1_000_000
  learning_starts: 10000
  batch_size: 256
  tau: 0.005
  gamma: 0.99
  train_freq: 1 # For SB3, often (1, "step") or (N, "episode")
  gradient_steps: 1 # How many gradient steps to do after N rollouts
  learning_rate: 1e-4
  name: "HierarchicalAgent"

  # BC / Hybrid training specific parameters (from agent_cfg)
  expert_buffer_size: 200000
  expert_ratio: 0.05 # Percentage of expert data in RL batches
  bc_weight: 0.05 # Weight for BC auxiliary loss in RL
  bc_weight_schedule: "constant" # Corresponds to --bc_weight_schedule似乎这个图片显示，时间聚合的统计误差很大，我在想会不会是图片的统计数值变量没有正确使用？或者其它类似的问题
  bc_weight_final: 0.1 # Corresponds to --bc_weight_final

  # Specific policy configurations
  decision_policy:
    # Allows overrides for decision layer, e.g., learning_rate, specific policy_kwargs
    learning_rate: 0.0001
    num_candidates: 10 # Number of candidate poses for decision layer
    policy_kwargs: {}
      # Overrides/extends agent.policy_kwargs for decision policy
      # e.g., different net_arch or activation_fn

  execution_policy:
    # Allows overrides for execution layer, e.g., learning_rate, specific policy_kwargs
    learning_rate: 1e-5 # 进一步降低学习率以适应小数据集
    policy_kwargs:
      # 🎯 ExecutionACTPolicyModel的Transformer架构参数 (ACT模型核心)
      hidden_dim: 512
      dim_feedforward: 2048
      enc_layers: 4
      dec_layers: 7
      nheads: 8
      num_queries: ${env.seq_length} # chunk_size, must match env.seq_length
      dropout: 0.3 # 进一步增加dropout从0.2到0.3以强力防止过拟合
      kl_weight: 0.000 # BC mode: set to 0

      # 🔄 ExecutionFusionModule的CrossModalAttention参数 (跨模态注意力)
      enable_cross_modal: false # 是否启用跨模态注意力 (默认关闭，向后兼容)
      cross_modal_heads: 8 # 跨模态注意力头数
      cross_modal_dropout: 0.1 # 跨模态注意力dropout率
      conditional_pose_dim: 25 # 新增：启用条件目标姿势支持 (25维单手姿势)
      tactile_shape: [ 1140, 3 ] # 修复：使用实际触觉数据维度 (1140个传感器点，每个3维力向量)
      object_dim: 9
      hand_joint_dim: 16
      hand_pose_dim: 9
      #: 单手训练模式配置
      enable_single_hand: true # 启用单手训练模式
      single_hand_config:
        target_hand: "right" # 指定训练的手 ("left", "right", "both")
        segment_length: ${env.seq_length} # 轨迹片段长度，必须与env.seq_length一致
        mix_hands: true # 是否混合左右手数据（推荐为true以增加数据量）
        include_tactile: false # 是否包含触觉数据（预留功能）
        gradient_clip_norm: 0.3 # 进一步降低梯度裁剪范数以严格控制梯度爆炸
        weight_decay: 1e-4 # 添加权重衰减以防过拟合

      #: 物体中心坐标系配置
      use_object_centric: false # 是否启用物体中心坐标系学习
      enable_pose_randomization: false # 是否启用物体位置随机化（数据增强）
      randomization_range: 0.5 # 物体位置随机化范围（米）

      #: 动作缩放和数据归一化配置
      enable_action_scaling: false # 是否启用动作缩放
      obs_normalization: "none" # 观察归一化方式: "standard", "minmax", "none"
      action_scaling: "none" # 动作缩放方式: "standard", "minmax", "tanh", "none"
      target_pose_normalization: "none" # 目标位姿归一化方式: "standard", "minmax", "none"
      action_clip_range: [ -1, 1.0 ] # 动作裁剪范围
      scaling_stats_path: null # 统计信息路径（可选，默认自动保存到模型目录下的scaling_stats.pkl）
      force_regenerate_stats: false # 强制重新生成统计信息（跳过旧文件加载）

  # Reward Model configuration for Task 20
  reward_model:
    type: "reconstruction" # Type of reward model to use
    batch_size: 64 # Batch size for reward model training
    epochs: 50 # Number of epochs for reward model training
    model_kwargs:
      encoder_dim: 256 # Encoder dimension
      latent_dim: 128 # Latent dimension
      learning_rate: 0.001 # Learning rate for reward model
      weight_decay: 0.0001 # Weight decay for regularization
    save_interval: 10 # Save model every N epochs
    checkpoint_path: null # Path to load pretrained reward model (for Task 17)

  # Policy_kwargs specific to the policy architecture
  policy_kwargs:
    # Shared Encoders / Fusion Modules (example, adjust as per actual model structure)
    base_hidden_dim: 512
    exec_fused_dim: 256 # Used by ExecutionFusionModule and as feature_dim for critic
    decision_state_dim: 256 # Used by DecisionFusionModule

    # Tactile and Object specific (example, adjust as per actual model structure)
    tactile_shape: [ 1140, 3 ] # 修复：使用实际触觉数据维度 (1140个传感器点，每个3维力向量)
    enable_tactile: false # 新增：禁用触觉输入避免维度不匹配问题
    num_objects: 5 # Example

    # Note: Transformer specific parameters are now managed in execution_policy.policy_kwargs

    # Decision Model specific (within DecisionBCScoringPolicy or DecisionRLPolicy)
    decision_pose_dim: 18
    decision_attn_heads: 8
    decision_mlp_hidden: 512
    dropout: 0.1
    # Other policy specific flags if any
    # e.g., use_sde, target_entropy (for SAC), temporal_agg from original agent_cfg

    # Training control parameters
    net_arch: [ 512, 512, 256 ] # Example architecture
    # features_extractor_class: null # If using custom features extractor
    # features_extractor_kwargs: {}
    # activation_fn: torch.nn.ReLU
    # optimizer_class: torch.optim.Adam
    # num_queries: 3 # Should match env.seq_length or how data is processed
    # tactile_shape: [12, 120, 3] # Example tactile shape, update if different

training:
  # training_mode: "hybrid" # Replaces --training_mode, e.g., 'bc', 'hybrid_sac_bc'
  training_phase: "execution_bc_train" # Replaces --training_phase
  # Available training phases:
  # Execution Layer Training:
  # - "execution_bc_actor_train": Train execution layer BC actor
  # - "execution_bc_critic_train": Train execution layer BC critic (using reward model)
  # - "execution_joint_rl": Joint RL training for execution layer
  # Decision Layer Training:
  # - "decision_bc_train": Train decision layer with BC
  # - "decision_rl_train": Train decision layer with RL
  # Reward Model Training:
  # - "reward_model_pretrain": Pre-train self-supervised reward model (Task 20)
  # Full System Training:
  # - "joint_rl": Joint RL training for full hierarchical system
  # Legacy (for backward compatibility):
  # - "execution_bc_train": Train execution layer with BC (equivalent to execution_bc_actor_train)
  hierarchical_mode: "joint" # Replaces --hierarchical_mode, e.g., 'joint', 'decision_only', 'execution_only', 'sequential'
  n_timesteps: 1_000_000 # Corresponds to --n_timesteps
  decision_epochs: 1000 # Corresponds to --decision_epochs (used in sequential or specific BC phase)
  execution_epochs: 10 # 大幅降低从50到20，防止过拟合
  batch_size: 512 # 进一步降低batch_size以节省内存并增加随机性
  load_model: null # Corresponds to --load_model, path to model or null
  dataset_dir: "./simulated_data" # Corresponds to --dataset_dir
  save_freq: 20000 # Corresponds to --save_freq
  eval_freq: 50000 # Timesteps, for RL eval callback
  n_eval_episodes: 10
  # ➕ Checkpoint settings for BC training
  checkpoint:
    save_epoch_freq: 50 # 每多少个epoch保存一次 (0 表示关闭)
    save_step_freq: 0 # 每多少个batch保存一次 (0 表示关闭)
    max_to_keep: 5 # 最多保留最近的N个checkpoint
  pretrained_execution_actor_path: null # Path to a pretrained execution actor for decision layer training or RL fine-tuning
  pretrained_decision_model_path: null # Path to a pretrained decision model for RL fine-tuning or evaluation

# Video recording settings (corresponds to --video, --video_length, --video_interval)
video:
  record: false
  length: 200
  interval: 100000

# AppLauncher specific args (already handled by AppLauncher.add_app_launcher_args)
# We might not need to duplicate these in hydra config if AppLauncher handles them separately
# before hydra takes over.
# For example:
# app_launcher: # This can be removed or kept commented if not used as 'app'
#   headless: true
#   multi_gpu: false
#   etc.

# Logging configuration
logging:
  use_swanlab: true # Enable/disable SwanLab integration
  swanlab_project_name: "px_tactrix_project"
  swanlab_workspace: "paxiniadmin" # Optional, defaults to your SwanLab default workspace
  # swanlab_entity: "your_entity_name" # Optional, if using a specific entity/team
  current_run_id: ${generate_run_name:} # Stores the generated run ID for access in script
  # experiment_name: "px_execution_bc_detailed" # This will now be overridden by current_run_id
  experiment_description: "Behavioral Cloning training run for px_tactrix execution layer."
  bc_log_interval: 1 # 训练日志打印间隔（BC按batch计算，RL按episode计算）
  # log_level: "INFO" # For python logging module (DEBUG, INFO, WARNING, ERROR)

  # Default AppLauncher arguments (can be overridden from CLI)
hydra:
  run:
    # Set the main output directory for this run.
    # It will be 'outputs/[model_type]/YYYY-MM-DD/HH-MM-SS/generated_run_name'.
    dir: outputs/${model_type}/${now:%Y-%m-%d}/${now:%H-%M-%S}/${generate_run_name:}
  job:
    # Job name, can also be set to the generated name for consistency.
    name: ${generate_run_name:}
    config:
      override_dirname:
        # Exclude all keys from being appended to the directory name.
        exclude_keys: []
  job_logging:
    formatters:
      colorlog:
        # '()' specifies the class to instantiate for this formatter.
        # Requires 'colorlog' package to be installed (pip install colorlog).
        (): colorlog.ColoredFormatter
        fmt: '[%(cyan)s%(asctime)s%(reset)s][%(blue)s%(name)s%(reset)s][%(log_color)s%(levelname)-8s%(reset)s] %(white)s%(message)s%(reset)s'
        datefmt: '%H:%M:%S'
        log_colors:
          DEBUG: "cyan"
          INFO: "green"
          WARNING: "yellow"
          ERROR: "red"
          CRITICAL: "red,bg_white"
    handlers:
      console:
        class: logging.StreamHandler # Specify the handler class
        formatter: colorlog # Use the 'colorlog' formatter defined above
        level: INFO # Set the level for this specific handler
    root:
      handlers: [ console ] # Attach the 'console' handler to the root logger
      level: INFO # Set the root logger level
    disable_existing_loggers: false # Recommended default
  # sweep:
  #   dir: multirun/${now:%Y-%m-%d_%H-%M-%S}
  #   subdir: ${hydra.job.num}
