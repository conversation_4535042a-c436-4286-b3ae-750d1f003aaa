# 专门用于过拟合小数据集的配置
# 基于 config.yaml，调整关键参数以实现过拟合

defaults:
- config # 继承基础配置
- _self_ # 覆盖设置

# 环境配置保持不变
env:
  seq_length: 15 # 保持与数据一致

# 🎯 关键：专门为过拟合优化的智能体配置
agent:
  execution_policy:
    # 🔥 学习率：从1e-2大幅降低到1e-4，防止梯度爆炸
    learning_rate: 3e-5

    # 🔥 使用常数学习率调度器（等效于固定学习率）
    lr_schedule:
      type: constant
      value: 1e-4

    policy_kwargs:
      # 🎯 Transformer架构：适度简化以利于小数据集过拟合
      hidden_dim: 256 # 从512降低到256
      dim_feedforward: 1024 # 从2048降低到1024
      enc_layers: 3 # 从4降低到3
      dec_layers: 5 # 从7降低到5
      nheads: 8 # 保持8
      num_queries: ${env.seq_length}

      # 🔥 完全关闭所有正则化
      dropout: 0.0 # 确保为0
      kl_weight: 0.0 # 确保为0

      # 🔥 关闭跨模态注意力（减少复杂度）
      enable_cross_modal: false
      cross_modal_dropout: 0.0

      # 🔥 移除权重衰减和梯度裁剪
      weight_decay: 0.0
      gradient_clip_norm: 0 # 关闭梯度裁剪

      # 保持其他设置
      conditional_pose_dim: 25
      tactile_shape: [ 1140, 3 ]
      object_dim: 9
      hand_joint_dim: 16
      hand_pose_dim: 9
      enable_single_hand: true
      single_hand_config:
        target_hand: "right"
        segment_length: ${env.seq_length}
        mix_hands: true
        include_tactile: false

      # 🔥 关闭数据增强
      enable_pose_randomization: false
      randomization_range: 0.0

      # 🔥 重新启用动作缩放（已修复enable_clip问题）
      enable_action_scaling: true
      obs_normalization: "minmax"
      action_scaling: "minmax"
      target_pose_normalization: "minmax"
      action_clip_range: [ -1, 1.0 ]
      force_regenerate_stats: false

# 🎯 训练配置：针对过拟合优化
training:
  training_phase: "execution_bc_train"
  hierarchical_mode: "execution_only"

  # 🔥 大幅增加训练轮数，确保充分过拟合
  execution_epochs: 500 # 从10增加到500

  # 🔥 较小的批次大小，增加梯度更新频率
  batch_size: 16 # 从512降低到16

  # 🔥 其他训练参数
  n_timesteps: 1000000
  dataset_dir: "./simulated_data"
  save_freq: 50000
  eval_freq: 100000
  n_eval_episodes: 5

  # 🔥 频繁保存检查点以监控过拟合进度
  checkpoint:
    save_epoch_freq: 50 # 每50个epoch保存一次
    save_step_freq: 0
    max_to_keep: 20

  pretrained_execution_actor_path: null
  pretrained_decision_model_path: null

# 🎯 日志配置：更频繁的记录
logging:
  use_swanlab: true
  swanlab_project_name: "px_tactrix_overfit_experiment"
  current_run_id: ${generate_run_name:}
  experiment_description: "专门用于过拟合167个样本的实验"
  bc_log_interval: 1 # 每个batch都记录

# 其他配置保持默认
video:
  record: false
  length: 200
  interval: 100000
