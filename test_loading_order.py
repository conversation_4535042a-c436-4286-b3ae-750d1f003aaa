#!/usr/bin/env python3
"""
测试修复后的加载顺序
"""

import sys
import argparse
from unittest.mock import Mock

def test_loading_order():
    """测试加载顺序"""
    print("🧪 测试修复后的加载顺序...")
    
    # 创建模拟的args
    args = Mock()
    args.model_path = "test_model.pth"
    args.dataset_path = "test_data.hdf5"
    args.hand_type = "right"
    args.episodes = 1
    args.max_steps = 10
    args.headless = True
    args.position_tolerance = 0.05
    args.joint_tolerance = 0.1
    args.success_hold_time = 10
    args.joint_range = [-0.5, 0.5]
    args.position_range_x = [-0.2, 0.2]
    args.position_range_y = [-0.2, 0.2]
    args.position_range_z = [0.8, 1.2]
    args.save_trajectories = False
    args.disable_render = True
    args.debug_table_interval = 50
    args.enable_debug_table = False
    args.debug = False
    args.debug_table = False
    args.use_object_centric = True
    args.disable_object_centric = False
    args.enable_action_scaling = False
    args.disable_action_scaling = False
    args.scaling_stats_path = None
    args.action_selection_strategy = "first"
    args.use_dataset_poses = False
    args.num_envs = 1
    
    # 添加其他必需的参数
    args.num_samples = 5
    args.output_dir = "test_output"
    args.no_visualization = False
    args.detailed_debug = True
    args.no_detailed_debug = False
    args.no_postprocess = False
    args.no_6d_ortho = False
    args.no_unscaling = False
    args.no_coord_transform = False
    args.use_default_scaling = False
    args.no_preprocessing = False
    args.no_object_centric = False
    args.no_normalization = False
    args.force_traditional_norm = False
    args.enable_object_centric = False
    args.enable_normalization = False
    args.disable_memory_optimization = False
    args.batch_size = 32
    args.max_cache_size = 500
    args.analysis_batch_size = 16
    args.disable_streaming = False
    args.chunk_size = 50
    args.checkpoint_interval = 100
    args.disable_checkpoints = False
    args.max_memory_usage = 0.8
    args.enable_rolling_prediction = False
    args.rolling_chunk_size = 5
    args.rolling_observation_mode = "auto"
    args.rolling_use_true_actions = False
    args.rolling_state_reset = "gt_state"
    args.enable_sequence_variance_analysis = False
    args.show_normalized_component_errors = True
    args.sanity_check = False
    args.force = False
    
    print("✅ 模拟参数创建完成")
    
    # 测试配置管理器
    try:
        from eval_model_vis import SimulationConfigManager
        config_manager = SimulationConfigManager(args)
        config = config_manager.get_config()
        print("✅ 配置管理器测试通过")
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False
    
    # 测试分析器工厂
    try:
        from eval_model_vis import SimulationAnalyzerFactory
        analyzer_factory = SimulationAnalyzerFactory(config)
        analyzer = analyzer_factory.create_analyzer()
        print("✅ 分析器工厂测试通过")
    except Exception as e:
        print(f"❌ 分析器工厂测试失败: {e}")
        return False
    
    print("✅ 所有模块测试通过！")
    return True

def main():
    """主函数"""
    print("🚀 开始测试修复后的加载顺序...")
    print("=" * 50)
    
    success = test_loading_order()
    
    print("=" * 50)
    if success:
        print("✅ 加载顺序修复成功！")
        print("📝 现在环境模块会在AppLauncher启动后立即导入")
        return 0
    else:
        print("❌ 加载顺序测试失败")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 