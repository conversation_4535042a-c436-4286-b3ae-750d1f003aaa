# PX-Tactrix UV包分发完整指南

## 📋 目录

1. [项目背景](#项目背景)
2. [分发需求](#分发需求)
3. [技术方案](#技术方案)
4. [实施步骤](#实施步骤)
5. [用户使用流程](#用户使用流程)
6. [最佳实践](#最佳实践)
7. [故障排除](#故障排除)

---

## 项目背景

**PX-Tactrix** 是一个基于VTLA (Variable-Topology Learning Architecture)的触觉增强机器人操作项目。项目使用Python 3.12+，依赖多个机器学习库，需要确保在不同环境中的可靠部署。

### 核心挑战
- 复杂的依赖关系管理
- 跨平台兼容性要求
- 用户友好的安装体验
- 版本一致性保证

---

## 分发需求

### 用户期望
- **一键安装**：最小化安装步骤
- **环境隔离**：不影响系统Python环境
- **依赖完整**：自动解决所有依赖
- **版本锁定**：确保运行环境一致性

### 技术要求
- Python 3.12+ 支持
- GPU加速（CUDA）支持
- 跨Linux/macOS/Windows平台
- 离线安装能力 

---

## 技术方案

### 选择UV的原因

**UV** 是Astral公司开发的现代Python包管理器，相比传统的pip + virtualenv方案具有显著优势：

| 特性 | UV | pip + venv |
|------|----|-----------| 
| **安装速度** | 🚀 极快 (Rust实现) | 🐌 较慢 (Python实现) |
| **依赖解析** | ✅ 智能解析 | ⚠️ 基础解析 |
| **锁定文件** | ✅ uv.lock | ❌ 需要额外工具 |
| **跨平台** | ✅ 原生支持 | ⚠️ 需要配置 |
| **缓存管理** | ✅ 智能缓存 | ⚠️ 基础缓存 |

### 核心组件

1. **pyproject.toml** - 项目配置和依赖定义
2. **uv.lock** - 精确的依赖版本锁定
3. **分发脚本** - 自动化打包流程
4. **安装脚本** - 用户一键安装

### 分发架构

```
px_tactrix_distribution/
├── pyproject.toml          # 项目配置
├── uv.lock                 # 锁定依赖
├── requirements.txt        # pip兼容
├── quick_install.sh        # 一键安装脚本
├── *.whl                   # 预构建包
├── train/                  # 训练模块
├── eval/                   # 评估模块
├── conf/                   # 配置文件
└── docs/                   # 文档
```

---

## 实施步骤

### Step 1: 配置项目元数据

编辑 `pyproject.toml` 文件，添加完整的项目信息：

```toml
[project]
name = "px-tactrix"
version = "0.1.0"
description = "VTLA (Variable-Topology Learning Architecture) for tactile-enhanced robotic manipulation"
readme = "README.md"
requires-python = ">=3.12"
authors = [
    {name = "Your Name", email = "<EMAIL>"},
]
license = {text = "MIT"}
keywords = ["robotics", "machine-learning", "tactile", "manipulation"]

dependencies = [
    "torch>=2.0.0",
    "torchvision>=0.15.0",
    "numpy>=1.24.0",
    "h5py>=3.8.0",
    "pandas>=2.0.0",
    "matplotlib>=3.7.0",
    "hydra-core>=1.3.0",
    "omegaconf>=2.3.0",
    "tqdm>=4.65.0",
    "wandb>=0.15.0",
    # ... 其他依赖
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "pre-commit>=3.3.0",
]
```

### Step 2: 创建自动化打包脚本

创建 `scripts/package_for_distribution.sh`:

```bash
#!/bin/bash
set -e

echo "🚀 开始打包 PX-Tactrix 项目..."

# 清理旧文件
rm -rf dist/ build/ *.egg-info/

# 锁定依赖
uv lock

# 构建项目
uv build

# 创建分发目录
DIST_DIR="px_tactrix_distribution_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$DIST_DIR"

# 复制文件
cp -r train/ eval/ conf/ "$DIST_DIR/"
cp pyproject.toml uv.lock README.md "$DIST_DIR/"
cp dist/*.whl "$DIST_DIR/"

# 创建安装脚本
cat > "$DIST_DIR/quick_install.sh" << 'EOF'
#!/bin/bash
echo "🚀 开始安装 PX-Tactrix..."
uv sync
echo "✅ 安装完成！"
EOF

chmod +x "$DIST_DIR/quick_install.sh"

# 压缩
tar -czf "${DIST_DIR}.tar.gz" "$DIST_DIR"
echo "✅ 打包完成: ${DIST_DIR}.tar.gz"
```

### Step 3: 执行打包

```bash
# 设置执行权限
chmod +x scripts/package_for_distribution.sh

# 运行打包脚本
./scripts/package_for_distribution.sh
```

## 用户使用流程

### 用户端操作（4步完成）

```bash
# 1. 解压分发包
tar -xzf px_tactrix_distribution_20250623_180323.tar.gz

# 2. 进入目录
cd px_tactrix_distribution_20250623_180323

# 3. 一键安装
./quick_install.sh

# 4. 开始使用
source .venv/bin/activate
python train_model.py  # 开始训练
python eval_model_vis.py  # 评估模型
```

### 安装验证

```bash
# 检查Python环境
python --version  # 应该显示 3.12+

# 检查关键依赖
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import numpy; print(f'NumPy: {numpy.__version__}')"

# 运行测试
pytest test/ --verbose
```

## 最佳实践

### 开发者端

1. **版本管理**
   ```bash
   # 更新版本号
   sed -i 's/version = "0.1.0"/version = "0.1.1"/' pyproject.toml
   
   # 重新锁定依赖
   uv lock --upgrade
   ```

2. **依赖审查**
   ```bash
   # 检查过时依赖
   uv tree
   
   # 安全检查
   uv audit
   ```

3. **测试验证**
   ```bash
   # 在干净环境测试
   docker run --rm -v $(pwd):/app -w /app python:3.12 bash -c "
   curl -LsSf https://astral.sh/uv/install.sh | sh &&
   source ~/.bashrc &&
   tar -xzf *.tar.gz &&
   cd px_tactrix_distribution_* &&
   ./quick_install.sh
   "
   ```

### 用户端

1. **环境要求**
   - Python 3.12+ (必需)
   - 16GB+ RAM (推荐)
   - NVIDIA GPU (可选，用于加速)

2. **网络要求**
   - 首次安装需要网络连接
   - 后续可离线运行

3. **存储空间**
   - 基础安装: ~2GB
   - 完整数据: ~10GB

## 故障排除

### 常见问题

**1. Python版本不兼容**
```bash
# 错误信息
❌ Python版本不足，需要 3.12+，当前版本: 3.10

# 解决方案
# Ubuntu/Debian
sudo apt update && sudo apt install python3.12

# macOS
brew install python@3.12

# 或使用pyenv
pyenv install 3.12.0 && pyenv global 3.12.0
```

**2. UV安装失败**
```bash
# 错误信息
curl: command not found

# 解决方案
# 手动下载安装
wget https://github.com/astral-sh/uv/releases/latest/download/uv-x86_64-unknown-linux-gnu.tar.gz
tar -xzf uv-*.tar.gz
sudo mv uv /usr/local/bin/
```

**3. CUDA相关问题**
```bash
# 检查CUDA版本
nvidia-smi

# 安装对应PyTorch版本
uv add torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

**4. 内存不足**
```bash
# 修改批处理大小
# 编辑 conf/config.yaml
training:
  batch_size: 256  # 从512降到256
```

### 日志分析

**训练日志位置**
```bash
# 查看最新训练日志
tail -f outputs/*/logs/*.log

# 搜索错误信息
grep -r "ERROR\|Exception" outputs/
```

**性能监控**
```bash
# GPU使用率
watch -n 1 nvidia-smi

# 内存使用
htop

# 磁盘空间
df -h
```

---

## 总结

使用UV进行Python包分发的优势：

✅ **快速**: 比传统pip快10-100倍  
✅ **可靠**: 精确的依赖锁定和解析  
✅ **简单**: 一键安装脚本  
✅ **兼容**: 支持多平台和多Python版本  
✅ **现代**: 基于最新的Python打包标准  

这个方案确保了PX-Tactrix项目能够在任何符合要求的环境中快速、可靠地部署和运行。 