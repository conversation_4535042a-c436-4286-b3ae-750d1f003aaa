#!/usr/bin/env python3
"""
参数解析模块 - 从main.py中提取
"""

import argparse
from typing import List
from dataclasses import dataclass

@dataclass
class ValidationResult:
    is_valid: bool
    errors: List[str]

class ArgumentParser:
    """轨迹数据评估参数解析器"""
    
    def __init__(self):
        self.parser = self._create_parser()
    
    def parse_args(self) -> argparse.Namespace:
        """解析命令行参数"""
        return self.parser.parse_args()
    
    def validate_args(self, args: argparse.Namespace) -> ValidationResult:
        """验证参数有效性"""
        errors = []
        
        # 基础验证
        if not args.model_path:
            errors.append("模型路径不能为空")
        if not args.dataset_path:
            errors.append("数据集路径不能为空")
            
        # 逻辑验证
        if args.disable_streaming and args.disable_memory_optimization:
            errors.append("不能同时禁用流式处理和内存优化")
            
        if args.num_samples == 0:
            errors.append("样本数量不能为0")
            
        # 滚动预测参数验证
        if args.enable_rolling_prediction:
            if args.rolling_chunk_size <= 0:
                errors.append("滚动预测分块大小必须大于0")
            if args.rolling_observation_mode not in ["auto", "sequence", "cumulative"]:
                errors.append("滚动预测观察模式必须是 auto, sequence, 或 cumulative")
            if args.rolling_state_reset not in ["gt_state", "predicted_state"]:
                errors.append("滚动预测状态重置模式必须是 gt_state 或 predicted_state")
        
        # 内存使用率验证
        if not (0.0 <= args.max_memory_usage <= 1.0):
            errors.append("最大内存使用率必须在0.0到1.0之间")
            
        return ValidationResult(len(errors) == 0, errors)
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """创建参数解析器"""
        parser = argparse.ArgumentParser(description="轨迹数据分析脚本")
        
        # 添加所有参数组
        self._add_basic_arguments(parser)
        self._add_processing_arguments(parser)
        self._add_postprocess_arguments(parser)
        self._add_preprocess_arguments(parser)
        self._add_memory_optimization_arguments(parser)
        self._add_streaming_arguments(parser)
        self._add_rolling_prediction_arguments(parser)
        self._add_analysis_arguments(parser)
        self._add_utility_arguments(parser)
        
        return parser
    
    def _add_basic_arguments(self, parser):
        """添加基础参数"""
        parser.add_argument(
            "--model_path",
            type=str,
            required=True,
            help="模型文件路径 (例如: model_checkpoint.pth)",
        )
        parser.add_argument(
            "--dataset_path",
            type=str,
            required=True,
            help="数据集路径 (例如: /path/to/dataset)",
        )
        parser.add_argument(
            "--hand_type",
            type=str,
            default="right",
            choices=["left", "right"],
            help="手部类型 (默认: right)",
        )
        parser.add_argument(
            "--num_samples",
            type=int,
            default=5,
            help="分析的样本数量 (默认: 5, 使用 -1 提取所有样本，推荐使用默认流式处理)",
        )
        parser.add_argument(
            "--output_dir",
            type=str,
            default="model_performance_analysis_output",
            help="输出目录 (默认: model_performance_analysis_output)",
        )
    
    def _add_processing_arguments(self, parser):
        """添加处理参数"""
        parser.add_argument(
            "--no_visualization", 
            action="store_true", 
            help="跳过可视化生成"
        )
        parser.add_argument(
            "--detailed_debug",
            action="store_true",
            help="启用详细调试表格（显示动作对比、差值、百分比和时间步连续性验证），默认已启用",
        )
        parser.add_argument(
            "--no_detailed_debug", 
            action="store_true", 
            help="禁用详细调试表格"
        )
    
    def _add_postprocess_arguments(self, parser):
        """添加动作后处理参数"""
        parser.add_argument(
            "--no_postprocess",
            action="store_true",
            help="禁用动作后处理（6D正交化、反缩放、坐标转换）",
        )
        parser.add_argument(
            "--no_6d_ortho", 
            action="store_true", 
            help="禁用6D旋转正交化"
        )
        parser.add_argument(
            "--no_unscaling", 
            action="store_true", 
            help="禁用动作反缩放"
        )
        parser.add_argument(
            "--no_coord_transform", 
            action="store_true", 
            help="禁用坐标系转换"
        )
        parser.add_argument(
            "--use_default_scaling",
            action="store_true",
            help="当缺少动作缩放处理器时，使用默认缩放参数",
        )
    
    def _add_preprocess_arguments(self, parser):
        """添加数据预处理参数"""
        parser.add_argument(
            "--no_preprocessing",
            action="store_true",
            help="禁用所有数据预处理（物体中心坐标变换 + 归一化）",
        )
        parser.add_argument(
            "--no_object_centric", 
            action="store_true", 
            help="禁用物体中心坐标变换"
        )
        parser.add_argument(
            "--no_normalization", 
            action="store_true", 
            help="禁用归一化处理"
        )
        parser.add_argument(
            "--force_traditional_norm",
            action="store_true",
            help="强制使用传统归一化（即使检测到分离归一化）",
        )
        parser.add_argument(
            "--enable_object_centric",
            action="store_true",
            help="强制启用物体中心坐标变换（向后兼容）",
        )
        parser.add_argument(
            "--enable_normalization",
            action="store_true",
            help="强制启用归一化处理（向后兼容）",
        )
    
    def _add_memory_optimization_arguments(self, parser):
        """添加内存优化参数"""
        parser.add_argument(
            "--disable_memory_optimization",
            action="store_true",
            help="禁用内存优化模式（仅在禁用流式处理时有效，启用批处理、缓存、tensor复用等）",
        )
        parser.add_argument(
            "--batch_size",
            type=int,
            default=32,
            help="批处理大小 (默认: 32)",
        )
        parser.add_argument(
            "--max_cache_size",
            type=int,
            default=500,
            help="最大缓存大小 (默认: 500)",
        )
        parser.add_argument(
            "--analysis_batch_size",
            type=int,
            default=16,
            help="分析批次大小 (默认: 16)",
        )
    
    def _add_streaming_arguments(self, parser):
        """添加流式处理参数"""
        parser.add_argument(
            "--disable_streaming",
            action="store_true",
            help="禁用流式处理模式（回退到批处理模式）",
        )
        parser.add_argument(
            "--chunk_size",
            type=int,
            default=50,
            help="流式处理块大小 (默认: 50)",
        )
        parser.add_argument(
            "--checkpoint_interval",
            type=int,
            default=100,
            help="检查点保存间隔 (默认: 100)",
        )
        parser.add_argument(
            "--disable_checkpoints",
            action="store_true",
            help="禁用检查点保存功能",
        )
        parser.add_argument(
            "--max_memory_usage",
            type=float,
            default=0.8,
            help="最大内存使用率 (0.0-1.0, 默认: 0.8)",
        )
    
    def _add_rolling_prediction_arguments(self, parser):
        """添加滚动预测策略参数"""
        parser.add_argument(
            "--enable_rolling_prediction", 
            action="store_true", 
            help="启用滚动预测策略分析"
        )
        parser.add_argument(
            "--rolling_chunk_size", 
            type=int, 
            default=5, 
            help="滚动预测分块大小 (默认: 5)"
        )
        parser.add_argument(
            "--rolling_observation_mode",
            type=str,
            default="auto",
            choices=["auto", "sequence", "cumulative"],
            help="滚动预测观察模式: auto(自动选择), sequence(使用真实观察序列), cumulative(使用状态累积) (默认: auto)",
        )
        parser.add_argument(
            "--rolling_use_true_actions",
            action="store_true",
            help="滚动预测状态更新时使用真实动作（更准确但不现实）",
        )
        parser.add_argument(
            "--rolling_state_reset",
            type=str,
            default="gt_state",
            choices=["gt_state", "predicted_state"],
            help="滚动预测状态重置模式 (默认: gt_state)",
        )
    
    def _add_analysis_arguments(self, parser):
        """添加分析参数"""
        parser.add_argument(
            "--enable_sequence_variance_analysis",
            action="store_true",
            help="启用序列观察方差分析（诊断归一化参数不匹配问题）",
        )
        parser.add_argument(
            "--show_normalized_component_errors",
            action="store_true",
            default=True,
            help="在组件误差图中额外显示归一化域的误差图（默认启用）",
        )
        parser.add_argument(
            "--sanity_check",
            action="store_true",
            help="启用 Sanity Check 模式：使用GT动作替代模型预测 (误差应≈0)",
        )
    
    def _add_utility_arguments(self, parser):
        """添加工具参数"""
        parser.add_argument(
            "-f",
            "--force",
            action="store_true",
            help="强制重新评估，即使模型已存在评估结果",
        )



class ArgumentParser_vis:
    """可视化参数解析器"""
    
    def __init__(self):
        self.parser = self._create_parser()
    
    def parse_args(self) -> argparse.Namespace:
        """解析命令行参数"""
        return self.parser.parse_args()
    
    def _create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
        description="单手DexH13控制模型评估脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
        示例用法：
        python evaluate_single_hand_model.py --model_path ./outputs/single_hand_model.pth --hand_type right
        python evaluate_single_hand_model.py --model_path ./model.pth --hand_type left --episodes 20 --detailed
                """,
        )

        # === 基础配置 ===
        parser.add_argument(
            "--model_path", type=str, required=True, help="单手控制模型路径"
        )
        parser.add_argument(
            "--hand_type",
            type=str,
            choices=["left", "right"],
            default="right",
            help="手部类型",
        )
        parser.add_argument("--episodes", type=int, default=10, help="评估的episode数量")
        parser.add_argument(
            "--max_steps", type=int, default=500, help="每个episode的最大步数"
        )

        # === 环境配置 ===
        parser.add_argument("--num_envs", type=int, default=1, help="并行环境数量")

        # === 评估配置 ===
        parser.add_argument(
            "--position_tolerance", type=float, default=0.05, help="位置达到阈值 (米)"
        )
        parser.add_argument(
            "--joint_tolerance", type=float, default=0.1, help="关节角度达到阈值 (弧度)"
        )
        parser.add_argument(
            "--success_hold_time", type=int, default=10, help="成功保持时间步数"
        )

        # === 随机化配置 ===
        parser.add_argument(
            "--joint_range",
            type=float,
            nargs=2,
            default=[-0.5, 0.5],
            help="关节角度随机范围",
        )
        parser.add_argument(
            "--position_range_x",
            type=float,
            nargs=2,
            default=[-0.2, 0.2],
            help="X轴位置随机范围",
        )
        parser.add_argument(
            "--position_range_y",
            type=float,
            nargs=2,
            default=[-0.2, 0.2],
            help="Y轴位置随机范围",
        )
        parser.add_argument(
            "--position_range_z",
            type=float,
            nargs=2,
            default=[0.8, 1.2],
            help="Z轴位置随机范围",
        )

        # === 输出配置 ===
        parser.add_argument("--output_dir", type=str, default=None, help="结果输出目录")
        parser.add_argument("--save_trajectories", action="store_true", help="保存轨迹数据")
        parser.add_argument("--detailed", action="store_true", help="详细输出模式")
        parser.add_argument("--disable_render", action="store_true", help="禁用渲染")

        # === 调试配置 ===
        parser.add_argument(
            "--debug_table_interval",
            type=int,
            default=50,
            help="调试表格打印间隔步数 (设为1表示每步都打印，设为0表示禁用)",
        )
        parser.add_argument(
            "--enable_debug_table", action="store_true", help="启用详细调试表格输出"
        )

        # === 物体中心坐标系配置 ===
        parser.add_argument(
            "--use_object_centric",
            action="store_true",
            default=True,
            help="启用物体中心坐标系转换（默认启用，与训练时保持一致）",
        )
        parser.add_argument(
            "--disable_object_centric",
            action="store_true",
            help="禁用物体中心坐标系转换（用于调试世界坐标系模式）",
        )

        # === 动作缩放配置 ===
        parser.add_argument(
            "--enable_action_scaling",
            action="store_true",
            default=True,
            help="启用动作缩放（默认启用）",
        )
        parser.add_argument(
            "--disable_action_scaling", action="store_true", help="禁用动作缩放"
        )
        parser.add_argument(
            "--scaling_stats_path", type=str, help="动作缩放统计文件路径(.pkl格式)"
        )

        # === 调试配置 ===
        parser.add_argument("--debug", action="store_true", help="启用调试输出")
        parser.add_argument(
            "--debug_table",
            action="store_true",
            help="启用详细的调试表格输出（显示手指语义信息和动作处理流程）",
        )

        # === ACT模型序列动作选择配置 ===
        parser.add_argument(
            "--action_selection_strategy",
            type=str,
            choices=["first", "last", "weighted_avg"],
            default="first",
            help="ACT模型序列动作选择策略",
        )
        parser.add_argument("--dataset_path", type=str, help="数据集路径，用于加载真实轨迹")
        parser.add_argument(
            "--use_dataset_poses", action="store_true", help="使用数据集中的物体位姿"
        )
        return parser