"""
配置管理模块 - 统一管理所有配置
"""

from dataclasses import dataclass
from pathlib import Path
from typing import Dict, Any
from argparse import Namespace

@dataclass
class ProcessingConfig:
    """处理配置"""
    mode: str  # "streaming", "batch", "traditional"
    num_samples: int
    chunk_size: int = 50
    batch_size: int = 32
    max_memory_usage: float = 0.8
    checkpoint_interval: int = 100
    enable_checkpoints: bool = True

@dataclass
class PostprocessConfig:
    """后处理配置"""
    enable_6d_orthogonalization: bool = False  # ❌ 默认关闭
    enable_action_unscaling: bool = False      # ❌ 默认关闭
    enable_coord_transform: bool = False       # ❌ 默认关闭
    use_default_scaling: bool = False

@dataclass
class PreprocessConfig:
    """预处理配置"""
    enable_object_centric: bool = False   # ❌ 默认关闭
    enable_normalization: bool = False    # ❌ 默认关闭
    force_traditional_norm: bool = False

@dataclass
class AnalysisConfig:
    """完整分析配置"""
    processing: ProcessingConfig
    postprocess: PostprocessConfig
    preprocess: PreprocessConfig
    output_dir: Path
    model_path: str
    dataset_path: str
    hand_type: str
    no_visualization: bool = False
    detailed_debug: bool = True
    enable_rolling_prediction: bool = False
    rolling_chunk_size: int = 5
    rolling_observation_mode: str = "auto"
    rolling_use_true_actions: bool = False
    rolling_state_reset: str = "gt_state"
    show_normalized_component_errors: bool = True
    sanity_check: bool = False
    force: bool = False

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, args: Namespace):
        self.args = args
        self.config = self._build_config()
    
    def get_config(self) -> AnalysisConfig:
        """获取完整配置"""
        return self.config
    
    def _build_config(self) -> AnalysisConfig:
        """构建配置对象"""
        # 确定处理模式
        processing_mode = self._determine_processing_mode()
        
        # 构建各子配置
        processing_config = self._build_processing_config(processing_mode)
        postprocess_config = self._build_postprocess_config()
        preprocess_config = self._build_preprocess_config()
        
        return AnalysisConfig(
            processing=processing_config,
            postprocess=postprocess_config,
            preprocess=preprocess_config,
            output_dir=Path(self.args.output_dir),
            model_path=self.args.model_path,
            dataset_path=self.args.dataset_path,
            hand_type=self.args.hand_type,
            no_visualization=self.args.no_visualization,
            detailed_debug=not self.args.no_detailed_debug,
            enable_rolling_prediction=self.args.enable_rolling_prediction,
            rolling_chunk_size=self.args.rolling_chunk_size,
            rolling_observation_mode=self.args.rolling_observation_mode,
            rolling_use_true_actions=self.args.rolling_use_true_actions,
            rolling_state_reset=self.args.rolling_state_reset,
            show_normalized_component_errors=self.args.show_normalized_component_errors,
            sanity_check=self.args.sanity_check,
            force=self.args.force
        )
    
    def _determine_processing_mode(self) -> str:
        """确定处理模式"""
        if self.args.sanity_check:
            return "sanity_check"
        elif not self.args.disable_streaming:
            return "streaming"
        elif not self.args.disable_memory_optimization:
            return "batch"
        else:
            return "traditional"
    
    def _build_processing_config(self, mode: str) -> ProcessingConfig:
        """构建处理配置"""
        return ProcessingConfig(
            mode=mode,
            num_samples=self.args.num_samples,
            chunk_size=self.args.chunk_size,
            batch_size=self.args.batch_size,
            max_memory_usage=self.args.max_memory_usage,
            checkpoint_interval=self.args.checkpoint_interval,
            enable_checkpoints=not self.args.disable_checkpoints
        )
    
    def _build_postprocess_config(self) -> PostprocessConfig:
        """构建后处理配置"""
        if self.args.no_postprocess:
            return PostprocessConfig(
                enable_6d_orthogonalization=False,
                enable_action_unscaling=False,
                enable_coord_transform=False
            )
        
        # 默认关闭所有后处理，只有显式启用时才开启
        return PostprocessConfig(
            enable_6d_orthogonalization=False,  # 默认关闭
            enable_action_unscaling=False,      # 默认关闭
            enable_coord_transform=False,       # 默认关闭
            use_default_scaling=self.args.use_default_scaling
        )
    
    def _build_preprocess_config(self) -> PreprocessConfig:
        """构建预处理配置"""
        if self.args.no_preprocessing:
            return PreprocessConfig(
                enable_object_centric=False,
                enable_normalization=False
            )
        
        # 默认关闭所有预处理，只有显式启用时才开启
        return PreprocessConfig(
            enable_object_centric=False,  # 默认关闭
            enable_normalization=False,   # 默认关闭
            force_traditional_norm=self.args.force_traditional_norm
        )