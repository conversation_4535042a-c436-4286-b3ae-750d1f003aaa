"""
重构后的主入口 - 简洁清晰
"""

import sys
import logging
import argparse
from pathlib import Path

try:
    from px_janus_learnsim.utils.logger import setup_logger
except ImportError:
    import logging
    def setup_logger(name):
        return logging.getLogger(name)
from .argument_parser import ArgumentParser
from .config_manager import ConfigManager
from .analyzer_factory import AnalyzerFactory
from .execution_engine import ExecutionEngine


def main():
    """主执行函数 - 重构后约80行"""
    # 设置主函数logger
    main_logger = setup_logger("main")

    try:
        main_logger.info("🚀 轨迹数据分析开始")
        main_logger.info("=" * 60)
            
        # 1. 参数解析
        parser = ArgumentParser()
        args = parser.parse_args()
        validation_result = parser.validate_args(args)
        
        if not validation_result.is_valid:
            main_logger.error(f"❌ 参数验证失败: {validation_result.errors}")
            return 1
        
        # 显示基础信息
        main_logger.info(f"模型路径: {args.model_path}")
        main_logger.info(f"数据集路径: {args.dataset_path}")
        main_logger.info(f"手部类型: {args.hand_type}")
        main_logger.info(f"样本数量: {'所有样本' if args.num_samples == -1 else args.num_samples}")
        main_logger.info(f"输出目录: {args.output_dir}")

        # 2. 配置管理
        config_manager = ConfigManager(args)
        config = config_manager.get_config()
        
        # 显示处理模式信息
        _display_processing_mode_info(config, main_logger)

        # 3. 分析器创建
        analyzer_factory = AnalyzerFactory(config)
        analyzer = analyzer_factory.create_analyzer()
        analyzer_factory.configure_analyzer(analyzer)

        # 4. 执行引擎
        execution_engine = ExecutionEngine(analyzer, config, main_logger)
        success = execution_engine.execute()

        if success:
            main_logger.info("✅ 分析完成")
            return 0
        else:
            main_logger.error("❌ 分析失败")
            return 1

    except KeyboardInterrupt:
        main_logger.warning("⏹️ 用户中断程序")
        return 130
    except Exception as e:
        main_logger.error(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        return 1


def _display_processing_mode_info(config, logger):
    """显示处理模式信息"""
    mode = config.processing.mode
    
    if mode == "streaming":
        logger.info("🌊 处理模式: 流式处理（默认，内存友好）")
        logger.info(f"  - 块大小: {config.processing.chunk_size}")
        logger.info(f"  - 检查点间隔: {config.processing.checkpoint_interval}")
        logger.info(f"  - 最大内存使用: {config.processing.max_memory_usage:.1%}")
        logger.info(f"  - 检查点保存: {'禁用' if not config.processing.enable_checkpoints else '启用'}")
    elif mode == "batch":
        logger.info("🔧 处理模式: 内存优化批处理（传统模式）")
        logger.info(f"  - 批处理大小: {config.processing.batch_size}")
    else:
        logger.info("📝 处理模式: 传统逐个处理（最基础模式）")
    
    logger.info("=" * 60)


if __name__ == "__main__":
    sys.exit(main())