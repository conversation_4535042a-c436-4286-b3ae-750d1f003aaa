"""
执行引擎 - 协调整个分析流程
"""

import logging
from pathlib import Path
from typing import Optional, Tuple, List, Dict, Any
from ..utils.file_utils import (
    extract_model_name_from_path,
    create_model_info_file,
    check_existing_evaluation,
    cleanup_temporary_files,
)
from ..data.dataset_sampler import DatasetSampler
from .config_manager import AnalysisConfig

class ExecutionEngine:
    """执行引擎"""
    
    def __init__(self, analyzer, config: AnalysisConfig, logger: logging.Logger):
        self.analyzer = analyzer
        self.config = config
        self.logger = logger
        self.model_name = extract_model_name_from_path(config.model_path)
    
    def execute(self) -> bool:
        """执行完整分析流程"""
        try:
            # 1. 检查现有评估
            if not self._check_existing_evaluation():
                return False
            
            # 2. 准备输出目录
            if not self._prepare_output_directory():
                return False
            
            # 3. 加载模型
            if not self._load_model():
                return False
            
            # 4. 执行分析
            if not self._execute_analysis():
                return False
            
            # 5. 处理结果
            if not self._process_results():
                return False
            
            # 6. 清理临时文件
            self._cleanup()
            
            return True
            
        except Exception as e:
            self.logger.error(f"执行过程中发生错误: {e}")
            return False
    
    def _check_existing_evaluation(self) -> bool:
        """检查现有评估"""
        exists, model_output_dir = check_existing_evaluation(
            self.config.output_dir, self.model_name, self.config.force
        )
        if exists and not self.config.force:
            self.logger.info(f"✅ 模型 {self.model_name} 的评估结果已存在: {model_output_dir}")
            self.logger.info("💡 使用 -f/--force 参数可强制重新评估")
            return False
        elif exists and self.config.force:
            self.logger.info(f"🔄 强制重新评估模型 {self.model_name}")
            self.logger.info(f"   现有结果目录: {model_output_dir}")
        return True
    
    def _prepare_output_directory(self) -> bool:
        """准备输出目录"""
        model_output_dir = Path(self.config.output_dir) / self.model_name
        
        # 如果使用force参数且目录存在，先清理
        if self.config.force and model_output_dir.exists():
            import shutil
            try:
                shutil.rmtree(model_output_dir)
                self.logger.info(f"🗑️ 已清理现有输出目录: {model_output_dir}")
            except Exception as e:
                self.logger.warning(f"⚠️ 清理目录失败: {e}")
        
        model_output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建模型信息文件
        create_model_info_file(
            self.config.model_path,
            self.config.dataset_path,
            self.config.hand_type,
            self.model_name,
            model_output_dir
        )
        
        # 更新配置中的输出目录
        self.config.output_dir = model_output_dir
        self.logger.info(f"📁 模型输出目录: {model_output_dir}")
        
        return True
    
    def _load_model(self) -> bool:
        """加载模型"""
        if self.analyzer.load_model():
            self.logger.info("✅ 模型加载成功")
            return True
        else:
            self.logger.warning("⚠️ 模型加载失败，将只进行数据结构分析")
            return True  # 继续执行，因为可能只是数据结构分析
    
    def _execute_analysis(self) -> bool:
        """执行分析"""
        mode = self.config.processing.mode
        
        if mode == "streaming":
            return self._execute_streaming_analysis()
        elif mode == "batch":
            return self._execute_batch_analysis()
        else:
            return self._execute_traditional_analysis()
    
    def _execute_streaming_analysis(self) -> bool:
        """执行流式分析"""
        self.logger.info("🌊 使用流式处理模式，将按需加载数据...")
        
        # 创建数据集采样器
        self.analyzer.dataset_sampler = DatasetSampler(
            dataset_path=str(self.config.dataset_path)
        )
        
        # 执行流式分析
        summary_stats, predictions_summary = self.analyzer.analyze_dataset_streaming(
            num_samples=self.config.processing.num_samples,
            output_dir=str(self.config.output_dir)
        )
        
        # 加载完整结果
        if summary_stats["successful_analyses"] > 0:
            analysis_results, all_predictions = self.analyzer.load_results_from_chunks(
                str(self.config.output_dir)
            )
            
            # 存储结果供后续处理
            self.analysis_results = analysis_results
            self.all_predictions = all_predictions
            self.summary_stats = summary_stats
            
            return True
        else:
            self.logger.error("❌ 流式处理没有成功分析任何样本")
            return False
    
    def _execute_batch_analysis(self) -> bool:
        """执行批处理分析"""
        # 加载数据集
        if not self.analyzer.load_dataset_optimized(num_samples=self.config.processing.num_samples):
            self.logger.error("❌ 优化数据集加载失败")
            return False
        
        # 批量分析
        analysis_results, all_predictions = self.analyzer.analyze_trajectories_batch(
            batch_size=self.config.processing.batch_size
        )
        
        self.analysis_results = analysis_results
        self.all_predictions = all_predictions
        
        return True
    
    def _execute_traditional_analysis(self) -> bool:
        """执行传统分析"""
        # 加载数据集
        if not self.analyzer.load_dataset(num_samples=self.config.processing.num_samples):
            self.logger.error("❌ 数据集加载失败")
            return False
        
        # 逐个分析
        analysis_results = []
        all_predictions = []
        
        for i in range(len(self.analyzer.samples)):
            self.logger.info(f"📈 正在分析轨迹 {i+1}/{len(self.analyzer.samples)}...")
            result = self.analyzer.analyze_single_trajectory(i)
            if result:
                analysis_results.append(result)
                if "predictions" in result:
                    all_predictions.append(result["predictions"])
            else:
                self.logger.error(f"❌ 轨迹 {i+1} 分析失败")
        
        self.analysis_results = analysis_results
        self.all_predictions = all_predictions
        
        return True
    
    def _process_results(self) -> bool:
        """处理分析结果"""
        if not self.analysis_results:
            self.logger.error("❌ 没有成功分析的轨迹")
            return False
        
        # 生成总体报告
        output_dir = Path(self.config.output_dir)
        output_dir.mkdir(exist_ok=True)
        
        report_path = output_dir / "analysis_report.json"
        self.analyzer.generate_report(self.analysis_results, str(report_path))
        
        # 生成汇总预测误差分析
        if not self.config.no_visualization and self.all_predictions:
            try:
                self.logger.info("📊 生成汇总预测误差分析...")
                self.logger.info(f"   - 基于 {len(self.all_predictions)} 个轨迹的预测数据")
                
                # 验证预测数据质量
                valid_predictions = 0
                for pred in self.all_predictions:
                    if isinstance(pred, dict) and "step_errors" in pred:
                        valid_predictions += 1
                
                if valid_predictions < len(self.all_predictions) * 0.8:
                    self.logger.warning(f"⚠️ 预测数据质量警告: {valid_predictions}/{len(self.all_predictions)} 个有效")
                
                self.analyzer._plot_aggregated_prediction_errors(self.all_predictions, output_dir)
                self.logger.info(f"✅ 汇总误差分析已生成: {output_dir}/aggregated_prediction_errors.png")
                
            except Exception as e:
                error_msg = f"❌ 汇总误差分析生成失败: {e}"
                self.logger.error(error_msg)
                import traceback
                traceback.print_exc()
                raise RuntimeError(error_msg)
        
        # 生成详细总结报告
        self._generate_summary_report(report_path)
        
        # 清理临时文件
        from eval.utils.file_utils import cleanup_temporary_files
        cleanup_temporary_files(output_dir, self.logger)
        
        return True
    
    def _generate_summary_report(self, report_path):
        """生成详细的总结报告"""
        self.logger.info("")
        self.logger.info("=" * 60)
        self.logger.info("📊 分析完成总结:")
        self.logger.info(f"   请求样本数: {'所有样本' if self.config.processing.num_samples == -1 else self.config.processing.num_samples}")
        
        # 根据处理模式显示不同信息
        if self.config.processing.mode == "streaming":
            self.logger.info("   处理模式: 真正流式处理（内存友好）")
            if hasattr(self.analyzer, 'streaming_stats'):
                stats = self.analyzer.streaming_stats
                self.logger.info(f"   成功分析: {stats.get('successful_analyses', 0)}")
                self.logger.info(f"   失败分析: {stats.get('failed_analyses', 0)}")
                self.logger.info(f"   处理块数: {stats.get('chunks_processed', 0)}")
                
                # 内存使用统计
                if 'performance_metrics' in stats:
                    mem_stats = stats['performance_metrics'].get('memory_usage_stats', {})
                    max_memory = mem_stats.get('max_memory_percent', 0)
                    avg_memory = mem_stats.get('avg_memory_percent', 0)
                    memory_warnings = mem_stats.get('memory_warnings', 0)
                    
                    self.logger.info(f"   最大内存使用: {max_memory:.1f}%")
                    self.logger.info(f"   平均内存使用: {avg_memory:.1f}%")
                    if memory_warnings > 0:
                        self.logger.info(f"   内存警告次数: {memory_warnings}")
        else:
            # 传统模式统计
            actual_samples = len(self.analyzer.samples) if hasattr(self.analyzer, 'samples') else 0
            self.logger.info(f"   实际提取轨迹数: {actual_samples}")
            self.logger.info(f"   成功分析: {len(self.analysis_results)}")
            
            mode_name = "内存优化批处理" if self.config.processing.mode == "batch" else "传统逐个处理"
            self.logger.info(f"   处理模式: {mode_name}")
            
            # 内存优化统计
            if self.config.processing.mode == "batch" and hasattr(self.analyzer, "efficient_loader"):
                self.analyzer.efficient_loader._print_cache_stats()
        
        self.logger.info(f"   有预测数据的轨迹: {len(self.all_predictions)}")
        self.logger.info(f"   输出目录: {self.config.output_dir}")
        self.logger.info(f"   报告文件: {report_path}")
        
        if not self.config.no_visualization and self.analysis_results:
            self.logger.info(f"   单轨迹可视化: {self.config.output_dir}/trajectory_1_viz/")
            if self.all_predictions:
                self.logger.info(f"   汇总误差分析: {self.config.output_dir}/aggregated_prediction_errors.png")
        
        self.logger.info("=" * 60)
    
    def _generate_visualizations(self):
        """生成可视化"""
        try:
            self.logger.info("📊 生成汇总预测误差分析...")
            
            self.analyzer._plot_aggregated_prediction_errors(
                self.all_predictions,
                self.config.output_dir,
                model_name=self.analyzer.model_name,
                dataset_name=self.analyzer.dataset_name,
                plot_normalized_errors=self.config.show_normalized_component_errors,
            )
            
            # 生成其他可视化...
            self._generate_additional_visualizations()
            
        except Exception as e:
            self.logger.error(f"❌ 汇总误差分析生成失败: {e}")
    
    def _generate_additional_visualizations(self):
        """生成额外的可视化"""
        # 这里可以添加其他可视化生成逻辑
        pass
    
    def _cleanup(self):
        """清理临时文件"""
        cleanup_temporary_files(self.config.output_dir, self.logger)