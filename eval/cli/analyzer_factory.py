"""
分析器工厂 - 根据配置创建合适的分析器
"""

from typing import Optional
from ..core.analyzers import (
    TrajectoryAnalyzer,
    MemoryOptimizedTrajectoryAnalyzer,
    StreamingTrajectoryAnalyzer
)
from ..core.sanity_check_analyzer import SanityCheckAnalyzer
from .config_manager import AnalysisConfig

class AnalyzerFactory:
    """分析器工厂"""
    
    def __init__(self, config: AnalysisConfig):
        self.config = config
    
    def create_analyzer(self):
        """创建分析器实例"""
        mode = self.config.processing.mode
        
        if mode == "sanity_check":
            return self._create_sanity_check_analyzer()
        elif mode == "streaming":
            return self._create_streaming_analyzer()
        elif mode == "batch":
            return self._create_batch_analyzer()
        else:
            return self._create_traditional_analyzer()
    
    def _create_sanity_check_analyzer(self) -> SanityCheckAnalyzer:
        """创建Sanity Check分析器"""
        analyzer = SanityCheckAnalyzer(
            model_path=self.config.model_path,
            dataset_path=self.config.dataset_path,
            hand_type=self.config.hand_type,
        )
        return analyzer
    
    def _create_streaming_analyzer(self) -> StreamingTrajectoryAnalyzer:
        """创建流式分析器"""
        analyzer = StreamingTrajectoryAnalyzer(
            model_path=self.config.model_path,
            dataset_path=self.config.dataset_path,
            hand_type=self.config.hand_type,
        )
        
        # 配置流式处理参数
        analyzer.streaming_config.update({
            "chunk_size": self.config.processing.chunk_size,
            "checkpoint_interval": self.config.processing.checkpoint_interval,
            "enable_checkpoints": self.config.processing.enable_checkpoints,
            "max_memory_usage": self.config.processing.max_memory_usage,
            "no_visualization": self.config.no_visualization,
        })
        
        return analyzer
    
    def _create_batch_analyzer(self) -> MemoryOptimizedTrajectoryAnalyzer:
        """创建批处理分析器"""
        analyzer = MemoryOptimizedTrajectoryAnalyzer(
            model_path=self.config.model_path,
            dataset_path=self.config.dataset_path,
            hand_type=self.config.hand_type,
        )
        
        # 配置内存优化参数
        analyzer.memory_config.update({
            "batch_size": self.config.processing.batch_size,
            "max_cache_size": self.config.processing.max_cache_size,
        })
        
        return analyzer
    
    def _create_traditional_analyzer(self) -> TrajectoryAnalyzer:
        """创建传统分析器"""
        analyzer = TrajectoryAnalyzer(
            model_path=self.config.model_path,
            dataset_path=self.config.dataset_path,
            hand_type=self.config.hand_type,
        )
        
        return analyzer
    
    def configure_analyzer(self, analyzer):
        """配置分析器"""
        # 设置日志模式
        processing_mode = self.config.processing.mode
        analyzer.set_log_mode(self.config.processing.num_samples, processing_mode)
        
        # 设置调试模式
        analyzer.enable_detailed_debug = self.config.detailed_debug
        
        # 设置后处理配置
        analyzer.postprocess_config = self.config.postprocess.__dict__
        
        # 设置预处理配置
        analyzer.preprocess_config = self.config.preprocess.__dict__
        
        # 设置滚动预测配置
        if self.config.enable_rolling_prediction:
            analyzer.rolling_prediction_config.update({
                "enable_rolling_prediction": True,
                "chunk_size": self.config.rolling_chunk_size,
                "observation_mode": self.config.rolling_observation_mode,
                "use_true_actions_for_state_update": self.config.rolling_use_true_actions,
                "state_reset_mode": self.config.rolling_state_reset,
            })
        
        # 记录配置信息到日志
        self._log_configuration(analyzer)
    
    def _log_configuration(self, analyzer):
        """记录配置信息到日志"""
        logger = analyzer.logger
        
        # 记录后处理配置
        enabled_steps = []
        if analyzer.postprocess_config["enable_6d_orthogonalization"]:
            enabled_steps.append("6D正交化")
        if analyzer.postprocess_config["enable_action_unscaling"]:
            enabled_steps.append("动作反缩放")
        if analyzer.postprocess_config["enable_coord_transform"]:
            enabled_steps.append("坐标转换")

        if enabled_steps:
            logger.info(f"🔧 动作后处理已启用: {' + '.join(enabled_steps)}")
        else:
            logger.info("🔧 动作后处理已全部禁用")
        
        # 记录预处理配置
        preprocessing_steps = []
        if analyzer.preprocess_config["enable_object_centric"]:
            preprocessing_steps.append("物体中心坐标变换")
        if analyzer.preprocess_config["enable_normalization"]:
            norm_type = (
                "传统归一化"
                if analyzer.preprocess_config["force_traditional_norm"]
                else "自动检测归一化"
            )
            preprocessing_steps.append(norm_type)

        if preprocessing_steps:
            logger.info(f"🔧 数据预处理已启用: {' + '.join(preprocessing_steps)}")
        else:
            logger.info("🔧 数据预处理已全部禁用（适配预处理数据）")
        
        # 记录滚动预测配置
        if self.config.enable_rolling_prediction:
            logger.info("🔄 滚动预测策略已启用")
            logger.info(f"   - 分块大小: {self.config.rolling_chunk_size}")
            logger.info(f"   - 观察模式: {self.config.rolling_observation_mode}")
            logger.info(f"   - 状态更新: {'真实动作' if self.config.rolling_use_true_actions else '预测动作'}")
            logger.info(f"   - 状态重置模式: {self.config.rolling_state_reset}")
        else:
            logger.info("🔄 滚动预测策略已禁用（使用标准预测）")

# 确保类可以被正确导入
__all__ = ["AnalyzerFactory"]