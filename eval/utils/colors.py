"""
终端颜色工具类

提供ANSI颜色码用于美化终端输出
"""


class Colors:
    """终端颜色ANSI码 - 用于美化调试表格输出"""

    RED = "\033[91m"
    GREEN = "\033[92m"
    YELLOW = "\033[93m"
    BLUE = "\033[94m"
    PURPLE = "\033[95m"
    CYAN = "\033[96m"
    WHITE = "\033[97m"
    ORANGE = "\033[38;5;208m"  # 橙色（使用256色模式）
    BOLD = "\033[1m"
    RESET = "\033[0m"

    # 特殊效果颜色
    BRIGHT_MAGENTA = "\033[95m"  # 亮紫色
    BG_BLUE = "\033[44m"  # 蓝色背景
    BG_MAGENTA = "\033[45m"  # 紫色背景
    BG_CYAN = "\033[46m"  # 青色背景
    BG_GREEN = "\033[42m"  # 绿色背景
    BG_RED = "\033[41m"  # 红色背景
    BG_YELLOW = "\033[43m"  # 黄色背景
    UNDERLINE = "\033[4m"  # 下划线
    REVERSE = "\033[7m"  # 反色

    # 特殊组合 - 用于主标题
    SPECIAL_HEADER = "\033[1m\033[97m\033[45m"  # 加粗+白字+紫背景
    ERROR_HEADER = "\033[1m\033[97m\033[41m"  # 加粗+白字+红背景
    SUCCESS_HEADER = "\033[1m\033[97m\033[42m"  # 加粗+白字+绿背景
    WARNING_HEADER = "\033[1m\033[30m\033[43m"  # 加粗+黑字+黄背景
