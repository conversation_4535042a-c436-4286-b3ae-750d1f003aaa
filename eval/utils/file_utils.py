"""
文件操作相关工具函数

包含：
- 模型名提取
- 模型信息文件创建
- 重复评估检查
- 临时文件清理
"""

import re
import shutil
import json
from datetime import datetime
from pathlib import Path
from typing import Tuple
import logging


def extract_model_name_from_path(model_path: str) -> str:
    """从模型路径中提取模型名称"""
    path = Path(model_path)

    # 尝试从路径中找到模型名（倒数第二个目录）
    parts = path.parts
    for i in range(len(parts) - 1, -1, -1):
        part = parts[i]
        # 跳过文件名和常见的目录名
        if part.endswith(".pth") or part.endswith(".pt"):
            continue
        if part in ["outputs", "execution_bc_actor", "models", "checkpoints"]:
            continue
        # 如果包含日期格式，跳过
        if re.match(r"\d{4}-\d{2}-\d{2}", part):
            continue
        if re.match(r"\d{2}-\d{2}-\d{2}", part):
            continue
        # 找到可能的模型名
        if len(part) > 3 and "-" in part:
            return part

    # 如果没找到，使用文件名（去掉扩展名）
    return path.stem


def create_model_info_file(
    model_path: str,
    dataset_path: str,
    hand_type: str,
    model_name: str,
    output_dir: Path,
) -> None:
    """创建模型信息文件"""
    info = {
        "evaluation_info": {
            "model_name": model_name,
            "model_path": str(Path(model_path).absolute()),
            "dataset_path": str(Path(dataset_path).absolute()),
            "hand_type": hand_type,
            "evaluation_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "script_version": "eval_traj_data.py v2.0",
        }
    }

    info_file = output_dir / "model_evaluation_info.json"
    with open(info_file, "w", encoding="utf-8") as f:
        json.dump(info, f, indent=2, ensure_ascii=False)

    # 同时创建人类可读的文本文件
    readable_file = output_dir / "README_evaluation.txt"
    with open(readable_file, "w", encoding="utf-8") as f:
        f.write("=" * 60 + "\n")
        f.write("模型性能评估报告\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"模型名称: {model_name}\n")
        f.write(f"模型路径: {info['evaluation_info']['model_path']}\n")
        f.write(f"数据集路径: {info['evaluation_info']['dataset_path']}\n")
        f.write(f"手部类型: {hand_type}\n")
        f.write(f"评估时间: {info['evaluation_info']['evaluation_date']}\n")
        f.write(f"脚本版本: {info['evaluation_info']['script_version']}\n\n")
        f.write("文件说明:\n")
        f.write("- analysis_report.json: 详细分析报告\n")
        f.write("- aggregated_prediction_errors.png: 汇总误差分析图\n")
        f.write("- trajectory_1_viz/: 第一条轨迹的可视化图表\n")
        f.write("- final_statistics.json: 最终统计信息\n")


def check_existing_evaluation(
    base_output_dir: str, model_name: str, force: bool = False
) -> Tuple[bool, Path]:
    """检查是否已存在评估结果"""
    base_path = Path(base_output_dir)
    model_output_dir = base_path / model_name

    if not model_output_dir.exists():
        return False, model_output_dir

    # 检查关键文件是否存在
    key_files = ["model_evaluation_info.json", "analysis_report.json"]

    all_exist = all((model_output_dir / f).exists() for f in key_files)

    if all_exist and not force:
        return True, model_output_dir

    return False, model_output_dir


def cleanup_temporary_files(output_dir: Path, logger: logging.Logger) -> None:
    """清理临时文件和文件夹"""
    temp_dirs = ["chunks", "intermediate_results"]

    for temp_dir_name in temp_dirs:
        temp_dir = output_dir / temp_dir_name
        if temp_dir.exists():
            try:
                shutil.rmtree(temp_dir)
                logger.info(f"✅ 已清理临时目录: {temp_dir}")
            except Exception as e:
                logger.warning(f"⚠️ 清理临时目录失败 {temp_dir}: {e}")
