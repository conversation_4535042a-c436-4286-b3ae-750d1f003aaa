"""
坐标转换工具类

提供坐标系转换相关的工具函数
"""

import torch
import numpy as np


def transform_action_to_world_frame(
    action_object_frame: torch.Tensor,
    object_pos_world: torch.Tensor,
    object_quat_world: torch.Tensor,
    device: torch.device
) -> torch.Tensor:
    """
    将模型输出的物体中心坐标系动作转换回世界坐标系

    这是评估阶段坐标系一致性的关键步骤：
    1. 模型接收物体中心坐标系的观察
    2. 模型输出物体中心坐标系的动作
    3. 需要将动作转换回世界坐标系给环境执行

    Args:
        action_object_frame: 物体中心坐标系下的动作 [batch_size, 25]
        object_pos_world: 物体世界位置 [batch_size, 3]
        object_quat_world: 物体世界四元数 [batch_size, 4] (w,x,y,z)
        device: 计算设备

    Returns:
        action_world_frame: 世界坐标系下的动作 [batch_size, 25]
    """
    try:
        # 验证输入维度
        if action_object_frame.dim() != 2:
            if action_object_frame.dim() == 1:
                # 如果是1维，扩展为2维 [action_dim] -> [1, action_dim]
                action_object_frame = action_object_frame.unsqueeze(0)
            else:
                return action_object_frame

        if action_object_frame.shape[-1] != 25:
            return action_object_frame

        # 分解动作为关节、位置、旋转组件
        batch_size = action_object_frame.shape[0]
        action_joints = action_object_frame[:, :16]  # 关节动作不需要坐标转换
        action_position_obj = action_object_frame[:, 16:19]  # 物体中心坐标系位置
        action_rotation_6d_obj = action_object_frame[:, 19:25]  # 物体中心坐标系旋转

        # 导入坐标转换工具
        from px_janus_learnsim.utils.coordinate_transform_utils import (
            batch_transform_hand_states_from_object_frame,
        )

        # 将位置和旋转从物体中心坐标系转换回世界坐标系
        action_position_world, action_rotation_6d_world = (
            batch_transform_hand_states_from_object_frame(
                action_position_obj,  # 物体坐标系下的位置
                action_rotation_6d_obj,  # 物体坐标系下的6D旋转
                object_pos_world,  # 物体世界位置
                object_quat_world,  # 物体世界四元数
            )
        )

        # 重新组合动作
        action_world_frame = torch.cat(
            [
                action_joints,  # [batch_size, 16] 关节动作
                action_position_world,  # [batch_size, 3] 世界坐标系位置
                action_rotation_6d_world,  # [batch_size, 6] 世界坐标系6D旋转
            ],
            dim=-1,
        )  # [batch_size, 25]

        return action_world_frame

    except Exception as e:
        print(f"⚠️ 动作坐标转换详细错误: {e}")
        return action_object_frame


def extract_target_pose_from_obs(obs, device: torch.device):
    """
    从观察字典中提取完整的目标姿态信息（简化版）

    Args:
        obs: 环境观察字典
        device: 计算设备

    Returns:
        target_pose: 完整目标状态 [1, 25] 或 None
    """
    if not isinstance(obs, dict):
        return None

    # 优先方法：直接从evaluation_info获取预构建的25维目标状态
    if "evaluation_info" in obs:
        eval_info = obs["evaluation_info"]
        if "target_full_state_25d" in eval_info:
            target_full_state = eval_info["target_full_state_25d"]

            # 统一类型转换
            if isinstance(target_full_state, (list, np.ndarray)):
                target_full_state = (
                    torch.from_numpy(np.array(target_full_state))
                    .float()
                    .to(device)
                )
            elif isinstance(target_full_state, torch.Tensor):
                target_full_state = target_full_state.float().to(device)
            else:
                return None

            # 统一维度处理
            if target_full_state.dim() == 1:
                target_full_state = target_full_state.unsqueeze(0)  # [1, 25]

            # 维度验证
            if target_full_state.shape[-1] == 25:
                return target_full_state
            else:
                print(f"⚠️ target_full_state_25d维度错误: {target_full_state.shape}")
        else:
            print("⚠️ Env返回的Obs中，未找到target_full_state_25d")

    return None 