"""
内存监控相关工具

包含：
- MemoryMonitor: 内存使用监控和管理
"""

import psutil
from typing import Dict


class MemoryMonitor:
    """内存监控器，用于动态调整处理策略"""

    def __init__(self, warning_threshold: float = 0.8, critical_threshold: float = 0.9):
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold

    def get_memory_info(self) -> Dict[str, float]:
        """获取当前内存使用情况"""
        memory = psutil.virtual_memory()
        return {
            "total": memory.total / (1024**3),  # GB
            "available": memory.available / (1024**3),  # GB
            "used": memory.used / (1024**3),  # GB
            "percent": memory.percent / 100.0,  # 0-1
        }

    def is_memory_critical(self) -> bool:
        """检查内存是否达到临界状态"""
        return self.get_memory_info()["percent"] > self.critical_threshold

    def is_memory_warning(self) -> bool:
        """检查内存是否达到警告状态"""
        return self.get_memory_info()["percent"] > self.warning_threshold

    def suggest_chunk_size(
        self, base_chunk_size: int, sample_size_mb: float = 10.0
    ) -> int:
        """根据内存情况建议块大小"""
        memory_info = self.get_memory_info()
        available_gb = memory_info["available"]

        # 使用可用内存的30%作为安全缓冲
        safe_memory_gb = available_gb * 0.3
        safe_memory_mb = safe_memory_gb * 1024

        # 计算安全的块大小
        suggested_size = max(1, int(safe_memory_mb / sample_size_mb))

        # 限制在合理范围内
        return min(suggested_size, base_chunk_size * 2, 200)
