"""
JSON编码器工具类

提供自定义JSON编码器，处理NumPy和PyTorch数据类型
"""

import json
import numpy as np
import torch


class NumpyEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理NumPy和PyTorch数据类型"""

    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, torch.Tensor):
            return obj.cpu().numpy().tolist()
        return super(NumpyEncoder, self).default(obj) 