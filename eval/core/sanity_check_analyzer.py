from eval.core.analyzers import TrajectoryAnalyzer


class SanityCheckAnalyzer(TrajectoryAnalyzer):
    """专用于评估 sanity check 的分析器。

    该分析器会在执行预测阶段，将模型的输出强行替换为当前样本的真实动作，
    从而验证评估流程（误差计算、坐标转换、归一化等）本身是否正确。
    理论上，在此模式下所有误差应接近 0，若仍出现较大误差，则表明
    评估管线存在不一致或实现问题。"""

    # ---------------------------------------------------------------------
    # 记录父类转换后的 Ground Truth，以便在 forward 中直接返回
    # ---------------------------------------------------------------------
    def _apply_training_coordinate_transform_with_gt(  # noqa: N802
        self,
        current_hand_pos,
        current_hand_rot_6d,
        target_hand_pos,
        target_hand_rot_6d,
        obj_pos_world,
        obj_rot_6d_world,
        true_actions,
    ):
        """在保持父类完整逻辑的同时，将转换后的 GT 存为实例属性。"""
        (
            cur_pos_obj,
            cur_rot_6d_obj,
            tgt_pos_obj,
            tgt_rot_6d_obj,
            object_state,
            true_actions_transformed,
        ) = super()._apply_training_coordinate_transform_with_gt(
            current_hand_pos,
            current_hand_rot_6d,
            target_hand_pos,
            target_hand_rot_6d,
            obj_pos_world,
            obj_rot_6d_world,
            true_actions,
        )

        # 保存两份：转换后的GT（物理域），以及归一化后的GT
        self._sanity_gt_transformed = true_actions_transformed  # 物理域

        # 计算归一化域版本，形状兼容处理
        if self.action_scaling_processor is not None:
            scaled_gt = self.action_scaling_processor.scale_actions(
                true_actions_transformed
            )
            if scaled_gt.shape != true_actions_transformed.shape:
                # 可能处理器只接受2D，做形状展开再reshape
                b, t, d = true_actions_transformed.shape
                scaled_gt = self.action_scaling_processor.scale_actions(
                    true_actions_transformed.view(-1, d)
                ).view(b, t, d)
            self._sanity_gt_scaled = scaled_gt
        else:
            # 若处理器缺失，作为回退仍用未归一化GT
            self._sanity_gt_scaled = true_actions_transformed

        return (
            cur_pos_obj,
            cur_rot_6d_obj,
            tgt_pos_obj,
            tgt_rot_6d_obj,
            object_state,
            true_actions_transformed,
        )

    # ------------------------------------------------------------------
    # 关键改动：在分析阶段，将 model.forward Monkey-patch 成恒返回 GT
    # ------------------------------------------------------------------
    def _analyze_predictions(self, obs_dict, true_actions, target_pose):  # noqa: N802
        """使用 Ground-Truth 替代模型预测，并调用父类逻辑计算误差。"""
        # 保存原始 forward 方法，稍后恢复
        orig_forward = getattr(self.model, "forward", None)

        # 保证 true_actions 至少是 3D，避免父类解包错误
        if true_actions.dim() == 2:
            # 将单步GT复制为完整序列长度（默认15步），以便父类流程能绘制完整时间序列
            seq_len = 15  # 若后续需要，可改为从配置中读取
            true_actions_3d = true_actions.unsqueeze(1).repeat(
                1, seq_len, 1
            )  # [B, seq_len, D]
        else:
            true_actions_3d = true_actions

        # 在 forward 被调用时，转换后的 GT 已保存在 _sanity_gt_scaled
        def _gt_forward(_obs, target_pose=None):  # noqa: D401, N802
            return getattr(self, "_sanity_gt_scaled").clone().detach()

        try:
            # Monkey-patch
            self.model.forward = _gt_forward  # type: ignore[assignment]
            # 调用父类实现，复用全部误差/可视化流程
            return super()._analyze_predictions(obs_dict, true_actions_3d, target_pose)
        finally:
            # 保证恢复原方法，防止副作用
            if orig_forward is not None:
                self.model.forward = orig_forward

    # ------------------------------------------------------------------
    # 覆写后处理：直接返回物体坐标系下的物理域GT，跳过反缩放等步骤
    # 这样物理域误差计算将使用与true_actions_transformed一致的张量 → 误差≈0
    # ------------------------------------------------------------------
    def _postprocess_predicted_actions(self, pred_actions):  # noqa: N802
        """Sanity-Check 模式下跳过任何后处理，保持与 GT 完全一致。"""
        return getattr(self, "_sanity_gt_transformed")
