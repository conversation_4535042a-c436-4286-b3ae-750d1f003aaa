"""
轨迹分析器核心类

包含：
- TrajectoryAnalyzer: 基础轨迹分析器
- MemoryOptimizedTrajectoryAnalyzer: 内存优化版本
- StreamingTrajectoryAnalyzer: 流式处理版本
"""

import numpy as np
import torch
from pathlib import Path
import json
from typing import Dict, List, Optional, Tuple, Any
from tqdm import tqdm
import os
import yaml
import time
import pickle
from ..visualization.plotters import VisualizationMixin
from ..utils.memory_utils import MemoryMonitor
from px_janus_learnsim.utils.logger import (
    setup_logger,
    print_table_with_logger,
    PRETTYTABLE_AVAILABLE,
)
from eval.core.data.iterators import LazyDatasetIterator
from .data.data_loaders import EfficientDataLoader
from eval.utils.colors import Colors

# 导入项目相关模块
try:
    from px_janus_learnsim.learning.models.bc_utils import process_imitation_batch
except ImportError as e:
    print(f"⚠️ 导入项目模块失败: {e}")
    process_imitation_batch = None

# 导入DatasetSampler

from eval.data.dataset_sampler import DatasetSampler


# 导入PrettyTable
try:
    from prettytable import PrettyTable
except ImportError:
    PrettyTable = None
# 这里将放置从eval_traj_data.py转移过来的分析器类：

from px_janus_learnsim.utils.math import calculate_6d_rotation_distance


class TrajectoryAnalyzer(VisualizationMixin):
    """轨迹数据分析器"""

    def __init__(self, model_path: str, dataset_path: str, hand_type: str = "right"):
        """
        初始化分析器

        Args:
            model_path: 模型文件路径
            dataset_path: 数据集路径
            hand_type: 手部类型 ("left" 或 "right")
        """
        self.model_path = Path(model_path)
        self.dataset_path = Path(dataset_path)
        self.hand_type = hand_type
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.logger = setup_logger(f"TrajectoryAnalyzer_{hand_type}")

        # 模型和数据相关属性
        self.model = None
        self.dataset_sampler = None
        self.samples = []

        # 分析结果存储
        self.analysis_results = {}

        # 🆕 日志控制配置
        self.log_config = {
            "enable_detailed_analysis": True,  # 是否启用详细分析打印
            "enable_trajectory_debug": True,  # 是否启用轨迹调试表格
            "enable_prediction_details": True,  # 是否启用预测详情打印
            "enable_component_analysis": True,  # 是否启用组件分析打印
            "enable_temporal_check": True,  # 是否启用时间步一致性检查打印
            "quiet_mode": False,  # 静默模式（仅显示关键信息）
            "batch_processing_mode": False,  # 批处理模式（减少单样本日志）
        }

        # 配置参数
        self.enable_detailed_debug = True  # 详细调试模式开关

        # 🆕 动作后处理配置 - 适配预处理数据
        self.postprocess_config = {
            "enable_6d_orthogonalization": False,  # 与训练时保持一致（训练时enable_6d_rotation_fix=False）
            "enable_action_unscaling": False,  # 数据已预处理，无需反缩放
            "enable_coord_transform": False,  # 数据已在物体中心坐标系，无需转换
            "use_default_scaling": False,
        }

        # 🆕 数据预处理配置（适配预处理数据）
        self.preprocess_config = {
            "enable_object_centric": False,  # 数据已预处理为物体中心坐标系
            "enable_normalization": False,  # 数据已预处理，无需归一化
            "force_traditional_norm": False,
        }

        # 🆕 滚动预测策略配置
        self.rolling_prediction_config = {
            "enable_rolling_prediction": False,  # 是否启用滚动预测分析
            "chunk_size": 5,  # 分块大小
            "overlap_size": 0,  # 重叠大小（暂不实现）
            "state_reset_mode": "gt_state",  # 状态重置模式：gt_state, predicted_state
            "compare_with_standard": True,  # 是否与标准预测进行对比
            "use_true_actions_for_state_update": False,  # 是否使用真实动作进行状态更新
            "observation_mode": "auto",  # 观察模式：auto, sequence, cumulative
        }

        # 动作缩放处理器
        self.action_scaling_processor = None

        # 提取模型和数据集名称用于可视化
        self.model_name = self._extract_model_name()
        self.dataset_name = self._extract_dataset_name()

        self.logger.info(f"🔧 轨迹分析器初始化完成 - 手部类型: {hand_type}")
        self.logger.info(f"   模型路径: {self.model_path}")
        self.logger.info(f"   数据集路径: {self.dataset_path}")
        self.logger.info(f"   模型名称: {self.model_name}")
        self.logger.info(f"   数据集名称: {self.dataset_name}")

    def set_log_mode(self, num_samples: int, processing_mode: str = "standard"):
        """
        根据样本数量和处理模式设置日志级别

        Args:
            num_samples: 样本数量，-1表示所有样本
            processing_mode: 处理模式 ("streaming", "batch", "standard")
        """
        if num_samples == -1:
            # 处理所有样本时，启用静默模式
            self.log_config.update(
                {
                    "enable_detailed_analysis": False,
                    "enable_trajectory_debug": False,
                    "enable_prediction_details": False,
                    "enable_component_analysis": False,
                    "enable_temporal_check": False,
                    "quiet_mode": True,
                    "batch_processing_mode": True,
                }
            )
            self.logger.info("🔇 已启用静默模式 - 处理所有样本时将减少详细日志输出")

        elif num_samples > 50:
            # 大批量样本时，部分静默
            self.log_config.update(
                {
                    "enable_detailed_analysis": False,
                    "enable_trajectory_debug": False,
                    "enable_prediction_details": False,
                    "enable_component_analysis": False,
                    "enable_temporal_check": False,
                    "quiet_mode": False,
                    "batch_processing_mode": True,
                }
            )
            self.logger.info(
                f"🔉 已启用批处理模式 - 处理{num_samples}个样本时将减少详细日志输出"
            )

        elif processing_mode == "streaming":
            # 流式处理模式 - 但如果样本数很少，仍然启用详细模式
            if num_samples <= 100:
                # 少样本流式处理：启用详细模式
                self.log_config.update(
                    {
                        "enable_detailed_analysis": True,
                        "enable_trajectory_debug": True,
                        "enable_prediction_details": True,
                        "enable_component_analysis": True,
                        "enable_temporal_check": True,
                        "quiet_mode": False,
                        "batch_processing_mode": False,
                    }
                )
                self.logger.info(
                    "🌊 流式处理模式 + 详细日志 - 少样本时显示完整分析信息"
                )
            else:
                # 大样本流式处理：简化模式
                self.log_config.update(
                    {
                        "enable_detailed_analysis": False,
                        "enable_trajectory_debug": False,
                        "enable_prediction_details": False,
                        "enable_component_analysis": False,
                        "enable_temporal_check": False,
                        "quiet_mode": False,
                        "batch_processing_mode": True,
                    }
                )
                self.logger.info("🌊 已启用流式处理模式 - 将减少单轨迹详细日志输出")

        else:
            # 标准模式（少量样本）
            self.log_config.update(
                {
                    "enable_detailed_analysis": True,
                    "enable_trajectory_debug": True,
                    "enable_prediction_details": True,
                    "enable_component_analysis": True,
                    "enable_temporal_check": True,
                    "quiet_mode": False,
                    "batch_processing_mode": False,
                }
            )
            self.logger.info("🔊 已启用详细模式 - 将显示完整的分析日志")

    def _should_log_detail(self, log_type: str) -> bool:
        """
        检查是否应该输出特定类型的详细日志

        Args:
            log_type: 日志类型 ("analysis", "debug", "prediction", "component", "temporal",
                     "trajectory", "structure", "conversion", "preprocessing", "postprocessing")

        Returns:
            是否应该输出该类型的日志
        """
        # 静默模式：只输出错误和最终汇总
        if self.log_config.get("silent_mode", False):
            return False

        # 批处理模式：减少详细输出
        if self.log_config.get("batch_processing_mode", False):
            # 批处理模式下屏蔽所有详细日志，只保留最终汇总
            return False  # 完全静默详细日志，只输出最终汇总统计

        # 流式处理模式：进一步减少单轨迹详细日志
        if self.log_config.get("streaming_mode", False):
            return False  # 流式模式下也完全静默详细日志

        # 兼容旧的quiet_mode配置
        if self.log_config.get("quiet_mode", False):
            return False

        # 旧的类型映射（向后兼容）
        type_mapping = {
            "analysis": "enable_detailed_analysis",
            "debug": "enable_trajectory_debug",
            "prediction": "enable_prediction_details",
            "component": "enable_component_analysis",
            "temporal": "enable_temporal_check",
            "preprocessing": "enable_detailed_analysis",  # 预处理日志归类到详细分析
            "postprocessing": "enable_detailed_analysis",  # 后处理日志归类到详细分析
            "structure": "enable_detailed_analysis",  # 结构分析日志归类到详细分析
        }

        config_key = type_mapping.get(log_type, "enable_detailed_analysis")
        return self.log_config.get(config_key, True)

    def load_model(self):
        """加载训练好的模型"""
        if not self.model_path.exists():
            self.logger.error(f"❌ 模型文件不存在: {self.model_path}")
            self.logger.warning("将只进行数据结构分析")
            self.model = None
            return False

        self.logger.info(f"🔄 加载模型: {self.model_path}")

        # 直接复用evaluate_single_hand_model.py的成功逻辑
        model_data = torch.load(
            self.model_path, map_location=self.device, weights_only=False
        )

        if isinstance(model_data, dict):
            # 从字典中提取policy对象
            if "policy" in model_data:
                self.model = model_data["policy"]
                self.logger.info("   ✅ 从字典中提取policy对象")
            else:
                self.logger.error("   ❌ 字典中未找到policy键")
                self.model = None
                return False
        else:
            # 直接是policy对象
            self.model = model_data
            self.logger.info("   ✅ 直接加载policy对象")

        # 设置模型为评估模式
        if hasattr(self.model, "eval"):
            self.model.eval()

        # 确保模型在正确设备上
        if hasattr(self.model, "to"):
            self.model = self.model.to(self.device)

        # 尝试从模型中获取动作缩放处理器和坐标变换器
        self.action_scaling_processor = None
        self.object_centric_transformer = None
        self.enable_action_scaling = False
        self.use_object_centric = False

        # 🆕 改进：默认启用关键预处理步骤（因为训练时通常都会用到）
        # 首先设置合理的默认值，然后尝试从各种来源获取具体配置
        self.enable_action_scaling = True  # 默认启用动作缩放
        self.use_object_centric = True  # 默认启用物体中心坐标转换

        if self._should_log_detail("preprocessing"):
            print("   🔧 设置预处理默认值: 动作缩放=True, 物体中心坐标=True")

        # 🆕 尝试从训练配置文件中读取具体配置
        training_config = self._load_training_config_from_model_dir()
        if training_config:
            if self._should_log_detail("preprocessing"):
                print("   📋 从训练配置中读取预处理设置...")
            # 从训练配置中获取具体的预处理配置
            exec_policy_kwargs = (
                training_config.get("agent", {})
                .get("execution_policy", {})
                .get("policy_kwargs", {})
            )

            # 更新物体中心坐标配置
            config_use_object_centric = exec_policy_kwargs.get(
                "use_object_centric", True
            )
            self.use_object_centric = config_use_object_centric
            if self._should_log_detail("preprocessing"):
                print(
                    f"     - 物体中心坐标转换: {self.use_object_centric} (来自训练配置)"
                )

            # 更新动作缩放配置
            config_enable_action_scaling = exec_policy_kwargs.get(
                "enable_action_scaling", True
            )
            self.enable_action_scaling = config_enable_action_scaling
            if self._should_log_detail("preprocessing"):
                print(f"     - 动作缩放: {self.enable_action_scaling} (来自训练配置)")

            # 读取其他相关配置
            self.enable_separated_normalization = exec_policy_kwargs.get(
                "enable_separated_normalization", True
            )
            if self._should_log_detail("preprocessing"):
                print(
                    f"     - 分离归一化: {self.enable_separated_normalization} (来自训练配置)"
                )

            # 读取单手配置
            single_hand_config = exec_policy_kwargs.get("single_hand_config", {})
            if single_hand_config:
                self.hand_type = single_hand_config.get("target_hand", "left")
                if self._should_log_detail("preprocessing"):
                    print(f"     - 目标手: {self.hand_type} (来自训练配置)")

        # 检查模型是否有缩放处理器 - 更全面的搜索
        processor_found = False
        transformer_found = False

        # 1. 检查模型实例属性
        if hasattr(self.model, "action_scaling_processor"):
            self.action_scaling_processor = self.model.action_scaling_processor
            processor_found = True
            if self._should_log_detail("preprocessing"):
                print(
                    f"   📊 找到动作缩放处理器 (模型属性): {type(self.action_scaling_processor)}"
                )

        # 2. 检查模型字典
        if not processor_found and isinstance(model_data, dict):
            # 检查多种可能的键名
            possible_processor_keys = [
                "action_scaling_processor",
                "scaling_processor",
                "processor",
                "action_processor",
                "data_processor",
            ]

            for key in possible_processor_keys:
                if key in model_data:
                    self.action_scaling_processor = model_data[key]
                    processor_found = True
                    if self._should_log_detail("preprocessing"):
                        print(
                            f"   📊 找到动作缩放处理器 (模型字典[{key}]): {type(self.action_scaling_processor)}"
                        )
                    break

            # 检查嵌套字典
            if not processor_found:
                nested_dicts = ["model_info", "training_info", "metadata", "config"]
                for nested_key in nested_dicts:
                    if nested_key in model_data and isinstance(
                        model_data[nested_key], dict
                    ):
                        for proc_key in possible_processor_keys:
                            if proc_key in model_data[nested_key]:
                                self.action_scaling_processor = model_data[nested_key][
                                    proc_key
                                ]
                                processor_found = True
                                if self._should_log_detail("preprocessing"):
                                    print(
                                        f"   📊 找到动作缩放处理器 (嵌套字典[{nested_key}][{proc_key}])"
                                    )
                                break
                        if processor_found:
                            break

        # 3. 尝试从文件自动加载处理器
        if not processor_found and self.enable_action_scaling:
            if self._should_log_detail("preprocessing"):
                print("   🔍 模型中未找到处理器，尝试从文件自动加载...")
            model_dir = (
                os.path.dirname(self.model_path)
                if os.path.isfile(self.model_path)
                else self.model_path
            )

            # 尝试多种可能的文件名
            possible_files = [
                "separated_scaling_stats.pkl",
                "scaling_stats.pkl",
                "action_scaling_stats.pkl",
                "normalization_stats.pkl",
                "compute_scaling_stats",
            ]

            for filename in possible_files:
                stats_path = os.path.join(model_dir, filename)
                if os.path.exists(stats_path):
                    try:
                        if "separated" in filename:
                            # 加载分离归一化处理器
                            from px_janus_learnsim.utils.action_scaling_utils import (
                                load_separated_scaling_processor,
                            )

                            self.action_scaling_processor = (
                                load_separated_scaling_processor(
                                    stats_path, enable_clip=False
                                )
                            )
                            if self._should_log_detail("preprocessing"):
                                print(f"   📊 自动加载分离归一化处理器: {stats_path}")
                        else:
                            # 加载传统处理器
                            from px_janus_learnsim.utils.action_scaling_utils import (
                                load_scaling_processor,
                            )

                            self.action_scaling_processor = load_scaling_processor(
                                stats_path
                            )
                            if self._should_log_detail("preprocessing"):
                                print(f"   📊 自动加载动作缩放处理器: {stats_path}")
                        processor_found = True
                        break
                    except Exception as e:
                        if self._should_log_detail("preprocessing"):
                            print(f"   ⚠️ 加载处理器失败 {stats_path}: {e}")
                        continue

            # 4. 物体中心坐标变换器检测和加载
            from px_janus_learnsim.utils.object_centric_transforms import (
                ObjectCentricTransformer,
            )

            self.object_centric_transformer = ObjectCentricTransformer(
                device=str(self.device)
            )
            if self._should_log_detail("preprocessing"):
                print("   🌍 创建新的物体中心坐标变换器")

        # 检查其他可能的配置属性
        if hasattr(self.model, "enable_action_scaling"):
            self.enable_action_scaling = self.model.enable_action_scaling
        if hasattr(self.model, "use_object_centric"):
            self.use_object_centric = self.model.use_object_centric

        # 🆕 检测训练时的归一化模式（分离归一化 vs 传统归一化）
        # 重新评估，在加载处理器之后统一判断
        if (
            self.action_scaling_processor is not None
            and hasattr(self.action_scaling_processor, "separated_stats")
            and self.action_scaling_processor.separated_stats is not None
        ):
            self.enable_separated_normalization = True
            if self._should_log_detail("preprocessing"):
                print("   📊 检测到分离归一化模式 (使用separated_stats)")
        else:
            self.enable_separated_normalization = False
            if self._should_log_detail("preprocessing"):
                print("   📊 检测到传统归一化模式")

        # 最终状态报告
        if self._should_log_detail("preprocessing"):
            print("   ✅ 模型加载完成:")
            print(
                f"      - 动作缩放: {'✅' if self.enable_action_scaling else '❌'} ({'有处理器' if processor_found else '将使用默认参数'})"
            )
            print(
                f"      - 物体中心坐标: {'✅' if self.use_object_centric else '❌'} ({'有变换器' if transformer_found else '将使用基本转换'})"
            )
            print(
                f"      - 分离归一化: {'✅' if self.enable_separated_normalization else '❌'}"
            )
            print(f"      - 目标手: {getattr(self, 'hand_type', 'left')}")

        # 打印动作缩放处理器统计信息
        if self.action_scaling_processor is not None and self._should_log_detail(
            "preprocessing"
        ):
            print("   📊 动作缩放处理器统计信息:")
            try:
                if (
                    hasattr(self.action_scaling_processor, "separated_stats")
                    and self.action_scaling_processor.separated_stats is not None
                ):
                    s = self.action_scaling_processor.separated_stats
                    print("      - 分离归一化统计:")
                    for part in ["observations", "target_pose", "actions"]:
                        d = getattr(s, part, None)
                        if isinstance(d, dict):
                            mean_shape = np.shape(d.get("mean", []))
                            std_shape = np.shape(d.get("std", []))
                            print(
                                f"        {part}: mean_shape={mean_shape}, std_shape={std_shape}"
                            )
                else:
                    print("   ⚠️ 未找到统计量")
            except Exception as e:
                print(f"   ⚠️ 打印统计信息失败: {e}")

        # === 表格形式打印34维观察统计 (仅一次) ===
        if (
            self.action_scaling_processor is not None
            and hasattr(self.action_scaling_processor, "separated_stats")
            and self.action_scaling_processor.separated_stats is not None
            and not hasattr(self, "_printed_obs_stats")
            and self._should_log_detail("preprocessing")
        ):
            try:
                obs_stats = self.action_scaling_processor.separated_stats.observations
                mean = obs_stats.get("mean")
                std = obs_stats.get("std")

                labels = [
                    *[f"hand_joint[{i}]" for i in range(16)],  # 0–15
                    "hand_pos_x",  # 16
                    "hand_pos_y",  # 17
                    "hand_pos_z",  # 18
                    "hand_rot6d_c1_x",  # 19
                    "hand_rot6d_c1_y",  # 20
                    "hand_rot6d_c1_z",  # 21
                    "hand_rot6d_c2_x",  # 22
                    "hand_rot6d_c2_y",  # 23
                    "hand_rot6d_c2_z",  # 24
                    "obj_pos_x(0)",  # 25
                    "obj_pos_y(0)",  # 26
                    "obj_pos_z(0)",  # 27
                    "obj_rot6d_c1_x(1)",  # 28
                    "obj_rot6d_c1_y(0)",  # 29
                    "obj_rot6d_c1_z(0)",  # 30
                    "obj_rot6d_c2_x(0)",  # 31
                    "obj_rot6d_c2_y(1)",  # 32
                    "obj_rot6d_c2_z(0)",  # 33
                ]

                # 构建表格内容
                table_lines = []
                table_lines.append(f"{'Idx':>3} | {'Mean':>10} | {'Std':>10} | Feature")
                table_lines.append("-" * 55)
                for idx in range(34):
                    table_lines.append(
                        f"{idx:3d} | {mean[idx]:10.4e} | {std[idx]:10.4e} | {labels[idx]}"
                    )

                table_content = "\n".join(table_lines)
                print_table_with_logger(
                    self.logger, table_content, "🧮 训练统计 (Observations 34D)"
                )

                self._printed_obs_stats = True
            except Exception as e:
                self.logger.warning(f"⚠️ 无法打印观察统计表: {e}")

        # === 表格形式打印25维动作统计 (仅一次) ===
        if (
            self.action_scaling_processor is not None
            and hasattr(self.action_scaling_processor, "separated_stats")
            and self.action_scaling_processor.separated_stats is not None
            and not hasattr(self, "_printed_action_stats")
            and self._should_log_detail("preprocessing")
        ):
            try:
                action_stats = self.action_scaling_processor.separated_stats.actions
                mean = action_stats.get("mean")
                std = action_stats.get("std")

                labels = [
                    *[f"joint[{i}]" for i in range(16)],  # 0–15
                    "hand_pos_x",  # 16
                    "hand_pos_y",  # 17
                    "hand_pos_z",  # 18
                    "rot6d_c1_x",  # 19
                    "rot6d_c1_y",  # 20
                    "rot6d_c1_z",  # 21
                    "rot6d_c2_x",  # 22
                    "rot6d_c2_y",  # 23
                    "rot6d_c2_z",  # 24
                ]

                # 构建表格内容
                table_lines = []
                table_lines.append(f"{'Idx':>3} | {'Mean':>10} | {'Std':>10} | Feature")
                table_lines.append("-" * 52)
                for idx in range(25):
                    table_lines.append(
                        f"{idx:3d} | {mean[idx]:10.4e} | {std[idx]:10.4e} | {labels[idx]}"
                    )

                table_content = "\n".join(table_lines)
                print_table_with_logger(
                    self.logger, table_content, "🧮 训练统计 (Actions 25D)"
                )

                # 记录极小方差通道 (<1e-2)
                std_np = np.array(std)
                small_std_indices = np.where(std_np < 1e-2)[0].tolist()
                if small_std_indices:
                    self.logger.warning(
                        f"⚠️ 恒定/低方差通道 (σ < 1e-2): {small_std_indices}"
                    )
                # 保存布尔mask供后续误差计算过滤
                self._small_std_mask = torch.tensor(
                    [idx in small_std_indices for idx in range(25)], dtype=torch.bool
                )
                self._small_std_indices = small_std_indices

                self._printed_action_stats = True
            except Exception as e:
                self.logger.warning(f"⚠️ 无法打印动作统计表: {e}")

        return True

    def load_dataset(self, num_samples: int = 10):
        """加载数据集样本"""
        try:
            self.logger.info(f"📊 加载数据集: {self.dataset_path}")

            # 创建数据集采样器
            # 🔧 修复：确保评估时只使用训练时见过的数据
            self.dataset_sampler = DatasetSampler(
                dataset_path=str(self.dataset_path),
                use_train_split_only=True,  # 只使用训练集，不包含验证集
            )

            self.logger.info("   ✅ 数据集采样器创建成功（仅使用训练集）")

            # 🆕 处理提取所有样本的情况
            if num_samples == -1:
                self.logger.info("   📋 准备提取数据集中的所有样本...")
                return self._load_all_samples()
            else:
                self.logger.info(f"   📋 准备提取 {num_samples} 个样本进行分析")
                return self._load_limited_samples(num_samples)

        except Exception as e:
            self.logger.error(f"❌ 数据集加载失败: {e}")
            import traceback

            traceback.print_exc()
            return False

    def _load_limited_samples(self, num_samples: int):
        """加载指定数量的样本"""
        self.samples = []
        for i in tqdm(range(num_samples), desc="提取样本"):
            try:
                sample = self.dataset_sampler.get_sample()
                self.samples.append(sample)
            except Exception as e:
                self.logger.warning(f"   ⚠️ 样本 {i} 提取失败: {e}")
                continue

        self.logger.info(f"   ✅ 成功提取 {len(self.samples)} 个样本")
        return True

    def _load_all_samples(self):
        """加载数据集中的所有样本 - 直接遍历底层数据集"""
        self.samples = []
        sample_count = 0
        max_samples = 10000  # 🛡️ 安全限制，防止无限循环
        consecutive_failures = 0
        max_consecutive_failures = 50  # 连续失败次数限制

        self.logger.info("   🔍 尝试检测数据集大小...")

        # 尝试从数据集采样器获取大小信息
        dataset_size = self._get_dataset_size()
        if dataset_size:
            self.logger.info(f"   📊 检测到数据集大小: {dataset_size} 个样本")
            max_samples = min(max_samples, dataset_size)
        else:
            self.logger.warning("   ⚠️ 无法检测数据集大小，将动态提取直到无更多样本")

        # 🆕 尝试直接访问底层数据集
        underlying_dataset = None

        # 方法1: 检查DatasetSampler是否有train_dataloader属性
        if hasattr(self.dataset_sampler, "train_dataloader"):
            dataloader = self.dataset_sampler.train_dataloader
            if hasattr(dataloader, "dataset"):
                underlying_dataset = dataloader.dataset
                self.logger.info(
                    f"   ✅ 找到底层数据集，类型: {type(underlying_dataset)}"
                )

        # 方法2: 检查是否有dataset属性
        if underlying_dataset is None and hasattr(self.dataset_sampler, "dataset"):
            underlying_dataset = self.dataset_sampler.dataset
            self.logger.info(f"   ✅ 找到底层数据集，类型: {type(underlying_dataset)}")

        # 如果找到了底层数据集，直接遍历
        if underlying_dataset is not None:
            try:
                dataset_len = len(underlying_dataset)
                self.logger.info(f"   📊 底层数据集大小: {dataset_len} 个样本")

                # 更新最大样本数
                max_samples = min(max_samples, dataset_len)

                # 使用进度条直接遍历数据集
                pbar = tqdm(total=max_samples, desc="提取所有样本", unit="样本")

                for i in range(max_samples):
                    try:
                        sample = underlying_dataset[i]
                        self.samples.append(sample)
                        sample_count += 1

                        pbar.update(1)
                        pbar.set_postfix({"已提取": sample_count})

                    except Exception as e:
                        consecutive_failures += 1
                        if consecutive_failures >= max_consecutive_failures:
                            self.logger.warning(
                                f"\n   ⚠️ 连续失败 {max_consecutive_failures} 次，停止提取"
                            )
                            self.logger.error(f"   最后一个错误: {e}")
                            break

                        self.logger.warning(f"   ⚠️ 样本 {i} 提取失败: {e}")
                        continue

                pbar.close()

                if len(self.samples) > 0:
                    self.logger.info(f"   ✅ 成功提取 {len(self.samples)} 个样本")
                    return True

            except Exception as e:
                self.logger.warning(f"   ⚠️ 直接遍历数据集失败: {e}")
                # 回退到原来的方法
                pass

        # 回退方法：使用原来的DatasetSampler.get_sample()方法
        self.logger.info("   🔄 回退到DatasetSampler.get_sample()方法...")

        # 使用进度条
        pbar = tqdm(desc="提取所有样本", unit="样本")

        try:
            while sample_count < max_samples:
                try:
                    sample = self.dataset_sampler.get_sample()
                    self.samples.append(sample)
                    sample_count += 1
                    consecutive_failures = 0  # 重置连续失败计数

                    pbar.update(1)
                    pbar.set_postfix(
                        {
                            "已提取": sample_count,
                            "失败": (
                                len(self.samples) - sample_count
                                if len(self.samples) < sample_count
                                else 0
                            ),
                        }
                    )

                except StopIteration:
                    self.logger.info(
                        f"\n   ✅ 数据集已遍历完毕，共提取 {len(self.samples)} 个样本"
                    )
                    break
                except IndexError:
                    self.logger.info(
                        f"\n   ✅ 数据集索引超出范围，共提取 {len(self.samples)} 个样本"
                    )
                    break
                except Exception as e:
                    consecutive_failures += 1
                    if consecutive_failures >= max_consecutive_failures:
                        self.logger.warning(
                            f"\n   ⚠️ 连续失败 {max_consecutive_failures} 次，停止提取"
                        )
                        self.logger.error(f"   最后一个错误: {e}")
                        break

                    # 对于偶发错误，继续尝试
                    if consecutive_failures <= 5:
                        continue
                    else:
                        self.logger.warning(f"   ⚠️ 样本 {sample_count} 提取失败: {e}")

        finally:
            pbar.close()

        if len(self.samples) == 0:
            self.logger.error("   ❌ 未能提取到任何有效样本")
            return False
        elif len(self.samples) >= max_samples:
            self.logger.warning(f"   ⚠️ 达到安全限制 ({max_samples})，停止提取")

        self.logger.info(f"   ✅ 成功提取 {len(self.samples)} 个样本")
        return True

    def _get_dataset_size(self):
        """尝试获取数据集大小"""
        try:
            # 🆕 方法1: 检查底层数据集大小
            underlying_dataset = None

            # 检查DatasetSampler是否有train_dataloader属性
            if hasattr(self.dataset_sampler, "train_dataloader"):
                dataloader = self.dataset_sampler.train_dataloader
                if hasattr(dataloader, "dataset"):
                    underlying_dataset = dataloader.dataset

            # 检查是否有dataset属性
            if underlying_dataset is None and hasattr(self.dataset_sampler, "dataset"):
                underlying_dataset = self.dataset_sampler.dataset

            # 如果找到底层数据集，获取其大小
            if underlying_dataset is not None:
                try:
                    dataset_size = len(underlying_dataset)
                    self.logger.info(f"   📊 从底层数据集获取大小: {dataset_size}")
                    return dataset_size
                except Exception as e:
                    self.logger.warning(f"   ⚠️ 获取底层数据集大小失败: {e}")

            # 方法2: 检查采样器是否有长度属性
            if hasattr(self.dataset_sampler, "__len__"):
                return len(self.dataset_sampler)

            # 方法3: 检查采样器的数据集属性
            if hasattr(self.dataset_sampler, "dataset") and hasattr(
                self.dataset_sampler.dataset, "__len__"
            ):
                return len(self.dataset_sampler.dataset)

            # 方法4: 检查是否有size或length属性
            for attr_name in ["size", "length", "num_samples", "total_samples"]:
                if hasattr(self.dataset_sampler, attr_name):
                    size = getattr(self.dataset_sampler, attr_name)
                    if isinstance(size, int) and size > 0:
                        return size

            # 方法5: 如果是文件夹，尝试计算文件数量
            if self.dataset_path.is_dir():
                # 计算可能的数据文件
                data_files = (
                    list(self.dataset_path.glob("*.pkl"))
                    + list(self.dataset_path.glob("*.npz"))
                    + list(self.dataset_path.glob("*.h5"))
                )
                if data_files:
                    return len(data_files)

            return None

        except Exception as e:
            self.logger.warning(f"   ⚠️ 获取数据集大小失败: {e}")
            return None

    def convert_to_single_hand_format(self, obs_dict, actions_dict, target_pose):
        """
        转换为单手Policy格式
        复用evaluate_single_hand_model.py中的逻辑
        """
        if self._should_log_detail("conversion"):
            self.logger.info("   🔄 转换数据格式到单手Policy格式...")

        # 直接复用evaluate_single_hand_model.py中成功的转换逻辑
        return self._convert_to_single_hand_policy_format(
            obs_dict, actions_dict, target_pose, self.hand_type
        )

    def _convert_to_single_hand_policy_format(
        self, obs_dict, actions_dict, target_pose, hand_type
    ):
        """
        转换双手数据为单手Policy格式 - 直接复用evaluate_single_hand_model.py的实现
        """
        # 提取双手数据
        left_hand_actions = actions_dict.get("lefthand")
        right_hand_actions = actions_dict.get("righthand")

        if left_hand_actions is None or right_hand_actions is None:
            self.logger.error("   ❌ 缺少手部动作数据")
            return None, None, None

        # 根据hand_type选择对应的手部数据
        if hand_type == "left":
            selected_hand_actions = left_hand_actions
        else:  # right
            selected_hand_actions = right_hand_actions

        # 提取手部动作组件
        action_joints = selected_hand_actions.get("joints")  # [B, T, 16]
        action_handpose = selected_hand_actions.get("handpose")  # [B, T, 9]

        # # 🆕 调查步骤1: 打印原始动作数据形状
        # print(f"   🔍 调查动作数据形状:")
        # print(f"      action_joints 原始形状: {action_joints.shape if action_joints is not None else 'None'}")
        # print(f"      action_handpose 原始形状: {action_handpose.shape if action_handpose is not None else 'None'}")
        # print(f"      action_joints 数据类型: {type(action_joints)}")
        # print(f"      action_handpose 数据类型: {type(action_handpose)}")

        if action_joints is None or action_handpose is None:
            self.logger.error("   ❌ 手部动作数据不完整")
            return None, None, None

        # 确保所有数据在正确设备上
        action_joints = action_joints.to(self.device)
        action_handpose = action_handpose.to(self.device)

        # 组合成25维Policy动作
        policy_actions = torch.cat(
            [action_joints, action_handpose], dim=-1
        )  # [B, T, 25]

        # 提取观察数据组件 - 复用evaluate_single_hand_model.py的成功逻辑
        hand_key = f"{hand_type}hand"  # lefthand 或 righthand

        if hand_key not in obs_dict:
            self.logger.error(f"   ❌ 未找到手部观察数据键: {hand_key}")
            self.logger.info(f"   📋 可用键: {list(obs_dict.keys())}")
            return None, None, None

        # 提取手部观察数据 (16关节 + 9位姿 = 25维) - 直接复用evaluate_single_hand_model.py的处理方式
        try:
            hand_joints = obs_dict[hand_key]["joints"]  # [B, 16] 或 [B, T, 16]?
            hand_pose = obs_dict[hand_key]["handpose"]  # [B, 9] 或 [B, T, 9]?

            # # 🆕 调查步骤1: 打印原始观察数据形状
            # print(f"   🔍 调查观察数据形状:")
            # print(f"      hand_joints 原始形状: {hand_joints.shape}")
            # print(f"      hand_pose 原始形状: {hand_pose.shape}")
            # print(f"      hand_joints 数据类型: {type(hand_joints)}")
            # print(f"      hand_pose 数据类型: {type(hand_pose)}")

            # 确保设备一致性
            hand_joints = hand_joints.to(self.device)
            hand_pose = hand_pose.to(self.device)

            if self._should_log_detail("conversion"):
                self.logger.info(
                    f"   ✅ 成功提取{hand_key}数据: joints{hand_joints.shape}, handpose{hand_pose.shape}"
                )
        except (KeyError, TypeError) as e:
            self.logger.error(f"   ❌ 手部数据结构错误: {e}")
            self.logger.info(f"   📋 {hand_key}数据类型: {type(obs_dict[hand_key])}")
            if isinstance(obs_dict[hand_key], dict):
                self.logger.info(
                    f"   📋 {hand_key}可用键: {list(obs_dict[hand_key].keys())}"
                )
            return None, None, None

        # 提取物体数据 - 复用evaluate_single_hand_model.py的成功逻辑
        object_data = None
        for obj_key in ["obj1", "object", "obj2"]:  # 按优先级查找
            if obj_key in obs_dict:
                object_data = obs_dict[obj_key]  # [B, 9]
                object_data = object_data.to(self.device)  # 确保设备一致性
                if self._should_log_detail("conversion"):
                    self.logger.info(
                        f"   📦 使用物体数据: {obj_key}, shape={object_data.shape}"
                    )
                break

        if object_data is None:
            self.logger.error("   ❌ 未找到物体数据")
            return None, None, None

        # 组合34维观察空间 (25维当前手部状态 + 9维物体状态)
        policy_obs_34d = torch.cat(
            [hand_joints, hand_pose, object_data], dim=-1
        )  # [B, 34] 或 [B, T, 34]

        # 构建Policy观察字典
        policy_obs_dict = {"policy": policy_obs_34d}  # [B, 34] 或 [B, T, 34]
        # 转换目标位姿格式
        if target_pose is not None:
            target_pose = target_pose.to(self.device)  # 确保设备一致性

            # 🆕 处理3D target_pose: [B, T, 18] 或 2D: [B, 18]
            if target_pose.dim() == 3:  # [B, T, 18] 序列格式
                # target_pose原始格式: [B, T, 18] (双手9维位姿序列)
                if hand_type == "left":
                    policy_target_pose = target_pose[:, :, :9]  # [B, T, 9] 左手位姿序列
                else:  # right
                    policy_target_pose = target_pose[:, :, 9:]  # [B, T, 9] 右手位姿序列

                # 转换为25维格式：16维关节(从当前观察获取) + 9维位姿
                target_joints = hand_joints  # [B, T, 16] 使用当前关节作为目标关节
                policy_target_pose = torch.cat(
                    [target_joints, policy_target_pose], dim=-1
                )  # [B, T, 25]
            else:  # [B, 18] 单时间步格式
                # target_pose原始格式: [B, 18] (双手9维位姿)
                if hand_type == "left":
                    policy_target_pose = target_pose[:, :9]  # [B, 9] 左手位姿
                else:  # right
                    policy_target_pose = target_pose[:, 9:]  # [B, 9] 右手位姿

                # 转换为25维格式：16维关节(从当前观察获取) + 9维位姿
                target_joints = hand_joints  # [B, 16] 使用当前关节作为目标关节
                if target_joints.dim() == 3:  # 如果关节是3D，取第一个时间步
                    target_joints = target_joints[:, 0, :]  # [B, 16]
                policy_target_pose = torch.cat(
                    [target_joints, policy_target_pose], dim=-1
                )  # [B, 25]
        else:
            policy_target_pose = None

        return policy_obs_dict, policy_actions, policy_target_pose

    def analyze_single_trajectory(self, sample_idx: int = 0):
        """
        分析单条轨迹的预测准确性

        Args:
            sample_idx: 样本索引
        """
        if sample_idx >= len(self.samples):
            self.logger.error(
                f"❌ 样本索引 {sample_idx} 超出范围 (总数: {len(self.samples)})"
            )
            return None

        # 只在详细模式下显示轨迹分析开始信息
        if self._should_log_detail("trajectory"):
            self.logger.info(f"🔍 分析轨迹样本 {sample_idx + 1}/{len(self.samples)}")

        sample = self.samples[sample_idx]

        try:
            # 调试：打印样本结构 - 只在详细模式下显示
            if self._should_log_detail("structure"):
                print(f"   📋 样本类型: {type(sample)}")
                if isinstance(sample, (list, tuple)):
                    print(f"   📋 样本长度: {len(sample)}")
                    for i, item in enumerate(sample):
                        print(f"   📋 元素 {i}: {type(item)}")
                        if isinstance(item, dict):
                            print(f"      键: {list(item.keys())}")

            # 🆕 处理数据加载器返回的 (index, actual_sample) 格式
            actual_sample = sample
            if isinstance(sample, tuple) and len(sample) == 2:
                # 检查是否是 (index, actual_sample) 格式
                first_elem, second_elem = sample
                if isinstance(first_elem, int) and isinstance(second_elem, list):
                    # 这是 (index, actual_sample) 格式，提取实际样本
                    actual_sample = second_elem
                    if self._should_log_detail("structure"):
                        print("   📋 检测到 (index, sample) 格式，提取实际样本")
                        print(
                            f"   📋 索引: {first_elem}, 实际样本类型: {type(actual_sample)}"
                        )
                elif isinstance(first_elem, dict) and isinstance(second_elem, dict):
                    # 这可能是直接的 (state_dict, actions_dict) 格式
                    actual_sample = [first_elem, second_elem]
                    if self._should_log_detail("structure"):
                        print(
                            "   📋 检测到 (state_dict, actions_dict) tuple 格式，转换为 list"
                        )
                        print(f"   📋 转换后样本类型: {type(actual_sample)}")
                else:
                    # 其他 tuple 格式，记录详细信息
                    if self._should_log_detail("structure"):
                        print("   📋 未知 tuple 格式:")
                        print(f"   📋 第一个元素类型: {type(first_elem)}")
                        print(f"   📋 第二个元素类型: {type(second_elem)}")

            # 处理训练数据格式
            if isinstance(actual_sample, list) and len(actual_sample) == 2:
                state_dict, actions_dict = actual_sample

                if self._should_log_detail("structure"):
                    print(f"   📋 state_dict 类型: {type(state_dict)}")
                    print(f"   📋 actions_dict 类型: {type(actions_dict)}")

                    if isinstance(state_dict, dict):
                        print(f"   📋 state_dict 键: {list(state_dict.keys())}")
                    if isinstance(actions_dict, dict):
                        print(f"   📋 actions_dict 键: {list(actions_dict.keys())}")

                # 🆕 使用统一数据处理，启用序列保留
                obs_dict, target_pose, _, _, _ = process_imitation_batch(
                    actual_sample, self.device, preserve_sequence=True
                )

                if self._should_log_detail("structure"):
                    print(f"   📋 处理后 obs_dict 类型: {type(obs_dict)}")
                    if isinstance(obs_dict, dict):
                        print(f"   📋 处理后 obs_dict 键: {list(obs_dict.keys())}")

                # 转换为单手格式
                policy_obs_dict, policy_actions, policy_target_pose = (
                    self.convert_to_single_hand_format(
                        obs_dict, actions_dict, target_pose
                    )
                )

                # 分析轨迹数据结构
                trajectory_info = self._analyze_trajectory_structure(
                    policy_obs_dict, policy_actions, policy_target_pose
                )

                # 如果有模型，进行预测分析
                if hasattr(self, "model") and self.model is not None:
                    prediction_analysis = self._analyze_predictions(
                        policy_obs_dict, policy_actions, policy_target_pose
                    )
                    trajectory_info["predictions"] = prediction_analysis

                return trajectory_info

        except Exception as e:
            # 🆕 更详细的错误信息
            error_msg = f"❌ 轨迹分析失败 (样本 {sample_idx}): {str(e)}"
            self.logger.error(error_msg)
            self.logger.error(f"   异常类型: {type(e).__name__}")

            # 🆕 根据异常类型提供具体建议
            if "process_imitation_batch" in str(e):
                self.logger.error("   💡 可能原因：数据预处理函数导入或调用失败")
                self.logger.error(
                    "   💡 建议：检查  px_janus_learnsim.learning.models.bc_utils 模块"
                )
            elif "object_centric_transformer" in str(e):
                self.logger.error("   💡 可能原因：坐标转换器未正确初始化")
                self.logger.error("   💡 建议：检查模型加载过程中的坐标转换器设置")
            elif "model" in str(e).lower():
                self.logger.error("   💡 可能原因：模型相关问题")
                self.logger.error("   💡 建议：检查模型加载和推理过程")
            elif "cuda" in str(e).lower() or "device" in str(e).lower():
                self.logger.error("   💡 可能原因：设备或GPU相关问题")
                self.logger.error("   💡 建议：检查设备设置和GPU可用性")

            # 🆕 只在前几个失败样本时打印完整堆栈
            if sample_idx < 5:
                import traceback

                self.logger.error("   完整异常堆栈:")
                for line in traceback.format_exc().split("\n"):
                    if line.strip():
                        self.logger.error(f"     {line}")

            return None

    def _analyze_trajectory_structure(self, obs_dict, actions, target_pose):
        """分析轨迹数据结构"""
        info = {
            "obs_structure": {},
            "action_structure": {},
            "target_structure": {},
            "trajectory_stats": {},
        }

        # 分析观察数据结构
        if isinstance(obs_dict, dict):
            for key, value in obs_dict.items():
                if hasattr(value, "shape"):
                    info["obs_structure"][key] = {
                        "shape": list(value.shape),
                        "dtype": str(value.dtype),
                        "min": float(value.min()) if value.numel() > 0 else 0,
                        "max": float(value.max()) if value.numel() > 0 else 0,
                        "mean": float(value.mean()) if value.numel() > 0 else 0,
                    }

        # 分析动作数据结构
        if actions is not None and hasattr(actions, "shape"):
            info["action_structure"] = {
                "shape": list(actions.shape),
                "dtype": str(actions.dtype),
                "min": float(actions.min()) if actions.numel() > 0 else 0,
                "max": float(actions.max()) if actions.numel() > 0 else 0,
                "mean": float(actions.mean()) if actions.numel() > 0 else 0,
            }

            # 分析轨迹长度
            if len(actions.shape) >= 2:
                seq_length = actions.shape[1]
                action_dim = actions.shape[-1]
                info["trajectory_stats"] = {
                    "sequence_length": seq_length,
                    "action_dimension": action_dim,
                }

        # 分析目标数据结构
        if target_pose is not None and hasattr(target_pose, "shape"):
            info["target_structure"] = {
                "shape": list(target_pose.shape),
                "dtype": str(target_pose.dtype),
                "min": float(target_pose.min()) if target_pose.numel() > 0 else 0,
                "max": float(target_pose.max()) if target_pose.numel() > 0 else 0,
                "mean": float(target_pose.mean()) if target_pose.numel() > 0 else 0,
            }

        return info

    def _calculate_rolling_chunks(
        self, total_length: int, chunk_size: int
    ) -> List[Dict]:
        """
        计算滚动预测的分块信息

        Args:
            total_length: 总序列长度
            chunk_size: 分块大小

        Returns:
            List[Dict]: 分块信息列表，每个包含start_idx, end_idx, actual_size
        """
        chunks = []

        for start_idx in range(0, total_length, chunk_size):
            end_idx = min(start_idx + chunk_size, total_length)
            actual_size = end_idx - start_idx

            chunk_info = {
                "start_idx": start_idx,
                "end_idx": end_idx,
                "actual_size": actual_size,
                "is_final_chunk": end_idx == total_length,
                "chunk_id": len(chunks),
            }
            chunks.append(chunk_info)

        # 只在非批处理模式下输出分块信息
        if not self.log_config.get("batch_processing_mode", False):
            print(f"   📊 分块信息: 总长度={total_length}, 分块大小={chunk_size}")
            for i, chunk in enumerate(chunks):
                print(
                    f"   📦 分块{i+1}: [{chunk['start_idx']}:{chunk['end_idx']}] (大小:{chunk['actual_size']})"
                )

        return chunks

    def _simulate_state_update(
        self, current_obs, executed_actions, gt_actions, chunk_info
    ):
        """
        模拟状态更新过程

        Args:
            current_obs: 当前观察状态 [B, obs_dim]
            executed_actions: 已执行的动作 [B, chunk_size, action_dim]
            gt_actions: 对应的GT动作 [B, chunk_size, action_dim]
            chunk_info: 当前分块信息

        Returns:
            updated_obs: 更新后的观察状态 [B, obs_dim]
        """
        batch_size = current_obs.shape[0]

        if self.rolling_prediction_config.get("state_reset_mode") == "gt_state":
            # 使用GT状态：从原始观察序列中提取对应时间步的状态
            next_timestep = chunk_info["end_idx"]  # 下一个chunk的起始时间步

            # 尝试从原始观察序列中获取对应时间步的状态
            if (
                hasattr(self, "_current_obs_sequence")
                and self._current_obs_sequence is not None
            ):
                seq_length = self._current_obs_sequence.shape[1]
                if next_timestep < seq_length:
                    # 使用GT观察状态
                    updated_obs = self._current_obs_sequence[
                        :, next_timestep, :
                    ].clone()

                    if self._should_log_detail("prediction"):
                        print(
                            f"   🔄 状态更新: 使用GT状态 t={next_timestep} (分块{chunk_info['chunk_id']+1})"
                        )
                        print(
                            f"       更新前状态范围: [{current_obs.min():.3f}, {current_obs.max():.3f}]"
                        )
                        print(
                            f"       更新后状态范围: [{updated_obs.min():.3f}, {updated_obs.max():.3f}]"
                        )
                else:
                    # 超出序列长度，保持当前状态
                    updated_obs = current_obs.clone()
                    if self._should_log_detail("prediction"):
                        print(
                            f"   🔄 状态更新: 超出序列长度，保持当前状态 (分块{chunk_info['chunk_id']+1})"
                        )
            else:
                # 没有原始观察序列，进行简化的状态更新
                updated_obs = self._simulate_simple_state_update(
                    current_obs, executed_actions, gt_actions, chunk_info
                )

        else:  # predicted_state
            # 使用预测状态：基于执行的动作更新状态
            updated_obs = self._simulate_action_based_state_update(
                current_obs, executed_actions, chunk_info
            )

            if self._should_log_detail("prediction"):
                print(
                    f"   🔄 状态更新: 使用预测状态更新 (分块{chunk_info['chunk_id']+1})"
                )
                print(
                    f"       更新前状态范围: [{current_obs.min():.3f}, {current_obs.max():.3f}]"
                )
                print(
                    f"       更新后状态范围: [{updated_obs.min():.3f}, {updated_obs.max():.3f}]"
                )

        return updated_obs

    def _simulate_simple_state_update(
        self, current_obs, executed_actions, gt_actions, chunk_info
    ):
        """简化的状态更新：基于动作差异调整状态"""
        updated_obs = current_obs.clone()

        # 计算动作执行的累积效应
        if executed_actions.shape[1] > 0:
            # 取最后一个时间步的动作作为状态更新的参考
            last_executed = executed_actions[:, -1, :]  # [B, action_dim]
            last_gt = gt_actions[:, -1, :]  # [B, action_dim]

            # 简化的状态更新：将动作差异的一部分反映到观察状态中
            action_diff = last_executed - last_gt

            # 只更新手部状态部分（假设观察的16:25是手部状态）
            if updated_obs.shape[1] >= 25:
                # 将动作差异的一小部分加到手部状态上（模拟累积误差）
                state_update_factor = 0.1  # 调整因子，避免状态变化过大
                updated_obs[:, 16:25] += action_diff[:, 16:25] * state_update_factor

        if self._should_log_detail("prediction"):
            print(f"   🔄 简化状态更新完成 (分块{chunk_info['chunk_id']+1})")

        return updated_obs

    def _simulate_action_based_state_update(
        self, current_obs, executed_actions, chunk_info
    ):
        """基于动作的状态更新"""
        updated_obs = current_obs.clone()

        # 基于执行的动作序列更新状态
        if executed_actions.shape[1] > 0:
            # 计算动作序列的累积效应
            cumulative_action = torch.mean(executed_actions, dim=1)  # [B, action_dim]

            # 模拟动作对状态的影响（这是一个简化的物理模拟）
            if updated_obs.shape[1] >= 25:
                # 关节状态更新
                updated_obs[:, :16] += cumulative_action[:, :16] * 0.05

                # 手部位置更新（假设动作中16:19是位置增量）
                updated_obs[:, 16:19] += cumulative_action[:, 16:19] * 0.02

                # 手部旋转更新（6D旋转的简化更新）
                updated_obs[:, 19:25] += cumulative_action[:, 19:25] * 0.02

        return updated_obs

    def _analyze_predictions(self, obs_dict, true_actions, target_pose):
        """分析模型预测序列 vs 真实动作序列"""
        if self._should_log_detail("prediction"):
            print("   🤖 开始模型预测序列分析...")
            print("   📝 模型工作模式: 基于当前观察预测完整动作序列")

        prediction_results = {
            "step_errors": [],
            "cumulative_errors": [],
            "action_component_errors": {},
            "prediction_stats": {},
            "scaled_prediction_stats": {},  # 新增：归一化域统计
            "target_pose_errors": {},  # 新增：target pose误差分析
        }

        if true_actions is None or true_actions.shape[1] == 0:
            if self._should_log_detail("prediction"):
                print("   ⚠️ 没有真实动作数据进行对比")
            return prediction_results

        # 获取轨迹信息
        batch_size, seq_length, action_dim = true_actions.shape

        # 一次性预测整个动作序列
        if self._should_log_detail("prediction"):
            print("   🤖 模型预测整个动作序列...")
        elif self.log_config.get("batch_processing_mode", False):
            # 批处理模式下的简化输出
            pass  # 不输出详细信息

        # 获取观察（取第一个时间步作为输入，因为模型基于当前观察预测整个序列）
        policy_obs = obs_dict["policy"]  # 可能是 [B, 34] 或 [B, T, 34]

        # 🆕 从完整序列中提取单步观察（与训练时一致）
        if policy_obs.dim() == 3:  # [B, T, 34] 完整序列
            current_obs = policy_obs[:, 0, :]  # [B, 34] 取第一个时间步
            # 保存完整序列供rolling prediction使用
            self._current_obs_sequence = policy_obs
        else:  # [B, 34] 已经是单步
            current_obs = policy_obs
            self._current_obs_sequence = None

        current_target_pose = target_pose  # [B, 25]

        # 🆕 应用与训练时相同的数据预处理
        if self._should_log_detail("preprocessing"):
            print("   🔧 应用训练时的数据预处理...")
        # --- 坐标系转换 ---
        # 导入将在合并的坐标转换函数中进行

        # 提取各部分状态（世界坐标系）
        current_hand_pos = current_obs[:, 16:19]
        current_hand_rot_6d = current_obs[:, 19:25]

        # 🆕 处理target_pose的维度问题
        if current_target_pose.dim() == 3:  # [B, T, 25] -> 取第一个时间步
            target_hand_pos = current_target_pose[:, 0, 16:19]  # [B, 3]
            target_hand_rot_6d = current_target_pose[:, 0, 19:25]  # [B, 6]
        else:  # [B, 25]
            target_hand_pos = current_target_pose[:, 16:19]
            target_hand_rot_6d = current_target_pose[:, 19:25]

        obj_pos_world = current_obs[:, 25:28]
        obj_rot_6d_world = current_obs[:, 28:34]

        # 🆕 验证6D旋转数据的完整性并进行坐标转换
        if self._should_log_detail("preprocessing"):
            print("   🔍 6D旋转数据验证:")
            print(f"      current_hand_rot_6d: {current_hand_rot_6d.shape}")
            print(f"      target_hand_rot_6d: {target_hand_rot_6d.shape}")
            print(f"      obj_rot_6d_world: {obj_rot_6d_world.shape}")

        # 🆕 检查6D旋转数据是否有效
        def validate_6d_rotation(rot_6d, name):
            if rot_6d.shape[-1] != 6:
                raise ValueError(
                    f"{name} 6D旋转数据维度错误: {rot_6d.shape} - 期望最后一维为6"
                )
            if torch.any(torch.isnan(rot_6d)):
                raise ValueError(f"{name} 6D旋转数据包含NaN值")
            if torch.all(rot_6d == 0):
                if self._should_log_detail("preprocessing"):
                    print(f"   ⚠️ {name} 6D旋转数据全为零，使用默认单位旋转")
                # 设置默认的6D旋转 [1,0,0,0,1,0] (单位矩阵的前两列)
                rot_6d = rot_6d.clone()  # 确保可以修改
                rot_6d[:, 0] = 1.0  # 第一列x分量
                rot_6d[:, 4] = 1.0  # 第二列y分量
            return rot_6d

        # 🚨 严格验证 - 失败时直接报错而不是静默处理
        current_hand_rot_6d = validate_6d_rotation(current_hand_rot_6d, "current_hand")
        target_hand_rot_6d = validate_6d_rotation(target_hand_rot_6d, "target_hand")
        obj_rot_6d_world = validate_6d_rotation(obj_rot_6d_world, "object")

        # 🆕 数据为原始数据，需要转换为物体中心坐标系
        if self.preprocess_config.get("enable_object_centric", False):
            # 如果需要坐标系转换（向后兼容）
            # 检查object_centric_transformer是否可用
            if (
                hasattr(self, "object_centric_transformer")
                and self.object_centric_transformer is not None
            ):
                (
                    cur_pos_obj,
                    cur_rot_6d_obj,
                    tgt_pos_obj,
                    tgt_rot_6d_obj,
                    object_state,
                    true_actions_transformed,
                ) = self._apply_training_coordinate_transform_with_gt(
                    current_hand_pos,
                    current_hand_rot_6d,
                    target_hand_pos,
                    target_hand_rot_6d,
                    obj_pos_world,
                    obj_rot_6d_world,
                    true_actions,
                )

                # 将转换结果写回观察 / 目标张量
                current_obs[:, 16:19] = cur_pos_obj
                current_obs[:, 19:25] = cur_rot_6d_obj

                # 🆕 处理target_pose写回时的维度问题
                if current_target_pose.dim() == 3:  # [B, T, 25]
                    current_target_pose[:, 0, 16:19] = tgt_pos_obj
                    current_target_pose[:, 0, 19:25] = tgt_rot_6d_obj
                else:  # [B, 25]
                    current_target_pose[:, 16:19] = tgt_pos_obj
                    current_target_pose[:, 19:25] = tgt_rot_6d_obj

                # 覆写物体状态（位置0，单位旋转6D）
                current_obs[:, 25:34] = object_state
                if self._should_log_detail("preprocessing"):
                    print(f"   📊 物体状态: {object_state}")

                if self._should_log_detail("preprocessing"):
                    print(" 🌍 已应用物体中心坐标变换")
                    print(
                        f"   📊 手部位置范围: [{cur_pos_obj.min():.3f}, {cur_pos_obj.max():.3f}]，目标位置范围: [{tgt_pos_obj.min():.3f}, {tgt_pos_obj.max():.3f}]"
                    )
            else:
                if self._should_log_detail("preprocessing"):
                    print(" ⚠️ object_centric_transformer不可用，跳过坐标转换")
                true_actions_transformed = true_actions
        else:
            # 数据已预处理为物体中心坐标系，直接使用
            true_actions_transformed = true_actions
            if self._should_log_detail("preprocessing"):
                print(" ✅ 数据已预处理为物体中心坐标系，跳过坐标转换")
                print(
                    f"   📊 手部位置范围: [{current_hand_pos.min():.3f}, {current_hand_pos.max():.3f}]"
                )
                print(
                    f"   📊 目标位置范围: [{target_hand_pos.min():.3f}, {target_hand_pos.max():.3f}]"
                )

        # 2. 应用标准化 / 归一化
        if (
            self.preprocess_config.get("enable_normalization", False)
            and self.enable_action_scaling
            and self.action_scaling_processor is not None
        ):
            try:
                if self.enable_separated_normalization:
                    # 分离归一化模式
                    if hasattr(
                        self.action_scaling_processor,
                        "normalize_observations_separated",
                    ):

                        current_obs = self.action_scaling_processor.normalize_observations_separated(
                            current_obs
                        )

                        if self._should_log_detail("preprocessing"):
                            print(
                                f"   📊 观察归一化后范围: [{current_obs.min():.3f}, {current_obs.max():.3f}]"
                            )
                    else:
                        if self._should_log_detail("preprocessing"):
                            print(
                                "   ⚠️ 处理器缺少 normalize_observations_separated，跳过观察归一化"
                            )

                    if hasattr(self.action_scaling_processor, "normalize_target_pose"):
                        current_target_pose = (
                            self.action_scaling_processor.normalize_target_pose(
                                current_target_pose
                            )
                        )
                        if self._should_log_detail("preprocessing"):
                            print(
                                f"   📊 目标位姿归一化后范围: [{current_target_pose.min():.3f}, {current_target_pose.max():.3f}]"
                            )
                    else:
                        if self._should_log_detail("preprocessing"):
                            print(
                                "   ⚠️ 处理器缺少 normalize_target_pose，跳过目标位姿归一化"
                            )
                else:
                    raise ValueError("未打开分离归一化模式")

            except Exception as e:
                if self._should_log_detail("preprocessing"):
                    print(f"   ⚠️ 归一化失败: {e}")
        else:
            if self._should_log_detail("preprocessing"):
                if not self.preprocess_config.get("enable_normalization", False):
                    print("   ✅ 数据已预处理，跳过归一化")
                elif not self.enable_action_scaling:
                    print("   ✅ 动作缩放已禁用，跳过归一化")
                elif self.action_scaling_processor is None:
                    print("   ⚠️ 动作缩放处理器不可用，跳过归一化")

        # 🆕 确保target_pose维度正确 - 模型期望2维输入
        model_target_pose = current_target_pose
        if current_target_pose.dim() == 3:  # [B, T, 25] -> [B, 25]
            model_target_pose = current_target_pose[:, 0, :]  # 取第一个时间步
            if self._should_log_detail("preprocessing"):
                print(
                    f"   🔧 调整target_pose维度: {current_target_pose.shape} -> {model_target_pose.shape}"
                )

        if self._should_log_detail("preprocessing"):
            print(
                f"   📊 预处理后数据维度: obs={current_obs.shape}, target_pose={model_target_pose.shape}"
            )

        # 模型预测完整动作序列
        with torch.no_grad():
            pred_actions_sequence = self.model.forward(
                current_obs, target_pose=model_target_pose
            )

        if self._should_log_detail("prediction"):
            print(f"   📊 模型输出维度: {pred_actions_sequence.shape}")

        # 确保预测序列是3维：[B, seq_length, action_dim]
        if pred_actions_sequence.dim() == 2 and self._should_log_detail("prediction"):
            print(f"   ⚠️ 意外的2维输出维度: {pred_actions_sequence.shape}")

        # 🆕 对预测动作进行后处理（6D正交化 + 反缩放 + 坐标转换）
        if self._should_log_detail("postprocessing"):
            print("   🔄 开始预测动作后处理...")
            print(
                f"      原始预测范围: [{pred_actions_sequence.min():.4f}, {pred_actions_sequence.max():.4f}]"
            )

        pred_actions_processed = self._postprocess_predicted_actions(
            pred_actions_sequence
        )

        if self._should_log_detail("postprocessing"):
            print("   ✅ 预测动作后处理完成")
            print(
                f"      处理后预测范围: [{pred_actions_processed.min():.4f}, {pred_actions_processed.max():.4f}]"
            )
            if pred_actions_processed.dim() >= 2:
                pred_pos_processed = pred_actions_processed[:, :, 16:19]
                print(
                    f"      处理后位置范围: [{pred_pos_processed.min():.4f}, {pred_pos_processed.max():.4f}]"
                )
                print(f"      处理后位置均值: {pred_pos_processed.mean(dim=(0,1))}")

        # 逐步对比预测序列与真实序列
        step_errors = []
        pred_seq_length = (
            pred_actions_processed.shape[1] if pred_actions_processed.dim() >= 2 else 1
        )

        if self._should_log_detail("prediction"):
            print(
                f"   🔄 开始逐步对比: 预测序列长度={pred_seq_length}, 真实序列长度={seq_length}"
            )
            print("   📊 误差计算坐标系: 物体坐标系（预测和Ground Truth均已转换）")

            # 🔍 验证坐标系一致性
            if pred_actions_processed.dim() >= 2:
                pred_pos_sample = pred_actions_processed[:, 0, 16:19]  # 预测位置样本
                gt_pos_sample = true_actions_transformed[:, 0, 16:19]  # GT位置样本
                print("   📊 坐标系验证 (第一个时间步):")
                print(
                    f"      预测位置范围: [{pred_pos_sample.min():.4f}, {pred_pos_sample.max():.4f}]"
                )
                print(
                    f"      GT位置范围: [{gt_pos_sample.min():.4f}, {gt_pos_sample.max():.4f}]"
                )
                print(
                    f"      位置差异: {(pred_pos_sample - gt_pos_sample).abs().mean():.4f}"
                )

                # 检查是否在合理的物体坐标系范围内
                if (
                    gt_pos_sample.abs().max() > 1.0
                ):  # 物体坐标系下位置通常应该在[-1,1]范围内
                    print("   ⚠️ 警告: GT位置数据可能仍在世界坐标系，转换可能未生效")
                if pred_pos_sample.abs().max() > 1.0:
                    print("   ⚠️ 警告: 预测位置数据可能仍在世界坐标系，后处理可能有问题")

        # 🆕 收集所有时间步的组件误差用于计算平均值
        all_joint_errors = []
        all_pos_errors = []
        all_rot_errors = []

        for step in range(min(seq_length, pred_seq_length)):
            # 从后处理的预测序列中取出对应步骤的动作
            if pred_actions_processed.dim() >= 2:
                pred_action = pred_actions_processed[:, step, :]  # [B, 25]
            else:
                pred_action = pred_actions_processed  # 单步情况

            # 获取真实动作（使用转换后的物体坐标系数据）
            true_action = true_actions_transformed[:, step, :]  # [B, 25] 物体坐标系

            # 计算误差
            if isinstance(pred_action, torch.Tensor) and isinstance(
                true_action, torch.Tensor
            ):
                # 确保设备一致性
                if pred_action.device != true_action.device:
                    true_action = true_action.to(pred_action.device)

                # 计算各种误差
                mse_error = torch.mean((pred_action - true_action) ** 2, dim=1)
                mae_error = torch.mean(torch.abs(pred_action - true_action), dim=1)

                step_errors.append(
                    {
                        "step": step,
                        "mse": mse_error.mean().item(),
                        "mae": mae_error.mean().item(),
                        "max_error": torch.max(
                            torch.abs(pred_action - true_action)
                        ).item(),
                    }
                )

                # 分析动作分量误差 - 为每个时间步都计算，用于分析序列预测的特点
                joint_error = torch.mean(
                    torch.abs(pred_action[:, :16] - true_action[:, :16])
                )
                pos_error = torch.mean(
                    torch.abs(pred_action[:, 16:19] - true_action[:, 16:19])
                )

                # 🔧 修复：使用四元数角度距离计算旋转误差，与target_pose_accuracy保持一致
                try:
                    pred_rot_6d = pred_action[:, 19:25]  # [B, 6]
                    true_rot_6d = true_action[:, 19:25]  # [B, 6]

                    # 使用与_calculate_target_pose_errors相同的方法
                    rotation_distance_rad = calculate_6d_rotation_distance(
                        pred_rot_6d, true_rot_6d
                    )
                    rotation_distance_deg = rotation_distance_rad * 180.0 / torch.pi
                    rot_error = torch.mean(rotation_distance_deg)  # 度
                except Exception as e:
                    # 回退到6D欧氏距离
                    rot_error = torch.mean(
                        torch.abs(pred_action[:, 19:25] - true_action[:, 19:25])
                    )
                    if self._should_log_detail("prediction"):
                        print(f"   ⚠️ 四元数距离计算失败，使用6D欧氏距离: {e}")

                # 🆕 收集所有时间步的组件误差
                all_joint_errors.append(joint_error.item())
                all_pos_errors.append(pos_error.item())
                all_rot_errors.append(rot_error.item())

                # 为每步添加组件误差信息，便于分析序列预测模式
                step_errors[-1].update(
                    {
                        "joint_mae": joint_error.item(),
                        "position_mae": pos_error.item() * 1000,  # 转换为mm
                        "rotation_mae": rot_error.item(),
                    }
                )

        # 🆕 计算所有时间步组件误差的平均值
        if all_joint_errors:
            prediction_results["action_component_errors"] = {
                "joint_mae": np.mean(all_joint_errors),
                "position_mae": np.mean(all_pos_errors) * 1000,  # 转换为mm
                "rotation_mae": np.mean(all_rot_errors),
            }

        prediction_results["step_errors"] = step_errors

        # 计算整体统计
        if step_errors:
            all_mse = [e["mse"] for e in step_errors]
            all_mae = [e["mae"] for e in step_errors]

            prediction_results["prediction_stats"] = {
                "avg_mse": np.mean(all_mse),
                "avg_mae": np.mean(all_mae),
                "mse_std": np.std(all_mse),
                "mae_std": np.std(all_mae),
                "total_steps": len(step_errors),
            }

            if self._should_log_detail("prediction"):
                print(
                    f"   📊 预测统计: 平均MAE={np.mean(all_mae):.4f}, 平均MSE={np.mean(all_mse):.4f}"
                )

        # === 归一化域误差计算（直接比较 raw 预测 与 scale 后真实动作） ===
        calc_scaled_errors = (
            self.enable_action_scaling and self.action_scaling_processor is not None
        )
        scaled_mse_list: List[float] = []
        scaled_mae_list: List[float] = []
        scaled_mse_list_valid: List[float] = []  # 排除恒定通道
        scaled_mae_list_valid: List[float] = []

        if calc_scaled_errors:
            try:
                # 使用object frame下的GT进行归一化误差计算
                scaled_true_seq = self.action_scaling_processor.scale_actions(
                    true_actions_transformed
                )

                # 如果处理器要求 2D 输入，可尝试自动展平
                if scaled_true_seq.shape != true_actions_transformed.shape:
                    # 回退：展平时间维度再 reshape
                    b, tlen, d = true_actions_transformed.shape
                    scaled_true_seq = self.action_scaling_processor.scale_actions(
                        true_actions_transformed.view(-1, d)
                    ).view(b, tlen, d)

                min_len = min(seq_length, pred_seq_length)

                for step_i in range(min_len):
                    raw_pred_step = (
                        pred_actions_sequence[:, step_i, :]
                        if pred_actions_sequence.dim() == 3
                        else pred_actions_sequence
                    )
                    true_scaled_step = scaled_true_seq[:, step_i, :]

                    mse_s = torch.mean((raw_pred_step - true_scaled_step) ** 2, dim=1)
                    mae_s = torch.mean(
                        torch.abs(raw_pred_step - true_scaled_step), dim=1
                    )

                    scaled_mse_list.append(mse_s.mean().item())
                    scaled_mae_list.append(mae_s.mean().item())

                    # 如果存在小sigma mask，则过滤这些通道重新计算误差
                    if hasattr(self, "_small_std_mask") and self._small_std_mask.any():
                        valid_mask = (~self._small_std_mask).to(raw_pred_step.device)
                        mse_s_valid = torch.mean(
                            (
                                raw_pred_step[:, valid_mask]
                                - true_scaled_step[:, valid_mask]
                            )
                            ** 2,
                            dim=1,
                        )
                        mae_s_valid = torch.mean(
                            torch.abs(
                                raw_pred_step[:, valid_mask]
                                - true_scaled_step[:, valid_mask]
                            ),
                            dim=1,
                        )
                        scaled_mse_list_valid.append(mse_s_valid.mean().item())
                        scaled_mae_list_valid.append(mae_s_valid.mean().item())

                    # 🆕 计算归一化域的组件误差
                    joint_error_scaled = torch.mean(
                        torch.abs(raw_pred_step[:, :16] - true_scaled_step[:, :16])
                    )
                    pos_error_scaled = torch.mean(
                        torch.abs(raw_pred_step[:, 16:19] - true_scaled_step[:, 16:19])
                    )
                    rot_error_scaled = torch.mean(
                        torch.abs(raw_pred_step[:, 19:25] - true_scaled_step[:, 19:25])
                    )

                    # 将结果写入对应的 step_errors
                    if step_i < len(step_errors):
                        step_errors[step_i].update(
                            {
                                "mse_scaled": mse_s.mean().item(),
                                "mae_scaled": mae_s.mean().item(),
                                # 🆕 添加归一化域的组件误差
                                "joint_mae_scaled": joint_error_scaled.item(),
                                "position_mae_scaled": pos_error_scaled.item(),
                                "rotation_mae_scaled": rot_error_scaled.item(),
                                **(
                                    {
                                        "mse_scaled_noConst": mse_s_valid.mean().item(),
                                        "mae_scaled_noConst": mae_s_valid.mean().item(),
                                    }
                                    if "mse_s_valid" in locals()
                                    else {}
                                ),
                            }
                        )

                # 汇总总体统计
                if scaled_mse_list:
                    prediction_results["scaled_prediction_stats"] = {
                        "avg_mse_scaled": np.mean(scaled_mse_list),
                        "avg_mae_scaled": np.mean(scaled_mae_list),
                        "mse_scaled_std": np.std(scaled_mse_list),
                        "mae_scaled_std": np.std(scaled_mae_list),
                        **(
                            {
                                "avg_mse_scaled_noConst": (
                                    np.mean(scaled_mse_list_valid)
                                    if scaled_mse_list_valid
                                    else None
                                ),
                                "avg_mae_scaled_noConst": (
                                    np.mean(scaled_mae_list_valid)
                                    if scaled_mae_list_valid
                                    else None
                                ),
                            }
                        ),
                    }

                    if self._should_log_detail("prediction"):
                        print(
                            f"   📊 标准化域统计: 平均MAE={np.mean(scaled_mae_list):.4f}, 平均MSE={np.mean(scaled_mse_list):.4f}"
                        )

                    if scaled_mae_list_valid and self._should_log_detail("prediction"):
                        ratio = np.mean(all_mae) / np.mean(scaled_mae_list_valid)
                        print(f"   📊 Phys / Norm(noConst) MAE ratio: {ratio:.2f}")

            except Exception as e:
                if self._should_log_detail("prediction"):
                    print(f"   ⚠️ 归一化域误差计算失败: {e}")
                calc_scaled_errors = False

        # 🆕 计算target pose误差
        if target_pose is not None:
            try:
                # 🔧 修复：使用预测的最后一个时间步作为target pose预测
                # 因为target pose代表序列执行后要达到的最终目标状态
                if pred_actions_processed.dim() >= 2:
                    predicted_target_pose = pred_actions_processed[
                        :, -1, :
                    ]  # [B, 25] 使用最后一步
                else:
                    predicted_target_pose = pred_actions_processed  # [B, 25]

                # 🆕 添加调试信息
                if self._should_log_detail("prediction"):
                    print("   🔍 Target Pose误差计算调试:")
                    print(
                        f"      predicted_target_pose形状: {predicted_target_pose.shape}"
                    )
                    print(f"      target_pose形状: {target_pose.shape}")
                    print(
                        f"      predicted_target_pose最后几维: {predicted_target_pose.shape[-1]}"
                    )
                    print(f"      target_pose最后几维: {target_pose.shape[-1]}")

                target_pose_errors = self._calculate_target_pose_errors(
                    predicted_target_pose, target_pose
                )
                prediction_results["target_pose_errors"] = target_pose_errors

                if self._should_log_detail("prediction"):
                    print("   ✅ Target Pose误差计算完成 (使用序列最后一步)")

            except Exception as e:
                if self._should_log_detail("prediction"):
                    print(f"   ⚠️ Target Pose误差计算失败: {e}")
                prediction_results["target_pose_errors"] = {}

        # 生成详细的调试表格
        if (
            hasattr(self, "enable_detailed_debug")
            and self.enable_detailed_debug
            and self._should_log_detail("debug")
        ):
            self._print_detailed_trajectory_debug_table(
                obs_dict,
                true_actions_transformed,
                target_pose,
                pred_actions_processed,
                step_errors,
            )

        # 🆕 滚动预测分析
        if self.rolling_prediction_config.get("enable_rolling_prediction", False):
            rolling_results = self._analyze_rolling_predictions(
                obs_dict, true_actions_transformed, target_pose
            )
            prediction_results["rolling_prediction"] = rolling_results

            # 对比分析
            if self.rolling_prediction_config.get("compare_with_standard", True):
                comparison = self._compare_prediction_strategies(
                    prediction_results, rolling_results
                )
                prediction_results["strategy_comparison"] = comparison

        # === 新增：训练时风格的序列级L1损失 ===
        try:
            # 保证维度一致
            if pred_actions_sequence.dim() == 2:
                pred_seq = pred_actions_sequence.unsqueeze(1)
            else:
                pred_seq = pred_actions_sequence
            min_seq_len = min(pred_seq.shape[1], true_actions_transformed.shape[1])
            pred_seq = pred_seq[:, :min_seq_len, :]
            true_seq = true_actions_transformed[:, :min_seq_len, :]

            # 与训练时完全一致的序列L1损失计算
            # 1. 使用 reduction="none" 计算每个元素的L1损失
            all_l1 = torch.nn.functional.l1_loss(pred_seq, true_seq, reduction="none")

            # 2. 创建填充掩码（假设没有填充，所有位置都是有效的）
            # 如果数据中有填充，这里需要根据实际情况调整
            is_pad = torch.zeros(
                pred_seq.shape[:2], dtype=torch.bool, device=pred_seq.device
            )

            # 3. 应用填充掩码并求平均（与训练时一致）
            l1_loss = (all_l1 * ~is_pad.unsqueeze(-1)).mean().item()
            prediction_results["training_style_sequence_loss"] = l1_loss

            if self._should_log_detail("prediction"):
                print(f"   📊 训练时风格序列L1损失: {l1_loss}")
                print(f"   📊 预测范围: [{pred_seq.min():.4f}, {pred_seq.max():.4f}]")
                print(f"   📊 GT范围: [{true_seq.min():.4f}, {true_seq.max():.4f}]")
                print(f"   📊 序列形状: {pred_seq.shape}, 填充掩码形状: {is_pad.shape}")
        except Exception as e:
            prediction_results["training_style_sequence_loss"] = None
            if self._should_log_detail("prediction"):
                print(f"   ⚠️ 训练时风格序列损失计算失败: {e}")

        return prediction_results

    def _analyze_rolling_predictions(self, obs_dict, true_actions, target_pose):
        """
        真实滚动预测策略分析

        核心思想：使用真实的中间观察状态作为每个chunk的预测起点，
        避免状态模拟的不准确性，体现滚动预测的真正优势。

        Args:
            obs_dict: 观察数据字典
            true_actions: 真实动作序列 [B, seq_length, action_dim]
            target_pose: 目标位姿 [B, pose_dim]

        Returns:
            Dict: 滚动预测分析结果
        """
        # 只在非批处理模式下输出rolling prediction开始信息
        if not self.log_config.get("batch_processing_mode", False):
            print("   🔄 开始真实滚动预测策略分析...")

        rolling_results = {
            "chunks": [],
            "chunk_errors": [],
            "cumulative_predictions": [],
            "rolling_stats": {},
            "comparison_with_standard": {},
        }

        if true_actions is None or true_actions.shape[1] == 0:
            if not self.log_config.get("batch_processing_mode", False):
                print("   ⚠️ 没有真实动作数据进行滚动预测分析")
            return rolling_results

        # 获取配置参数
        chunk_size = self.rolling_prediction_config.get("chunk_size", 5)

        # 获取序列信息
        batch_size, seq_length, action_dim = true_actions.shape

        # 🆕 改进方案：基于初始观察进行真实滚动预测
        if "policy" not in obs_dict:
            if not self.log_config.get("batch_processing_mode", False):
                print("   ⚠️ 缺少观察数据，无法进行滚动预测")
            return rolling_results

        obs_data = obs_dict["policy"]  # [B, obs_dim] 或 [B, seq_length, obs_dim]

        # 🆕 智能观察模式选择
        observation_mode = self.rolling_prediction_config.get(
            "observation_mode", "auto"
        )

        if obs_data.dim() == 2:
            # [B, obs_dim] - 单时间步观察
            initial_obs = obs_data  # [B, obs_dim]
            obs_sequence = None
            effective_mode = "cumulative"  # 强制使用累积模式
            if not self.log_config.get("batch_processing_mode", False):
                print(f"   📊 单步观察，使用累积模式: {initial_obs.shape}")
        elif obs_data.dim() == 3:
            # [B, seq_length, obs_dim] - 时间序列观察
            obs_sequence = obs_data
            initial_obs = obs_sequence[:, 0, :]  # [B, obs_dim] 初始观察
            if obs_sequence.shape[1] < seq_length:
                if not self.log_config.get("batch_processing_mode", False):
                    print(
                        f"   ⚠️ 观察序列长度({obs_sequence.shape[1]})小于动作序列长度({seq_length})"
                    )
                return rolling_results

            # 根据配置选择观察模式
            if observation_mode == "auto":
                effective_mode = "sequence"  # 默认使用序列模式
            elif observation_mode == "sequence":
                effective_mode = "sequence"
            elif observation_mode == "cumulative":
                effective_mode = "cumulative"
                obs_sequence = None  # 强制不使用序列
            else:
                effective_mode = "sequence"  # 默认回退

            if not self.log_config.get("batch_processing_mode", False):
                if effective_mode == "sequence":
                    print(f"   📊 序列模式: 使用真实观察序列 {obs_sequence.shape}")
                else:
                    print(f"   📊 累积模式: 使用状态传播 {initial_obs.shape}")
        else:
            if not self.log_config.get("batch_processing_mode", False):
                print(f"   ⚠️ 观察数据维度异常: {obs_data.shape}")
            return rolling_results

        # 只在非批处理模式下输出配置信息
        if not self.log_config.get("batch_processing_mode", False):
            print(
                f"   📊 滚动预测配置: 分块大小={chunk_size}, 序列长度={seq_length}, 模式={effective_mode}"
            )
            if obs_sequence is not None:
                print(f"   🔍 观察数据: 完整序列 {obs_sequence.shape}")
            else:
                print(f"   🔍 观察数据: 单步状态 {initial_obs.shape}")

        # 计算分块信息
        chunks = self._calculate_rolling_chunks(seq_length, chunk_size)
        rolling_results["chunks"] = chunks

        # 初始化累积预测结果和当前状态
        all_rolling_predictions = []
        current_obs = initial_obs.clone()  # [B, obs_dim] 当前观察状态

        # 🆕 逐块进行滚动预测，实现真实的状态传播
        for chunk_info in chunks:
            start_idx = chunk_info["start_idx"]
            end_idx = chunk_info["end_idx"]
            actual_size = chunk_info["actual_size"]

            # 只在非批处理模式下输出分块处理信息
            if not self.log_config.get("batch_processing_mode", False):
                print(
                    f"   📦 处理分块 {chunk_info['chunk_id']+1}/{len(chunks)}: [{start_idx}:{end_idx}] (大小:{actual_size})"
                )

            # 🆕 选择观察策略
            if obs_sequence is not None:
                # 策略A：使用真实观察序列（理想情况）
                chunk_obs = obs_sequence[:, start_idx, :].clone()  # [B, obs_dim]
                if not self.log_config.get("batch_processing_mode", False):
                    print(f"       🎯 使用真实观察: t={start_idx}")
            else:
                # 策略B：使用当前累积状态（实际情况）
                chunk_obs = current_obs.clone()  # [B, obs_dim]
                if not self.log_config.get("batch_processing_mode", False):
                    print("       🎯 使用累积状态预测")

            # 🆕 确保target_pose维度正确 - 模型期望2维输入
            model_target_pose = target_pose
            if target_pose.dim() == 3:  # [B, T, 25] -> [B, 25]
                model_target_pose = target_pose[:, 0, :]  # 取第一个时间步

            # 从当前观察状态预测动作序列
            with torch.no_grad():
                if hasattr(self.model, "predict_action"):
                    full_pred_sequence = self.model.predict_action(
                        chunk_obs, target_pose=model_target_pose
                    )
                elif hasattr(self.model, "forward"):
                    full_pred_sequence = self.model.forward(
                        chunk_obs, target_pose=model_target_pose
                    )
                else:
                    full_pred_sequence = self.model(
                        chunk_obs, target_pose=model_target_pose
                    )

            # 后处理预测动作
            full_pred_processed = self._postprocess_predicted_actions(
                full_pred_sequence
            )

            # 🆕 提取当前chunk需要的预测部分
            if full_pred_processed.dim() >= 2:
                # 从完整预测中取前actual_size步
                relative_end_idx = min(actual_size, full_pred_processed.shape[1])
                chunk_predictions = full_pred_processed[
                    :, :relative_end_idx, :
                ]  # [B, actual_size, action_dim]
            else:
                # 处理单步预测的情况
                chunk_predictions = full_pred_processed.unsqueeze(
                    1
                )  # [B, 1, action_dim]

            # 存储当前分块的预测
            all_rolling_predictions.append(chunk_predictions)

            # 计算当前分块的误差
            chunk_true_actions = true_actions[
                :, start_idx:end_idx, :
            ]  # [B, actual_size, action_dim]
            chunk_errors = []

            for step in range(actual_size):
                if step < chunk_predictions.shape[1]:
                    pred_action = chunk_predictions[:, step, :]
                    true_action = chunk_true_actions[:, step, :]

                    step_error = torch.mean(torch.abs(pred_action - true_action)).item()
                    chunk_errors.append(step_error)

            rolling_results["chunk_errors"].append(
                {
                    "chunk_id": chunk_info["chunk_id"],
                    "start_idx": start_idx,
                    "end_idx": end_idx,
                    "errors": chunk_errors,
                    "avg_error": np.mean(chunk_errors) if chunk_errors else 0.0,
                }
            )

            # 🆕 状态更新：模拟执行预测动作对观察状态的影响
            if obs_sequence is None:
                # 只有在使用累积状态时才需要状态更新
                current_obs = self._update_observation_state(
                    current_obs, chunk_predictions, chunk_true_actions, chunk_info
                )

        # 拼接所有滚动预测结果
        if all_rolling_predictions:
            rolling_predictions_concat = torch.cat(
                all_rolling_predictions, dim=1
            )  # [B, seq_length, action_dim]
            rolling_results["cumulative_predictions"] = rolling_predictions_concat

            # 计算整体滚动预测统计
            overall_rolling_errors = []
            for step in range(min(seq_length, rolling_predictions_concat.shape[1])):
                pred_action = rolling_predictions_concat[:, step, :]
                true_action = true_actions[:, step, :]
                step_error = torch.mean(torch.abs(pred_action - true_action)).item()
                overall_rolling_errors.append(step_error)

            rolling_results["rolling_stats"] = {
                "avg_error": np.mean(overall_rolling_errors),
                "std_error": np.std(overall_rolling_errors),
                "max_error": np.max(overall_rolling_errors),
                "min_error": np.min(overall_rolling_errors),
                "step_errors": overall_rolling_errors,
            }

            # 只在非批处理模式下输出rolling prediction统计结果
            if not self.log_config.get("batch_processing_mode", False):
                print("   📊 滚动预测整体统计:")
                print(
                    f"     平均误差: {rolling_results['rolling_stats']['avg_error']:.4f}"
                )
                print(
                    f"     误差标准差: {rolling_results['rolling_stats']['std_error']:.4f}"
                )
                print(
                    f"     最大误差: {rolling_results['rolling_stats']['max_error']:.4f}"
                )
                print(
                    f"     最小误差: {rolling_results['rolling_stats']['min_error']:.4f}"
                )
                print(f"     总处理块数: {len(chunks)}")

        return rolling_results

    def _compare_prediction_strategies(self, standard_results, rolling_results):
        """
        对比标准预测和滚动预测的结果

        Args:
            standard_results: 标准预测结果
            rolling_results: 滚动预测结果

        Returns:
            Dict: 对比分析结果
        """
        comparison = {
            "standard_avg_error": None,
            "rolling_avg_error": None,
            "improvement_ratio": None,
            "error_reduction": None,
            "step_by_step_comparison": [],
        }

        # 提取标准预测的平均误差
        if (
            "prediction_stats" in standard_results
            and "avg_mae" in standard_results["prediction_stats"]
        ):
            comparison["standard_avg_error"] = standard_results["prediction_stats"][
                "avg_mae"
            ]

        # 提取滚动预测的平均误差
        if (
            "rolling_stats" in rolling_results
            and "avg_error" in rolling_results["rolling_stats"]
        ):
            comparison["rolling_avg_error"] = rolling_results["rolling_stats"][
                "avg_error"
            ]

        # 计算改进比率
        if (
            comparison["standard_avg_error"] is not None
            and comparison["rolling_avg_error"] is not None
        ):
            standard_err = comparison["standard_avg_error"]
            rolling_err = comparison["rolling_avg_error"]

            if standard_err > 0:
                comparison["improvement_ratio"] = rolling_err / standard_err
                comparison["error_reduction"] = (
                    (standard_err - rolling_err) / standard_err * 100
                )

                if self._should_log_detail("prediction"):
                    print("   📊 预测策略对比:")
                    print(f"     标准预测平均误差: {standard_err:.4f}")
                    print(f"     滚动预测平均误差: {rolling_err:.4f}")
                    print(f"     改进比率: {comparison['improvement_ratio']:.3f}")
                    print(f"     误差减少: {comparison['error_reduction']:.1f}%")

        return comparison

    def _convert_obs_dict_to_separated_format(self, obs_dict, target_hand="left"):
        """
        将obs_dict格式转换为34维分离格式的向量

        Args:
            obs_dict: 观察字典，包含lefthand, righthand, object等
            target_hand: 目标手部 ("left", "right")

        Returns:
            obs_34d: [batch_size, 34] 的张量
        """
        try:
            # 根据target_hand选择正确的手部数据
            if target_hand == "left":
                current_hand_joints = obs_dict["lefthand"]["joints"]
                current_hand_handpose = obs_dict["lefthand"]["handpose"]
            elif target_hand == "right":
                current_hand_joints = obs_dict["righthand"]["joints"]
                current_hand_handpose = obs_dict["righthand"]["handpose"]
            else:
                raise ValueError(
                    f"不支持的target_hand: {target_hand}, 必须是 'left' 或 'right'"
                )

            object_data = obs_dict["object"]

            # 拼接为34维向量：16关节 + 9位姿 + 9物体 = 34维
            obs_34d = torch.cat(
                [
                    current_hand_joints,  # [B, 16]
                    current_hand_handpose,  # [B, 9]
                    object_data,  # [B, 9]
                ],
                dim=-1,
            )  # [B, 34]

            if self._should_log_detail("structure"):
                print(
                    f"     🔄 obs_dict转换为34维: {current_hand_joints.shape} -> {obs_34d.shape}, target_hand={target_hand}"
                )
            return obs_34d

        except Exception as e:
            if self._should_log_detail("structure"):
                print(f"     ❌ obs_dict转换为34维格式失败: {e}")
                print(f"     obs_dict键: {list(obs_dict.keys())}")
                print(f"     target_hand: {target_hand}")
                if "lefthand" in obs_dict:
                    print(f"     lefthand键: {list(obs_dict['lefthand'].keys())}")
                if "righthand" in obs_dict:
                    print(f"     righthand键: {list(obs_dict['righthand'].keys())}")
            raise

    def _postprocess_predicted_actions(self, pred_actions):
        """
        对模型预测的动作进行后处理

        包括：
        1. 6D旋转正交化 (将6D旋转表示转换为正交旋转矩阵)
        2. 动作反缩放 (使用真实统计信息进行反缩放)
        3. 坐标系转换 (使用RT矩阵从物体中心坐标系转换到世界坐标系)

        Args:
            pred_actions: 模型原始预测动作 [B, T, 25] 或 [B, 25]

        Returns:
            processed_actions: 后处理后的动作，与真实动作在同一空间
        """
        if self._should_log_detail("postprocessing"):
            print("   🔧 开始动作后处理...")

        # 保存原始形状
        original_shape = pred_actions.shape

        if self._should_log_detail("postprocessing"):
            print(f"   📊 处理动作维度: {pred_actions.shape}")

        # 数据已预处理，无需反缩放
        if (
            self.preprocess_config.get("enable_normalization", False)
            and self.postprocess_config["enable_action_unscaling"]
        ):
            if self.action_scaling_processor is not None:
                if self._should_log_detail("postprocessing"):
                    print("   📏 执行动作反缩放 (使用统计信息)...")
                processed_actions = self._apply_action_unscaling(pred_actions)
            else:
                if self._should_log_detail("postprocessing"):
                    print("   ⚠️ 动作缩放处理器不可用，跳过反缩放")
                processed_actions = pred_actions
        else:
            if self._should_log_detail("postprocessing"):
                print("   ✅ 数据已预处理，跳过动作反缩放")
            processed_actions = pred_actions

        # 6D旋转正交化（如果需要）
        if self.postprocess_config["enable_6d_orthogonalization"]:
            if self._should_log_detail("postprocessing"):
                print("   🔄 执行6D旋转正交化...")
            processed_actions = self._apply_6d_orthogonalization(processed_actions)
        else:
            if self._should_log_detail("postprocessing"):
                print("   ⏭️ 跳过6D旋转正交化")

        # 坐标系转换（如果需要）
        if self.postprocess_config[
            "enable_coord_transform"
        ] and self.preprocess_config.get("enable_object_centric", False):
            if self._should_log_detail("postprocessing"):
                print("   🌍 执行坐标系转换 (物体中心坐标系 → 世界坐标系)...")
            # 这里可以添加坐标系转换逻辑
            # processed_actions = self._apply_coordinate_transform(processed_actions)
        else:
            if self._should_log_detail("postprocessing"):
                print("   ✅ 数据已在物体中心坐标系，跳过坐标转换")

        # 用于热力图对比
        actions_before = (
            pred_actions.clone().detach()
            if isinstance(pred_actions, torch.Tensor)
            else None
        )

        # if actions_before is not None and getattr(self, "enable_detailed_debug", False):

        #     self._plot_component_block_heatmap(
        #         actions_before.cpu(),
        #         processed_actions.detach().cpu(),
        #         title_prefix=f"sample_{getattr(self, '_current_sample', '')}",

        #     )

        return processed_actions

    def _apply_6d_orthogonalization(self, actions):
        """应用6D旋转正交化"""
        try:
            try:
                from px_janus_learnsim.utils.math import (
                    rot6d_to_matrix_gram_schmidt,
                    matrix_to_rot6d,
                )

            except ImportError:
                raise ImportError("6D旋转正交化函数导入失败")

            # 保存原始形状
            original_shape = actions.shape

            # 处理批量和序列维度
            if actions.dim() == 3:
                batch_size, seq_length, action_dim = actions.shape
                actions_flat = actions.view(-1, action_dim)  # [B*T, 25]
            else:
                actions_flat = actions
                batch_size, seq_length = actions.shape[0], 1

            # 分解动作：16关节 + 3位置 + 6旋转
            action_joints = actions_flat[:, :16]
            action_position = actions_flat[:, 16:19]
            action_rotation_6d = actions_flat[:, 19:25]

            # 正交化6D旋转
            action_rotation_matrices = rot6d_to_matrix_gram_schmidt(
                action_rotation_6d, validate=True
            )
            action_rotation_6d_corrected = matrix_to_rot6d(action_rotation_matrices)

            # 重新组合
            actions_corrected = torch.cat(
                [
                    action_joints,
                    action_position,
                    action_rotation_6d_corrected,
                ],
                dim=-1,
            )

            # 恢复原始形状
            if len(original_shape) == 3:
                actions_corrected = actions_corrected.view(batch_size, seq_length, -1)

            # invalid_rotations = (~action_rotation_valid).sum().item()
            # if invalid_rotations > 0:
            #     print(f"     🔧 6D旋转正交化: 修正了{invalid_rotations}个无效旋转")

            return actions_corrected

        except ImportError:
            print("     ⚠️ 6D旋转处理函数导入失败，跳过正交化")
            return actions
        except Exception as e:
            print(f"     ⚠️ 6D旋转正交化失败: {e}")
            return actions

    def _apply_action_unscaling(self, actions):
        """应用动作反缩放"""
        try:
            if hasattr(self.action_scaling_processor, "unscale_actions"):
                # 兼容两种返回格式：
                # 1) 仅返回 unscaled_actions
                # 2) 返回 (unscaled_actions, scaling_method)
                unscale_result = self.action_scaling_processor.unscale_actions(actions)

                if isinstance(unscale_result, tuple):
                    actions_unscaled, scaling_method = unscale_result
                    if self._should_log_detail("postprocessing"):
                        print(f"     📊 动作反缩放方法: {scaling_method}")
                else:
                    actions_unscaled = unscale_result

                return actions_unscaled
            else:
                if self._should_log_detail("postprocessing"):
                    print("     ⚠️ 动作缩放处理器缺少反缩放方法")
                return actions
        except Exception as e:
            if self._should_log_detail("postprocessing"):
                print(f"     ⚠️ 动作反缩放失败: {e}")
            return actions

    def _apply_default_action_unscaling(self, actions):
        """应用默认的动作反缩放"""
        try:
            if self._should_log_detail("postprocessing"):
                print("     📊 使用默认反缩放参数 (可能不准确)")
            # 使用一些常见的反缩放参数
            # 这些参数可能不完全正确，但可以提供一个基线

            # 保存原始形状
            original_shape = actions.shape

            # 处理批量和序列维度
            if actions.dim() == 3:
                batch_size, seq_length, action_dim = actions.shape
                actions_flat = actions.view(-1, action_dim)  # [B*T, 25]
            else:
                actions_flat = actions
                batch_size, seq_length = actions.shape[0], 1

            # 分解动作：16关节 + 3位置 + 6旋转
            action_joints = actions_flat[:, :16]
            action_position = actions_flat[:, 16:19]
            action_rotation_6d = actions_flat[:, 19:25]

            # 应用默认反缩放
            # 关节角度: 假设归一化到[-1, 1]，反缩放到[-π, π]
            joints_unscaled = action_joints * np.pi

            # 位置: 假设归一化到[-1, 1]，反缩放到[-0.5, 0.5]米
            position_unscaled = action_position * 0.5

            # 旋转: 通常不需要反缩放
            rotation_unscaled = action_rotation_6d

            # 重新组合
            actions_unscaled = torch.cat(
                [joints_unscaled, position_unscaled, rotation_unscaled], dim=-1
            )

            # 恢复原始形状
            if len(original_shape) == 3:
                actions_unscaled = actions_unscaled.view(batch_size, seq_length, -1)

            if self._should_log_detail("postprocessing"):
                print("     📊 默认动作反缩放完成 (关节: *π, 位置: *0.5)")
            return actions_unscaled

        except Exception as e:
            if self._should_log_detail("postprocessing"):
                print(f"     ⚠️ 默认动作反缩放失败: {e}")
            return actions

    def _apply_training_coordinate_transform_with_gt(
        self,
        current_hand_pos,
        current_hand_rot_6d,
        target_hand_pos,
        target_hand_rot_6d,
        obj_pos_world,
        obj_rot_6d_world,
        true_actions,
    ):
        """
        合并的坐标系转换函数：同时处理观察数据和Ground Truth转换

        这个函数解决了原来分离转换导致的坐标系不一致问题：
        - 观察数据和GT使用相同的原始物体状态进行转换
        - 避免物体状态覆写导致的信息丢失

        Args:
            current_hand_pos: 当前手部位置 [B, 3] (世界坐标系)
            current_hand_rot_6d: 当前手部旋转 [B, 6] (世界坐标系)
            target_hand_pos: 目标手部位置 [B, 3] (世界坐标系)
            target_hand_rot_6d: 目标手部旋转 [B, 6] (世界坐标系)
            obj_pos_world: 物体世界位置 [B, 3]
            obj_rot_6d_world: 物体世界6D旋转 [B, 6]
            true_actions: Ground Truth动作序列 [B, T, 25] (世界坐标系)

        Returns:
            tuple: (cur_pos_obj, cur_rot_6d_obj, tgt_pos_obj, tgt_rot_6d_obj,
                   object_state, true_actions_transformed)
        """
        try:
            if self._should_log_detail("preprocessing"):
                print("   🔧 开始合并的坐标系转换（观察数据 + GT）...")
                print(f"      原始物体世界位置: {obj_pos_world}")
                print(f"      原始物体世界旋转6D: {obj_rot_6d_world}")

            # 1. 首先转换观察数据（使用原始的apply_training_coordinate_transform）
            # 检查object_centric_transformer是否可用
            if (
                not hasattr(self, "object_centric_transformer")
                or self.object_centric_transformer is None
            ):
                if self._should_log_detail("preprocessing"):
                    print("   ⚠️ object_centric_transformer不可用，跳过坐标转换")
                # 返回原始数据，不进行转换
                return (
                    current_hand_pos,
                    current_hand_rot_6d,
                    target_hand_pos,
                    target_hand_rot_6d,
                    torch.zeros_like(obj_pos_world),  # 物体状态设为0
                    true_actions,
                )

            from px_janus_learnsim.utils.object_centric_transforms import (
                apply_training_coordinate_transform,
            )

            (
                cur_pos_obj,
                cur_rot_6d_obj,
                tgt_pos_obj,
                tgt_rot_6d_obj,
                object_state,
            ) = apply_training_coordinate_transform(
                current_hand_pos,
                current_hand_rot_6d,
                target_hand_pos,
                target_hand_rot_6d,
                obj_pos_world,
                obj_rot_6d_world,
                self.object_centric_transformer,
            )

            if self._should_log_detail("preprocessing"):
                print("   ✅ 观察数据坐标转换完成")
                print(
                    f"      转换后手部位置范围: [{cur_pos_obj.min():.3f}, {cur_pos_obj.max():.3f}]"
                )
                print(
                    f"      转换后目标位置范围: [{tgt_pos_obj.min():.3f}, {tgt_pos_obj.max():.3f}]"
                )
                print(f"      标准化物体状态: {object_state}")

            # 2. 接下来转换GT动作（使用相同的原始物体状态）
            true_actions_transformed = self._transform_gt_actions_to_object_frame(
                true_actions, obj_pos_world, obj_rot_6d_world
            )

            if self._should_log_detail("preprocessing"):
                print("   ✅ 合并坐标转换完成")
                print("   📊 确保观察数据和GT使用相同的原始物体状态进行转换")

            return (
                cur_pos_obj,
                cur_rot_6d_obj,
                tgt_pos_obj,
                tgt_rot_6d_obj,
                object_state,
                true_actions_transformed,
            )

        except Exception as e:
            error_msg = f"合并坐标转换失败: {e}"
            if self._should_log_detail("preprocessing"):
                print(f"   ❌ {error_msg}")
            raise ValueError(error_msg) from e

    def _transform_gt_actions_to_object_frame(
        self, true_actions, obj_pos_world, obj_rot_6d_world
    ):
        """
        将Ground Truth动作从世界坐标系转换到物体坐标系下

        这是从原来的_transform_ground_truth_to_object_frame函数提取的核心逻辑，
        专门用于GT转换，避免与观察数据转换的耦合。

        Args:
            true_actions: Ground Truth动作序列 [B, T, 25] (世界坐标系)
            obj_pos_world: 物体世界位置 [B, 3] (原始状态)
            obj_rot_6d_world: 物体世界6D旋转 [B, 6] (原始状态)

        Returns:
            true_actions_transformed: 转换后的动作序列 [B, T, 25] (物体坐标系)
        """
        try:
            if self._should_log_detail("preprocessing"):
                print("   🔄 开始转换Ground Truth动作到物体坐标系...")

            # 保存原始形状
            batch_size, seq_length, action_dim = true_actions.shape

            # 🔍 添加转换前的详细信息
            if self._should_log_detail("preprocessing"):
                original_pos = true_actions[:, :, 16:19]  # 原始位置
                original_rot = true_actions[:, :, 19:25]  # 原始旋转
                print("   📊 转换前GT数据统计:")
                print(
                    f"      批次大小: {batch_size}, 序列长度: {seq_length}, 动作维度: {action_dim}"
                )
                print(
                    f"      原始位置范围: [{original_pos.min():.4f}, {original_pos.max():.4f}]"
                )
                print(f"      原始位置均值: {original_pos.mean(dim=(0,1))}")
                print(
                    f"      原始旋转范围: [{original_rot.min():.4f}, {original_rot.max():.4f}]"
                )
                print(f"      使用原始物体世界位置: {obj_pos_world}")
                print(f"      使用原始物体世界旋转6D: {obj_rot_6d_world}")

            # 检查转换器是否可用
            if (
                not hasattr(self, "object_centric_transformer")
                or self.object_centric_transformer is None
            ):
                if self._should_log_detail("preprocessing"):
                    print("   ⚠️ object_centric_transformer 不可用，跳过GT坐标转换")
                return true_actions

            # 初始化转换后的动作张量
            true_actions_transformed = true_actions.clone()

            # 逐时间步转换动作
            for t in range(seq_length):
                # 提取当前时间步的动作
                current_actions = true_actions[:, t, :]  # [B, 25]

                # 分解动作：16关节 + 3位置 + 6旋转
                action_joints = current_actions[:, :16]  # 关节不需要转换
                action_pos_world = current_actions[:, 16:19]  # 世界坐标系位置
                action_rot_6d_world = current_actions[:, 19:25]  # 世界坐标系6D旋转

                # 使用相同的坐标转换逻辑
                # 检查object_centric_transformer是否可用
                if (
                    not hasattr(self, "object_centric_transformer")
                    or self.object_centric_transformer is None
                ):
                    if self._should_log_detail("preprocessing"):
                        print("   ⚠️ object_centric_transformer不可用，跳过GT坐标转换")
                    return true_actions

                try:
                    from px_janus_learnsim.utils.object_centric_transforms import (
                        apply_training_coordinate_transform,
                    )
                except ImportError as e:
                    if self._should_log_detail("preprocessing"):
                        print(f"   ❌ 无法导入坐标转换模块: {e}")
                    return true_actions

                # 为每个批次样本转换动作位置和旋转
                action_pos_obj_list = []
                action_rot_6d_obj_list = []

                if (
                    self._should_log_detail("preprocessing") and t == 0
                ):  # 只在第一个时间步打印详细信息
                    print(f"   🔄 开始逐时间步转换，时间步 {t+1}/{seq_length}")
                    print(f"      当前时间步动作位置: {action_pos_world}")
                    print(f"      当前时间步动作旋转: {action_rot_6d_world}")

                for b in range(batch_size):
                    # 转换单个样本的动作
                    (
                        action_pos_obj_b,
                        action_rot_6d_obj_b,
                        _,  # 不需要目标位置
                        _,  # 不需要目标旋转
                        _,  # 不需要物体状态
                    ) = apply_training_coordinate_transform(
                        action_pos_world[b : b + 1],  # [1, 3]
                        action_rot_6d_world[b : b + 1],  # [1, 6]
                        action_pos_world[b : b + 1],  # 使用相同位置作为目标（dummy）
                        action_rot_6d_world[b : b + 1],  # 使用相同旋转作为目标（dummy）
                        obj_pos_world[b : b + 1],  # [1, 3] 使用原始物体状态
                        obj_rot_6d_world[b : b + 1],  # [1, 6] 使用原始物体状态
                        self.object_centric_transformer,
                    )

                    action_pos_obj_list.append(action_pos_obj_b)
                    action_rot_6d_obj_list.append(action_rot_6d_obj_b)

                # 合并结果
                action_pos_obj = torch.cat(action_pos_obj_list, dim=0)  # [B, 3]
                action_rot_6d_obj = torch.cat(action_rot_6d_obj_list, dim=0)  # [B, 6]

                # 重新组合动作
                transformed_action = torch.cat(
                    [
                        action_joints,  # [B, 16] 关节不变
                        action_pos_obj,  # [B, 3] 转换后的位置
                        action_rot_6d_obj,  # [B, 6] 转换后的旋转
                    ],
                    dim=-1,
                )  # [B, 25]

                # 更新转换后的动作序列
                true_actions_transformed[:, t, :] = transformed_action

            if self._should_log_detail("preprocessing"):
                print("   ✅ Ground Truth坐标转换完成")

                # 详细对比转换前后的数据
                original_pos = true_actions[:, :, 16:19]
                transformed_pos = true_actions_transformed[:, :, 16:19]
                original_rot = true_actions[:, :, 19:25]
                transformed_rot = true_actions_transformed[:, :, 19:25]

                print("   📊 GT转换结果详细对比:")
                print(
                    f"      转换前位置范围: [{original_pos.min():.4f}, {original_pos.max():.4f}]"
                )
                print(
                    f"      转换后位置范围: [{transformed_pos.min():.4f}, {transformed_pos.max():.4f}]"
                )
                print(f"      转换前位置均值: {original_pos.mean(dim=(0,1))}")
                print(f"      转换后位置均值: {transformed_pos.mean(dim=(0,1))}")
                print(
                    f"      位置变化量: {(transformed_pos - original_pos).abs().mean():.4f}"
                )

                print(
                    f"      转换前旋转范围: [{original_rot.min():.4f}, {original_rot.max():.4f}]"
                )
                print(
                    f"      转换后旋转范围: [{transformed_rot.min():.4f}, {transformed_rot.max():.4f}]"
                )
                print(
                    f"      旋转变化量: {(transformed_rot - original_rot).abs().mean():.4f}"
                )

                # 检查是否真的发生了转换
                pos_changed = not torch.allclose(
                    original_pos, transformed_pos, atol=1e-6
                )
                rot_changed = not torch.allclose(
                    original_rot, transformed_rot, atol=1e-6
                )
                print(f"      位置是否发生变化: {pos_changed}")
                print(f"      旋转是否发生变化: {rot_changed}")

                if not pos_changed and not rot_changed:
                    print("   ⚠️ 警告: GT数据在转换前后完全相同，可能转换未生效")
                else:
                    print("   ✅ GT数据转换成功，坐标系一致性得到保证")

            return true_actions_transformed

        except Exception as e:
            error_msg = f"Ground Truth坐标转换失败: {e}"
            if self._should_log_detail("preprocessing"):
                print(f"   ❌ {error_msg}")
            raise ValueError(error_msg) from e

    def _transform_ground_truth_to_object_frame(
        self, true_actions, obj_pos_world, obj_rot_6d_world
    ):
        """
        将Ground Truth动作从世界坐标系转换到物体坐标系下

        Args:
            true_actions: Ground Truth动作序列 [B, T, 25] (世界坐标系)
            obj_pos_world: 物体世界位置 [B, 3]
            obj_rot_6d_world: 物体世界6D旋转 [B, 6]

        Returns:
            true_actions_transformed: 转换后的动作序列 [B, T, 25] (物体坐标系)
        """
        try:
            if self._should_log_detail("preprocessing"):
                print("   🔄 开始转换Ground Truth动作到物体坐标系...")

            # 保存原始形状
            batch_size, seq_length, action_dim = true_actions.shape

            # 🔍 添加转换前的详细信息
            if self._should_log_detail("preprocessing"):
                original_pos = true_actions[:, :, 16:19]  # 原始位置
                original_rot = true_actions[:, :, 19:25]  # 原始旋转
                print("   📊 转换前GT数据统计:")
                print(
                    f"      批次大小: {batch_size}, 序列长度: {seq_length}, 动作维度: {action_dim}"
                )
                print(
                    f"      原始位置范围: [{original_pos.min():.4f}, {original_pos.max():.4f}]"
                )
                print(f"      原始位置均值: {original_pos.mean(dim=(0,1))}")
                print(
                    f"      原始旋转范围: [{original_rot.min():.4f}, {original_rot.max():.4f}]"
                )
                print(f"      物体世界位置: {obj_pos_world}")
                print(f"      物体世界旋转6D: {obj_rot_6d_world}")

            # 检查转换器是否可用
            if (
                not hasattr(self, "object_centric_transformer")
                or self.object_centric_transformer is None
            ):
                if self._should_log_detail("preprocessing"):
                    print("   ⚠️ object_centric_transformer 不可用，跳过GT坐标转换")
                return true_actions

            # 初始化转换后的动作张量
            true_actions_transformed = true_actions.clone()

            # 逐时间步转换动作
            for t in range(seq_length):
                # 提取当前时间步的动作
                current_actions = true_actions[:, t, :]  # [B, 25]

                # 分解动作：16关节 + 3位置 + 6旋转
                action_joints = current_actions[:, :16]  # 关节不需要转换
                action_pos_world = current_actions[:, 16:19]  # 世界坐标系位置
                action_rot_6d_world = current_actions[:, 19:25]  # 世界坐标系6D旋转

                # 使用相同的坐标转换逻辑
                # 检查object_centric_transformer是否可用
                if (
                    not hasattr(self, "object_centric_transformer")
                    or self.object_centric_transformer is None
                ):
                    if self._should_log_detail("preprocessing"):
                        print("   ⚠️ object_centric_transformer不可用，跳过GT坐标转换")
                    return true_actions

                try:
                    from px_janus_learnsim.utils.object_centric_transforms import (
                        apply_training_coordinate_transform,
                    )
                except ImportError as e:
                    if self._should_log_detail("preprocessing"):
                        print(f"   ❌ 无法导入坐标转换模块: {e}")
                    return true_actions

                # 为每个批次样本转换动作位置和旋转
                action_pos_obj_list = []
                action_rot_6d_obj_list = []

                if self._should_log_detail("preprocessing"):
                    print(f"   🔄 开始逐批次转换，时间步 {t+1}/{seq_length}")
                    print(f"      当前时间步动作位置: {action_pos_world}")
                    print(f"      当前时间步动作旋转: {action_rot_6d_world}")

                for b in range(batch_size):
                    # 转换单个样本的动作
                    (
                        action_pos_obj_b,
                        action_rot_6d_obj_b,
                        _,  # 不需要目标位置
                        _,  # 不需要目标旋转
                        _,  # 不需要物体状态
                    ) = apply_training_coordinate_transform(
                        action_pos_world[b : b + 1],  # [1, 3]
                        action_rot_6d_world[b : b + 1],  # [1, 6]
                        action_pos_world[b : b + 1],  # 使用相同位置作为目标（dummy）
                        action_rot_6d_world[b : b + 1],  # 使用相同旋转作为目标（dummy）
                        obj_pos_world[b : b + 1],  # [1, 3]
                        obj_rot_6d_world[b : b + 1],  # [1, 6]
                        self.object_centric_transformer,
                    )

                    action_pos_obj_list.append(action_pos_obj_b)
                    action_rot_6d_obj_list.append(action_rot_6d_obj_b)

                # 合并结果
                action_pos_obj = torch.cat(action_pos_obj_list, dim=0)  # [B, 3]
                action_rot_6d_obj = torch.cat(action_rot_6d_obj_list, dim=0)  # [B, 6]

                # 重新组合动作
                transformed_action = torch.cat(
                    [
                        action_joints,  # [B, 16] 关节不变
                        action_pos_obj,  # [B, 3] 转换后的位置
                        action_rot_6d_obj,  # [B, 6] 转换后的旋转
                    ],
                    dim=-1,
                )  # [B, 25]

                # 更新转换后的动作序列
                true_actions_transformed[:, t, :] = transformed_action

            if self._should_log_detail("preprocessing"):
                print("   ✅ Ground Truth坐标转换完成")

                # 详细对比转换前后的数据
                original_pos = true_actions[:, :, 16:19]
                transformed_pos = true_actions_transformed[:, :, 16:19]
                original_rot = true_actions[:, :, 19:25]
                transformed_rot = true_actions_transformed[:, :, 19:25]

                print("   📊 转换结果详细对比:")
                print(
                    f"      转换前位置范围: [{original_pos.min():.4f}, {original_pos.max():.4f}]"
                )
                print(
                    f"      转换后位置范围: [{transformed_pos.min():.4f}, {transformed_pos.max():.4f}]"
                )
                print(f"      转换前位置均值: {original_pos.mean(dim=(0,1))}")
                print(f"      转换后位置均值: {transformed_pos.mean(dim=(0,1))}")
                print(
                    f"      位置变化量: {(transformed_pos - original_pos).abs().mean():.4f}"
                )

                print(
                    f"      转换前旋转范围: [{original_rot.min():.4f}, {original_rot.max():.4f}]"
                )
                print(
                    f"      转换后旋转范围: [{transformed_rot.min():.4f}, {transformed_rot.max():.4f}]"
                )
                print(
                    f"      旋转变化量: {(transformed_rot - original_rot).abs().mean():.4f}"
                )

                # 检查是否真的发生了转换
                pos_changed = not torch.allclose(
                    original_pos, transformed_pos, atol=1e-6
                )
                rot_changed = not torch.allclose(
                    original_rot, transformed_rot, atol=1e-6
                )
                print(f"      位置是否发生变化: {pos_changed}")
                print(f"      旋转是否发生变化: {rot_changed}")

                if not pos_changed and not rot_changed:
                    print("   ⚠️ 警告: GT数据在转换前后完全相同，可能转换未生效")

            return true_actions_transformed

        except Exception as e:
            error_msg = f"Ground Truth坐标转换失败: {e}"
            if self._should_log_detail("preprocessing"):
                print(f"   ❌ {error_msg}")
            raise ValueError(error_msg) from e

    def _print_detailed_trajectory_debug_table(
        self, obs_dict, true_actions, target_pose, pred_actions_sequence, step_errors
    ):
        """
        打印详细的轨迹分析调试表格

        显示：
        1. 每个时间步的预测动作 vs 真实动作对比
        2. 动作差值和百分比误差
        3. 验证时间步连续性 (t时刻action vs t+1时刻obs)
        4. 动作组件分解 (关节、位置、旋转)
        """
        # 检查是否应该输出详细调试信息
        if not self.enable_detailed_debug or not self._should_log_detail("debug"):
            return

        try:
            print(f"\n{Colors.SPECIAL_HEADER}{'=' * 150}{Colors.RESET}")
            print(f"{Colors.SPECIAL_HEADER} 🔍 详细轨迹分析调试表格 {Colors.RESET}")
            print(f"{Colors.SPECIAL_HEADER}{'=' * 150}{Colors.RESET}")

            # 转换为numpy便于处理
            if isinstance(true_actions, torch.Tensor):
                true_actions_np = true_actions.cpu().numpy()
            else:
                true_actions_np = np.array(true_actions)

            if isinstance(pred_actions_sequence, torch.Tensor):
                pred_actions_np = pred_actions_sequence.cpu().numpy()
            else:
                pred_actions_np = np.array(pred_actions_sequence)

            if isinstance(obs_dict["policy"], torch.Tensor):
                obs_np = obs_dict["policy"].cpu().numpy()
            else:
                obs_np = np.array(obs_dict["policy"])

            # 确保是2维数组 (取第一个batch)
            if true_actions_np.ndim > 2:
                true_actions_np = true_actions_np[0]
            if pred_actions_np.ndim > 2:
                pred_actions_np = pred_actions_np[0]
            if obs_np.ndim > 1:
                obs_np = obs_np[0]

            seq_length = min(true_actions_np.shape[0], pred_actions_np.shape[0])

            print(f"📊 动作序列: {true_actions_np.shape}")
            print(f"📊 观察数据: {obs_np.shape}")
            print("📊 模型工作模式: 单帧观察 -> 序列预测")
            print(f"   输入: 观察{obs_np.shape} + 目标位姿")
            print(f"   输出: 动作序列{true_actions_np.shape}")

            # 显示后处理状态
            postprocess_status = []
            if self.postprocess_config["enable_6d_orthogonalization"]:
                postprocess_status.append("6D正交化")
            if self.postprocess_config["enable_action_unscaling"]:
                postprocess_status.append("动作反缩放")
            if self.postprocess_config["enable_coord_transform"]:
                postprocess_status.append("坐标转换")

            if postprocess_status:
                print(f"🔧 预测动作后处理: {' + '.join(postprocess_status)}")
            else:
                print("🔧 预测动作后处理: 已禁用")

            # === 1. 整体序列对比表格 ===
            self._print_sequence_comparison_table(
                true_actions_np, pred_actions_np, step_errors, seq_length
            )

            # === 2. 动作组件详细分析 ===
            self._print_action_components_analysis(
                true_actions_np, pred_actions_np, seq_length
            )

            # === 3. 时间步连续性验证 ===
            self._print_temporal_consistency_check(obs_np, true_actions_np, seq_length)

            print(f"{Colors.SUCCESS_HEADER}{'=' * 150}{Colors.RESET}")
            print(f"{Colors.SUCCESS_HEADER} 🏁 详细轨迹分析调试表格结束 {Colors.RESET}")
            print(f"{Colors.SUCCESS_HEADER}{'=' * 150}{Colors.RESET}")

        except Exception as e:
            print(f"⚠️ 调试表格生成失败: {e}")
            import traceback

            traceback.print_exc()

    def _print_simple_sequence_comparison(
        self, true_actions, pred_actions, step_errors, seq_length
    ):
        """简单格式的序列对比输出（当prettytable不可用时）"""
        print(
            f"\n{Colors.BG_CYAN}{Colors.BOLD} 🔄 1. 序列动作对比表格 (前5维动作展示) {Colors.RESET}"
        )
        print(f"{Colors.CYAN}{'-' * 100}{Colors.RESET}")

        for step in range(seq_length):
            true_action = true_actions[step]
            pred_action = (
                pred_actions[step]
                if step < pred_actions.shape[0]
                else np.zeros_like(true_action)
            )

            # 计算差值
            diff = np.abs(pred_action - true_action)

            # 前5维展示
            true_str = "[" + ", ".join([f"{v:.4f}" for v in true_action[:5]]) + "]"
            pred_str = "[" + ", ".join([f"{v:.4f}" for v in pred_action[:5]]) + "]"
            diff_str = "[" + ", ".join([f"{v:.4f}" for v in diff[:5]]) + "]"

            # 从step_errors获取详细误差信息
            step_info = None
            for err in step_errors:
                if err["step"] == step:
                    step_info = err
                    break

            print(f"{Colors.BOLD}{Colors.BLUE}步骤 {step}:{Colors.RESET}")
            print(f"  {Colors.GREEN}真实动作:{Colors.RESET} {true_str}")
            print(f"  {Colors.YELLOW}预测动作:{Colors.RESET} {pred_str}")
            print(f"  {Colors.RED}绝对差值:{Colors.RESET} {diff_str}")

            if step_info:
                mae_color = Colors.RED if step_info["mae"] > 0.01 else Colors.GREEN
                mse_color = Colors.RED if step_info["mse"] > 0.001 else Colors.GREEN
                max_error_color = (
                    Colors.RED if step_info["max_error"] > 0.05 else Colors.GREEN
                )

                print(
                    f"  {mae_color}MAE: {step_info['mae']:.6f}{Colors.RESET}, "
                    f"{mse_color}MSE: {step_info['mse']:.6f}{Colors.RESET}, "
                    f"{max_error_color}最大误差: {step_info['max_error']:.6f}{Colors.RESET}"
                )

                joint_mae_color = (
                    Colors.RED if step_info.get("joint_mae", 0) > 0.01 else Colors.GREEN
                )
                pos_mae_color = (
                    Colors.RED
                    if step_info.get("position_mae", 0) > 0.01
                    else Colors.GREEN
                )
                rot_mae_color = (
                    Colors.RED
                    if step_info.get("rotation_mae", 0) > 0.01
                    else Colors.GREEN
                )

                print(
                    f"  {joint_mae_color}关节MAE: {step_info.get('joint_mae', 0):.6f}{Colors.RESET}, "
                    f"{pos_mae_color}位置MAE: {step_info.get('position_mae', 0):.2f}mm{Colors.RESET}, "
                    f"{rot_mae_color}旋转MAE: {step_info.get('rotation_mae', 0):.6f}{Colors.RESET}"
                )
            print()

    def _print_sequence_comparison_table(
        self, true_actions, pred_actions, step_errors, seq_length
    ):
        """打印序列对比表格"""
        if not PRETTYTABLE_AVAILABLE:
            self._print_simple_sequence_comparison(
                true_actions, pred_actions, step_errors, seq_length
            )
            return

        print(
            f"\n{Colors.BG_CYAN}{Colors.BOLD} 🔄 1. 序列动作对比表格 (前5维动作展示) {Colors.RESET}"
        )

        # 创建序列对比表格
        table = PrettyTable()
        table.field_names = [
            "步骤",
            "真实动作(前5维)",
            "预测动作(前5维)",
            "绝对差值(前5维)",
            "MAE",
            "MSE",
            "MAE(Norm)",
            "MSE(Norm)",
            "最大误差",
            "关节MAE",
            "位置MAE",
            "旋转MAE",
        ]
        table.align["步骤"] = "c"
        table.align["真实动作(前5维)"] = "l"
        table.align["预测动作(前5维)"] = "l"
        table.align["绝对差值(前5维)"] = "l"

        for step in range(seq_length):
            true_action = true_actions[step]
            pred_action = (
                pred_actions[step]
                if step < pred_actions.shape[0]
                else np.zeros_like(true_action)
            )

            # 计算差值
            diff = np.abs(pred_action - true_action)

            # 前5维展示
            true_str = "[" + ", ".join([f"{v:.4f}" for v in true_action[:5]]) + "]"
            pred_str = "[" + ", ".join([f"{v:.4f}" for v in pred_action[:5]]) + "]"
            diff_str = "[" + ", ".join([f"{v:.4f}" for v in diff[:5]]) + "]"

            # 从step_errors获取详细误差信息
            step_info = None
            for err in step_errors:
                if err["step"] == step:
                    step_info = err
                    break

            if step_info:
                # 根据误差大小添加颜色
                mae_val = step_info["mae"]
                mse_val = step_info["mse"]
                mae_scaled_val = step_info.get("mae_scaled", None)
                mse_scaled_val = step_info.get("mse_scaled", None)
                max_err_val = step_info["max_error"]
                joint_mae_val = step_info.get("joint_mae", 0)
                pos_mae_val = step_info.get("position_mae", 0)
                rot_mae_val = step_info.get("rotation_mae", 0)

                mae_str = (
                    f"{Colors.RED}{mae_val:.6f}{Colors.RESET}"
                    if mae_val > 0.01
                    else f"{Colors.GREEN}{mae_val:.6f}{Colors.RESET}"
                )
                mse_str = (
                    f"{Colors.RED}{mse_val:.6f}{Colors.RESET}"
                    if mse_val > 0.001
                    else f"{Colors.GREEN}{mse_val:.6f}{Colors.RESET}"
                )

                if mae_scaled_val is not None:
                    mae_scaled_str = (
                        f"{Colors.RED}{mae_scaled_val:.6f}{Colors.RESET}"
                        if mae_scaled_val > 0.1  # 阈值不同域可调整
                        else f"{Colors.GREEN}{mae_scaled_val:.6f}{Colors.RESET}"
                    )
                    mse_scaled_str = (
                        f"{Colors.RED}{mse_scaled_val:.6f}{Colors.RESET}"
                        if mse_scaled_val > 0.1
                        else f"{Colors.GREEN}{mse_scaled_val:.6f}{Colors.RESET}"
                    )
                else:
                    mae_scaled_str = "-"
                    mse_scaled_str = "-"

                max_err_str = (
                    f"{Colors.RED}{max_err_val:.6f}{Colors.RESET}"
                    if max_err_val > 0.05
                    else f"{Colors.GREEN}{max_err_val:.6f}{Colors.RESET}"
                )
                joint_mae_str = (
                    f"{Colors.RED}{joint_mae_val:.6f}{Colors.RESET}"
                    if joint_mae_val > 0.01
                    else f"{Colors.GREEN}{joint_mae_val:.6f}{Colors.RESET}"
                )
                pos_mae_str = (
                    f"{Colors.RED}{pos_mae_val:.6f}{Colors.RESET}"
                    if pos_mae_val > 0.01
                    else f"{Colors.GREEN}{pos_mae_val:.6f}{Colors.RESET}"
                )
                rot_mae_str = (
                    f"{Colors.RED}{rot_mae_val:.6f}{Colors.RESET}"
                    if rot_mae_val > 0.01
                    else f"{Colors.GREEN}{rot_mae_val:.6f}{Colors.RESET}"
                )

                table.add_row(
                    [
                        f"{Colors.BOLD}{Colors.BLUE}{step}{Colors.RESET}",
                        f"{Colors.GREEN}{true_str}{Colors.RESET}",
                        f"{Colors.YELLOW}{pred_str}{Colors.RESET}",
                        f"{Colors.RED}{diff_str}{Colors.RESET}",
                        mae_str,
                        mse_str,
                        mae_scaled_str,
                        mse_scaled_str,
                        max_err_str,
                        joint_mae_str,
                        pos_mae_str,
                        rot_mae_str,
                    ]
                )
            else:
                # 如果没有step_errors信息，手动计算
                mae = np.mean(diff)
                mse = np.mean(diff**2)
                max_err = np.max(diff)

                table.add_row(
                    [
                        step,
                        true_str,
                        pred_str,
                        diff_str,
                        f"{mae:.6f}",
                        f"{mse:.6f}",
                        "-",
                        "-",
                        f"{max_err:.6f}",
                        "-",
                        "-",
                        "-",
                    ]
                )

        print(table)

    def _print_action_components_analysis(self, true_actions, pred_actions, seq_length):
        """打印动作组件详细分析"""
        # 检查是否应该输出组件分析
        if not self._should_log_detail("component"):
            return

        from prettytable import PrettyTable

        print(
            f"\n{Colors.BG_MAGENTA}{Colors.BOLD} 🖐️ 2. 动作组件分解分析 (25维: 16关节 + 3位置 + 6旋转) {Colors.RESET}"
        )

        # 组件分析表格
        comp_table = PrettyTable()
        comp_table.field_names = [
            "步骤",
            "关节误差均值",
            "关节误差百分比",
            "位置误差均值",
            "位置误差百分比",
            "旋转误差均值",
            "旋转误差百分比",
        ]
        comp_table.align = "c"

        for step in range(seq_length):
            true_action = true_actions[step]
            pred_action = (
                pred_actions[step]
                if step < pred_actions.shape[0]
                else np.zeros_like(true_action)
            )

            # 分解动作组件
            true_joints = true_action[:16]  # 关节 [0:16]
            true_pos = true_action[16:19]  # 位置 [16:19]
            true_rot = true_action[19:25]  # 旋转 [19:25]

            pred_joints = pred_action[:16]
            pred_pos = pred_action[16:19]
            pred_rot = pred_action[19:25]

            # 计算误差
            joint_diff = np.abs(pred_joints - true_joints)
            pos_diff = np.abs(pred_pos - true_pos)
            rot_diff = np.abs(pred_rot - true_rot)

            joint_mae = np.mean(joint_diff)
            pos_mae = np.mean(pos_diff)
            rot_mae = np.mean(rot_diff)

            # 计算百分比误差 (相对于真实值的平均绝对值)
            joint_mean_abs = np.mean(np.abs(true_joints)) + 1e-8  # 避免除零
            pos_mean_abs = np.mean(np.abs(true_pos)) + 1e-8
            rot_mean_abs = np.mean(np.abs(true_rot)) + 1e-8

            joint_percent = (joint_mae / joint_mean_abs) * 100
            pos_percent = (pos_mae / pos_mean_abs) * 100
            rot_percent = (rot_mae / rot_mean_abs) * 100

            # 根据误差百分比添加颜色
            joint_color = (
                Colors.RED
                if joint_percent > 10
                else (Colors.YELLOW if joint_percent > 5 else Colors.GREEN)
            )
            pos_color = (
                Colors.RED
                if pos_percent > 10
                else (Colors.YELLOW if pos_percent > 5 else Colors.GREEN)
            )
            rot_color = (
                Colors.RED
                if rot_percent > 10
                else (Colors.YELLOW if rot_percent > 5 else Colors.GREEN)
            )

            comp_table.add_row(
                [
                    f"{Colors.BOLD}{Colors.BLUE}{step}{Colors.RESET}",
                    f"{joint_color}{joint_mae:.6f}{Colors.RESET}",
                    f"{joint_color}{joint_percent:.2f}%{Colors.RESET}",
                    f"{pos_color}{pos_mae:.6f}{Colors.RESET}",
                    f"{pos_color}{pos_percent:.2f}%{Colors.RESET}",
                    f"{rot_color}{rot_mae:.6f}{Colors.RESET}",
                    f"{rot_color}{rot_percent:.2f}%{Colors.RESET}",
                ]
            )

        print(comp_table)

        # 详细组件值对比 (选择几个关键时间步)
        key_steps = [
            0,
            seq_length // 4,
            seq_length // 2,
            3 * seq_length // 4,
            seq_length - 1,
        ]
        key_steps = [s for s in key_steps if s < seq_length]

        if key_steps:
            print(
                f"\n{Colors.BG_BLUE}{Colors.BOLD} 🔍 3. 关键时间步详细组件值对比 {Colors.RESET}"
            )

            for step in key_steps:
                print(f"\n--- 时间步 {step} ---")

                true_action = true_actions[step]
                pred_action = (
                    pred_actions[step]
                    if step < pred_actions.shape[0]
                    else np.zeros_like(true_action)
                )

                detail_table = PrettyTable()
                detail_table.field_names = [
                    "组件",
                    "真实值",
                    "预测值",
                    "绝对差值",
                    "百分比差值",
                ]
                detail_table.align["组件"] = "l"
                detail_table.align["真实值"] = "r"
                detail_table.align["预测值"] = "r"
                detail_table.align["绝对差值"] = "r"
                detail_table.align["百分比差值"] = "r"

                # 关节组件 (显示全部16个关节)
                for i in range(16):
                    true_val = true_action[i]
                    pred_val = pred_action[i]
                    diff = abs(pred_val - true_val)
                    percent = (diff / (abs(true_val) + 1e-8)) * 100

                    # 根据百分比误差添加颜色
                    color = (
                        Colors.RED
                        if percent > 10
                        else (Colors.YELLOW if percent > 5 else Colors.GREEN)
                    )

                    detail_table.add_row(
                        [
                            f"{Colors.CYAN}关节{i+1}{Colors.RESET}",
                            f"{Colors.GREEN}{true_val:.6f}{Colors.RESET}",
                            f"{Colors.YELLOW}{pred_val:.6f}{Colors.RESET}",
                            f"{color}{diff:.6f}{Colors.RESET}",
                            f"{color}{percent:.2f}%{Colors.RESET}",
                        ]
                    )

                # 位置组件
                pos_names = ["位置X", "位置Y", "位置Z"]
                for i, name in enumerate(pos_names):
                    true_val = true_action[16 + i]
                    pred_val = pred_action[16 + i]
                    diff = abs(pred_val - true_val)
                    percent = (diff / (abs(true_val) + 1e-8)) * 100

                    # 根据百分比误差添加颜色
                    color = (
                        Colors.RED
                        if percent > 10
                        else (Colors.YELLOW if percent > 5 else Colors.GREEN)
                    )

                    detail_table.add_row(
                        [
                            f"{Colors.PURPLE}{name}{Colors.RESET}",
                            f"{Colors.GREEN}{true_val:.6f}{Colors.RESET}",
                            f"{Colors.YELLOW}{pred_val:.6f}{Colors.RESET}",
                            f"{color}{diff:.6f}{Colors.RESET}",
                            f"{color}{percent:.2f}%{Colors.RESET}",
                        ]
                    )

                # 旋转组件 (6D旋转)
                rot_names = ["旋转R1", "旋转R2", "旋转R3", "旋转R4", "旋转R5", "旋转R6"]
                for i, name in enumerate(rot_names):
                    true_val = true_action[19 + i]
                    pred_val = pred_action[19 + i]
                    diff = abs(pred_val - true_val)
                    percent = (diff / (abs(true_val) + 1e-8)) * 100

                    # 根据百分比误差添加颜色
                    color = (
                        Colors.RED
                        if percent > 10
                        else (Colors.YELLOW if percent > 5 else Colors.GREEN)
                    )

                    detail_table.add_row(
                        [
                            f"{Colors.ORANGE}{name}{Colors.RESET}",
                            f"{Colors.GREEN}{true_val:.6f}{Colors.RESET}",
                            f"{Colors.YELLOW}{pred_val:.6f}{Colors.RESET}",
                            f"{color}{diff:.6f}{Colors.RESET}",
                            f"{color}{percent:.2f}%{Colors.RESET}",
                        ]
                    )

                print(detail_table)

    def _print_temporal_consistency_check(self, obs, true_actions, seq_length):
        """打印时间步连续性验证"""
        # 检查是否应该输出时间步一致性检查
        if not self._should_log_detail("temporal"):
            return

        from prettytable import PrettyTable

        print(
            f"\n{Colors.BG_YELLOW}{Colors.BOLD} ⏰ 4. 观察与序列预测一致性验证 {Colors.RESET}"
        )
        print(f"{Colors.YELLOW}说明: ACT模型基于单帧观察预测整个动作序列{Colors.RESET}")
        print(
            f"{Colors.YELLOW}验证: 当前观察状态 vs 预测序列第一帧的期望状态{Colors.RESET}"
        )

        # 假设观察空间的前25维是手部状态 (16关节 + 9位姿)
        hand_state_obs = obs[:25]  # 当前时刻的手部状态观察

        consistency_table = PrettyTable()
        consistency_table.field_names = [
            "验证项",
            "观察状态",
            "序列首帧动作",
            "差值",
            "组件类型",
        ]
        consistency_table.align["验证项"] = "l"
        consistency_table.align["观察状态"] = "r"
        consistency_table.align["序列首帧动作"] = "r"
        consistency_table.align["差值"] = "r"
        consistency_table.align["组件类型"] = "l"

        # 显示前几个维度用于肉眼验证
        display_dims = min(10, len(hand_state_obs))

        for i in range(display_dims):
            obs_val = hand_state_obs[i]

            # 对比观察状态与序列首帧动作
            if seq_length > 0:
                # 序列第一帧动作：理论上应该接近当前观察状态
                action_val = true_actions[0][i] if i < true_actions.shape[1] else "N/A"

                if isinstance(action_val, (int, float)):
                    diff = abs(obs_val - action_val)

                    # 根据差值大小添加颜色
                    diff_color = (
                        Colors.RED
                        if diff > 0.1
                        else (Colors.YELLOW if diff > 0.05 else Colors.GREEN)
                    )
                    component_type = (
                        "关节角度" if i < 16 else ("位置" if i < 19 else "旋转")
                    )
                    type_color = (
                        Colors.CYAN
                        if i < 16
                        else (Colors.PURPLE if i < 19 else Colors.ORANGE)
                    )

                    consistency_table.add_row(
                        [
                            f"{Colors.BOLD}{Colors.BLUE}维度{i+1}{Colors.RESET}",
                            f"{Colors.GREEN}{action_val:.6f}{Colors.RESET}",
                            f"{Colors.YELLOW}{obs_val:.6f}{Colors.RESET}",
                            f"{diff_color}{diff:.6f}{Colors.RESET}",
                            f"{type_color}{component_type}{Colors.RESET}",
                        ]
                    )
                else:
                    consistency_table.add_row(
                        [
                            f"{Colors.BOLD}{Colors.BLUE}维度{i+1}{Colors.RESET}",
                            f"{Colors.RED}{str(action_val)}{Colors.RESET}",
                            f"{Colors.YELLOW}{obs_val:.6f}{Colors.RESET}",
                            f"{Colors.RED}N/A{Colors.RESET}",
                            f"{Colors.RED}无对应动作{Colors.RESET}",
                        ]
                    )

        print(consistency_table)

        # 动作序列概览
        print(
            f"\n{Colors.BG_GREEN}{Colors.BOLD} 📋 5. 完整动作序列概览 (用于验证连续性) {Colors.RESET}"
        )

        sequence_table = PrettyTable()
        sequence_table.field_names = ["时间步"] + [
            f"动作{i+1}" for i in range(min(8, true_actions.shape[1]))
        ]

        for step in range(min(seq_length, 10)):  # 只显示前10步
            row = [f"{Colors.BOLD}{Colors.CYAN}t={step}{Colors.RESET}"]
            for dim in range(min(8, true_actions.shape[1])):
                # 为不同的动作维度添加不同颜色
                if dim < 4:  # 前4维用绿色
                    color = Colors.GREEN
                elif dim < 6:  # 中间2维用黄色
                    color = Colors.YELLOW
                else:  # 后面的用蓝色
                    color = Colors.BLUE
                row.append(f"{color}{true_actions[step][dim]:.4f}{Colors.RESET}")
            sequence_table.add_row(row)

        if seq_length > 10:
            sequence_table.add_row(
                [f"{Colors.PURPLE}...{Colors.RESET}"]
                + [f"{Colors.PURPLE}...{Colors.RESET}"] * min(8, true_actions.shape[1])
            )

        print(sequence_table)

        # 观察与第一个动作的对比
        print(
            f"\n{Colors.BG_CYAN}{Colors.BOLD} 🔍 6. 当前观察 vs 第一个动作对比 {Colors.RESET}"
        )

        obs_action_table = PrettyTable()
        obs_action_table.field_names = [
            "维度",
            "当前观察",
            "第一个动作",
            "差值",
            "组件类型",
        ]

        first_action = true_actions[0] if seq_length > 0 else None

        for i in range(min(16, len(hand_state_obs))):  # 只比较前16个关节
            obs_val = hand_state_obs[i]

            if first_action is not None and i < len(first_action):
                action_val = first_action[i]
                diff = abs(obs_val - action_val)
                component = "关节角度"
            else:
                action_val = "N/A"
                diff = "N/A"
                component = "无对应"

            # 添加颜色高亮
            if diff != "N/A":
                diff_color = (
                    Colors.RED
                    if diff > 0.1
                    else (Colors.YELLOW if diff > 0.05 else Colors.GREEN)
                )
                diff_str = f"{diff_color}{diff:.6f}{Colors.RESET}"
                action_str = f"{Colors.GREEN}{action_val:.6f}{Colors.RESET}"
                component_str = f"{Colors.CYAN}{component}{Colors.RESET}"
            else:
                diff_str = f"{Colors.RED}N/A{Colors.RESET}"
                action_str = f"{Colors.RED}N/A{Colors.RESET}"
                component_str = f"{Colors.RED}{component}{Colors.RESET}"

            obs_action_table.add_row(
                [
                    f"{Colors.BOLD}{Colors.BLUE}维度{i+1}{Colors.RESET}",
                    f"{Colors.YELLOW}{obs_val:.6f}{Colors.RESET}",
                    action_str,
                    diff_str,
                    component_str,
                ]
            )

        print(obs_action_table)

    def _apply_basic_object_centric_transform(
        self, current_obs, current_target_pose, obs_dict
    ):
        """
        应用基本的物体中心坐标变换
        当没有专用变换器时使用的回退方法
        """
        try:
            print("     🔧 执行基本物体中心坐标变换...")

            # 从观察数据中提取物体位置作为变换中心
            if isinstance(obs_dict, dict):
                # 查找物体数据
                object_pos = None
                for obj_key in ["obj1", "object", "obj2"]:
                    if obj_key in obs_dict and isinstance(
                        obs_dict[obj_key], torch.Tensor
                    ):
                        obj_data = obs_dict[obj_key]
                        if obj_data.shape[-1] >= 3:  # 假设前3维是位置
                            object_pos = obj_data[..., :3]  # [B, 3]
                            print(
                                f"     📦 使用 {obj_key} 位置作为变换中心: {object_pos.shape}"
                            )
                            break

                if object_pos is not None:
                    # 将手部位置转换为相对于物体的坐标
                    if current_obs.shape[-1] >= 19:  # 假设16关节 + 3位置...
                        hand_pos = current_obs[..., 16:19]  # 提取位置部分
                        hand_pos_relative = hand_pos - object_pos

                        # 更新观察数据
                        current_obs = torch.cat(
                            [
                                current_obs[..., :16],  # 关节角度不变
                                hand_pos_relative,  # 相对位置
                                current_obs[..., 19:],  # 其他部分不变
                            ],
                            dim=-1,
                        )

                        print("     ✅ 手部位置转换为相对坐标")

                    # 同样转换目标位姿
                    if (
                        current_target_pose is not None
                        and current_target_pose.shape[-1] >= 19
                    ):
                        target_pos = current_target_pose[..., 16:19]
                        target_pos_relative = target_pos - object_pos

                        current_target_pose = torch.cat(
                            [
                                current_target_pose[..., :16],  # 关节角度不变
                                target_pos_relative,  # 相对位置
                                current_target_pose[..., 19:],  # 其他部分不变
                            ],
                            dim=-1,
                        )

                        print("     ✅ 目标位姿转换为相对坐标")
                else:
                    print("     ⚠️ 未找到物体位置信息，跳过坐标变换")
            else:
                print("     ⚠️ 观察数据格式不支持基本坐标变换")

            return current_obs, current_target_pose

        except Exception as e:
            print(f"     ❌ 基本物体中心坐标变换失败: {e}")
            return current_obs, current_target_pose

    def _load_training_config_from_model_dir(self):
        """从模型目录中加载训练配置"""
        model_dir = (
            os.path.dirname(self.model_path)
            if os.path.isfile(self.model_path)
            else self.model_path
        )
        config_path = os.path.join(model_dir, "resolved_config.yaml")
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                return yaml.safe_load(f)
        else:
            print(f"⚠️ 未找到训练配置文件: {config_path}")
            return None

    def _extract_model_name(self):
        """从模型路径中提取模型名称"""
        try:
            # 使用已有的工具函数
            from ..utils.file_utils import extract_model_name_from_path

            return extract_model_name_from_path(str(self.model_path))
        except ImportError:
            # 回退方法：简单从路径提取
            path = Path(self.model_path)
            # 尝试从路径中找到模型名（倒数第二个目录或文件名）
            parts = path.parts
            for i in range(len(parts) - 1, -1, -1):
                part = parts[i]
                # 跳过文件名和常见的目录名
                if part.endswith(".pth") or part.endswith(".pt"):
                    continue
                if part in ["outputs", "execution_bc_actor", "models", "checkpoints"]:
                    continue
                # 找到可能的模型名
                if len(part) > 3 and "-" in part:
                    return part
            # 如果没找到，使用文件名（去掉扩展名）
            return path.stem

    def _extract_dataset_name(self):
        """从数据集路径中提取数据集名称"""
        path = Path(self.dataset_path)
        # 取最后一个目录名作为数据集名称
        if path.is_dir():
            return path.name
        else:
            # 如果是文件，取父目录名
            return path.parent.name

    def _update_observation_state(
        self, current_obs, predicted_actions, true_actions, chunk_info
    ):
        """
        模拟执行动作对观察状态的影响

        Args:
            current_obs: 当前观察状态 [B, obs_dim]
            predicted_actions: 预测的动作序列 [B, chunk_size, action_dim]
            true_actions: 真实的动作序列 [B, chunk_size, action_dim]
            chunk_info: 分块信息

        Returns:
            torch.Tensor: 更新后的观察状态 [B, obs_dim]
        """
        # 🆕 简化的状态更新策略：基于动作的手部状态更新

        # 策略选择：使用真实动作还是预测动作进行状态更新
        use_true_actions = self.rolling_prediction_config.get(
            "use_true_actions_for_state_update", False
        )

        if use_true_actions and true_actions is not None:
            # 使用真实动作进行状态更新（更准确，但不够现实）
            actions_for_update = true_actions
            update_mode = "真实动作"
        else:
            # 使用预测动作进行状态更新（更现实，但可能累积误差）
            actions_for_update = predicted_actions
            update_mode = "预测动作"

        # 只在详细模式下输出状态更新信息
        if not self.log_config.get(
            "batch_processing_mode", False
        ) and self._should_log_detail("rolling"):
            print(f"       🔄 状态更新模式: {update_mode}")

        # 取最后一步的动作作为状态更新的基础
        if actions_for_update.shape[1] > 0:
            last_action = actions_for_update[:, -1, :]  # [B, action_dim]

            # 🆕 基于动作更新观察状态
            # 假设观察状态的前16维是手部关节，接下来的9维是手部位姿
            updated_obs = current_obs.clone()

            # 更新手部关节状态（前16维）
            if last_action.shape[-1] >= 16:
                # 假设动作的前16维对应手部关节的增量或目标值
                joint_update = last_action[:, :16]  # [B, 16]

                # 策略：使用指数移动平均进行平滑更新
                alpha = 0.3  # 更新率
                updated_obs[:, :16] = (1 - alpha) * current_obs[
                    :, :16
                ] + alpha * joint_update

            # 更新手部位姿状态（16-25维，如果观察中包含）
            if current_obs.shape[-1] > 25 and last_action.shape[-1] >= 25:
                # 假设动作的16-25维对应手部位姿的增量或目标值
                pose_update = last_action[:, 16:25]  # [B, 9]

                # 对位姿进行更平滑的更新
                alpha_pose = 0.2  # 位姿更新率（通常比关节更小）
                if current_obs.shape[-1] >= 25:
                    updated_obs[:, 16:25] = (1 - alpha_pose) * current_obs[
                        :, 16:25
                    ] + alpha_pose * pose_update

            # 🆕 可以添加更复杂的物理约束和状态传播逻辑
            # 例如：物体位置更新、接触状态更新等

            return updated_obs
        else:
            # 如果没有动作，保持当前状态不变
            return current_obs

    def configure_rolling_prediction(self, **kwargs):
        """
        配置滚动预测参数

        Args:
            **kwargs: 滚动预测配置参数
                - chunk_size: 分块大小
                - observation_mode: 观察模式 ('auto', 'sequence', 'cumulative')
                - use_true_actions_for_state_update: 是否使用真实动作进行状态更新
                - state_reset_mode: 状态重置模式
        """
        for key, value in kwargs.items():
            if key in self.rolling_prediction_config:
                self.rolling_prediction_config[key] = value
                self.logger.info(f"🔧 滚动预测配置更新: {key} = {value}")
            else:
                self.logger.warning(f"⚠️ 未知的滚动预测配置参数: {key}")

    def _perform_sequence_variance_analysis(
        self,
        num_samples: int,
        output_dir: Path,
        model_name: str = None,
        dataset_name: str = None,
    ):
        """
        执行序列观察方差分析

        Args:
            num_samples: 分析使用的样本数量，-1表示使用所有样本
            output_dir: 输出目录
            model_name: 模型名称
            dataset_name: 数据集名称
        """
        self.logger.info("🔍 开始序列观察方差分析...")

        # 创建序列观察方差分析器，传递手部类型
        variance_analyzer = SequenceObservationVarianceAnalyzer(
            logger=self.logger, hand_type=self.hand_type
        )

        # 配置分析参数
        variance_analyzer.configure_analysis(
            min_sequences_required=(
                min(10, max(5, num_samples // 10)) if num_samples > 0 else 10
            ),
            min_sequence_length=2,
            variance_ratio_threshold=0.3,
            enable_detailed_logging=num_samples <= 100,  # 少样本时启用详细日志
        )

        # 准备轨迹样本数据
        trajectory_samples = self._prepare_trajectory_samples_for_variance_analysis(
            num_samples
        )

        if not trajectory_samples:
            self.logger.error("❌ 无法获取轨迹样本数据，序列观察方差分析终止")
            return

        # 执行方差分析
        analysis_results = variance_analyzer.analyze_sequence_observation_variance(
            trajectory_samples
        )

        if not analysis_results:
            self.logger.error("❌ 序列观察方差分析失败")
            return

        # 保存分析结果
        self._save_variance_analysis_results(
            analysis_results, output_dir, model_name, dataset_name
        )

        # 生成可视化（如果有足够数据）
        if len(trajectory_samples) >= 20:
            try:
                self._generate_variance_analysis_visualization(
                    analysis_results,
                    trajectory_samples,
                    output_dir,
                    model_name,
                    dataset_name,
                )
            except Exception as e:
                self.logger.warning(f"⚠️ 方差分析可视化生成失败: {e}")

        self.logger.info("✅ 序列观察方差分析完成")

    def _prepare_trajectory_samples_for_variance_analysis(
        self, num_samples: int
    ) -> List[Dict]:
        """准备轨迹样本数据用于方差分析"""
        trajectory_samples = []

        try:
            # 如果已经有加载的样本，直接使用
            if hasattr(self, "samples") and self.samples:
                samples_to_use = (
                    self.samples[:num_samples] if num_samples > 0 else self.samples
                )
                self.logger.info(
                    f"📊 使用已加载的样本数据: {len(samples_to_use)} 个样本"
                )

                for sample in samples_to_use:
                    trajectory_samples.append(
                        {
                            "obs_dict": sample.get("obs_dict", {}),
                            "actions": sample.get("actions", {}),
                            "target_pose": sample.get("target_pose", {}),
                        }
                    )

            # 如果没有预加载样本，创建临时数据集采样器
            elif self.dataset_sampler is None:
                self.logger.info("📂 创建临时数据集采样器用于方差分析...")
                from eval.data.dataset_sampler import DatasetSampler

                temp_sampler = DatasetSampler(dataset_path=str(self.dataset_path))

                # 确定样本数量
                if num_samples > 0:
                    actual_num_samples = num_samples
                elif num_samples == -1:
                    actual_num_samples = len(temp_sampler)  # 使用所有样本
                else:
                    actual_num_samples = min(100, len(temp_sampler))  # 默认100

                self.logger.info(
                    f"📊 从数据集提取 {actual_num_samples} 个样本用于方差分析..."
                )

                for i in range(actual_num_samples):
                    try:
                        sample = temp_sampler.get_sample(i)
                        if sample:
                            # DatasetSampler返回的是原始训练数据格式，需要转换
                            # 🔧 修复：实际格式是 [state_dict, actions_dict] 长度为2，不是5
                            if isinstance(sample, (list, tuple)) and len(sample) == 2:
                                # 训练数据格式：[state_dict, actions_dict]
                                state_dict, actions_dict = sample

                                # 使用process_imitation_batch处理数据，保留序列数据用于方差分析
                                try:
                                    if process_imitation_batch is not None:
                                        obs_dict, target_pose, _, _, _ = (
                                            process_imitation_batch(
                                                sample,
                                                (
                                                    self.device
                                                    if hasattr(self, "device")
                                                    else "cpu"
                                                ),
                                                preserve_sequence=True,
                                            )
                                        )

                                        # 转换为分析器期望的字典格式
                                        trajectory_samples.append(
                                            {
                                                "obs_dict": obs_dict,
                                                "actions": actions_dict,
                                                "target_pose": target_pose,
                                            }
                                        )
                                    else:
                                        # 回退处理：直接使用原始数据
                                        trajectory_samples.append(
                                            {
                                                "obs_dict": state_dict,
                                                "actions": actions_dict,
                                                "target_pose": None,
                                            }
                                        )
                                except Exception as e:
                                    self.logger.warning(
                                        f"样本 {i}: process_imitation_batch处理失败: {e}"
                                    )
                                    # 回退处理：直接使用原始数据
                                    trajectory_samples.append(
                                        {
                                            "obs_dict": state_dict,
                                            "actions": actions_dict,
                                            "target_pose": None,
                                        }
                                    )
                            elif isinstance(sample, dict):
                                # 如果已经是字典格式，直接使用
                                obs_dict = sample.get("obs_dict", sample.get("obs", {}))
                                actions = sample.get("actions", {})
                                target_pose = sample.get("target_pose", {})

                                trajectory_samples.append(
                                    {
                                        "obs_dict": obs_dict,
                                        "actions": actions,
                                        "target_pose": target_pose,
                                    }
                                )
                            else:
                                self.logger.warning(
                                    f"样本 {i}: 数据格式不支持，类型: {type(sample)}，长度: {len(sample) if hasattr(sample, '__len__') else 'N/A'}"
                                )
                                continue
                        else:
                            self.logger.warning(
                                f"样本 {i}: get_sample 返回 None 或空值"
                            )
                    except Exception as e:
                        self.logger.warning(f"跳过样本 {i}: {e}")
                        self.logger.warning(
                            f"样本 {i} 错误详情: {type(e).__name__}: {str(e)}"
                        )
                        continue

            # 使用现有的数据集采样器
            else:
                if num_samples > 0:
                    actual_num_samples = num_samples
                elif num_samples == -1:
                    actual_num_samples = len(self.dataset_sampler)  # 使用所有样本
                else:
                    actual_num_samples = min(100, len(self.dataset_sampler))  # 默认100
                self.logger.info(
                    f"📊 使用现有采样器提取 {actual_num_samples} 个样本..."
                )

                for i in range(actual_num_samples):
                    try:
                        sample = self.dataset_sampler.get_sample(i)
                        if sample:
                            # DatasetSampler返回的是原始训练数据格式，需要转换
                            # 🔧 修复：实际格式是 [state_dict, actions_dict] 长度为2，不是5
                            if isinstance(sample, (list, tuple)) and len(sample) == 2:
                                # 训练数据格式：[state_dict, actions_dict]
                                state_dict, actions_dict = sample

                                # 使用process_imitation_batch处理数据，保留序列数据用于方差分析
                                try:
                                    if process_imitation_batch is not None:
                                        obs_dict, target_pose, _, _, _ = (
                                            process_imitation_batch(
                                                sample,
                                                (
                                                    self.device
                                                    if hasattr(self, "device")
                                                    else "cpu"
                                                ),
                                                preserve_sequence=True,
                                            )
                                        )

                                        # 转换为分析器期望的字典格式
                                        trajectory_samples.append(
                                            {
                                                "obs_dict": obs_dict,
                                                "actions": actions_dict,
                                                "target_pose": target_pose,
                                            }
                                        )
                                    else:
                                        # 回退处理：直接使用原始数据
                                        trajectory_samples.append(
                                            {
                                                "obs_dict": state_dict,
                                                "actions": actions_dict,
                                                "target_pose": None,
                                            }
                                        )
                                except Exception as e:
                                    self.logger.warning(
                                        f"样本 {i}: process_imitation_batch处理失败: {e}"
                                    )
                                    # 回退处理：直接使用原始数据
                                    trajectory_samples.append(
                                        {
                                            "obs_dict": state_dict,
                                            "actions": actions_dict,
                                            "target_pose": None,
                                        }
                                    )
                            elif isinstance(sample, dict):
                                # 如果已经是字典格式，直接使用
                                obs_dict = sample.get("obs_dict", sample.get("obs", {}))
                                actions = sample.get("actions", {})
                                target_pose = sample.get("target_pose", {})

                                trajectory_samples.append(
                                    {
                                        "obs_dict": obs_dict,
                                        "actions": actions,
                                        "target_pose": target_pose,
                                    }
                                )
                            else:
                                self.logger.warning(
                                    f"样本 {i}: 数据格式不支持，类型: {type(sample)}，长度: {len(sample) if hasattr(sample, '__len__') else 'N/A'}"
                                )
                                continue
                        else:
                            self.logger.warning(
                                f"样本 {i}: get_sample 返回 None 或空值"
                            )
                    except Exception as e:
                        self.logger.warning(f"跳过样本 {i}: {e}")
                        self.logger.warning(
                            f"样本 {i} 错误详情: {type(e).__name__}: {str(e)}"
                        )
                        continue

            self.logger.info(
                f"✅ 成功准备 {len(trajectory_samples)} 个轨迹样本用于方差分析"
            )
            return trajectory_samples

        except Exception as e:
            self.logger.error(f"❌ 准备轨迹样本数据失败: {e}")
            return []

    def _save_variance_analysis_results(
        self,
        analysis_results: Dict,
        output_dir: Path,
        model_name: str = None,
        dataset_name: str = None,
    ):
        """保存方差分析结果"""
        try:
            # 创建结果文件
            results_file = output_dir / "sequence_observation_variance_analysis.json"

            # 添加元数据
            full_results = {
                "metadata": {
                    "model_name": model_name or "unknown",
                    "dataset_name": dataset_name or "unknown",
                    "analysis_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "analyzer_version": "1.0.0",
                },
                "analysis_results": analysis_results,
            }

            with open(results_file, "w", encoding="utf-8") as f:
                json.dump(full_results, f, indent=2, ensure_ascii=False, default=str)

            self.logger.info(f"💾 方差分析结果已保存: {results_file}")

        except Exception as e:
            self.logger.error(f"❌ 保存方差分析结果失败: {e}")

    def _generate_variance_analysis_visualization(
        self,
        analysis_results: Dict,
        trajectory_samples: List[Dict],
        output_dir: Path,
        model_name: str = None,
        dataset_name: str = None,
    ):
        """生成方差分析可视化"""
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns

            # 设置图形样式
            plt.style.use("default")
            sns.set_palette("husl")

            # 创建图形
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(
                f'序列观察方差分析 - {model_name or "Unknown Model"}',
                fontsize=16,
                fontweight="bold",
            )

            # 1. 方差比例柱状图
            ax1 = axes[0, 0]
            variance_ratio = analysis_results["analysis_results"]["variance_ratio"]
            threshold = analysis_results["analysis_results"]["threshold_used"]

            bars = ax1.bar(
                ["序列内/序列间\n方差比例"],
                [variance_ratio],
                color="red" if variance_ratio > threshold else "green",
                alpha=0.7,
            )
            ax1.axhline(
                y=threshold, color="orange", linestyle="--", label=f"阈值 ({threshold})"
            )
            ax1.set_ylabel("方差比例")
            ax1.set_title("方差比例分析")
            ax1.legend()

            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                ax1.text(
                    bar.get_x() + bar.get_width() / 2.0,
                    height,
                    f"{height:.4f}",
                    ha="center",
                    va="bottom",
                )

            # 2. 方差分布对比
            ax2 = axes[0, 1]
            within_var = analysis_results["within_sequence_variance"]["mean_variance"]
            between_var = analysis_results["between_sequence_variance"]["mean_variance"]

            categories = ["序列内方差", "序列间方差"]
            values = [within_var, between_var]
            colors = ["lightcoral", "lightblue"]

            bars = ax2.bar(categories, values, color=colors, alpha=0.7)
            ax2.set_ylabel("方差值")
            ax2.set_title("方差分布对比")
            ax2.set_yscale("log")  # 使用对数尺度以便更好地显示差异

            # 添加数值标签
            for bar, val in zip(bars, values):
                ax2.text(
                    bar.get_x() + bar.get_width() / 2.0,
                    val,
                    f"{val:.6f}",
                    ha="center",
                    va="bottom",
                )

            # 3. 问题严重程度指示器
            ax3 = axes[1, 0]
            severity = analysis_results["analysis_results"]["problem_severity"]
            problem_detected = analysis_results["analysis_results"]["problem_detected"]

            severity_colors = {
                "none": "green",
                "mild": "yellow",
                "moderate": "orange",
                "severe": "red",
            }

            ax3.pie(
                [1],
                colors=[severity_colors.get(severity, "gray")],
                labels=[f"问题严重程度:\n{severity}"],
                startangle=90,
            )
            ax3.set_title(
                f'问题检测结果\n{"检测到问题" if problem_detected else "未检测到问题"}'
            )

            # 4. 建议摘要
            ax4 = axes[1, 1]
            ax4.axis("off")

            recommendations = analysis_results.get("recommendations", [])
            rec_text = "\n".join(
                [f"• {rec}" for rec in recommendations[:5]]
            )  # 显示前5条建议

            ax4.text(
                0.05,
                0.95,
                "修复建议:",
                fontsize=12,
                fontweight="bold",
                transform=ax4.transAxes,
                verticalalignment="top",
            )
            ax4.text(
                0.05,
                0.85,
                rec_text,
                fontsize=10,
                transform=ax4.transAxes,
                verticalalignment="top",
                wrap=True,
            )

            plt.tight_layout()

            # 保存图形
            viz_file = output_dir / "sequence_observation_variance_analysis.png"
            plt.savefig(viz_file, dpi=300, bbox_inches="tight")
            plt.close()

            self.logger.info(f"📊 方差分析可视化已生成: {viz_file}")

        except Exception as e:
            self.logger.error(f"❌ 生成方差分析可视化失败: {e}")
            import traceback

            traceback.print_exc()


class MemoryOptimizedTrajectoryAnalyzer(TrajectoryAnalyzer):
    """内存优化的轨迹分析器"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 内存优化配置
        self.memory_config = {
            "enable_batch_processing": True,
            "batch_size": 32,
            "max_cache_size": 500,
            "enable_lazy_loading": True,
            "enable_memory_mapping": True,
            "enable_tensor_reuse": True,
        }

        # 内存池
        self._tensor_pool = {}
        self._analysis_cache = {}

        # 只在直接实例化MemoryOptimizedTrajectoryAnalyzer时打印，避免子类重复打印
        if self.__class__ == MemoryOptimizedTrajectoryAnalyzer:
            self.logger.info("🔧 内存优化轨迹分析器初始化完成")

    def load_dataset_optimized(self, num_samples: int = 10):
        """优化的数据集加载方法"""
        try:
            self.logger.info(f"📊 优化加载数据集: {self.dataset_path}")

            # 创建数据集采样器
            # 🔧 修复：确保评估时只使用训练时见过的数据
            self.dataset_sampler = DatasetSampler(
                dataset_path=str(self.dataset_path),
                use_train_split_only=True,  # 只使用训练集，不包含验证集
            )

            # 创建高效数据加载器
            self.efficient_loader = EfficientDataLoader(
                self.dataset_sampler,
                batch_size=self.memory_config["batch_size"],
                max_cache_size=self.memory_config["max_cache_size"],
                enable_memory_mapping=self.memory_config["enable_memory_mapping"],
            )

            self.logger.info("✅ 高效数据加载器创建成功")

            # 处理提取所有样本的情况
            if num_samples == -1:
                self.logger.info("📋 准备提取数据集中的所有样本...")
                return self._load_all_samples_optimized()
            else:
                self.logger.info(f"📋 准备提取 {num_samples} 个样本进行分析")
                return self._load_limited_samples_optimized(num_samples)

        except Exception as e:
            self.logger.error(f"❌ 优化数据集加载失败: {e}")
            import traceback

            traceback.print_exc()
            return False

    def _load_limited_samples_optimized(self, num_samples: int):
        """优化的限量样本加载"""
        if self.memory_config["enable_batch_processing"]:
            # 使用批处理加载
            self.samples = self.efficient_loader.get_samples_batch(num_samples)
        else:
            # 回退到原始方法
            return self._load_limited_samples(num_samples)

        self.logger.info(f"✅ 成功提取 {len(self.samples)} 个样本")
        return True

    def _load_all_samples_optimized(self):
        """优化的全量样本加载"""
        # 首先检测数据集大小 - 避免重复日志输出
        dataset_size = self._get_dataset_size_silent()
        if not dataset_size:
            self.logger.warning("⚠️ 无法检测数据集大小，回退到原始方法")
            return self._load_all_samples()

        # 应用安全限制
        max_samples = min(10000, dataset_size)
        self.logger.info(f"📊 检测到数据集大小: {dataset_size}，将加载: {max_samples}")

        # 使用批处理加载
        if self.memory_config["enable_batch_processing"]:
            self.samples = self.efficient_loader.get_samples_batch(max_samples)
        else:
            return self._load_all_samples()

        self.logger.info(f"✅ 成功提取 {len(self.samples)} 个样本")
        return True

    def _get_dataset_size_silent(self):
        """静默获取数据集大小，不打印日志"""
        try:
            # 🆕 方法1: 优先检查采样器是否有长度属性（新的DatasetSampler支持）
            if hasattr(self.dataset_sampler, "__len__"):
                try:
                    size = len(self.dataset_sampler)
                    if size > 0:
                        return size
                except Exception:
                    pass

            # 方法2: 检查底层数据集大小
            underlying_dataset = None

            # 检查DatasetSampler是否有train_dataloader属性
            if hasattr(self.dataset_sampler, "train_dataloader"):
                dataloader = self.dataset_sampler.train_dataloader
                if hasattr(dataloader, "dataset"):
                    underlying_dataset = dataloader.dataset

            # 检查是否有dataset属性
            if underlying_dataset is None and hasattr(self.dataset_sampler, "dataset"):
                underlying_dataset = self.dataset_sampler.dataset

            # 如果找到底层数据集，获取其大小
            if underlying_dataset is not None:
                try:
                    dataset_size = len(underlying_dataset)
                    return dataset_size
                except Exception:
                    pass

            # 方法3: 检查采样器的数据集属性
            if hasattr(self.dataset_sampler, "dataset") and hasattr(
                self.dataset_sampler.dataset, "__len__"
            ):
                return len(self.dataset_sampler.dataset)

            # 方法4: 检查是否有size或length属性
            for attr_name in ["size", "length", "num_samples", "total_samples"]:
                if hasattr(self.dataset_sampler, attr_name):
                    size = getattr(self.dataset_sampler, attr_name)
                    if isinstance(size, int) and size > 0:
                        return size

            # 方法5: 如果是文件夹，尝试计算文件数量
            if self.dataset_path.is_dir():
                # 计算可能的数据文件
                data_files = (
                    list(self.dataset_path.glob("*.pkl"))
                    + list(self.dataset_path.glob("*.npz"))
                    + list(self.dataset_path.glob("*.h5"))
                )
                if data_files:
                    return len(data_files)

            return None

        except Exception:
            return None

    def _infer_dataset_size_from_checkpoints(
        self, theoretical_size: int, output_dir: str
    ) -> int:
        """基于实际结果文件推断数据集大小"""
        try:
            checkpoint_dir = Path(output_dir) / "checkpoints"
            if not checkpoint_dir.exists():
                self.logger.info(f"📊 无检查点数据，使用理论大小: {theoretical_size}")
                return theoretical_size

            # 🆕 扫描所有results_*.pkl文件，找到实际的最大索引
            results_files = list(checkpoint_dir.glob("results_*.pkl"))
            if not results_files:
                self.logger.info(f"📊 无结果文件，使用理论大小: {theoretical_size}")
                return theoretical_size

            max_actual_size = 0
            total_results = 0
            valid_files = 0

            # 解析所有结果文件的索引
            self.logger.info(f"📊 扫描 {len(results_files)} 个结果文件...")

            for results_file in results_files:
                try:
                    # 从文件名提取索引，如 results_008167.pkl -> 8167
                    file_idx = int(results_file.stem.split("_")[1])

                    # 检查文件是否有效（非空）
                    try:
                        with open(results_file, "rb") as f:
                            results = pickle.load(f)
                        if len(results) > 0:
                            max_actual_size = max(max_actual_size, file_idx)
                            total_results += len(results)
                            valid_files += 1
                            # 记录找到的有效文件
                            if file_idx == max_actual_size:
                                self.logger.debug(
                                    f"📂 找到有效结果文件: {results_file.name} (包含 {len(results)} 个结果)"
                                )
                        else:
                            self.logger.debug(f"📂 跳过空结果文件: {results_file.name}")
                    except Exception as e:
                        # 文件损坏或无法读取，跳过
                        self.logger.debug(f"📂 跳过损坏文件: {results_file.name} ({e})")
                        continue

                except (ValueError, IndexError):
                    # 文件名格式不正确，跳过
                    self.logger.debug(f"📂 跳过格式错误文件: {results_file.name}")
                    continue

            if max_actual_size > 0:
                # 基于实际结果文件推断数据集大小
                if max_actual_size < theoretical_size * 0.6:
                    # 实际大小明显小于理论大小
                    inferred_size = max_actual_size
                    self.logger.warning(
                        f"📊 实际数据集大小小于理论值: {inferred_size} < {theoretical_size}"
                    )
                    self.logger.info(
                        f"📊 基于 {valid_files} 个有效结果文件推断，总结果数: {total_results}"
                    )
                else:
                    # 实际大小接近理论大小
                    inferred_size = theoretical_size
                    self.logger.info(
                        f"📊 实际大小接近理论值，使用理论大小: {inferred_size}"
                    )

                return inferred_size
            else:
                self.logger.info(f"📊 无有效结果文件，使用理论大小: {theoretical_size}")
                return theoretical_size

        except Exception as e:
            self.logger.warning(
                f"⚠️ 结果文件分析失败: {e}，使用理论大小: {theoretical_size}"
            )
            return theoretical_size

    def _conservative_size_estimate(self, theoretical_size: int) -> int:
        """保守的数据集大小估计"""
        # 使用理论大小的60%作为保守估计，避免过于保守
        conservative_size = (
            max(1000, int(theoretical_size * 0.6)) if theoretical_size else 8000
        )
        self.logger.warning(
            f"⚠️ 使用保守估计: {conservative_size} (理论: {theoretical_size})"
        )
        return conservative_size

    def analyze_trajectories_batch(self, batch_size: int = 16):
        """批量分析轨迹，优化内存使用"""
        if not hasattr(self, "samples") or not self.samples:
            self.logger.error("❌ 没有可分析的样本")
            return []

        self.logger.info(
            f"📈 批量分析 {len(self.samples)} 条轨迹，批次大小: {batch_size}"
        )

        analysis_results = []
        all_predictions = []

        num_batches = (len(self.samples) + batch_size - 1) // batch_size

        with tqdm(total=len(self.samples), desc="批量分析轨迹") as pbar:
            for batch_idx in range(num_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, len(self.samples))

                # 分析当前批次
                batch_results = self._analyze_batch(start_idx, end_idx)

                for result in batch_results:
                    if result:
                        analysis_results.append(result)
                        if "predictions" in result:
                            all_predictions.append(result["predictions"])

                pbar.update(end_idx - start_idx)

                # 定期清理内存
                if batch_idx % 5 == 0:
                    self._cleanup_batch_memory()

        self.logger.info(f"✅ 批量分析完成: {len(analysis_results)} 个成功结果")
        return analysis_results, all_predictions

    def _analyze_batch(self, start_idx: int, end_idx: int):
        """分析一个批次的轨迹"""
        batch_results = []

        for i in range(start_idx, end_idx):
            try:
                # 检查分析缓存
                cache_key = f"analysis_{i}"
                if cache_key in self._analysis_cache:
                    result = self._analysis_cache[cache_key]
                else:
                    result = self.analyze_single_trajectory(i)
                    # 缓存结果（如果启用）
                    if self.memory_config.get("enable_analysis_cache", False):
                        self._analysis_cache[cache_key] = result

                batch_results.append(result)

            except Exception as e:
                self.logger.warning(f"⚠️ 轨迹 {i} 分析失败: {e}")
                batch_results.append(None)

        return batch_results

    def _cleanup_batch_memory(self):
        """清理批次处理中的内存"""
        import gc

        # 清理tensor池
        for key in list(self._tensor_pool.keys()):
            if not getattr(self._tensor_pool[key], "_in_use", False):
                del self._tensor_pool[key]

        # 清理分析缓存（保留最近的）
        if len(self._analysis_cache) > 100:
            # 保留最近的50个结果
            cache_keys = list(self._analysis_cache.keys())
            for key in cache_keys[:-50]:
                del self._analysis_cache[key]

        # 强制垃圾回收
        gc.collect()

        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    def get_reusable_tensor(self, shape, dtype=torch.float32, device=None):
        """获取可复用的tensor，减少内存分配"""
        if not self.memory_config["enable_tensor_reuse"]:
            return torch.zeros(shape, dtype=dtype, device=device)

        device = device or self.device
        key = f"{shape}_{dtype}_{device}"

        if key in self._tensor_pool:
            tensor = self._tensor_pool[key]
            tensor.zero_()  # 清零复用
            tensor._in_use = True
            return tensor
        else:
            tensor = torch.zeros(shape, dtype=dtype, device=device)
            tensor._in_use = True
            self._tensor_pool[key] = tensor
            return tensor

    def release_tensor(self, tensor):
        """释放tensor回到池中"""
        if hasattr(tensor, "_in_use"):
            tensor._in_use = False


class StreamingTrajectoryAnalyzer(MemoryOptimizedTrajectoryAnalyzer):
    """流式轨迹分析器 - 支持大数据集的内存友好处理"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 流式处理配置
        self.streaming_config = {
            "chunk_size": 50,  # 每个处理块的大小
            "save_frequency": 100,  # 每处理多少个样本保存一次结果
            "memory_threshold": 0.8,  # 内存使用阈值
            "enable_progress_bar": True,
            "max_retries": 3,  # 最大重试次数
        }

        # 内存监控
        self.memory_monitor = MemoryMonitor()

        # 统计信息
        self.streaming_stats = {
            "total_processed": 0,
            "successful_analyses": 0,
            "failed_analyses": 0,
            "chunks_processed": 0,
            "total_processing_time": 0,
            "average_chunk_time": 0,
            "memory_usage_history": [],
            "error_history": [],
        }

    def analyze_dataset_streaming(
        self, num_samples: int = -1, output_dir: str = "streaming_analysis_output"
    ) -> Tuple[Dict, Dict]:
        """
        流式分析数据集 - 真正的内存友好处理

        Args:
            num_samples: 要分析的样本数量，-1表示所有样本
            output_dir: 输出目录

        Returns:
            Tuple[summary_stats, final_predictions_summary]: 汇总统计和预测汇总
        """
        start_time = time.time()

        # 创建输出目录结构
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 创建专门的中间文件夹
        self.intermediate_dir = output_path / "intermediate_results"
        self.intermediate_dir.mkdir(parents=True, exist_ok=True)

        self.chunks_dir = output_path / "chunks"
        self.chunks_dir.mkdir(parents=True, exist_ok=True)

        self.logger.info("🌊 开始真正的流式数据集分析")
        self.logger.info(f"📁 中间结果目录: {self.intermediate_dir}")
        self.logger.info(f"📁 数据块目录: {self.chunks_dir}")

        # 获取数据集大小
        if num_samples == -1:
            total_samples = self._get_dataset_size_silent()
            self.logger.info(f"✅ 数据集大小: {total_samples}")
        else:
            total_samples = min(num_samples, self._get_dataset_size_silent())
            self.logger.info(f"✅ 将分析 {total_samples} 个样本")

        # 创建数据集迭代器
        iterator = LazyDatasetIterator(
            self.dataset_sampler,
            max_samples=total_samples,
            start_index=0,
            dataset_size=total_samples,
        )

        # 开始流式处理
        summary_stats, predictions_summary = self._process_streaming_chunks_true(
            iterator, total_samples, output_dir
        )

        # 更新统计信息
        total_time = time.time() - start_time
        self.streaming_stats["total_processing_time"] = total_time
        self.streaming_stats["average_chunk_time"] = total_time / max(
            1, self.streaming_stats["chunks_processed"]
        )

        # 保存最终统计
        final_stats = {
            **summary_stats,
            "processing_time": total_time,
            "streaming_stats": self.streaming_stats,
            "output_directories": {
                "main": str(output_path),
                "intermediate": str(self.intermediate_dir),
                "chunks": str(self.chunks_dir),
            },
        }

        # 保存最终统计到文件
        stats_file = output_path / "final_statistics.json"
        with open(stats_file, "w") as f:
            json.dump(final_stats, f, indent=2, default=str)

        self.logger.info(f"✅ 流式分析完成，总耗时: {total_time:.2f}秒")
        self.logger.info(f"📊 最终统计已保存: {stats_file}")

        # 生成第一条轨迹的可视化（如果有数据）
        if not self.streaming_config.get("no_visualization", False):
            try:
                # 从chunks中加载第一条轨迹数据
                first_trajectory = self._get_first_trajectory_from_chunks(output_dir)
                if first_trajectory:
                    viz_path = Path(output_dir) / "trajectory_1_viz"
                    self.generate_visualization(first_trajectory, str(viz_path))
                    self.logger.info(f"✅ 第一条轨迹可视化已生成: {viz_path}")
            except Exception as e:
                self.logger.warning(f"⚠️ 第一条轨迹可视化生成失败: {e}")

        return final_stats, predictions_summary

    def _get_first_trajectory_from_chunks(self, output_dir: str) -> Optional[Dict]:
        """从chunks中获取第一条轨迹数据"""
        try:
            chunks_dir = Path(output_dir) / "chunks"
            if not chunks_dir.exists():
                return None

            # 查找第一个chunk文件
            chunk_files = sorted(chunks_dir.glob("chunk_*.pkl"))
            if not chunk_files:
                return None

            # 加载第一个chunk
            with open(chunk_files[0], "rb") as f:
                chunk_data = pickle.load(f)

            # 返回第一条有效轨迹
            if chunk_data and len(chunk_data) > 0:
                return chunk_data[0]

            return None
        except Exception as e:
            self.logger.warning(f"⚠️ 从chunks获取第一条轨迹失败: {e}")
            return None

    def _process_streaming_chunks(
        self, iterator: LazyDatasetIterator, total_samples: int, output_dir: str
    ) -> Tuple[List[Dict], List[Dict]]:
        """处理流式数据块"""
        analysis_results = []
        all_predictions = []

        chunk_size = self.streaming_config["chunk_size"]
        save_frequency = self.streaming_config["save_frequency"]

        # 进度条
        if self.streaming_config["enable_progress_bar"]:
            pbar = tqdm(total=total_samples, desc="🔄 流式分析进度")

        chunk_samples = []
        chunk_indices = []
        processed_count = 0

        try:
            for sample_idx, sample in enumerate(iterator):
                if sample is None:
                    self.streaming_stats["failed_analyses"] += 1
                    continue

                chunk_samples.append(sample)
                chunk_indices.append(sample_idx)

                # 当块满了或者是最后一个样本时，处理当前块
                if len(chunk_samples) >= chunk_size or sample_idx >= total_samples - 1:
                    chunk_start_time = time.time()

                    # 处理当前块
                    chunk_results = self._process_chunk(chunk_samples, chunk_indices)

                    # 收集结果
                    for result in chunk_results:
                        if result:
                            analysis_results.append(result)
                            if "predictions" in result:
                                all_predictions.append(result["predictions"])
                            self.streaming_stats["successful_analyses"] += 1
                        else:
                            self.streaming_stats["failed_analyses"] += 1

                    processed_count += len(chunk_samples)

                    # 定期保存结果
                    if processed_count % save_frequency == 0:
                        self._save_intermediate_results(
                            analysis_results,
                            all_predictions,
                            output_dir,
                            processed_count,
                        )

                    # 更新统计信息
                    chunk_time = time.time() - chunk_start_time
                    self.streaming_stats["chunks_processed"] += 1
                    self.streaming_stats["total_processed"] = processed_count

                    # 内存监控
                    memory_info = self.memory_monitor.get_memory_info()
                    self.streaming_stats["memory_usage_history"].append(
                        memory_info["percent"]
                    )

                    # 内存清理
                    if self.memory_monitor.is_memory_warning():
                        self.logger.warning(
                            f"⚠️ 内存使用率较高: {memory_info['percent']*100:.1f}%"
                        )
                        self._cleanup_streaming_memory()

                    # 更新进度条
                    if self.streaming_config["enable_progress_bar"]:
                        pbar.update(len(chunk_samples))
                        pbar.set_postfix(
                            {
                                "成功": self.streaming_stats["successful_analyses"],
                                "失败": self.streaming_stats["failed_analyses"],
                                "内存": f"{memory_info['percent']*100:.1f}%",
                            }
                        )

                    # 清空当前块
                    chunk_samples = []
                    chunk_indices = []

                    # 检查是否需要停止
                    if processed_count >= total_samples:
                        break

        except Exception as e:
            self.logger.error(f"❌ 流式处理出错: {e}")
            self.streaming_stats["error_history"].append(str(e))

        finally:
            if self.streaming_config["enable_progress_bar"]:
                pbar.close()

        # 保存最终结果
        if analysis_results:
            self._save_intermediate_results(
                analysis_results,
                all_predictions,
                output_dir,
                processed_count,
                is_final=True,
            )

        self.logger.info(
            f"📊 流式处理完成: 成功 {self.streaming_stats['successful_analyses']}, 失败 {self.streaming_stats['failed_analyses']}"
        )

        return analysis_results, all_predictions

    def _save_intermediate_results(
        self,
        analysis_results: List[Dict],
        all_predictions: List[Dict],
        output_dir: str,
        processed_count: int,
        is_final: bool = False,
    ):
        """保存中间结果"""
        try:
            output_path = Path(output_dir)

            # 保存分析结果
            results_file = output_path / f"results_{processed_count:06d}.pkl"
            with open(results_file, "wb") as f:
                pickle.dump(analysis_results, f)

            # 保存预测数据
            if all_predictions:
                predictions_file = (
                    output_path / f"predictions_{processed_count:06d}.pkl"
                )
                with open(predictions_file, "wb") as f:
                    pickle.dump(all_predictions, f)

            if is_final:
                self.logger.info(f"💾 最终结果已保存: {results_file}")
            else:
                self.logger.debug(f"💾 中间结果已保存: {results_file}")

        except Exception as e:
            self.logger.warning(f"⚠️ 保存结果失败: {e}")

    def _cleanup_streaming_memory(self):
        """清理流式处理中的内存"""
        import gc

        # 强制垃圾回收
        gc.collect()

        # 清理GPU内存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        # 清理分析缓存
        if hasattr(self, "_analysis_cache"):
            self._analysis_cache.clear()

        # 清理tensor池
        if hasattr(self, "_tensor_pool"):
            self._tensor_pool.clear()

    def _process_chunk(
        self, chunk_samples: List, chunk_indices: List[int]
    ) -> List[Dict]:
        """处理一个数据块"""
        chunk_results = []

        # 🆕 诊断模式：详细分析第一个样本
        is_first_chunk = len(chunk_indices) > 0 and chunk_indices[0] == 0

        for i, (sample, sample_idx) in enumerate(zip(chunk_samples, chunk_indices)):
            try:
                # 🆕 第一个样本的详细诊断
                if is_first_chunk and i == 0:
                    self.logger.info(
                        f"🔍 诊断模式：详细分析第一个样本 (索引 {sample_idx})"
                    )
                    self.logger.info(f"   📋 样本类型: {type(sample)}")
                    if isinstance(sample, (list, tuple)):
                        self.logger.info(f"   📋 样本长度: {len(sample)}")
                        for j, item in enumerate(sample[:2]):  # 只显示前2个元素
                            self.logger.info(f"   📋 元素 {j}: {type(item)}")
                            if isinstance(item, dict):
                                self.logger.info(
                                    f"      键: {list(item.keys())[:5]}..."
                                )  # 只显示前5个键

                # 临时设置样本用于分析
                original_samples = getattr(self, "samples", None)
                self.samples = [sample]

                # 分析单个轨迹
                result = self.analyze_single_trajectory(0)
                if result:
                    result["original_sample_index"] = sample_idx  # 记录原始索引
                    chunk_results.append(result)

                else:
                    # 记录失败的样本
                    chunk_results.append(None)

                    # 🆕 详细记录失败原因
                    if is_first_chunk and i < 5:  # 前5个样本的详细信息
                        self.logger.error(
                            f"❌ 样本 {sample_idx} 分析返回 None，可能原因："
                        )
                        self.logger.error(
                            f"   - 模型未加载: {not hasattr(self, 'model') or self.model is None}"
                        )
                        self.logger.error(f"   - 样本格式错误: {type(sample)}")
                        if isinstance(sample, (list, tuple)):
                            self.logger.error(f"   - 样本长度: {len(sample)}")

                # 恢复原始样本列表
                if original_samples is not None:
                    self.samples = original_samples
                else:
                    delattr(self, "samples")

            except Exception as e:
                # 🆕 详细的异常信息
                error_msg = f"❌ 样本 {sample_idx} 分析异常: {str(e)}"
                self.logger.error(error_msg)

                # 🆕 前几个样本的详细异常信息
                if is_first_chunk and i < 5:
                    self.logger.error(f"   异常类型: {type(e).__name__}")
                    self.logger.error(f"   样本类型: {type(sample)}")
                    if isinstance(sample, (list, tuple)):
                        self.logger.error(f"   样本长度: {len(sample)}")
                        for j, item in enumerate(sample[:2]):  # 只显示前2个元素
                            self.logger.error(f"   元素 {j}: {type(item)}")

                    # 打印完整的异常堆栈
                    import traceback

                    self.logger.error("   完整异常堆栈:")
                    for line in traceback.format_exc().split("\n"):
                        if line.strip():
                            self.logger.error(f"     {line}")

                # 记录异常的样本
                chunk_results.append(None)
                continue

        # 🆕 块处理统计
        successful_in_chunk = len([r for r in chunk_results if r is not None])
        self.logger.debug(
            f"📊 块处理完成: {successful_in_chunk}/{len(chunk_samples)} 成功"
        )

        return chunk_results

    def get_streaming_stats(self) -> Dict[str, Any]:
        """获取流式处理统计信息"""
        memory_info = self.memory_monitor.get_memory_info()

        stats = {
            "memory_usage": memory_info,
            "streaming_config": self.streaming_config,
            "streaming_stats": self.streaming_stats,
            "total_processed": self.streaming_stats["total_processed"],
            "success_rate": (
                (
                    self.streaming_stats["successful_analyses"]
                    / max(1, self.streaming_stats["total_processed"])
                )
                if self.streaming_stats["total_processed"] > 0
                else 0
            ),
        }

        return stats

    def _process_streaming_chunks_true(
        self, iterator: LazyDatasetIterator, total_samples: int, output_dir: str
    ) -> Tuple[Dict, Dict]:
        """真正的流式处理 - 控制内存使用"""

        # 汇总统计信息
        summary_stats = {
            "total_samples": 0,
            "successful_analyses": 0,
            "failed_analyses": 0,
            "chunks_processed": 0,
            "error_summary": {},
            "performance_metrics": {
                "avg_processing_time_per_sample": 0,
                "memory_usage_stats": {
                    "max_memory_percent": 0,
                    "avg_memory_percent": 0,
                    "memory_warnings": 0,
                },
            },
        }

        # 预测数据汇总
        predictions_summary = {
            "total_predictions": 0,
            "prediction_stats": {},
            "error_distributions": {},
        }

        chunk_size = self.streaming_config["chunk_size"]
        save_frequency = self.streaming_config["save_frequency"]

        # 内存中保留少量最近的结果用于特殊操作（统计基于所有样本）
        recent_results_buffer = []
        recent_predictions_buffer = []
        max_buffer_size = 100  # 最多保留100个最近结果（不影响整体统计）

        # 进度条
        if self.streaming_config["enable_progress_bar"]:
            pbar = tqdm(total=total_samples, desc="🔄 流式分析")

        chunk_samples = []
        chunk_indices = []
        processed_count = 0
        chunk_counter = 0

        try:
            for sample_idx, sample in enumerate(iterator):
                if sample is None:
                    self.streaming_stats["failed_analyses"] += 1
                    summary_stats["failed_analyses"] += 1
                    continue

                chunk_samples.append(sample)
                chunk_indices.append(sample_idx)

                # 当块满了或者是最后一个样本时，处理当前块
                if len(chunk_samples) >= chunk_size or sample_idx >= total_samples - 1:
                    chunk_start_time = time.time()

                    # 处理当前块
                    chunk_results = self._process_chunk(chunk_samples, chunk_indices)

                    # 立即保存块结果到磁盘
                    chunk_file = self.chunks_dir / f"chunk_{chunk_counter:06d}.pkl"
                    with open(chunk_file, "wb") as f:
                        pickle.dump(chunk_results, f)

                    # 更新统计信息
                    successful_in_chunk = 0
                    failed_in_chunk = 0

                    for result in chunk_results:
                        if result:
                            successful_in_chunk += 1
                            # 保留最近的结果在内存中（用于特殊操作，不影响统计）
                            recent_results_buffer.append(result)
                            if "predictions" in result:
                                recent_predictions_buffer.append(result["predictions"])
                                predictions_summary[
                                    "total_predictions"
                                ] += 1  # 基于所有样本累积
                        else:
                            failed_in_chunk += 1

                    # 限制缓冲区大小
                    if len(recent_results_buffer) > max_buffer_size:
                        recent_results_buffer = recent_results_buffer[-max_buffer_size:]
                    if len(recent_predictions_buffer) > max_buffer_size:
                        recent_predictions_buffer = recent_predictions_buffer[
                            -max_buffer_size:
                        ]

                    # 更新汇总统计
                    summary_stats["successful_analyses"] += successful_in_chunk
                    summary_stats["failed_analyses"] += failed_in_chunk
                    summary_stats["chunks_processed"] += 1

                    processed_count += len(chunk_samples)
                    chunk_counter += 1

                    # 更新流式统计
                    chunk_time = time.time() - chunk_start_time
                    self.streaming_stats["chunks_processed"] += 1
                    self.streaming_stats["total_processed"] = processed_count
                    self.streaming_stats["successful_analyses"] += successful_in_chunk
                    self.streaming_stats["failed_analyses"] += failed_in_chunk

                    # 内存监控
                    memory_info = self.memory_monitor.get_memory_info()
                    memory_percent = memory_info["percent"] * 100
                    self.streaming_stats["memory_usage_history"].append(
                        memory_info["percent"]
                    )

                    # 更新内存统计
                    if (
                        memory_percent
                        > summary_stats["performance_metrics"]["memory_usage_stats"][
                            "max_memory_percent"
                        ]
                    ):
                        summary_stats["performance_metrics"]["memory_usage_stats"][
                            "max_memory_percent"
                        ] = memory_percent

                    if self.memory_monitor.is_memory_warning():
                        summary_stats["performance_metrics"]["memory_usage_stats"][
                            "memory_warnings"
                        ] += 1
                        self.logger.warning(f"⚠️ 内存使用率较高: {memory_percent:.1f}%")
                        self._cleanup_streaming_memory()

                    # 定期保存中间统计
                    if processed_count % save_frequency == 0:
                        self._save_intermediate_statistics(
                            summary_stats, predictions_summary, processed_count
                        )

                    # 更新进度条
                    if self.streaming_config["enable_progress_bar"]:
                        pbar.update(len(chunk_samples))
                        pbar.set_postfix(
                            {
                                "成功": summary_stats["successful_analyses"],
                                "失败": summary_stats["failed_analyses"],
                                "内存": f"{memory_percent:.1f}%",
                                "块": chunk_counter,
                            }
                        )

                    # 强制内存清理
                    del chunk_samples, chunk_results
                    chunk_samples = []
                    chunk_indices = []

                    # 定期强制垃圾回收
                    if chunk_counter % 10 == 0:
                        import gc

                        gc.collect()
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()

                    # 检查是否需要停止
                    if processed_count >= total_samples:
                        break

        except Exception as e:
            self.logger.error(f"❌ 流式处理出错: {e}")
            self.streaming_stats["error_history"].append(str(e))
            summary_stats["error_summary"]["critical_error"] = str(e)

        finally:
            if self.streaming_config["enable_progress_bar"]:
                pbar.close()

        # 计算最终统计
        summary_stats["total_samples"] = processed_count
        if len(self.streaming_stats["memory_usage_history"]) > 0:
            summary_stats["performance_metrics"]["memory_usage_stats"][
                "avg_memory_percent"
            ] = (
                sum(self.streaming_stats["memory_usage_history"])
                / len(self.streaming_stats["memory_usage_history"])
                * 100
            )

        if processed_count > 0:
            summary_stats["performance_metrics"]["avg_processing_time_per_sample"] = (
                self.streaming_stats.get("total_processing_time", 0) / processed_count
            )

        # 保存最终的中间统计
        self._save_intermediate_statistics(
            summary_stats, predictions_summary, processed_count, is_final=True
        )

        # 创建块索引文件
        self._create_chunk_index(chunk_counter)

        self.logger.info("📊 真正流式处理完成:")
        self.logger.info(f"   - 总样本: {summary_stats['total_samples']}")
        self.logger.info(f"   - 成功: {summary_stats['successful_analyses']}")
        self.logger.info(f"   - 失败: {summary_stats['failed_analyses']}")
        self.logger.info(f"   - 处理块数: {chunk_counter}")
        self.logger.info(
            f"   - 最大内存使用: {summary_stats['performance_metrics']['memory_usage_stats']['max_memory_percent']:.1f}%"
        )

        return summary_stats, predictions_summary

    def _save_intermediate_statistics(
        self,
        summary_stats: Dict,
        predictions_summary: Dict,
        processed_count: int,
        is_final: bool = False,
    ):
        """保存中间统计信息"""
        try:
            stats_data = {
                "timestamp": time.time(),
                "processed_count": processed_count,
                "summary_stats": summary_stats,
                "predictions_summary": predictions_summary,
                "is_final": is_final,
            }

            filename = (
                f"stats_{processed_count:06d}.json"
                if not is_final
                else "final_stats.json"
            )
            stats_file = self.intermediate_dir / filename

            with open(stats_file, "w") as f:
                json.dump(stats_data, f, indent=2, default=str)

            if is_final:
                self.logger.info(f"💾 最终统计已保存: {stats_file}")
            else:
                self.logger.debug(f"💾 中间统计已保存: {stats_file}")

        except Exception as e:
            self.logger.warning(f"⚠️ 保存统计信息失败: {e}")

    def _create_chunk_index(self, total_chunks: int):
        """创建块索引文件，方便后续访问"""
        try:
            chunk_index = {
                "total_chunks": total_chunks,
                "chunk_files": [f"chunk_{i:06d}.pkl" for i in range(total_chunks)],
                "created_at": time.time(),
                "chunk_directory": str(self.chunks_dir),
            }

            index_file = self.chunks_dir / "chunk_index.json"
            with open(index_file, "w") as f:
                json.dump(chunk_index, f, indent=2)

            self.logger.info(f"📋 块索引已创建: {index_file}")

        except Exception as e:
            self.logger.warning(f"⚠️ 创建块索引失败: {e}")

    def load_results_from_chunks(
        self, output_dir: str
    ) -> Tuple[List[Dict], List[Dict]]:
        """从保存的块中加载所有结果（如果需要的话）"""
        chunks_dir = Path(output_dir) / "chunks"
        index_file = chunks_dir / "chunk_index.json"

        if not index_file.exists():
            error_msg = f"❌ 未找到块索引文件: {index_file}"
            self.logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        try:
            with open(index_file, "r") as f:
                chunk_index = json.load(f)

            total_chunks = chunk_index["total_chunks"]
            if total_chunks == 0:
                error_msg = "❌ 块索引显示没有任何数据块"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            all_results = []
            all_predictions = []

            self.logger.info(f"📂 开始加载 {total_chunks} 个块...")

            # 🆕 数据完整性检查
            missing_chunks = []
            corrupted_chunks = []

            for i, chunk_file in enumerate(chunk_index["chunk_files"]):
                chunk_path = chunks_dir / chunk_file

                if not chunk_path.exists():
                    missing_chunks.append(chunk_file)
                    continue

                try:
                    with open(chunk_path, "rb") as f:
                        chunk_results = pickle.load(f)

                    # 验证块数据格式
                    if not isinstance(chunk_results, list):
                        corrupted_chunks.append(f"{chunk_file}: 不是列表格式")
                        continue

                    valid_results_in_chunk = 0
                    valid_predictions_in_chunk = 0

                    for result in chunk_results:
                        if result:  # 非None结果
                            all_results.append(result)
                            valid_results_in_chunk += 1

                            if "predictions" in result:
                                all_predictions.append(result["predictions"])
                                valid_predictions_in_chunk += 1

                    self.logger.debug(
                        f"   块 {i+1}/{total_chunks}: {valid_results_in_chunk} 个有效结果, {valid_predictions_in_chunk} 个预测"
                    )

                except Exception as e:
                    corrupted_chunks.append(f"{chunk_file}: {str(e)}")
                    continue

            # 🆕 检查数据完整性
            if missing_chunks:
                error_msg = f"❌ 缺失数据块: {missing_chunks}"
                self.logger.error(error_msg)
                raise FileNotFoundError(error_msg)

            if corrupted_chunks:
                error_msg = f"❌ 损坏的数据块: {corrupted_chunks}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            if len(all_results) == 0:
                error_msg = "❌ 所有块都没有有效的分析结果"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            if len(all_predictions) == 0:
                error_msg = "❌ 所有块都没有有效的预测数据，无法生成汇总分析"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 🆕 数据完整性报告
            self.logger.info("✅ 数据加载完成:")
            self.logger.info(f"   - 总块数: {total_chunks}")
            self.logger.info(f"   - 有效结果: {len(all_results)}")
            self.logger.info(f"   - 有效预测: {len(all_predictions)}")
            self.logger.info(
                f"   - 预测覆盖率: {len(all_predictions)/len(all_results)*100:.1f}%"
            )

            # 🆕 验证预测数据质量
            invalid_predictions = 0
            for i, pred in enumerate(all_predictions):
                if not isinstance(pred, dict) or "step_errors" not in pred:
                    invalid_predictions += 1

            if invalid_predictions > 0:
                self.logger.warning(
                    f"⚠️ 发现 {invalid_predictions} 个格式异常的预测数据"
                )
                if invalid_predictions / len(all_predictions) > 0.5:
                    error_msg = "❌ 超过50%的预测数据格式异常，数据质量不可接受"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)

            return all_results, all_predictions

        except (json.JSONDecodeError, KeyError) as e:
            error_msg = f"❌ 块索引文件格式错误: {e}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        except Exception as e:
            error_msg = f"❌ 加载块结果失败: {e}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)


class SequenceObservationVarianceAnalyzer:
    """
    序列观察方差分析器

    用于分析训练数据中序列内观察方差 vs 序列间观察方差的问题，
    帮助诊断归一化参数不匹配导致的模型性能问题。

    核心功能：
    1. 计算序列内观察方差（同一轨迹不同时间步的观察差异）
    2. 计算序列间观察方差（不同轨迹第一步观察的差异）
    3. 分析方差比例，评估问题严重程度
    4. 生成修复建议
    """

    def __init__(self, logger=None, hand_type="right"):
        """
        初始化序列观察方差分析器

        Args:
            logger: 日志记录器，如果为None则使用默认logger
            hand_type: 手部类型，"left" 或 "right"
        """
        self.logger = logger or setup_logger("SequenceObservationVarianceAnalyzer")
        self.hand_type = hand_type

        # 分析结果存储
        self.variance_results = {}
        self.analysis_config = {
            "min_sequences_required": 10,  # 最少需要的序列数
            "min_sequence_length": 2,  # 最少需要的序列长度
            "variance_ratio_threshold": 0.3,  # 方差比例阈值（问题严重程度判断）
            "enable_detailed_logging": True,  # 是否启用详细日志
        }

        self.logger.info("🔍 序列观察方差分析器初始化完成")

    def configure_analysis(self, **kwargs):
        """
        配置分析参数

        Args:
            **kwargs: 配置参数
                - min_sequences_required: 最少需要的序列数
                - min_sequence_length: 最少需要的序列长度
                - variance_ratio_threshold: 方差比例阈值
                - enable_detailed_logging: 是否启用详细日志
        """
        for key, value in kwargs.items():
            if key in self.analysis_config:
                self.analysis_config[key] = value
                self.logger.info(f"🔧 更新分析配置: {key} = {value}")
            else:
                self.logger.warning(f"⚠️ 未知配置参数: {key}")

    def reset_analysis(self):
        """重置分析结果"""
        self.variance_results = {}
        self.logger.info("🔄 分析结果已重置")

    def analyze_sequence_observation_variance(
        self, trajectory_samples: List[Dict]
    ) -> Dict[str, Any]:
        """
        分析序列观察方差的核心方法

        Args:
            trajectory_samples: 轨迹样本列表，每个样本包含obs_dict等信息

        Returns:
            Dict: 包含方差分析结果的字典
        """
        self.logger.info("🔍 开始序列观察方差分析...")

        # 数据验证
        if not self._validate_input_data(trajectory_samples):
            return {}

        # 提取观察序列数据
        observation_sequences = self._extract_observation_sequences(trajectory_samples)

        if len(observation_sequences) == 0:
            self.logger.error("❌ 未能提取到有效的观察序列数据")
            return {}

        # 计算序列内方差
        within_sequence_variance = self._compute_within_sequence_variance(
            observation_sequences
        )

        # 计算序列间方差（基于第一步观察）
        between_sequence_variance = self._compute_between_sequence_variance(
            observation_sequences
        )

        # 分析方差比例和问题严重程度
        analysis_results = self._analyze_variance_ratios(
            within_sequence_variance, between_sequence_variance
        )

        # 生成修复建议
        recommendations = self._generate_recommendations(analysis_results)

        # 整合最终结果
        final_results = {
            "within_sequence_variance": within_sequence_variance,
            "between_sequence_variance": between_sequence_variance,
            "analysis_results": analysis_results,
            "recommendations": recommendations,
            "num_sequences_analyzed": len(observation_sequences),
            "sequence_length_stats": self._get_sequence_length_stats(
                observation_sequences
            ),
        }

        # 存储结果
        self.variance_results = final_results

        # 打印分析摘要
        self._print_analysis_summary(final_results)

        self.logger.info("✅ 序列观察方差分析完成")
        return final_results

    def _validate_input_data(self, trajectory_samples: List[Dict]) -> bool:
        """验证输入数据的有效性"""
        if not trajectory_samples:
            self.logger.error("❌ 输入的轨迹样本列表为空")
            return False

        if len(trajectory_samples) < self.analysis_config["min_sequences_required"]:
            self.logger.warning(
                f"⚠️ 轨迹样本数量({len(trajectory_samples)})少于最小要求"
                f"({self.analysis_config['min_sequences_required']})"
            )
            return False

        # 检查样本格式
        sample = trajectory_samples[0]
        if "obs_dict" not in sample:
            self.logger.error("❌ 轨迹样本缺少obs_dict字段")
            return False

        self.logger.info(f"✅ 输入数据验证通过，共{len(trajectory_samples)}个轨迹样本")
        return True

    def _extract_observation_sequences(
        self, trajectory_samples: List[Dict]
    ) -> List[np.ndarray]:
        """
        从轨迹样本中提取观察序列数据

        Returns:
            List[np.ndarray]: 观察序列列表，每个序列形状为[seq_length, obs_dim]
        """
        observation_sequences = []

        for i, sample in enumerate(trajectory_samples):
            try:
                obs_dict = sample["obs_dict"]

                # 转换为34维分离格式（单手模式）
                obs_sequence = self._convert_obs_dict_to_34d_sequence(obs_dict)

                if (
                    obs_sequence is not None
                    and obs_sequence.shape[0]
                    >= self.analysis_config["min_sequence_length"]
                ):
                    observation_sequences.append(obs_sequence)
                else:
                    self.logger.debug(f"跳过样本{i}：序列长度不足")

            except Exception as e:
                self.logger.warning(f"处理样本{i}时出错: {e}")
                continue

        self.logger.info(f"✅ 成功提取{len(observation_sequences)}个有效观察序列")
        return observation_sequences

    def _convert_obs_dict_to_34d_sequence(self, obs_dict: Dict) -> Optional[np.ndarray]:
        """
        将obs_dict转换为34维观察序列

        Args:
            obs_dict: 观察字典，包含lefthand, righthand, object等

        Returns:
            np.ndarray: 形状为[seq_length, 34]的观察序列，失败时返回None
        """
        try:
            # 使用配置的手部类型
            target_hand = self.hand_type

            if target_hand == "left":
                hand_joints = obs_dict["lefthand"][
                    "joints"
                ]  # [1, 16] or [seq_length, 16]
                hand_pose = obs_dict["lefthand"][
                    "handpose"
                ]  # [1, 9] or [seq_length, 9]
            else:
                hand_joints = obs_dict["righthand"]["joints"]
                hand_pose = obs_dict["righthand"]["handpose"]

            # 寻找物体状态数据，优先使用obj1，如果没有则寻找object
            object_state = None
            if "obj1" in obs_dict:
                object_state = obs_dict["obj1"]  # [1, 9] or [seq_length, 9]
            elif "object" in obs_dict:
                object_state = obs_dict["object"]
            else:
                # 如果没有物体状态，创建零向量作为占位符
                if isinstance(hand_joints, torch.Tensor):
                    object_state = torch.zeros(
                        (hand_joints.shape[0], 9),
                        dtype=hand_joints.dtype,
                        device=hand_joints.device,
                    )
                else:
                    object_state = np.zeros((hand_joints.shape[0], 9))

            # 转换为numpy
            if isinstance(hand_joints, torch.Tensor):
                hand_joints = hand_joints.cpu().numpy()
            if isinstance(hand_pose, torch.Tensor):
                hand_pose = hand_pose.cpu().numpy()
            if isinstance(object_state, torch.Tensor):
                object_state = object_state.cpu().numpy()

            # 处理批次维度：如果有批次维度，取第一个批次
            if hand_joints.ndim == 3:  # [batch, seq_length, dim]
                hand_joints = hand_joints[0]  # [seq_length, dim]
            if hand_pose.ndim == 3:
                hand_pose = hand_pose[0]
            if object_state.ndim == 3:
                object_state = object_state[0]

            # 检查维度一致性
            if (
                hand_joints.shape[0] != hand_pose.shape[0]
                or hand_joints.shape[0] != object_state.shape[0]
            ):
                self.logger.debug(
                    f"维度不一致: joints={hand_joints.shape}, pose={hand_pose.shape}, object={object_state.shape}"
                )
                return None

            # 拼接为[seq_length, 44] (16+9+19=44)
            obs_sequence = np.concatenate(
                [hand_joints, hand_pose, object_state], axis=-1
            )

            return obs_sequence

        except Exception as e:
            self.logger.debug(f"转换obs_dict失败: {e}")
            return None

    def _compute_within_sequence_variance(
        self, observation_sequences: List[np.ndarray]
    ) -> Dict[str, float]:
        """
        计算序列内观察方差（同一轨迹不同时间步的观察差异）

        Args:
            observation_sequences: 观察序列列表

        Returns:
            Dict: 包含序列内方差统计的字典
        """
        within_seq_variances = []

        for seq in observation_sequences:
            # 计算该序列内各时间步的方差
            seq_variance = np.var(seq, axis=0)  # [obs_dim]
            within_seq_variances.append(seq_variance)

        # 聚合所有序列的方差
        within_seq_variances = np.array(
            within_seq_variances
        )  # [num_sequences, obs_dim]

        results = {
            "mean_variance": np.mean(within_seq_variances),
            "std_variance": np.std(within_seq_variances),
            "max_variance": np.max(within_seq_variances),
            "min_variance": np.min(within_seq_variances),
            "per_dimension_mean": np.mean(within_seq_variances, axis=0),  # [obs_dim]
            "per_dimension_std": np.std(within_seq_variances, axis=0),  # [obs_dim]
        }

        self.logger.info(
            f"📊 序列内方差统计 - 均值: {results['mean_variance']:.6f}, "
            f"标准差: {results['std_variance']:.6f}"
        )

        return results

    def _compute_between_sequence_variance(
        self, observation_sequences: List[np.ndarray]
    ) -> Dict[str, float]:
        """
        计算序列间观察方差（不同轨迹第一步观察的差异）

        Args:
            observation_sequences: 观察序列列表

        Returns:
            Dict: 包含序列间方差统计的字典
        """
        # 提取所有序列的第一步观察
        first_step_observations = []
        for seq in observation_sequences:
            first_step_observations.append(seq[0])  # 取第一个时间步

        first_step_observations = np.array(
            first_step_observations
        )  # [num_sequences, obs_dim]

        # 计算序列间方差
        between_seq_variance = np.var(first_step_observations, axis=0)  # [obs_dim]

        results = {
            "mean_variance": np.mean(between_seq_variance),
            "std_variance": np.std(between_seq_variance),
            "max_variance": np.max(between_seq_variance),
            "min_variance": np.min(between_seq_variance),
            "per_dimension_variance": between_seq_variance,  # [obs_dim]
            "total_variance": np.sum(between_seq_variance),
        }

        self.logger.info(
            f"📊 序列间方差统计 - 均值: {results['mean_variance']:.6f}, "
            f"标准差: {results['std_variance']:.6f}"
        )

        return results

    def _analyze_variance_ratios(
        self, within_variance: Dict, between_variance: Dict
    ) -> Dict[str, Any]:
        """分析方差比例和问题严重程度"""
        within_mean = within_variance["mean_variance"]
        between_mean = between_variance["mean_variance"]

        # 计算方差比例
        variance_ratio = within_mean / (between_mean + 1e-8)

        # 判断问题严重程度
        threshold = self.analysis_config["variance_ratio_threshold"]
        problem_severity = "none"

        if variance_ratio > threshold * 2:
            problem_severity = "severe"
        elif variance_ratio > threshold:
            problem_severity = "moderate"
        elif variance_ratio > threshold * 0.5:
            problem_severity = "mild"

        return {
            "variance_ratio": variance_ratio,
            "problem_detected": variance_ratio > threshold,
            "problem_severity": problem_severity,
            "threshold_used": threshold,
        }

    def _generate_recommendations(self, analysis_results: Dict) -> List[str]:
        """根据分析结果生成修复建议"""
        recommendations = []

        if analysis_results["problem_detected"]:
            severity = analysis_results["problem_severity"]
            ratio = analysis_results["variance_ratio"]

            recommendations.append(f"⚠️ 检测到归一化不一致问题（严重程度：{severity}）")
            recommendations.append(f"📊 序列内/序列间方差比例：{ratio:.4f}")

            if severity == "severe":
                recommendations.append(
                    "🔧 强烈建议：重新计算归一化统计参数，只基于第一个时间步"
                )
                recommendations.append("🔧 考虑：检查训练和评估数据处理流程的一致性")
            elif severity == "moderate":
                recommendations.append(
                    "🔧 建议：验证统计参数计算方式，考虑修正归一化逻辑"
                )
                recommendations.append("🔧 监控：关注模型预测精度是否受到影响")
            else:
                recommendations.append("🔧 监控：继续观察，可能需要轻微调整")
        else:
            recommendations.append("✅ 未检测到明显的归一化不一致问题")
            recommendations.append("📊 序列内外方差比例在合理范围内")

        return recommendations

    def _get_sequence_length_stats(
        self, observation_sequences: List[np.ndarray]
    ) -> Dict[str, float]:
        """获取序列长度统计信息"""
        lengths = [seq.shape[0] for seq in observation_sequences]
        return {
            "mean_length": np.mean(lengths),
            "std_length": np.std(lengths),
            "min_length": np.min(lengths),
            "max_length": np.max(lengths),
        }

    def _print_analysis_summary(self, results: Dict):
        """打印分析摘要"""
        if not self.analysis_config["enable_detailed_logging"]:
            return

        self.logger.info("\n" + "=" * 60)
        self.logger.info("📋 序列观察方差分析摘要")
        self.logger.info("=" * 60)

        # 基本统计
        self.logger.info(f"📊 分析序列数量: {results['num_sequences_analyzed']}")
        seq_stats = results["sequence_length_stats"]
        self.logger.info(
            f"📏 序列长度统计: 平均={seq_stats['mean_length']:.1f}, "
            f"范围=[{seq_stats['min_length']:.0f}, {seq_stats['max_length']:.0f}]"
        )

        # 方差统计
        within_var = results["within_sequence_variance"]["mean_variance"]
        between_var = results["between_sequence_variance"]["mean_variance"]
        analysis = results["analysis_results"]

        self.logger.info(f"📈 序列内平均方差: {within_var:.6f}")
        self.logger.info(f"📈 序列间平均方差: {between_var:.6f}")
        self.logger.info(f"📈 方差比例: {analysis['variance_ratio']:.6f}")

        # 问题诊断
        if analysis["problem_detected"]:
            self.logger.warning(
                f"⚠️ 检测到问题 - 严重程度: {analysis['problem_severity']}"
            )
        else:
            self.logger.info("✅ 未检测到明显问题")

        # 建议
        self.logger.info("\n💡 修复建议:")
        for i, rec in enumerate(results["recommendations"], 1):
            self.logger.info(f"   {i}. {rec}")

        self.logger.info("=" * 60)
