from __future__ import annotations

"""rotation_diagnostic.py
独立的旋转系统误差诊断模块。

该模块通过组合方式使用 eval.core.analyzers.TrajectoryAnalyzer，
并在导入时为 TrajectoryAnalyzer 动态注入 `create_rotation_diagnostic` 工厂方法，
从而做到 **零侵入** —— 无需修改现有 analyzers.py 文件。
"""

# 标准库
import random
from typing import Any, Dict, List, Tuple
import json
from pathlib import Path

# 第三方
import numpy as np
import torch

# 延迟导入，避免循环依赖
from importlib import import_module


class RotationSystemErrorDiagnostic:
    """旋转系统误差诊断工具（组合模式实现）。"""

    def __init__(self, trajectory_analyzer: "TrajectoryAnalyzer") -> None:
        self.analyzer = trajectory_analyzer
        # 独立配置，可按需扩展
        self.config: Dict[str, Any] = {
            "rotation_error_threshold": 0.1,  # rad
            "enable_coordinate_validation": True,
            "enable_detailed_logging": True,  # 新增：启用详细日志
            "save_results_to_file": True,  # 新增：保存结果到文件
        }

        # 使用与 TrajectoryAnalyzer 一致的 logger（如有）
        self.logger = getattr(self.analyzer, "logger", None)

    # ----------------------------- Public API -----------------------------
    def diagnose_system_error(self) -> Dict[str, Any]:
        """执行完整诊断流程并返回结果。"""
        if self.logger and self.config["enable_detailed_logging"]:
            self.logger.info("=" * 60)
            self.logger.info("🔧 开始旋转系统误差诊断...")
            self.logger.info("=" * 60)

        results: Dict[str, Any] = {}

        # Phase-1: 输入坐标系诊断
        if self.logger and self.config["enable_detailed_logging"]:
            self.logger.info("\n📍 Phase-1: 输入坐标系诊断")
            self.logger.info("-" * 40)

        results["input_coordinate"] = self._diag_input_coordinate_system()
        results["input_rotation_data"] = self._diag_input_rotation_data()

        # Phase-2: 6D 旋转处理诊断
        if self.logger and self.config["enable_detailed_logging"]:
            self.logger.info("\n📐 Phase-2: 6D旋转处理诊断")
            self.logger.info("-" * 40)

        results["orthogonalization"] = self._diag_6d_orthogonalization()
        results["rotation_conversion"] = self._diag_rotation_conversion_accuracy()

        # Phase-3: 坐标系定义验证
        if self.logger and self.config["enable_detailed_logging"]:
            self.logger.info("\n🗺️ Phase-3: 坐标系定义验证")
            self.logger.info("-" * 40)

        results["coord_system_def"] = self._diag_coordinate_system_definition()
        results["training_inference_diff"] = self._diag_training_vs_inference_config()

        # Phase-4: 端到端误差追踪
        if self.logger and self.config["enable_detailed_logging"]:
            self.logger.info("\n🎯 Phase-4: 端到端误差追踪")
            self.logger.info("-" * 40)

        results["end_to_end_tracking"] = self._diag_end_to_end_rotation_tracking()
        results["error_decomposition"] = self._diag_rotation_error_decomposition()

        # 聚合全局建议 & 总体状态
        global_recs: List[str] = []
        worst_level = "ok"
        level_rank = {"ok": 0, "warning": 1, "error": 2}

        for k, v in results.items():
            if isinstance(v, dict):
                global_recs.extend(v.get("recommendations", []))
                lvl = v.get("status", "ok")
                if level_rank.get(lvl, 0) > level_rank.get(worst_level, 0):
                    worst_level = lvl

                # 打印每个子诊断的结果
                if self.logger and self.config["enable_detailed_logging"]:
                    self._log_diagnostic_result(k, v)

        results["overall_status"] = worst_level
        results["global_recommendations"] = list(
            dict.fromkeys(global_recs)
        )  # 去重保持顺序

        # 打印总体诊断结果
        if self.logger and self.config["enable_detailed_logging"]:
            self.logger.info("\n" + "=" * 60)
            self.logger.info("📊 诊断总结")
            self.logger.info("=" * 60)
            self.logger.info(f"总体状态: {self._format_status(worst_level)}")
            if global_recs:
                self.logger.info("\n🔧 建议措施:")
                for i, rec in enumerate(global_recs, 1):
                    self.logger.info(f"  {i}. {rec}")
            else:
                self.logger.info("✅ 未发现需要改进的问题")
            self.logger.info("=" * 60)

        # 保存结果到文件
        if self.config["save_results_to_file"]:
            self._save_results_to_file(results)

        return results

    def _log_diagnostic_result(self, name: str, result: Dict[str, Any]):
        """打印单个诊断结果的详细信息"""
        status = result.get("status", "unknown")
        summary = result.get("summary", "")
        metrics = result.get("metrics", {})
        recommendations = result.get("recommendations", [])

        # 格式化诊断名称
        name_formatted = name.replace("_", " ").title()

        self.logger.info(f"\n  ▶ {name_formatted}")
        self.logger.info(f"    状态: {self._format_status(status)}")
        self.logger.info(f"    摘要: {summary}")

        if metrics:
            self.logger.info("    指标:")
            for k, v in metrics.items():
                if isinstance(v, float):
                    self.logger.info(f"      - {k}: {v:.6f}")
                else:
                    self.logger.info(f"      - {k}: {v}")

        if recommendations:
            self.logger.info("    建议:")
            for rec in recommendations:
                self.logger.info(f"      ⚠️ {rec}")

    def _format_status(self, status: str) -> str:
        """格式化状态显示"""
        status_map = {
            "ok": "✅ 正常",
            "warning": "⚠️ 警告",
            "error": "❌ 错误",
            "unknown": "❓ 未知",
        }
        return status_map.get(status, status)

    def _save_results_to_file(self, results: Dict[str, Any]):
        """保存诊断结果到文件"""
        try:
            # 确定输出目录
            if hasattr(self.analyzer, "output_dir") and self.analyzer.output_dir:
                output_dir = Path(self.analyzer.output_dir)
            elif hasattr(
                self.analyzer, "streaming_config"
            ) and self.analyzer.streaming_config.get("output_dir"):
                output_dir = Path(self.analyzer.streaming_config["output_dir"])
            else:
                # 尝试从当前工作目录创建默认输出目录
                output_dir = Path("rotation_diagnostic_output")

            output_dir.mkdir(parents=True, exist_ok=True)

            # 保存JSON格式
            json_path = output_dir / "rotation_diagnostic_results.json"
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            # 保存人类可读的报告
            report_path = output_dir / "rotation_diagnostic_report.txt"
            with open(report_path, "w", encoding="utf-8") as f:
                f.write("旋转系统误差诊断报告\n")
                f.write("=" * 60 + "\n\n")

                # 写入总体状态
                f.write(
                    f"总体状态: {self._format_status(results['overall_status'])}\n\n"
                )

                # 写入各项诊断结果
                for k, v in results.items():
                    if k in ["overall_status", "global_recommendations"]:
                        continue
                    if isinstance(v, dict):
                        f.write(f"{k.replace('_', ' ').title()}:\n")
                        f.write(
                            f"  状态: {self._format_status(v.get('status', 'unknown'))}\n"
                        )
                        f.write(f"  摘要: {v.get('summary', '')}\n")

                        metrics = v.get("metrics", {})
                        if metrics:
                            f.write("  指标:\n")
                            for mk, mv in metrics.items():
                                if isinstance(mv, float):
                                    f.write(f"    - {mk}: {mv:.6f}\n")
                                else:
                                    f.write(f"    - {mk}: {mv}\n")

                        recs = v.get("recommendations", [])
                        if recs:
                            f.write("  建议:\n")
                            for rec in recs:
                                f.write(f"    - {rec}\n")
                        f.write("\n")

                # 写入全局建议
                global_recs = results.get("global_recommendations", [])
                if global_recs:
                    f.write("\n全局建议:\n")
                    for i, rec in enumerate(global_recs, 1):
                        f.write(f"  {i}. {rec}\n")

            if self.logger:
                self.logger.info(f"\n📄 诊断结果已保存到:")
                self.logger.info(f"   - JSON: {json_path}")
                self.logger.info(f"   - 报告: {report_path}")

        except Exception as e:
            if self.logger:
                self.logger.warning(f"保存诊断结果失败: {e}")

    # ------------------------- Phase-1: 输入诊断 --------------------------
    def _diag_input_coordinate_system(self) -> Dict[str, Any]:
        samples = self._sample_dataset(16)

        if not samples:
            return {
                "status": "warning",
                "summary": "无法获取样本数据",
                "metrics": {},
                "recommendations": ["确保已加载数据集或提供样本"],
            }

        pos_stats: List[float] = []
        invalid_count = 0

        for s in samples:
            # 获取第一帧 observation 位置 (16:19)
            obs = s.get("obs_dict") or {}
            tensor_candidates = [v for v in obs.values() if isinstance(v, torch.Tensor)]
            if not tensor_candidates:
                continue

            first = tensor_candidates[0]
            if first.dim() >= 2:
                first = first[0]

            if first.shape[-1] >= 19:
                pos = first[16:19].cpu().float()
                pos_stats.append(pos.abs().max().item())
                if (pos.abs() > 2.0).any():
                    invalid_count += 1

        if not pos_stats:
            return {
                "status": "warning",
                "summary": "样本中缺少位置数据",
                "metrics": {},
                "recommendations": [],
            }

        ratio_invalid = invalid_count / len(samples)
        status = "ok" if ratio_invalid < 0.2 else "warning"

        rec = []
        if status == "warning":
            rec.append("检查坐标系转换或缩放，位置超过±2m 占比超过20%")

        return {
            "status": status,
            "summary": f"{invalid_count}/{len(samples)} 样本位置超界",
            "metrics": {"max_abs_pos": max(pos_stats), "invalid_ratio": ratio_invalid},
            "recommendations": rec,
        }

    def _diag_input_rotation_data(self) -> Dict[str, Any]:
        samples = self._sample_dataset(16)
        rot_list = []
        for s in samples:
            actions = s.get("actions")
            if isinstance(actions, torch.Tensor) and actions.shape[-1] >= 25:
                rot6d = (
                    actions[0, -1, 19:25] if actions.dim() == 3 else actions[0, 19:25]
                )
                rot_list.append(rot6d)

        if not rot_list:
            return {
                "status": "warning",
                "summary": "未找到旋转数据",
                "metrics": {},
                "recommendations": [],
            }

        rot6d_tensor = torch.stack(rot_list)
        rot_m = self._rot6d_to_matrix(rot6d_tensor)
        ortho_err = self._orthogonality_error(rot_m)

        mean_ortho = ortho_err.mean().item()
        status = "ok" if mean_ortho < 1e-3 else "warning"
        rec = []
        if status != "ok":
            rec.append("6D 旋转向量不满足正交性，考虑在数据预处理阶段正交化")

        return {
            "status": status,
            "summary": "6D 旋转正交误差诊断",
            "metrics": {"mean_orthogonality_error": mean_ortho},
            "recommendations": rec,
        }

    # ----------------------- Phase-2: 6D 旋转诊断 -------------------------
    def _diag_6d_orthogonalization(self) -> Dict[str, Any]:
        samples = self._sample_dataset(16)
        rot_raw: List[torch.Tensor] = []
        for s in samples:
            actions = s.get("actions")
            if isinstance(actions, torch.Tensor) and actions.shape[-1] >= 25:
                r = actions.reshape(-1, 25)[:, 19:25]
                rot_raw.append(r)

        if not rot_raw:
            return {
                "status": "warning",
                "summary": "缺少动作旋转 6D 数据",
                "metrics": {},
                "recommendations": [],
            }

        rot6d = torch.cat(rot_raw, dim=0)
        rot_m_before = self._rot6d_to_matrix(rot6d)
        ortho_before = self._orthogonality_error(rot_m_before)

        # 调用 TrajectoryAnalyzer 的正交化实现
        with torch.no_grad():
            orth_func = getattr(self.analyzer, "_apply_6d_orthogonalization", None)
            rot6d_corrected = (
                orth_func(rot6d.unsqueeze(0)).squeeze(0)
                if callable(orth_func)
                else rot6d
            )

        rot_m_after = self._rot6d_to_matrix(rot6d_corrected)
        ortho_after = self._orthogonality_error(rot_m_after)

        improvement = (ortho_before.mean() - ortho_after.mean()).item()

        status = "ok" if ortho_after.mean() < 1e-3 else "warning"

        rec = []
        if status == "warning":
            rec.append("考虑在推理阶段强制执行 6D 正交化后处理")

        return {
            "status": status,
            "summary": "6D 正交化前后误差改善 {:.2e}".format(improvement),
            "metrics": {
                "mean_ortho_before": ortho_before.mean().item(),
                "mean_ortho_after": ortho_after.mean().item(),
            },
            "recommendations": rec,
        }

    def _diag_rotation_conversion_accuracy(self) -> Dict[str, Any]:
        samples = self._sample_dataset(16)
        rot_raw = []
        for s in samples:
            actions = s.get("actions")
            if isinstance(actions, torch.Tensor) and actions.shape[-1] >= 25:
                rot_raw.append(actions.reshape(-1, 25)[:, 19:25])

        if not rot_raw:
            return {
                "status": "warning",
                "summary": "无旋转数据可测 cycle consistency",
                "metrics": {},
                "recommendations": [],
            }

        rot6d = torch.cat(rot_raw, dim=0)
        cycle_err = self._cycle_consistency_error(rot6d)

        mean_cycle = cycle_err.mean().item()
        status = "ok" if mean_cycle < 1e-3 else "warning"
        rec = []
        if status != "ok":
            rec.append("6D ↔ R 转换精度偏高，请检查转换实现")

        return {
            "status": status,
            "summary": "6D↔矩阵 循环误差",
            "metrics": {"mean_cycle_rmse": mean_cycle},
            "recommendations": rec,
        }

    # --------------------- Phase-3: 坐标系定义验证 ------------------------
    def _diag_coordinate_system_definition(self) -> Dict[str, Any]:
        train_cfg = {}
        try:
            train_cfg = self.analyzer._load_training_config_from_model_dir()
        except Exception:
            pass

        infer_cfg = getattr(self.analyzer, "postprocess_config", {})

        diff_keys = []
        for k, v in train_cfg.items():
            if k in infer_cfg and infer_cfg[k] != v:
                diff_keys.append(k)

        status = "ok" if not diff_keys else "warning"
        rec = []
        if diff_keys:
            rec.append(f"训练与推理配置不一致字段: {', '.join(diff_keys)}")

        return {
            "status": status,
            "summary": "配置差异检测",
            "metrics": {"diff_keys": diff_keys},
            "recommendations": rec,
        }

    def _diag_training_vs_inference_config(self) -> Dict[str, Any]:
        # 别名保持向后兼容，直接调用上面的实现
        return self._diag_coordinate_system_definition()

    # --------------------- Phase-4: 端到端误差追踪 ------------------------
    def _diag_end_to_end_rotation_tracking(self) -> Dict[str, Any]:
        try:
            # 调用现有单轨迹分析接口让其内部打印 & 计算
            self.analyzer.analyze_single_trajectory(sample_idx=0)
            return {
                "status": "ok",
                "summary": "已调用 TrajectoryAnalyzer.analyze_single_trajectory() 进行端到端检查",
                "metrics": {},
                "recommendations": [],
            }
        except Exception as e:
            return {
                "status": "warning",
                "summary": f"调用失败: {e}",
                "metrics": {},
                "recommendations": [],
            }

    def _diag_rotation_error_decomposition(self) -> Dict[str, Any]:
        samples = self._sample_dataset(1)
        if not samples:
            return {
                "status": "warning",
                "summary": "无样本可分解误差",
                "metrics": {},
                "recommendations": [],
            }

        s = samples[0]
        true_actions = s.get("actions")
        if not isinstance(true_actions, torch.Tensor):
            return {
                "status": "warning",
                "summary": "动作数据缺失",
                "metrics": {},
                "recommendations": [],
            }

        # 简单计算相邻帧增量作为预测 vs 真值差异近似
        diff = true_actions[1:] - true_actions[:-1]
        pos_err = diff[..., 16:19].pow(2).sum(dim=-1).sqrt().mean().item()
        rot_err = diff[..., 19:25].abs().mean().item()
        joint_err = diff[..., :16].abs().mean().item()

        status = (
            "ok" if rot_err < self.config["rotation_error_threshold"] else "warning"
        )

        rec = []
        if status != "ok":
            rec.append("旋转误差较大，检查推理流程中的坐标系与缩放一致性")

        return {
            "status": status,
            "summary": "基于相邻帧增量的粗略误差分解",
            "metrics": {
                "position_rmse": pos_err,
                "rotation_mae_6d": rot_err,
                "joint_mae": joint_err,
            },
            "recommendations": rec,
        }

    # ------------------------------------------------------------------
    # 内部工具
    # ------------------------------------------------------------------

    # --- 采样 -----------------------------------------------------------
    def _sample_dataset(self, num_samples: int = 16):
        """确保 self.analyzer.samples 可用，若无则尝试加载少量样本。"""

        if not hasattr(self.analyzer, "samples") or not self.analyzer.samples:
            # 尝试加载数据集（静默处理异常）
            try:
                if self.logger:
                    self.logger.info(
                        f"📂 RotationDiagnostic: 尝试临时加载 {num_samples} 个样本…"
                    )
                self.analyzer.load_dataset(num_samples=num_samples)
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"RotationDiagnostic: 数据集加载失败: {e}")

        samples = getattr(self.analyzer, "samples", [])
        if not samples:
            return []

        if len(samples) > num_samples:
            samples = random.sample(samples, num_samples)

        # 统一样本格式 -> dict
        normalized = []
        for s in samples:
            n = self._normalize_sample(s)
            if n:
                normalized.append(n)
        return normalized

    # --- 样本规范化 ------------------------------------------------------
    def _normalize_sample(self, sample):
        """将不同格式的样本统一转换为带 'obs_dict' / 'actions' 键的 dict。"""
        if isinstance(sample, dict):
            # 已经是期望格式
            return sample

        # 训练数据常见格式: [state_dict, actions_dict]
        if isinstance(sample, (list, tuple)):
            if len(sample) == 2:
                return {"obs_dict": sample[0], "actions": sample[1], "target_pose": {}}
            elif len(sample) >= 5:
                # (obs, actions, tgt_pose, *_)
                return {
                    "obs_dict": sample[0],
                    "actions": sample[1],
                    "target_pose": sample[2],
                }

        # 未知格式返回 None
        return None

    # --- 数学工具 -------------------------------------------------------

    @staticmethod
    def _rot6d_to_matrix(rot_6d: torch.Tensor) -> torch.Tensor:
        """将 6D 旋转表示转换为旋转矩阵 (B,3,3)。若可用则复用外部库。"""
        try:
            from px_janus_learnsim.utils.math import rot6d_to_matrix_gram_schmidt

            return rot6d_to_matrix_gram_schmidt(rot_6d, validate=False)
        except Exception:
            # 手写 Gram–Schmidt
            a1 = rot_6d[:, 0:3]
            a2 = rot_6d[:, 3:6]

            b1 = torch.nn.functional.normalize(a1, dim=-1, eps=1e-6)
            proj = (b1 * a2).sum(dim=-1, keepdim=True) * b1
            b2 = torch.nn.functional.normalize(a2 - proj, dim=-1, eps=1e-6)
            b3 = torch.cross(b1, b2, dim=-1)

            return torch.stack((b1, b2, b3), dim=-1)  # (B,3,3)

    @staticmethod
    def _matrix_to_rot6d(rot_m: torch.Tensor) -> torch.Tensor:
        """旋转矩阵 -> 6D 表示 (B,6)。"""
        return rot_m[..., :3, 0:2].reshape(rot_m.shape[0], 6)

    @staticmethod
    def _orthogonality_error(rot_m: torch.Tensor) -> torch.Tensor:
        """计算 R^T R - I 的 Frobenius 范数 (B,)。"""
        iden = torch.eye(3, device=rot_m.device).unsqueeze(0)
        diff = torch.bmm(rot_m.transpose(1, 2), rot_m) - iden
        return diff.flatten(1).norm(dim=1)

    def _cycle_consistency_error(self, rot_6d: torch.Tensor) -> torch.Tensor:
        rot_m = self._rot6d_to_matrix(rot_6d)
        rot_6d_rec = self._matrix_to_rot6d(rot_m)
        return torch.sqrt(torch.mean((rot_6d_rec - rot_6d) ** 2, dim=1))


# ---------------------------------------------------------------------------
# 动态为 TrajectoryAnalyzer 注入工厂方法（猴子补丁）
# ---------------------------------------------------------------------------

a_module = import_module("eval.core.analyzers")
TrajectoryAnalyzer = getattr(a_module, "TrajectoryAnalyzer")


def _create_rotation_diagnostic(self: "TrajectoryAnalyzer") -> RotationSystemErrorDiagnostic:  # type: ignore[name-defined]
    """TrajectoryAnalyzer.create_rotation_diagnostic() 动态注入方法。"""
    return RotationSystemErrorDiagnostic(self)


# 仅在未定义时注入，避免重复
if not hasattr(TrajectoryAnalyzer, "create_rotation_diagnostic"):
    setattr(
        TrajectoryAnalyzer, "create_rotation_diagnostic", _create_rotation_diagnostic
    )
