"""
IsaacSim初始化器模块

专门处理IsaacSim的导入和初始化，确保正确的导入顺序
"""

import torch
import gymnasium as gym


class IsaacInitializer:
    """专门处理IsaacSim初始化的类"""
    
    def __init__(self):
        self._isaac_initialized = False
        self._app_launcher = None
        self._simulation_app = None
        self._env_cfg_class = None
        self._model_class = None
    
    def initialize_isaac_environment(self, args):
        """按正确顺序初始化IsaacSim环境
        
        Args:
            args: 命令行参数
            
        Returns:
            simulation_app: IsaacSim应用实例
            env_cfg_class: 环境配置类
            model_class: 模型类
        """
        if self._isaac_initialized:
            return self._simulation_app, self._env_cfg_class, self._model_class
            
        try:
            # 1. 启动Isaac Lab应用（必须在环境导入之前）
            from isaaclab.app import AppLauncher
            self._app_launcher = AppLauncher(args)
            self._simulation_app = self._app_launcher.app
            
            # 2. 注册自定义环境（必须在环境创建之前）
            try:
                from env.env_registry import register_all_environments
                register_all_environments()
            except Exception as e:
                print(f"⚠️ 环境注册失败或已注册: {e}")
            
            # 3. 导入环境配置类
            from env.IsaacSim_direct_task_table_set_env_DexH13_single_hand import (
                SingleHandDexH13DirectEnvCfg,
            )
            self._env_cfg_class = SingleHandDexH13DirectEnvCfg
            
            # 4. 导入模型类
            from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
                HierarchicalHybridSAC,
            )
            self._model_class = HierarchicalHybridSAC
            
            self._isaac_initialized = True
            print("✅ IsaacSim环境初始化成功")
            
            return self._simulation_app, self._env_cfg_class, self._model_class
            
        except Exception as e:
            print(f"❌ IsaacSim环境初始化失败: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def create_environment(self, env_cfg):
        """创建IsaacSim环境
        
        Args:
            env_cfg: 环境配置
            
        Returns:
            env: 创建的环境实例
        """
        if not self._isaac_initialized:
            raise RuntimeError("IsaacSim环境未初始化，请先调用initialize_isaac_environment")
        
        try:
            # 创建环境
            env = gym.make("Isaac-SingleHand-DexH13-Direct-v0", cfg=env_cfg)
            
            print("✅ IsaacSim环境创建成功")
            return env
            
        except Exception as e:
            print(f"❌ IsaacSim环境创建失败: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def cleanup(self):
        """清理IsaacSim资源"""
        try:
            if self._simulation_app is not None:
                print("🔄 关闭仿真应用...")
                self._simulation_app.close()
                self._simulation_app = None
            
            self._isaac_initialized = False
            print("✅ IsaacSim资源清理完成")
            
        except Exception as e:
            print(f"⚠️ IsaacSim资源清理时出错: {e}")


# 全局初始化器实例
_isaac_initializer = None


def get_isaac_initializer():
    """获取全局IsaacSim初始化器实例"""
    global _isaac_initializer
    if _isaac_initializer is None:
        _isaac_initializer = IsaacInitializer()
    return _isaac_initializer


def initialize_isaac_environment(args):
    """便捷函数：初始化IsaacSim环境"""
    initializer = get_isaac_initializer()
    return initializer.initialize_isaac_environment(args)


def cleanup_isaac_environment():
    """便捷函数：清理IsaacSim环境"""
    global _isaac_initializer
    if _isaac_initializer is not None:
        _isaac_initializer.cleanup()
        _isaac_initializer = None 