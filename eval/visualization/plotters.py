"""
可视化绘图功能

包含：
- VisualizationMixin: 可视化功能混入类，包含所有绘图方法
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
from typing import Dict, List, Any
import torch
import pandas as pd
import json


class VisualizationMixin:
    def _add_model_dataset_info(
        self, fig, model_name: str, dataset_name: str, position: str = "bottom_right"
    ):
        """
        在图表中添加模型和数据集信息

        Args:
            fig: matplotlib figure对象
            model_name: 模型名称
            dataset_name: 数据集名称
            position: 信息位置 ("bottom_right", "bottom_left", "top_right", "top_left")
        """
        info_text = f"Model: {model_name}\nDataset: {dataset_name}"

        # 根据位置设置坐标
        if position == "bottom_right":
            x, y = 0.98, 0.02
            ha, va = "right", "bottom"
        elif position == "bottom_left":
            x, y = 0.02, 0.02
            ha, va = "left", "bottom"
        elif position in ["top_right", "upper_right"]:
            x, y = 0.98, 0.98
            ha, va = "right", "top"
        elif position in ["top_left", "upper_left"]:
            x, y = 0.02, 0.98
            ha, va = "left", "top"
        else:
            x, y = 0.98, 0.02
            ha, va = "right", "bottom"

        fig.text(
            x,
            y,
            info_text,
            fontsize=8,
            alpha=0.7,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
            ha=ha,
            va=va,
            transform=fig.transFigure,
        )

    def generate_visualization(
        self,
        trajectory_info: Dict,
        save_path: str = "trajectory_analysis",
        model_name: str = "Unknown Model",
        dataset_name: str = "Unknown Dataset",
    ):
        """生成可视化图表"""
        print("\n📊 生成可视化图表...")

        save_path = Path(save_path)
        save_path.mkdir(exist_ok=True)

        # 设置图表样式
        plt.style.use("default")
        sns.set_palette("husl")

        # 优先使用传入的参数，如果没有则从对象属性获取
        if model_name == "Unknown Model":
            model_name = getattr(self, "model_name", "Unknown Model")
        if dataset_name == "Unknown Dataset":
            dataset_name = getattr(self, "dataset_name", "Unknown Dataset")

        # 1. 数据结构概览图
        self._plot_data_structure_overview(
            trajectory_info, save_path, model_name, dataset_name
        )

        # 2. 动作分布图
        self._plot_action_distribution(
            trajectory_info, save_path, model_name, dataset_name
        )

        # 3. 如果有预测结果，绘制预测误差图
        if "predictions" in trajectory_info:
            self._plot_prediction_errors(
                trajectory_info["predictions"], save_path, model_name, dataset_name
            )

            # 4. 如果有rolling prediction结果，绘制对比图
            if "rolling_prediction" in trajectory_info["predictions"]:
                self._plot_rolling_prediction_analysis(
                    trajectory_info["predictions"], save_path, model_name, dataset_name
                )

        print(f"   ✅ 可视化图表已保存到: {save_path}")

    def _plot_data_structure_overview(
        self, trajectory_info: Dict, save_path: Path, model_name: str, dataset_name: str
    ):
        """绘制数据结构概览"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(
            f"Trajectory Data Structure Overview - {model_name}",
            fontsize=16,
            fontweight="bold",
        )

        # 添加说明文字
        fig.text(
            0.5,
            0.95,
            "Analysis of observation, action, and target data dimensions and statistics",
            ha="center",
            va="top",
            fontsize=12,
            style="italic",
            transform=fig.transFigure,
        )

        # 观察数据维度
        if trajectory_info["obs_structure"]:
            ax = axes[0, 0]
            obs_dims = [
                info["shape"][-1]
                for info in trajectory_info["obs_structure"].values()
                if "shape" in info and len(info["shape"]) > 0
            ]
            obs_names = list(trajectory_info["obs_structure"].keys())

            if obs_dims and obs_names:
                ax.bar(obs_names, obs_dims, color="skyblue", alpha=0.7)
                ax.set_title("Observation Data Dimensions")
                ax.set_ylabel("Dimension Size")
                ax.tick_params(axis="x", rotation=45)

        # 动作数据分析
        if trajectory_info["action_structure"]:
            ax = axes[0, 1]
            action_info = trajectory_info["action_structure"]
            stats = ["min", "max", "mean"]
            values = [action_info.get(stat, 0) for stat in stats]

            ax.bar(stats, values, color="lightcoral", alpha=0.7)
            ax.set_title("Action Data Statistics")
            ax.set_ylabel("Values")

        # 轨迹统计信息
        if trajectory_info["trajectory_stats"]:
            ax = axes[1, 0]
            stats = trajectory_info["trajectory_stats"]

            info_text = f"Sequence Length: {stats.get('sequence_length', 'N/A')}\n"
            info_text += f"Action Dimension: {stats.get('action_dimension', 'N/A')}"

            ax.text(
                0.1,
                0.5,
                info_text,
                fontsize=12,
                verticalalignment="center",
                transform=ax.transAxes,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"),
            )
            ax.set_title("Trajectory Statistics")
            ax.axis("off")

        # 目标数据分析
        if trajectory_info["target_structure"]:
            ax = axes[1, 1]
            target_info = trajectory_info["target_structure"]
            stats = ["min", "max", "mean"]
            values = [target_info.get(stat, 0) for stat in stats]

            ax.bar(stats, values, color="lightgreen", alpha=0.7)
            ax.set_title("Target Data Statistics")
            ax.set_ylabel("Values")

        plt.tight_layout()

        # 添加模型和数据集信息
        self._add_model_dataset_info(fig, model_name, dataset_name)

        plt.savefig(
            save_path / "data_structure_overview.png", dpi=300, bbox_inches="tight"
        )
        plt.close()

    def _plot_action_distribution(
        self, trajectory_info: Dict, save_path: Path, model_name: str, dataset_name: str
    ):
        """绘制动作分布图"""
        if not trajectory_info.get("action_structure"):
            return

        # 这里需要访问原始动作数据来绘制分布
        # 暂时创建示例图表
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        # 示例：随机生成一些数据用于演示
        action_info = trajectory_info["action_structure"]

        info_text = f"Action Dimension: {action_info.get('shape', 'N/A')}\n"
        info_text += f"Value Range: [{action_info.get('min', 0):.3f}, {action_info.get('max', 0):.3f}]\n"
        info_text += f"Mean Value: {action_info.get('mean', 0):.3f}\n\n"
        info_text += "This chart shows the statistical summary of action data from the trajectory.\n"
        info_text += "Action dimensions typically include joint angles, positions, and rotations."

        ax.text(
            0.1,
            0.5,
            info_text,
            fontsize=12,
            verticalalignment="center",
            transform=ax.transAxes,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow"),
        )

        ax.set_title(
            f"Action Data Distribution Summary - {model_name}",
            fontsize=14,
            fontweight="bold",
        )
        ax.axis("off")

        plt.tight_layout()

        # 添加模型和数据集信息
        self._add_model_dataset_info(fig, model_name, dataset_name)

        plt.savefig(save_path / "action_distribution.png", dpi=300, bbox_inches="tight")
        plt.close()

    def _plot_prediction_errors(
        self, prediction_info: Dict, save_path: Path, model_name: str, dataset_name: str
    ):
        """绘制预测误差图"""
        if not prediction_info.get("step_errors"):
            # 没有预测数据时的占位符
            fig, ax = plt.subplots(1, 1, figsize=(10, 6))
            ax.text(
                0.5,
                0.5,
                f"Prediction Error Analysis Chart - {model_name}\n(Model not loaded or prediction failed)",
                ha="center",
                va="center",
                transform=ax.transAxes,
                fontsize=14,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"),
            )
            ax.set_title(f"Model Prediction Error Analysis - {model_name}")
            ax.axis("off")
            plt.tight_layout()

            # 添加模型和数据集信息
            self._add_model_dataset_info(fig, model_name, dataset_name)

            plt.savefig(
                save_path / "prediction_errors.png", dpi=300, bbox_inches="tight"
            )
            plt.close()
            return

        # 创建详细的预测误差分析图 - 扩展为2
        fig, axes = plt.subplots(2, 3, figsize=(24, 16))  # 恢复并调大figsize
        fig.suptitle(
            f"Detailed Model Prediction Error Analysis - {model_name}",
            fontsize=16,
            fontweight="bold",
            y=0.98,  # 调整标题位置，增加与图表的间距
        )

        # 添加说明文字
        fig.text(
            0.5,
            0.96,
            "Comparison of model predictions vs ground truth across time steps in both physical and normalized domains",
            ha="center",
            va="top",
            fontsize=12,
            style="italic",
            transform=fig.transFigure,
        )

        step_errors = prediction_info["step_errors"]
        steps = [e["step"] for e in step_errors]
        mse_errors = [e["mse"] for e in step_errors]
        mae_errors = [e["mae"] for e in step_errors]
        max_errors = [e["max_error"] for e in step_errors]

        # 归一化域误差列表，若某 step 无对应键则置 None
        mse_norm = [e.get("mse_scaled") for e in step_errors]
        mae_norm = [e.get("mae_scaled") for e in step_errors]

        # 全局归一化统计
        scaled_stats = prediction_info.get("scaled_prediction_stats", {})

        # 1. MSE Error Over Time (phys + norm)
        axes[0, 0].plot(
            steps, mse_errors, "b-o", markersize=4, label="Physical MSE", linewidth=2
        )
        if any(v is not None for v in mse_norm):
            axes[0, 0].plot(
                steps,
                mse_norm,
                "c--o",
                markersize=4,
                label="Normalized MSE",
                linewidth=2,
            )
        axes[0, 0].set_title("MSE Error Trends\n(Both Domains)")
        axes[0, 0].set_xlabel("Time Step")
        axes[0, 0].set_ylabel("Mean Squared Error")
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        # 添加说明
        axes[0, 0].text(
            0.02,
            0.98,
            "Lower is better",
            transform=axes[0, 0].transAxes,
            fontsize=8,
            va="top",
            bbox=dict(boxstyle="round,pad=0.2", facecolor="yellow", alpha=0.7),
        )

        # 2. MAE Error Over Time (phys + norm)
        axes[0, 1].plot(
            steps, mae_errors, "r-o", markersize=4, label="Physical MAE", linewidth=2
        )
        if any(v is not None for v in mae_norm):
            axes[0, 1].plot(
                steps,
                mae_norm,
                "g--o",
                markersize=4,
                label="Normalized MAE",
                linewidth=2,
            )
        axes[0, 1].set_title("MAE Error Trends\n(Both Domains)")
        axes[0, 1].set_xlabel("Time Step")
        axes[0, 1].set_ylabel("Mean Absolute Error")
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        # 添加说明
        axes[0, 1].text(
            0.02,
            0.98,
            "Lower is better",
            transform=axes[0, 1].transAxes,
            fontsize=8,
            va="top",
            bbox=dict(boxstyle="round,pad=0.2", facecolor="yellow", alpha=0.7),
        )

        # 3. 物理域误差综合对比
        axes[0, 2].plot(
            steps, mse_errors, "b-o", markersize=4, label="MSE", linewidth=2
        )
        axes[0, 2].plot(
            steps, mae_errors, "r-s", markersize=4, label="MAE", linewidth=2
        )
        axes[0, 2].plot(
            steps, max_errors, "g-^", markersize=4, label="Max Error", linewidth=2
        )
        axes[0, 2].set_title("Physical Domain Error Metrics\n(Comprehensive View)")
        axes[0, 2].set_xlabel("Time Step")
        axes[0, 2].set_ylabel("Error Value (Physical Units)")
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)
        # 添加说明
        axes[0, 2].text(
            0.02,
            0.98,
            "Real-world units:\njoints(rad), pos(m), rot(deg)",
            transform=axes[0, 2].transAxes,
            fontsize=8,
            va="top",
            bbox=dict(boxstyle="round,pad=0.2", facecolor="lightblue", alpha=0.7),
        )

        # 4. 归一化域误差对比
        if any(v is not None for v in mse_norm) and any(
            v is not None for v in mae_norm
        ):
            # 过滤掉None值
            valid_steps = [s for s, v in zip(steps, mse_norm) if v is not None]
            valid_mse_norm = [v for v in mse_norm if v is not None]
            valid_mae_norm = [v for v in mae_norm if v is not None]

            axes[1, 0].plot(
                valid_steps,
                valid_mse_norm,
                "c-o",
                markersize=4,
                label="Normalized MSE",
                linewidth=2,
            )
            axes[1, 0].plot(
                valid_steps,
                valid_mae_norm,
                "m-s",
                markersize=4,
                label="Normalized MAE",
                linewidth=2,
            )
            axes[1, 0].set_title("Normalized Domain Error Metrics\n(Training Space)")
            axes[1, 0].set_xlabel("Time Step")
            axes[1, 0].set_ylabel("Error Value (Normalized Units)")
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
            # 添加说明
            axes[1, 0].text(
                0.02,
                0.98,
                "Model training domain:\nstandardized inputs/outputs",
                transform=axes[1, 0].transAxes,
                fontsize=8,
                va="top",
                bbox=dict(boxstyle="round,pad=0.2", facecolor="lightcyan", alpha=0.7),
            )
        else:
            axes[1, 0].text(
                0.5,
                0.5,
                "Normalized Domain Errors\n(Data not available)\n\nThis would show errors in the\nmodel's training space",
                ha="center",
                va="center",
                transform=axes[1, 0].transAxes,
                fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"),
            )
            axes[1, 0].set_title("Normalized Domain Error Metrics\n(Training Space)")
            axes[1, 0].axis("off")

        # 5. 动作组件误差对比 (序列平均)
        if prediction_info.get("action_component_errors"):
            comp_errors = prediction_info["action_component_errors"]

            # 重新映射组件名称，添加单位信息
            component_mapping = {
                "joint_mae": "Joint\n(rad)",
                "position_mae": "Position\n(mm)",
                "rotation_mae": "Rotation\n(deg)",
            }

            components = [component_mapping.get(k, k) for k in comp_errors.keys()]
            values = list(comp_errors.values())

            bars = axes[1, 1].bar(
                components,
                values,
                color=["lightgreen", "skyblue", "lightcoral"],
                alpha=0.7,
                edgecolor="black",
                linewidth=1,
            )

            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                axes[1, 1].text(
                    bar.get_x() + bar.get_width() / 2.0,
                    height + max(values) * 0.02,
                    f"{value:.4f}",
                    ha="center",
                    va="bottom",
                    fontweight="bold",
                )

            axes[1, 1].set_title("Action Component Errors\n(Sequence Average)")
            axes[1, 1].set_ylabel("Mean Absolute Error")
            axes[1, 1].grid(True, alpha=0.3, axis="y")
            # 添加说明
            axes[1, 1].text(
                0.02,
                0.98,
                "Average across all\ntime steps",
                transform=axes[1, 1].transAxes,
                fontsize=8,
                va="top",
                bbox=dict(boxstyle="round,pad=0.2", facecolor="lightyellow", alpha=0.7),
            )
        else:
            axes[1, 1].text(
                0.5,
                0.5,
                "Action Component Errors\n(Data not available)\n\nWould show joint, position,\nand rotation error breakdown",
                ha="center",
                va="center",
                transform=axes[1, 1].transAxes,
                fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"),
            )
            axes[1, 1].set_title("Action Component Errors\n(Sequence Average)")
            axes[1, 1].axis("off")

        # 6. 统计汇总和模型性能指标
        stats = prediction_info.get("prediction_stats", {})

        stats_text = "📊 Overall Performance Metrics\n\n"
        stats_text += f"Physical Domain:\n"
        stats_text += f"  • Avg MAE: {stats.get('avg_mae', 0):.6f}\n"
        stats_text += f"  • Avg MSE: {stats.get('avg_mse', 0):.6f}\n"
        stats_text += f"  • MAE Std: {stats.get('mae_std', 0):.6f}\n"
        stats_text += f"  • Total Steps: {stats.get('total_steps', 0)}\n\n"

        # 添加归一化域统计（如果有）
        if scaled_stats:
            stats_text += f"Normalized Domain:\n"
            stats_text += f"  • Avg MAE: {scaled_stats.get('avg_mae_scaled', 0):.6f}\n"
            stats_text += (
                f"  • Avg MSE: {scaled_stats.get('avg_mse_scaled', 0):.6f}\n\n"
            )

        # 添加组件误差汇总（如果有）
        if prediction_info.get("action_component_errors"):
            comp_errors = prediction_info["action_component_errors"]
            stats_text += f"Component Breakdown:\n"
            stats_text += f"  • Joint MAE: {comp_errors.get('joint_mae', 0):.6f} rad\n"
            stats_text += (
                f"  • Position MAE: {comp_errors.get('position_mae', 0):.2f} mm\n"
            )
            stats_text += (
                f"  • Rotation MAE: {comp_errors.get('rotation_mae', 0):.2f} deg\n"
            )

        axes[1, 2].text(
            0.05,
            0.95,
            stats_text,
            fontsize=10,
            verticalalignment="top",
            transform=axes[1, 2].transAxes,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8),
        )
        axes[1, 2].set_title("Performance Summary\n(All Metrics)")
        axes[1, 2].axis("off")

        plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为标题和说明文字留出更多空间

        # 添加模型和数据集信息
        self._add_model_dataset_info(fig, model_name, dataset_name)

        plt.savefig(save_path / "prediction_errors.png", dpi=300, bbox_inches="tight")
        plt.close()

    def _create_mae_radar_chart(
        self,
        ax,
        all_mae_phys,
        all_mae_norm,
        all_joint_mae,
        all_pos_mae,
        all_rot_mae,
        num_trajectories,
    ):
        """创建MAE对比雷达图"""
        import numpy as np

        # 定义雷达图的维度和数值
        categories = [
            "Physical\nMAE",
            "Normalized\nMAE",
            "Joint\nMAE (rad)",
            "Position\nMAE (mm)",
            "Rotation\nMAE (deg)",
        ]
        values = [
            np.mean(all_mae_phys),
            np.mean(all_mae_norm),
            np.mean(all_joint_mae),
            np.mean(all_pos_mae),
            np.mean(all_rot_mae),
        ]

        # 计算标准差
        stds = [
            np.std(all_mae_phys),
            np.std(all_mae_norm),
            np.std(all_joint_mae),
            np.std(all_pos_mae),
            np.std(all_rot_mae),
        ]

        # 角度计算
        N = len(categories)
        angles = [n / float(N) * 2 * np.pi for n in range(N)]
        angles += angles[:1]  # 闭合雷达图

        # 数值也需要闭合
        values_closed = values + values[:1]
        stds_closed = stds + stds[:1]

        # 清除当前轴内容
        ax.clear()

        # 手动创建极坐标效果
        # 将极坐标转换为笛卡尔坐标
        max_val = max(values) * 1.3  # 留出空间

        # 绘制同心圆网格
        for r in np.linspace(0.2, 1.0, 4):
            circle_x = [r * np.cos(angle) for angle in angles]
            circle_y = [r * np.sin(angle) for angle in angles]
            ax.plot(circle_x, circle_y, "k-", alpha=0.2, linewidth=0.5)

        # 绘制径向线
        for angle in angles[:-1]:
            ax.plot(
                [0, np.cos(angle)], [0, np.sin(angle)], "k-", alpha=0.2, linewidth=0.5
            )

        # 归一化数值到0-1范围用于绘图
        normalized_values = [v / max_val for v in values_closed]

        # 转换为笛卡尔坐标
        x_coords = [r * np.cos(angle) for r, angle in zip(normalized_values, angles)]
        y_coords = [r * np.sin(angle) for r, angle in zip(normalized_values, angles)]

        # 绘制雷达图
        ax.plot(
            x_coords,
            y_coords,
            "o-",
            linewidth=2.5,
            color="blue",
            markersize=6,
            alpha=0.8,
        )
        ax.fill(x_coords, y_coords, alpha=0.25, color="blue")

        # 添加数值标签
        for i, (x, y, val, std) in enumerate(
            zip(x_coords[:-1], y_coords[:-1], values, stds)
        ):
            # 标签位置稍微向外偏移
            label_x = x * 1.15
            label_y = y * 1.15
            ax.text(
                label_x,
                label_y,
                f"{val:.3f}",
                ha="center",
                va="center",
                fontsize=8,
                fontweight="bold",
                bbox=dict(boxstyle="round,pad=0.2", facecolor="white", alpha=0.8),
            )

        # 添加类别标签
        for i, (angle, category) in enumerate(zip(angles[:-1], categories)):
            label_x = 1.25 * np.cos(angle)
            label_y = 1.25 * np.sin(angle)
            ax.text(
                label_x,
                label_y,
                category,
                ha="center",
                va="center",
                fontsize=9,
                fontweight="bold",
            )

        # 设置轴属性
        ax.set_xlim(-1.4, 1.4)
        ax.set_ylim(-1.4, 1.4)
        ax.set_aspect("equal")
        ax.axis("off")  # 隐藏坐标轴

        # 添加标题
        ax.set_title(
            f"MAE Performance Radar\n({num_trajectories} Trajectories)",
            fontsize=11,
            fontweight="bold",
            pad=20,
        )

        # 添加图例和统计信息
        legend_text = (
            f"Physical: {np.mean(all_mae_phys):.3f}±{np.std(all_mae_phys):.3f}\n"
        )
        legend_text += (
            f"Normalized: {np.mean(all_mae_norm):.3f}±{np.std(all_mae_norm):.3f}\n"
        )
        legend_text += (
            f"Joint: {np.mean(all_joint_mae):.3f}±{np.std(all_joint_mae):.3f}"
        )

        ax.text(
            0.02,
            0.02,
            legend_text,
            transform=ax.transAxes,
            fontsize=7,
            verticalalignment="bottom",
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
        )

        return ax

    def _plot_aggregated_prediction_errors(
        self,
        all_predictions: List[Dict],
        save_path: Path,
        model_name: str = "Unknown Model",
        dataset_name: str = "Unknown Dataset",
        plot_normalized_errors: bool = False,
    ):
        """绘制所有轨迹的汇总预测误差图"""
        if not all_predictions:
            error_msg = "❌ 没有预测数据进行汇总分析"
            print(f"   {error_msg}")
            raise ValueError(error_msg)

        num_trajectories = len(all_predictions)
        print(f"   📊 汇总分析 {num_trajectories} 条轨迹的预测误差...")

        # 🆕 数据质量验证
        valid_predictions = 0
        invalid_predictions = []

        for i, pred in enumerate(all_predictions):
            if not isinstance(pred, dict):
                invalid_predictions.append(f"轨迹{i+1}: 不是字典格式")
                continue
            if "step_errors" not in pred:
                invalid_predictions.append(f"轨迹{i+1}: 缺少step_errors")
                continue
            if not pred["step_errors"]:
                invalid_predictions.append(f"轨迹{i+1}: step_errors为空")
                continue
            valid_predictions += 1

        if invalid_predictions:
            print(f"   ⚠️ 发现 {len(invalid_predictions)} 个无效预测数据:")
            for invalid in invalid_predictions[:5]:  # 只显示前5个
                print(f"      - {invalid}")
            if len(invalid_predictions) > 5:
                print(f"      - ... 还有 {len(invalid_predictions)-5} 个")

        if valid_predictions == 0:
            error_msg = "❌ 所有预测数据都无效，无法生成汇总分析"
            print(f"   {error_msg}")
            raise ValueError(error_msg)

        if valid_predictions < num_trajectories * 0.5:
            error_msg = f"❌ 有效预测数据不足50% ({valid_predictions}/{num_trajectories})，数据质量不可接受"
            print(f"   {error_msg}")
            raise ValueError(error_msg)

        print(
            f"   ✅ 数据质量验证通过: {valid_predictions}/{num_trajectories} 个有效预测"
        )

        # 🆕 检测样本数量并选择可视化模式
        use_statistical_mode = num_trajectories > 100
        if use_statistical_mode:
            print(
                f"   🎯 检测到大样本数量 ({num_trajectories} > 100)，启用统计聚合显示模式"
            )
        else:
            print(f"   📈 使用标准详细显示模式 ({num_trajectories} ≤ 100)")

        # 🆕 检测是否有rolling prediction数据
        has_rolling_data = any("rolling_prediction" in pred for pred in all_predictions)
        if has_rolling_data:
            print("   🔄 检测到Rolling Prediction数据，将包含策略对比分析")

        # 创建汇总分析图 - 扩展为3x3布局以容纳时间步分析
        fig, axes = plt.subplots(3, 3, figsize=(28, 22))  # 恢复并调大figsize
        fig.suptitle(
            f"Aggregated Prediction Error Analysis - {model_name} (Object-Centric Frame)\n({num_trajectories} Trajectories)"
            + (" - Statistical Mode" if use_statistical_mode else " - Detailed Mode"),
            fontsize=16,
            fontweight="bold",
            y=0.98,  # 调整标题位置，增加与图表的间距
        )

        # 添加说明文字，位置稍微下移
        fig.text(
            0.5,
            0.95,
            f"Statistical analysis across {num_trajectories} trajectories comparing predictions vs ground truth",
            ha="center",
            va="top",
            fontsize=12,
            style="italic",
            transform=fig.transFigure,
        )

        # 收集所有轨迹的误差数据
        all_mae_phys = []
        all_mse_phys = []
        all_mae_norm = []
        all_mse_norm = []
        all_joint_mae = []
        all_pos_mae = []
        all_rot_mae = []
        trajectory_labels = []

        # 🆕 收集时间步误差数据
        all_step_errors_phys = {}  # {step: [mae_values_across_trajectories]}
        all_step_errors_norm = {}  # {step: [mae_values_across_trajectories]}
        # 🆕 收集组件误差的时间步数据
        all_step_joint_errors = {}  # {step: [joint_mae_values_across_trajectories]}
        all_step_pos_errors = {}  # {step: [position_mae_values_across_trajectories]}
        all_step_rot_errors = (
            {}
        )  # {step: [rotation_distance_deg_values_across_trajectories]}
        # 🆕 收集归一化域组件误差的时间步数据
        all_step_joint_errors_scaled = (
            {}
        )  # {step: [joint_mae_scaled_values_across_trajectories]}
        all_step_pos_errors_scaled = (
            {}
        )  # {step: [position_mae_scaled_values_across_trajectories]}
        all_step_rot_errors_scaled = (
            {}
        )  # {step: [rotation_mae_scaled_values_across_trajectories]}
        max_steps = 0

        for i, pred_info in enumerate(all_predictions):
            trajectory_labels.append(f"Traj {i+1}")

            # 物理域统计
            stats = pred_info.get("prediction_stats", {})
            all_mae_phys.append(stats.get("avg_mae", 0))
            all_mse_phys.append(stats.get("avg_mse", 0))

            # 归一化域统计
            scaled_stats = pred_info.get("scaled_prediction_stats", {})
            all_mae_norm.append(
                scaled_stats.get("avg_mae_scaled", 0) if scaled_stats else 0
            )
            all_mse_norm.append(
                scaled_stats.get("avg_mse_scaled", 0) if scaled_stats else 0
            )

            # 动作组件误差
            comp_errors = pred_info.get("action_component_errors", {})
            all_joint_mae.append(comp_errors.get("joint_mae", 0))
            all_pos_mae.append(comp_errors.get("position_mae", 0))
            all_rot_mae.append(comp_errors.get("rotation_mae", 0))

            # 🆕 收集逐时间步误差数据
            step_errors = pred_info.get("step_errors", [])
            for step_info in step_errors:
                step = step_info["step"]
                max_steps = max(max_steps, step + 1)

                # 物理域时间步误差
                if step not in all_step_errors_phys:
                    all_step_errors_phys[step] = []
                all_step_errors_phys[step].append(step_info.get("mae", 0))

                # 归一化域时间步误差
                if step not in all_step_errors_norm:
                    all_step_errors_norm[step] = []
                mae_scaled = step_info.get("mae_scaled")
                if mae_scaled is not None:
                    all_step_errors_norm[step].append(mae_scaled)

                # 🆕 收集组件误差的时间步数据
                joint_mae = step_info.get("joint_mae", 0)
                pos_mae = step_info.get("position_mae", 0)
                rot_mae = step_info.get("rotation_mae", 0)

                if step not in all_step_joint_errors:
                    all_step_joint_errors[step] = []
                if step not in all_step_pos_errors:
                    all_step_pos_errors[step] = []
                if step not in all_step_rot_errors:
                    all_step_rot_errors[step] = []

                all_step_joint_errors[step].append(joint_mae)
                all_step_pos_errors[step].append(pos_mae)
                all_step_rot_errors[step].append(rot_mae)

                # 🆕 收集归一化域组件误差的时间步数据
                joint_mae_scaled = step_info.get("joint_mae_scaled", 0)
                pos_mae_scaled = step_info.get("position_mae_scaled", 0)
                rot_mae_scaled = step_info.get("rotation_mae_scaled", 0)

                if step not in all_step_joint_errors_scaled:
                    all_step_joint_errors_scaled[step] = []
                if step not in all_step_pos_errors_scaled:
                    all_step_pos_errors_scaled[step] = []
                if step not in all_step_rot_errors_scaled:
                    all_step_rot_errors_scaled[step] = []

                all_step_joint_errors_scaled[step].append(joint_mae_scaled)
                all_step_pos_errors_scaled[step].append(pos_mae_scaled)
                all_step_rot_errors_scaled[step].append(rot_mae_scaled)

        # 1. 轨迹间MAE分布箱线图
        mae_data = [all_mae_phys, all_mae_norm]
        mae_labels = ["Physical MAE", "Normalized MAE"]

        box_plot = axes[0, 0].boxplot(
            mae_data, tick_labels=mae_labels, patch_artist=True
        )
        box_plot["boxes"][0].set_facecolor("lightblue")
        box_plot["boxes"][1].set_facecolor("lightcoral")
        axes[0, 0].set_title("MAE Distribution Across Trajectories")
        axes[0, 0].set_ylabel("MAE Value")
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 轨迹间MSE分布箱线图
        mse_data = [all_mse_phys, all_mse_norm]
        mse_labels = ["Physical MSE", "Normalized MSE"]

        box_plot2 = axes[0, 1].boxplot(
            mse_data, tick_labels=mse_labels, patch_artist=True
        )
        box_plot2["boxes"][0].set_facecolor("lightgreen")
        box_plot2["boxes"][1].set_facecolor("lightyellow")
        axes[0, 1].set_title("MSE Distribution Across Trajectories")
        axes[0, 1].set_ylabel("MSE Value")
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 各轨迹误差趋势对比
        x_pos = range(len(trajectory_labels))
        width = 0.35

        # 🎯 根据样本数量选择显示方式
        if use_statistical_mode:
            # 大样本：雷达图显示MAE对比
            self._create_mae_radar_chart(
                axes[0, 2],
                all_mae_phys,
                all_mae_norm,
                all_joint_mae,
                all_pos_mae,
                all_rot_mae,
                num_trajectories,
            )
        else:
            # 小样本：保持原有详细显示
            axes[0, 2].bar(
                [x - width / 2 for x in x_pos],
                all_mae_phys,
                width,
                label="Physical MAE",
                color="skyblue",
                alpha=0.7,
            )
            axes[0, 2].bar(
                [x + width / 2 for x in x_pos],
                all_mae_norm,
                width,
                label="Normalized MAE",
                color="lightcoral",
                alpha=0.7,
            )
            axes[0, 2].set_title("MAE Comparison Across Trajectories")
            axes[0, 2].set_xlabel("Trajectory")
            axes[0, 2].set_ylabel("MAE Value")
            axes[0, 2].set_xticks(x_pos)
            axes[0, 2].set_xticklabels(trajectory_labels, rotation=45)
            axes[0, 2].legend()
            axes[0, 2].grid(True, alpha=0.3)

        # 4. 🆕 归一化域时间步误差汇总
        if all_step_errors_norm and any(all_step_errors_norm.values()):
            steps_norm = sorted(
                [s for s in all_step_errors_norm.keys() if all_step_errors_norm[s]]
            )

            # 计算每个时间步的统计量
            step_means_norm = []
            step_stds_norm = []
            step_medians_norm = []

            for step in steps_norm:
                values = all_step_errors_norm[step]
                if values:
                    step_means_norm.append(np.mean(values))
                    step_stds_norm.append(np.std(values))
                    step_medians_norm.append(np.median(values))

            # 绘制归一化域时间步误差
            axes[1, 0].plot(
                steps_norm,
                step_means_norm,
                "c-o",
                markersize=4,
                label="Mean MAE (Norm)",
                linewidth=2,
            )
            axes[1, 0].fill_between(
                steps_norm,
                [m - s for m, s in zip(step_means_norm, step_stds_norm)],
                [m + s for m, s in zip(step_means_norm, step_stds_norm)],
                alpha=0.3,
                color="cyan",
                label="±1 Std",
            )
            axes[1, 0].plot(
                steps_norm,
                step_medians_norm,
                "m--",
                markersize=3,
                label="Median MAE (Norm)",
                linewidth=1,
            )
            axes[1, 0].set_title(
                "Normalized Domain Errors Over Time\n(Aggregated Across Trajectories)"
            )
            axes[1, 0].set_xlabel("Time Step")
            axes[1, 0].set_ylabel("MAE Value")
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        else:
            axes[1, 0].text(
                0.5,
                0.5,
                "Normalized Domain Time-step Errors\n(No data available)",
                ha="center",
                va="center",
                transform=axes[1, 0].transAxes,
                fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"),
            )
            axes[1, 0].set_title("Normalized Domain Errors Over Time")
            axes[1, 0].axis("off")

        # 6. 动作组件误差汇总箱线图
        component_data = [all_joint_mae, all_pos_mae, all_rot_mae]
        component_labels = ["Joint MAE", "Position MAE", "Rotation Distance"]

        box_plot3 = axes[1, 1].boxplot(
            component_data, tick_labels=component_labels, patch_artist=True
        )
        colors = ["lightblue", "lightgreen", "lightyellow"]
        for patch, color in zip(box_plot3["boxes"], colors):
            patch.set_facecolor(color)
        axes[1, 1].set_title("Action Component Error Distribution")
        axes[1, 1].set_ylabel("Error Value")
        axes[1, 1].tick_params(axis="x", rotation=45)
        axes[1, 1].grid(True, alpha=0.3)

        # 7. 轨迹性能排名 (右中图优化)
        # 计算综合误差分数（物理域MAE + 归一化域MAE）
        combined_scores = [p + n for p, n in zip(all_mae_phys, all_mae_norm)]

        # 🎯 根据样本数量选择显示方式
        if use_statistical_mode:
            # 大样本：性能分布直方图
            n_bins = min(50, max(10, num_trajectories // 20))  # 自适应分箱数量

            # 绘制直方图
            n, bins, patches = axes[1, 2].hist(
                combined_scores,
                bins=n_bins,
                alpha=0.7,
                color="skyblue",
                edgecolor="black",
                linewidth=0.5,
            )

            # 使用渐变色彩
            for i, (patch, bin_center) in enumerate(
                zip(patches, (bins[:-1] + bins[1:]) / 2)
            ):
                color_intensity = (bin_center - min(combined_scores)) / (
                    max(combined_scores) - min(combined_scores)
                )
                patch.set_facecolor(plt.cm.RdYlGn_r(color_intensity))

            # 添加统计线
            mean_score = np.mean(combined_scores)
            median_score = np.median(combined_scores)
            axes[1, 2].axvline(
                mean_score,
                color="red",
                linestyle="-",
                linewidth=2,
                label=f"Mean: {mean_score:.3f}",
            )
            axes[1, 2].axvline(
                median_score,
                color="green",
                linestyle="--",
                linewidth=2,
                label=f"Median: {median_score:.3f}",
            )

            # 添加性能分级标识线
            q25, q75 = np.percentile(combined_scores, [25, 75])
            axes[1, 2].axvline(
                q25, color="orange", linestyle=":", alpha=0.7, label=f"Q1: {q25:.3f}"
            )
            axes[1, 2].axvline(
                q75, color="orange", linestyle=":", alpha=0.7, label=f"Q3: {q75:.3f}"
            )

            # 添加统计信息文本
            std_score = np.std(combined_scores)
            stats_text = f"Count: {num_trajectories}\nStd: {std_score:.3f}\nRange: [{min(combined_scores):.3f}, {max(combined_scores):.3f}]"
            axes[1, 2].text(
                0.98,
                0.98,
                stats_text,
                transform=axes[1, 2].transAxes,
                fontsize=9,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                verticalalignment="top",
                horizontalalignment="right",
            )

            axes[1, 2].set_title(
                "Trajectory Performance Distribution\n(Combined MAE Score)"
            )
            axes[1, 2].set_xlabel("Combined MAE Score")
            axes[1, 2].set_ylabel("Frequency")
            axes[1, 2].legend(fontsize=8)
        else:
            # 小样本：保持原有排名显示
            sorted_indices = sorted(
                range(len(combined_scores)), key=lambda i: combined_scores[i]
            )
            sorted_labels = [trajectory_labels[i] for i in sorted_indices]
            sorted_scores = [combined_scores[i] for i in sorted_indices]

            bars = axes[1, 2].bar(
                range(len(sorted_labels)),
                sorted_scores,
                color=plt.cm.RdYlGn_r([s / max(sorted_scores) for s in sorted_scores]),
            )
            axes[1, 2].set_title("Trajectory Performance Ranking\n(Combined MAE Score)")
            axes[1, 2].set_xlabel("Trajectory (Best → Worst)")
            axes[1, 2].set_ylabel("Combined MAE Score")
            axes[1, 2].set_xticks(range(len(sorted_labels)))
            axes[1, 2].set_xticklabels(sorted_labels, rotation=45)

        axes[1, 2].grid(True, alpha=0.3)

        # 8. 🆕 物理域时间步误差汇总 (右上图优化 - 移至axes[2,0])
        if all_step_errors_phys and max_steps > 0:
            steps = sorted(all_step_errors_phys.keys())

            # 计算每个时间步的统计量
            step_means_phys = []
            step_stds_phys = []
            step_medians_phys = []
            step_q25_phys = []
            step_q75_phys = []

            for step in steps:
                values = all_step_errors_phys[step]
                if values:
                    step_means_phys.append(np.mean(values))
                    step_stds_phys.append(np.std(values))
                    step_medians_phys.append(np.median(values))
                    step_q25_phys.append(np.percentile(values, 25))
                    step_q75_phys.append(np.percentile(values, 75))
                else:
                    step_means_phys.append(0)
                    step_stds_phys.append(0)
                    step_medians_phys.append(0)
                    step_q25_phys.append(0)
                    step_q75_phys.append(0)

            # 🎯 根据样本数量选择显示方式
            if use_statistical_mode:
                # 大样本：统计聚合显示
                # 主趋势线（均值）
                axes[2, 0].plot(
                    steps,
                    step_means_phys,
                    "b-",
                    linewidth=3,
                    label="Mean MAE",
                    alpha=0.9,
                )

                # 置信区间带（±1标准差）
                axes[2, 0].fill_between(
                    steps,
                    [m - s for m, s in zip(step_means_phys, step_stds_phys)],
                    [m + s for m, s in zip(step_means_phys, step_stds_phys)],
                    alpha=0.3,
                    color="blue",
                    label="±1 Std Dev",
                )

                # 分位数线
                axes[2, 0].plot(
                    steps,
                    step_q25_phys,
                    "g--",
                    linewidth=1.5,
                    alpha=0.7,
                    label="25th Percentile",
                )
                axes[2, 0].plot(
                    steps,
                    step_medians_phys,
                    "r-",
                    linewidth=2,
                    alpha=0.8,
                    label="Median",
                )
                axes[2, 0].plot(
                    steps,
                    step_q75_phys,
                    "g--",
                    linewidth=1.5,
                    alpha=0.7,
                    label="75th Percentile",
                )

                # 添加统计信息文本
                mean_overall = np.mean(step_means_phys)
                std_overall = np.mean(step_stds_phys)
                axes[2, 0].text(
                    0.02,
                    0.98,
                    f"Overall: μ={mean_overall:.3f}, σ={std_overall:.3f}",
                    transform=axes[2, 0].transAxes,
                    fontsize=9,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                    verticalalignment="top",
                )
            else:
                # 小样本：保持原有详细显示
                axes[2, 0].plot(
                    steps,
                    step_means_phys,
                    "b-o",
                    markersize=4,
                    label="Mean MAE",
                    linewidth=2,
                )
                axes[2, 0].fill_between(
                    steps,
                    [m - s for m, s in zip(step_means_phys, step_stds_phys)],
                    [m + s for m, s in zip(step_means_phys, step_stds_phys)],
                    alpha=0.3,
                    color="blue",
                    label="±1 Std",
                )
                axes[2, 0].plot(
                    steps,
                    step_medians_phys,
                    "r--",
                    markersize=3,
                    label="Median MAE",
                    linewidth=1,
                )

            axes[2, 0].set_title(
                "Physical Domain Errors Over Time\n(Aggregated Across Trajectories)"
            )
            axes[2, 0].set_xlabel("Time Step")
            axes[2, 0].set_ylabel("MAE Value")
            axes[2, 0].legend(fontsize=8)
            axes[2, 0].grid(True, alpha=0.3)
        else:
            axes[2, 0].text(
                0.5,
                0.5,
                "Physical Domain Time-step Errors\n(No data available)",
                ha="center",
                va="center",
                transform=axes[2, 0].transAxes,
                fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"),
            )
            axes[2, 0].set_title("Physical Domain Errors Over Time")
            axes[2, 0].axis("off")

        # 9. 汇总统计概览
        stats_text = f"Total Trajectories: {len(all_predictions)}\n\n"

        # 物理域统计
        stats_text += "Physical Domain:\n"
        stats_text += (
            f"  Avg MAE: {np.mean(all_mae_phys):.4f} ± {np.std(all_mae_phys):.4f}\n"
        )
        stats_text += (
            f"  Avg MSE: {np.mean(all_mse_phys):.4f} ± {np.std(all_mse_phys):.4f}\n"
        )
        stats_text += f"  Best MAE: {np.min(all_mae_phys):.4f}\n"
        stats_text += f"  Worst MAE: {np.max(all_mae_phys):.4f}\n\n"

        # 归一化域统计
        if any(all_mae_norm):
            stats_text += "Normalized Domain:\n"
            stats_text += (
                f"  Avg MAE: {np.mean(all_mae_norm):.4f} ± {np.std(all_mae_norm):.4f}\n"
            )
            stats_text += (
                f"  Avg MSE: {np.mean(all_mse_norm):.4f} ± {np.std(all_mse_norm):.4f}\n"
            )
            stats_text += f"  Best MAE: {np.min(all_mae_norm):.4f}\n"
            stats_text += f"  Worst MAE: {np.max(all_mae_norm):.4f}\n\n"

        # 动作组件统计
        stats_text += "Action Components:\n"
        stats_text += (
            f"  Joint MAE: {np.mean(all_joint_mae):.4f} ± {np.std(all_joint_mae):.4f}\n"
        )
        stats_text += f"  Position MAE: {np.mean(all_pos_mae):.2f} ± {np.std(all_pos_mae):.2f} mm\n"
        stats_text += f"  Rotation Distance: {np.mean(all_rot_mae):.2f} ± {np.std(all_rot_mae):.2f} deg\n\n"

        # 🆕 时间步统计
        if all_step_errors_phys:
            stats_text += "Time-step Analysis:\n"
            stats_text += f"  Max time steps: {max_steps}\n"
            stats_text += f"  Trajectories with step data: {len([p for p in all_predictions if p.get('step_errors')])}\n\n"

        # 🆕 添加模型和数据集信息到统计概览中
        stats_text += "Model & Dataset Info:\n"
        stats_text += f"  Model: {model_name}\n"
        stats_text += f"  Dataset: {dataset_name}"

        axes[2, 1].text(
            0.05,
            0.95,
            stats_text,
            fontsize=10,
            verticalalignment="top",
            transform=axes[2, 1].transAxes,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8),
        )
        axes[2, 1].set_title("Aggregated Statistics Overview")
        axes[2, 1].axis("off")

        # 10. 🆕 时间步误差分布分析 (右下图优化)
        if all_step_errors_phys and max_steps > 1:
            # 🎯 根据样本数量选择显示方式
            if use_statistical_mode:
                # 大样本：时间步箱线图显示
                steps = sorted(all_step_errors_phys.keys())
                step_data_for_boxplot = []
                step_labels = []

                for step in steps:
                    values = all_step_errors_phys[step]
                    if values and len(values) > 1:  # 至少需要2个数据点才能绘制箱线图
                        step_data_for_boxplot.append(values)
                        step_labels.append(f"T{step}")

                if step_data_for_boxplot:
                    # 绘制箱线图
                    box_plot = axes[2, 2].boxplot(
                        step_data_for_boxplot,
                        tick_labels=step_labels,
                        patch_artist=True,
                        showfliers=True,
                    )

                    # 美化箱线图
                    colors = plt.cm.viridis(np.linspace(0, 1, len(box_plot["boxes"])))
                    for patch, color in zip(box_plot["boxes"], colors):
                        patch.set_facecolor(color)
                        patch.set_alpha(0.7)

                    # 设置异常值样式
                    for flier in box_plot["fliers"]:
                        flier.set(marker="o", color="red", alpha=0.5, markersize=3)

                    # 添加均值点
                    means = [np.mean(data) for data in step_data_for_boxplot]
                    axes[2, 2].scatter(
                        range(1, len(means) + 1),
                        means,
                        color="red",
                        marker="D",
                        s=30,
                        zorder=10,
                        label="Mean",
                    )

                    # 添加统计信息
                    overall_mean = np.mean(
                        [val for data in step_data_for_boxplot for val in data]
                    )
                    overall_std = np.std(
                        [val for data in step_data_for_boxplot for val in data]
                    )
                    axes[2, 2].text(
                        0.02,
                        0.98,
                        f"Overall: μ={overall_mean:.3f}, σ={overall_std:.3f}",
                        transform=axes[2, 2].transAxes,
                        fontsize=9,
                        bbox=dict(
                            boxstyle="round,pad=0.3", facecolor="white", alpha=0.8
                        ),
                        verticalalignment="top",
                    )

                    axes[2, 2].set_title(
                        "Time-step MAE Distribution\n(Physical Domain - Box Plot)"
                    )
                    axes[2, 2].set_xlabel("Time Step")
                    axes[2, 2].set_ylabel("MAE Value")
                    axes[2, 2].legend(fontsize=8)
                    axes[2, 2].grid(True, alpha=0.3)
                else:
                    axes[2, 2].text(
                        0.5,
                        0.5,
                        "Time-step MAE Distribution\n(Insufficient data for box plot)",
                        ha="center",
                        va="center",
                        transform=axes[2, 2].transAxes,
                        fontsize=12,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"),
                    )
                    axes[2, 2].axis("off")
            else:
                # 小样本：保持原有热力图显示
                heatmap_data = []
                traj_labels_for_heatmap = []

                for i, pred_info in enumerate(all_predictions):
                    step_errors = pred_info.get("step_errors", [])
                    if step_errors:
                        traj_labels_for_heatmap.append(f"Traj {i+1}")
                        row_data = [0] * max_steps  # 初始化为0

                        for step_info in step_errors:
                            step = step_info["step"]
                            if step < max_steps:
                                row_data[step] = step_info.get("mae", 0)

                        heatmap_data.append(row_data)

                if heatmap_data:
                    heatmap_data = np.array(heatmap_data)
                    im = axes[2, 2].imshow(heatmap_data, cmap="YlOrRd", aspect="auto")

                    # 设置坐标轴
                    axes[2, 2].set_xticks(range(max_steps))
                    axes[2, 2].set_xticklabels([f"T{i}" for i in range(max_steps)])
                    axes[2, 2].set_yticks(range(len(traj_labels_for_heatmap)))
                    axes[2, 2].set_yticklabels(traj_labels_for_heatmap)

                    # 添加颜色条
                    plt.colorbar(im, ax=axes[2, 2], shrink=0.8, label="MAE Value")

                    axes[2, 2].set_title("Time-step MAE Heatmap\n(Physical Domain)")
                    axes[2, 2].set_xlabel("Time Step")
                    axes[2, 2].set_ylabel("Trajectory")
                else:
                    axes[2, 2].text(
                        0.5,
                        0.5,
                        "Time-step MAE Heatmap\n(No valid data)",
                        ha="center",
                        va="center",
                        transform=axes[2, 2].transAxes,
                        fontsize=12,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"),
                    )
                    axes[2, 2].axis("off")
        else:
            axes[2, 2].text(
                0.5,
                0.5,
                "Time-step MAE Analysis\n(Insufficient time steps)",
                ha="center",
                va="center",
                transform=axes[2, 2].transAxes,
                fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"),
            )
            axes[2, 2].axis("off")

        plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为标题和说明文字留出更多空间

        # 不再使用_add_model_dataset_info，因为信息已经放在统计概览中

        plt.savefig(
            save_path / "aggregated_prediction_errors.png", dpi=300, bbox_inches="tight"
        )
        plt.close()

        # 🆕 用户提示信息
        if use_statistical_mode:
            print(
                f"   🎯 已使用统计聚合显示模式优化大样本可视化 ({num_trajectories} 个轨迹)"
            )
            print(
                "      - 左下图 (axes[2,0]): 物理域时间步误差统计趋势 (均值±标准差 + 分位数)"
            )
            print("      - 右中图 (axes[1,2]): 性能分布直方图 (统计线 + 分级标识)")
            print(
                "      - 右下图 (axes[2,2]): 时间步误差箱线图 (分布统计 + 异常值检测)"
            )
        else:
            print(f"   📈 使用标准详细显示模式 ({num_trajectories} 个轨迹)")

        print(
            f"   ✅ 汇总误差分析图已保存: {save_path / 'aggregated_prediction_errors.png'}"
        )
        print(f"   📊 分析完成: 基于 {valid_predictions} 个有效轨迹的完整预测数据")

        # 🆕 如果有rolling prediction数据，生成汇总对比分析
        if has_rolling_data:
            try:
                self._plot_aggregated_rolling_comparison(
                    all_predictions, save_path, model_name, dataset_name
                )
                print(
                    f"   ✅ Rolling Prediction汇总对比已保存: {save_path / 'aggregated_rolling_comparison.png'}"
                )
            except Exception as e:
                print(f"   ⚠️ Rolling Prediction汇总对比生成失败: {e}")

        # 🆕 生成组件误差随时间变化的分析图
        if all_step_joint_errors and all_step_pos_errors and all_step_rot_errors:
            try:
                self._plot_component_errors_over_time(
                    all_step_joint_errors,
                    all_step_pos_errors,
                    all_step_rot_errors,
                    save_path,
                    model_name,
                    dataset_name,
                    all_step_joint_errors_scaled,
                    all_step_pos_errors_scaled,
                    all_step_rot_errors_scaled,
                    plot_normalized_errors=plot_normalized_errors,
                )
                print(
                    f"   ✅ 组件误差时间变化分析图已保存: {save_path / 'component_errors_over_time.png'}"
                )
            except Exception as e:
                print(f"   ⚠️ 组件误差时间变化分析图生成失败: {e}")
        else:
            print("   ⚠️ 缺少组件误差时间步数据，跳过组件误差时间变化分析")

    def generate_report(
        self, analysis_results: List[Dict], save_path: str = "analysis_report.json"
    ):
        """生成分析报告"""
        print("\n📝 生成分析报告...")

        report = {
            "analysis_summary": {
                "total_trajectories": len(analysis_results),
                "successful_analyses": len(
                    [r for r in analysis_results if r is not None]
                ),
                "analysis_timestamp": pd.Timestamp.now().isoformat(),
            },
            "trajectory_details": analysis_results,
        }

        # 保存JSON报告
        with open(save_path, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        print(f"   ✅ 分析报告已保存: {save_path}")

        return report

    def _plot_component_block_heatmap(
        self,
        actions_before: "torch.Tensor",
        actions_after: "torch.Tensor",
        title_prefix: str = "",
        save_path: "Path" = None,
    ):
        """绘制组件分块热力图 (关节/位置/旋转)"""
        import matplotlib.pyplot as plt
        import seaborn as sns
        import numpy as np

        # 将输入统一为 [N,25] 形状，其中 N=批*序列
        def _flatten(t: "torch.Tensor"):
            if t.dim() == 3:
                b, tlen, d = t.shape
                return t.view(b * tlen, d)
            return t

        before_np = _flatten(actions_before).numpy()
        after_np = _flatten(actions_after).numpy()

        # 差值 (符号差便于看方向)
        diff_np = after_np - before_np

        comp_slices = {
            "Joints(0-15)": slice(0, 16),
            "Pos(16-18)": slice(16, 19),
            "Rotation(19-24)": slice(19, 25),
        }

        fig, axes = plt.subplots(1, 3, figsize=(18, 6), sharey=True)
        cmap_main = "coolwarm"  # 原始值色图：负→蓝，正→红
        cmap_diff = "viridis"  # 差值色图：小→紫， 大→黄

        for ax_idx, (ax, (name, slc)) in enumerate(zip(axes, comp_slices.items())):
            # 纵向拼接：before | after | abs diff
            before_block = before_np[:, slc]
            after_block = after_np[:, slc]
            diff_block = np.abs(diff_np[:, slc])

            data_block = np.concatenate([before_block, after_block, diff_block], axis=1)

            # 主 heatmap
            im = sns.heatmap(
                data_block,
                ax=ax,
                cmap=cmap_main,
                cbar_kws={"label": "Action value"},
                xticklabels=False,
                yticklabels=False,
            )

            # 计算列界限
            n_before = before_block.shape[1]
            n_after = after_block.shape[1]
            n_diff = diff_block.shape[1]

            # 垂直分隔线
            ax.axvline(n_before, color="black", linewidth=1)
            ax.axvline(n_before + n_after, color="black", linewidth=1)

            # 顶部标签
            ax.text(
                n_before / 2,
                -1.5,
                "Before",
                ha="center",
                va="center",
                fontsize=9,
                fontweight="bold",
            )
            ax.text(
                n_before + n_after / 2,
                -1.5,
                "After",
                ha="center",
                va="center",
                fontsize=9,
                fontweight="bold",
            )
            ax.text(
                n_before + n_after + n_diff / 2,
                -1.5,
                "|Δ|",
                ha="center",
                va="center",
                fontsize=9,
                fontweight="bold",
            )

            # 数值范围注释 (左侧)
            rng_min = data_block.min()
            rng_max = data_block.max()
            ax.text(
                -0.5,
                data_block.shape[0] / 2,
                f"{rng_min:.2f}\n↓\n{rng_max:.2f}",
                ha="right",
                va="center",
                fontsize=8,
                fontweight="bold",
                rotation=90,
            )

            # 设置轴标题
            ax.set_title(name)
            if ax_idx == 0:
                ax.set_ylabel("Time step")
            ax.set_xlabel("Action dim")

        plt.suptitle(f"{title_prefix} Component Heatmaps (before | after | abs diff)")
        plt.subplots_adjust(top=0.88)

        if save_path is not None:
            fname = save_path / f"component_heatmap_{title_prefix}.png"
            plt.savefig(fname, dpi=300)
            print(f"   📊 组件热力图已保存: {fname}")
            plt.close()
        else:
            plt.show()

    def _plot_target_pose_accuracy(
        self,
        all_predictions: List[Dict],
        save_path: Path,
        model_name: str = "Unknown Model",
        dataset_name: str = "Unknown Dataset",
    ):
        """
        绘制target pose准确性分析图

        Args:
            all_predictions: 所有轨迹的预测结果列表
            save_path: 保存路径
        """
        # 收集所有target pose误差数据
        target_pose_data = []
        for pred in all_predictions:
            if "target_pose_errors" in pred and pred["target_pose_errors"]:
                target_pose_data.append(pred["target_pose_errors"])

        if not target_pose_data:
            print("   ⚠️ 没有target pose误差数据，跳过可视化")
            return

        num_trajectories = len(target_pose_data)
        print(f"   📊 基于 {num_trajectories} 个轨迹生成target pose准确性分析...")

        # 🆕 检测样本数量并选择可视化模式
        use_statistical_mode = num_trajectories > 100
        if use_statistical_mode:
            print(
                f"   🎯 检测到大样本数量 ({num_trajectories} > 100)，启用统计聚合显示模式"
            )

        # 创建2x3布局的图表
        fig, axes = plt.subplots(2, 3, figsize=(22, 16))  # 恢复并调大figsize
        fig.suptitle(
            f"Target Pose Accuracy Analysis - {model_name} (Object-Centric Frame)\n({num_trajectories} Trajectories)"
            + (" - Statistical Mode" if use_statistical_mode else " - Detailed Mode"),
            fontsize=16,
            fontweight="bold",
        )

        # 添加说明文字
        fig.text(
            0.5,
            0.95,
            f"Analysis of final pose prediction accuracy across {len(target_pose_data)} trajectories",
            ha="center",
            va="top",
            fontsize=12,
            style="italic",
            transform=fig.transFigure,
        )

        # 1. 位置误差分布 (左上)
        position_maes = [data["position_mae"] for data in target_pose_data]
        position_l2s = [data["position_l2_distance"] for data in target_pose_data]

        axes[0, 0].hist(
            position_maes, bins=20, alpha=0.7, color="skyblue", label="Position MAE"
        )
        axes[0, 0].axvline(
            np.mean(position_maes),
            color="red",
            linestyle="--",
            label=f"Mean: {np.mean(position_maes):.2f}mm",
        )
        axes[0, 0].set_title("Position Error Distribution")
        axes[0, 0].set_xlabel("Position MAE (mm)")
        axes[0, 0].set_ylabel("Frequency")
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 关节误差分布 (中上)
        joint_maes = [data["joint_mae"] for data in target_pose_data]

        axes[0, 1].hist(
            joint_maes, bins=20, alpha=0.7, color="lightgreen", label="Joint MAE"
        )
        axes[0, 1].axvline(
            np.mean(joint_maes),
            color="red",
            linestyle="--",
            label=f"Mean: {np.mean(joint_maes):.4f}",
        )
        axes[0, 1].set_title("Joint Error Distribution")
        axes[0, 1].set_xlabel("Joint MAE (rad)")
        axes[0, 1].set_ylabel("Frequency")
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 旋转误差分布 (右上) - 🆕 使用四元数距离（度）
        # 优先使用四元数距离，如果不存在则回退到传统欧氏距离
        rotation_errors = []
        rotation_label = ""
        rotation_unit = ""

        if all("rotation_distance_deg" in data for data in target_pose_data):
            rotation_errors = [
                data["rotation_distance_deg"] for data in target_pose_data
            ]
            rotation_label = "Rotation Distance (Quaternion)"
            rotation_unit = "deg"
        elif all("rotation_mae_euclidean" in data for data in target_pose_data):
            rotation_errors = [
                data["rotation_mae_euclidean"] for data in target_pose_data
            ]
            rotation_label = "Rotation MAE (Euclidean)"
            rotation_unit = "6D"
        else:
            # 兼容旧版本字段名
            rotation_errors = [data.get("rotation_mae", 0) for data in target_pose_data]
            rotation_label = "Rotation MAE (Legacy)"
            rotation_unit = "6D"

        axes[0, 2].hist(
            rotation_errors,
            bins=20,
            alpha=0.7,
            color="lightcoral",
            label=rotation_label,
        )
        axes[0, 2].axvline(
            np.mean(rotation_errors),
            color="red",
            linestyle="--",
            label=f"Mean: {np.mean(rotation_errors):.2f}",
        )
        axes[0, 2].set_title("Rotation Error Distribution")
        axes[0, 2].set_xlabel(f"Rotation Error ({rotation_unit})")
        axes[0, 2].set_ylabel("Frequency")
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)

        # 4. 误差组件对比 (左下) - Physical版本
        components = ["Joint\n(rad)", "Position\n(mm)", f"Rotation\n({rotation_unit})"]
        mean_errors = [
            np.mean(joint_maes),  # 关节误差 (弧度)
            np.mean(position_maes),  # 位置误差 (毫米) - 保持原始单位
            np.mean(rotation_errors),  # 旋转误差 (四元数距离度或6D无量纲)
        ]
        std_errors = [
            np.std(joint_maes),
            np.std(position_maes),
            np.std(rotation_errors),
        ]

        x_pos = np.arange(len(components))
        bars = axes[1, 0].bar(
            x_pos,
            mean_errors,
            yerr=std_errors,
            capsize=5,
            color=["lightgreen", "skyblue", "lightcoral"],
            alpha=0.7,
        )

        # 在每个柱子上显示数值和单位
        for i, (bar, mean_val, std_val) in enumerate(
            zip(bars, mean_errors, std_errors)
        ):
            height = bar.get_height()
            if i == 0:  # Joint
                label_text = f"{mean_val:.4f}±{std_val:.4f}"
            elif i == 1:  # Position
                label_text = f"{mean_val:.1f}±{std_val:.1f}"
            else:  # Rotation
                label_text = f"{mean_val:.4f}±{std_val:.4f}"

            axes[1, 0].text(
                bar.get_x() + bar.get_width() / 2.0,
                height + std_val + max(mean_errors) * 0.02,
                label_text,
                ha="center",
                va="bottom",
                fontsize=8,
            )

        axes[1, 0].set_title("Error Components Comparison (Physical Units)")
        axes[1, 0].set_xlabel("Component")
        axes[1, 0].set_ylabel("Mean Error (Physical Units)")
        axes[1, 0].set_xticks(x_pos)
        axes[1, 0].set_xticklabels(components)
        axes[1, 0].grid(True, alpha=0.3)

        # 添加注释说明单位差异
        axes[1, 0].text(
            0.02,
            0.98,
            "Note: Different units\nmake direct comparison\ndifficult",
            transform=axes[1, 0].transAxes,
            fontsize=8,
            verticalalignment="top",
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
        )

        # 5. 位置误差分析 (中下) - 🆕 自适应可视化
        if use_statistical_mode:
            # 大样本：统计分布分析
            n_bins = min(50, max(10, num_trajectories // 50))

            # 绘制分布直方图
            n, bins, patches = axes[1, 1].hist(
                position_maes,
                bins=n_bins,
                alpha=0.7,
                color="skyblue",
                edgecolor="black",
                linewidth=0.5,
            )

            # 使用渐变色彩表示性能等级
            for i, (patch, bin_center) in enumerate(
                zip(patches, (bins[:-1] + bins[1:]) / 2)
            ):
                color_intensity = (bin_center - min(position_maes)) / (
                    max(position_maes) - min(position_maes)
                )
                patch.set_facecolor(plt.cm.RdYlGn_r(color_intensity))

            # 添加统计线
            mean_pos = np.mean(position_maes)
            median_pos = np.median(position_maes)
            axes[1, 1].axvline(
                mean_pos,
                color="red",
                linestyle="-",
                linewidth=2,
                label=f"Mean: {mean_pos:.2f}mm",
            )
            axes[1, 1].axvline(
                median_pos,
                color="green",
                linestyle="--",
                linewidth=2,
                label=f"Median: {median_pos:.2f}mm",
            )

            # 添加分位数线
            q25, q75 = np.percentile(position_maes, [25, 75])
            axes[1, 1].axvline(
                q25, color="orange", linestyle=":", alpha=0.7, label=f"Q1: {q25:.2f}mm"
            )
            axes[1, 1].axvline(
                q75, color="orange", linestyle=":", alpha=0.7, label=f"Q3: {q75:.2f}mm"
            )

            # 统计信息
            std_pos = np.std(position_maes)
            stats_text = f"Count: {num_trajectories}\nStd: {std_pos:.2f}mm\nRange: [{min(position_maes):.1f}, {max(position_maes):.1f}]mm"
            axes[1, 1].text(
                0.98,
                0.98,
                stats_text,
                transform=axes[1, 1].transAxes,
                fontsize=9,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                verticalalignment="top",
                horizontalalignment="right",
            )

            axes[1, 1].set_title("Position Error Distribution\n(Statistical Analysis)")
            axes[1, 1].set_xlabel("Position MAE (mm)")
            axes[1, 1].set_ylabel("Frequency")

        else:
            # 小样本：保持原有散点图
            trajectory_indices = list(range(len(position_maes)))
            axes[1, 1].scatter(
                trajectory_indices, position_maes, alpha=0.6, color="skyblue"
            )
            axes[1, 1].plot(trajectory_indices, position_maes, alpha=0.3, color="blue")
            axes[1, 1].axhline(
                np.mean(position_maes),
                color="red",
                linestyle="--",
                label=f"Mean: {np.mean(position_maes):.2f}mm",
            )
            axes[1, 1].set_title("Position Error per Trajectory")
            axes[1, 1].set_xlabel("Trajectory Index")
            axes[1, 1].set_ylabel("Position MAE (mm)")

        axes[1, 1].legend(fontsize=8)
        axes[1, 1].grid(True, alpha=0.3)

        # 6. 统计摘要 (右下)
        stats_text = f"""Target Pose Accuracy Summary (Object-Centric Frame)

Trajectories Analyzed: {len(target_pose_data)}

Position Errors:
  Mean MAE: {np.mean(position_maes):.2f} mm
  Std MAE: {np.std(position_maes):.2f} mm
  Mean L2: {np.mean(position_l2s):.2f} mm

Joint Errors:
  Mean MAE: {np.mean(joint_maes):.4f} rad
  Std MAE: {np.std(joint_maes):.4f} rad

Rotation Errors ({rotation_label}):
  Mean: {np.mean(rotation_errors):.2f} {rotation_unit}
  Std: {np.std(rotation_errors):.2f} {rotation_unit}

Overall Performance:
  Position Accuracy: {'Good' if np.mean(position_maes) < 10 else 'Needs Improvement'}
  Joint Accuracy: {'Good' if np.mean(joint_maes) < 0.1 else 'Needs Improvement'}
  Rotation Accuracy: {'Good' if (rotation_unit == 'deg' and np.mean(rotation_errors) < 5) or (rotation_unit == '6D' and np.mean(rotation_errors) < 0.1) else 'Needs Improvement'}
        """

        axes[1, 2].text(
            0.05,
            0.95,
            stats_text,
            transform=axes[1, 2].transAxes,
            fontsize=10,
            verticalalignment="top",
            fontfamily="monospace",
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8),
        )
        axes[1, 2].set_title("Statistical Summary")
        axes[1, 2].axis("off")

        plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为标题和说明文字留出更多空间

        # 添加模型和数据集信息
        self._add_model_dataset_info(fig, model_name, dataset_name)

        plt.savefig(
            save_path / "target_pose_accuracy_analysis.png",
            dpi=300,
            bbox_inches="tight",
        )
        plt.close()

        print(
            f"   ✅ Target pose准确性分析已保存: {save_path}/target_pose_accuracy_analysis.png"
        )

        # 🆕 用户提示信息
        if use_statistical_mode:
            print(
                f"   🎯 已使用统计聚合显示模式优化大样本可视化 ({num_trajectories} 个轨迹)"
            )
            print("      - 位置误差图 (中下): 从散点图切换为统计分布直方图")
        else:
            print(f"   📈 使用标准详细显示模式 ({num_trajectories} 个轨迹)")

    def _plot_component_errors_over_time(
        self,
        all_step_joint_errors: Dict[int, List[float]],
        all_step_pos_errors: Dict[int, List[float]],
        all_step_rot_errors: Dict[int, List[float]],
        save_path: Path,
        model_name: str = "Unknown Model",
        dataset_name: str = "Unknown Dataset",
        all_step_joint_errors_scaled: Dict[int, List[float]] = None,
        all_step_pos_errors_scaled: Dict[int, List[float]] = None,
        all_step_rot_errors_scaled: Dict[int, List[float]] = None,
        plot_normalized_errors: bool = False,
    ):
        """
        绘制组件误差随时间变化的分析图

        Args:
            all_step_joint_errors: {step: [joint_mae_values_across_trajectories]}
            all_step_pos_errors: {step: [position_mae_values_across_trajectories]}
            all_step_rot_errors: {step: [rotation_distance_deg_values_across_trajectories]}
            save_path: 保存路径
            model_name: 模型名称
            dataset_name: 数据集名称
        """
        print(f"   📊 生成组件误差时间变化分析图...")

        # 根据是否显示归一化误差来决定布局
        if plot_normalized_errors and all_step_joint_errors_scaled is not None:
            # 2x3布局：上排显示物理域，下排显示归一化域
            fig, axes = plt.subplots(2, 3, figsize=(26, 16))  # 恢复并调大figsize
            fig.suptitle(
                f"Component Errors Over Time - {model_name}\n(Physical vs Normalized Domain Comparison)",
                fontsize=16,
                fontweight="bold",
                y=0.95,
            )

            # 添加说明文字
            fig.text(
                0.5,
                0.91,
                f"[PHYSICAL] Upper Row: Physical Domain (post-processed, unscaled to real units) | [NORMALIZED] Lower Row: Normalized Domain (raw model output vs training normalized GT)",
                ha="center",
                va="top",
                fontsize=12,
                style="italic",
                transform=fig.transFigure,
            )
        else:
            # 1x3布局：只显示物理域
            fig, axes = plt.subplots(1, 3, figsize=(26, 16))  # 恢复并调大figsize
            fig.suptitle(
                f"Component Errors Over Time - {model_name}\n(Aggregated Across Trajectories)",
                fontsize=16,
                fontweight="bold",
                y=0.92,
            )

            # 添加说明文字
            fig.text(
                0.5,
                0.85,
                f"Analysis of joint MAE (rad), position MAE (mm), and rotation distance (deg) across time steps (Object-Centric Frame)",
                ha="center",
                va="top",
                fontsize=12,
                style="italic",
                transform=fig.transFigure,
            )

        # 获取所有时间步（取交集，确保所有组件都有数据）
        joint_steps = set(all_step_joint_errors.keys())
        pos_steps = set(all_step_pos_errors.keys())
        rot_steps = set(all_step_rot_errors.keys())
        common_steps = sorted(joint_steps & pos_steps & rot_steps)

        if not common_steps:
            print("   ⚠️ 没有共同的时间步数据，无法生成组件误差分析")
            return

        print(
            f"   📊 分析时间步范围: {min(common_steps)} - {max(common_steps)} (共{len(common_steps)}步)"
        )

        if plot_normalized_errors and all_step_joint_errors_scaled is not None:
            # 2x3布局：上排显示物理域，下排显示归一化域

            # [PHYSICAL] 物理域图表 (上排)
            # 1. 关节误差随时间变化 (左上)
            self._plot_single_component_over_time(
                axes[0, 0],
                all_step_joint_errors,
                common_steps,
                "[PHYSICAL] Joint Errors Over Time\n(Post-processed & Unscaled to Real Units)",
                "Joint MAE (rad)",
                "lightgreen",
                "Joint (Physical)",
                "upper_right",
            )

            # 2. 位置误差随时间变化 (中上)
            self._plot_single_component_over_time(
                axes[0, 1],
                all_step_pos_errors,
                common_steps,
                "[PHYSICAL] Position Errors Over Time\n(Post-processed & Unscaled to Real Units)",
                "Position MAE (mm)",
                "skyblue",
                "Position (Physical)",
                "upper_right",
            )

            # 3. 旋转误差随时间变化 (右上)
            self._plot_single_component_over_time(
                axes[0, 2],
                all_step_rot_errors,
                common_steps,
                "[PHYSICAL] Rotation Errors Over Time\n(Post-processed & Unscaled to Real Units)",
                "Rotation Distance (deg)",
                "lightcoral",
                "Rotation (Physical)",
                "upper_right",
            )

            # [NORMALIZED] 归一化域图表 (下排)
            # 4. 关节误差随时间变化 (左下)
            self._plot_single_component_over_time(
                axes[1, 0],
                all_step_joint_errors_scaled,
                common_steps,
                "[NORMALIZED] Joint Errors Over Time\n(Raw Model Output vs Training Normalized GT)",
                "Joint MAE (normalized)",
                "darkgreen",
                "Joint (Normalized)",
                "upper_right",
            )

            # 5. 位置误差随时间变化 (中下)
            self._plot_single_component_over_time(
                axes[1, 1],
                all_step_pos_errors_scaled,
                common_steps,
                "[NORMALIZED] Position Errors Over Time\n(Raw Model Output vs Training Normalized GT)",
                "Position MAE (normalized)",
                "darkblue",
                "Position (Normalized)",
                "upper_right",
            )

            # 6. 旋转误差随时间变化 (右下)
            self._plot_single_component_over_time(
                axes[1, 2],
                all_step_rot_errors_scaled,
                common_steps,
                "[NORMALIZED] Rotation Errors Over Time\n(Raw Model Output vs Training Normalized GT)",
                "Rotation MAE (normalized)",
                "darkred",
                "Rotation (Normalized)",
                "upper_right",
            )

            plt.tight_layout(rect=[0, 0, 1, 0.88])  # 为2x3布局调整间距
        else:
            # 1x3布局：只显示物理域
            # 1. 关节误差随时间变化 (左图)
            self._plot_single_component_over_time(
                axes[0],
                all_step_joint_errors,
                common_steps,
                "Joint Errors Over Time",
                "Joint MAE (rad)",
                "lightgreen",
                "Joint",
                "upper_right",
            )

            # 2. 位置误差随时间变化 (中图)
            self._plot_single_component_over_time(
                axes[1],
                all_step_pos_errors,
                common_steps,
                "Position Errors Over Time",
                "Position MAE (mm)",
                "skyblue",
                "Position",
                "upper_right",
            )

            # 3. 旋转误差随时间变化 (右图)
            self._plot_single_component_over_time(
                axes[2],
                all_step_rot_errors,
                common_steps,
                "Rotation Errors Over Time",
                "Rotation Distance (deg)",
                "lightcoral",
                "Rotation",
                "upper_right",
            )

            plt.tight_layout(rect=[0, 0, 1, 0.78])  # 为1x3布局调整间距

        # 将模型和数据集信息添加到右上角
        self._add_model_dataset_info(
            fig, model_name, dataset_name, position="upper_right"
        )

        plt.savefig(
            save_path / "component_errors_over_time.png", dpi=300, bbox_inches="tight"
        )
        plt.close()

    def _plot_single_component_over_time(
        self,
        ax,
        step_errors_dict: Dict[int, List[float]],
        steps: List[int],
        title: str,
        ylabel: str,
        color: str,
        component_name: str,
        legend_position: str = "upper_left",
    ):
        """
        绘制单个组件的误差随时间变化

        Args:
            ax: matplotlib轴对象
            step_errors_dict: {step: [error_values_across_trajectories]}
            steps: 时间步列表
            title: 图表标题
            ylabel: Y轴标签
            color: 颜色
            component_name: 组件名称
        """
        # 计算每个时间步的统计量
        step_means = []
        step_stds = []
        step_medians = []
        step_q25 = []
        step_q75 = []

        for step in steps:
            values = step_errors_dict.get(step, [])
            if values:
                step_means.append(np.mean(values))
                step_stds.append(np.std(values))
                step_medians.append(np.median(values))
                step_q25.append(np.percentile(values, 25))
                step_q75.append(np.percentile(values, 75))
            else:
                # 如果某个时间步没有数据，用0填充
                step_means.append(0)
                step_stds.append(0)
                step_medians.append(0)
                step_q25.append(0)
                step_q75.append(0)

        # 绘制均值线
        ax.plot(
            steps,
            step_means,
            "o-",
            color=color,
            linewidth=2,
            markersize=4,
            label=f"Mean {component_name}",
            alpha=0.8,
        )

        # 绘制±1标准差区域
        ax.fill_between(
            steps,
            [m - s for m, s in zip(step_means, step_stds)],
            [m + s for m, s in zip(step_means, step_stds)],
            alpha=0.3,
            color=color,
            label="±1 Std",
        )

        # 绘制四分位数区域
        ax.fill_between(
            steps, step_q25, step_q75, alpha=0.2, color=color, label="IQR (25%-75%)"
        )

        # 绘制中位数线
        ax.plot(
            steps,
            step_medians,
            "--",
            color="darkred",
            linewidth=1,
            label="Median",
            alpha=0.7,
        )

        # 设置标题和标签
        ax.set_title(title, fontweight="bold", pad=20)  # 增加标题间距
        ax.set_xlabel("Time Step")
        ax.set_ylabel(ylabel)

        # 根据图例位置设置不同的位置
        if legend_position == "upper_right":
            ax.legend(fontsize=8, loc="upper right", bbox_to_anchor=(1.02, 1.02))
        elif legend_position == "upper_left":
            ax.legend(fontsize=8, loc="upper left", bbox_to_anchor=(0, 1.02))
        elif legend_position == "lower_right":
            ax.legend(fontsize=8, loc="lower right")
        elif legend_position == "lower_left":
            ax.legend(fontsize=8, loc="lower left")
        else:
            ax.legend(
                fontsize=8, loc="upper left", bbox_to_anchor=(0, 1.02)
            )  # 默认位置

        ax.grid(True, alpha=0.3)

        # 添加统计信息文本
        overall_mean = np.mean(step_means)
        overall_std = np.std(step_means)
        trend_info = (
            "Up"
            if step_means[-1] > step_means[0]
            else "Down" if step_means[-1] < step_means[0] else "Stable"
        )

        stats_text = f"{component_name}\nMean: {overall_mean:.4f}\nStd: {overall_std:.4f}\nTrend: {trend_info}"
        ax.text(
            0.02,
            0.98,
            stats_text,
            transform=ax.transAxes,
            fontsize=8,
            verticalalignment="top",
            bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
        )

    def _plot_rolling_prediction_analysis(
        self, prediction_info: Dict, save_path: Path, model_name: str, dataset_name: str
    ):
        """绘制Rolling Prediction策略分析图表"""
        rolling_data = prediction_info.get("rolling_prediction", {})
        strategy_comparison = prediction_info.get("strategy_comparison", {})

        if not rolling_data:
            return

        print("   📊 生成Rolling Prediction分析图表...")

        # 创建包含3个子图的图表
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))  # 恢复并调大figsize
        fig.suptitle(
            f"🔄 Rolling Prediction Strategy Analysis - {model_name}",
            fontsize=16,
            fontweight="bold",
        )

        # 1. Chunk误差分析
        self._plot_chunk_errors(axes[0, 0], rolling_data)

        # 2. 策略对比
        self._plot_strategy_comparison(axes[0, 1], strategy_comparison)

        # 3. 滚动预测误差时间序列
        self._plot_rolling_error_timeline(axes[1, 0], rolling_data)

        # 4. 性能改进可视化
        self._plot_performance_improvement(axes[1, 1], strategy_comparison)

        plt.tight_layout()
        self._add_model_dataset_info(fig, model_name, dataset_name)

        plt.savefig(
            save_path / "rolling_prediction_analysis.png", dpi=300, bbox_inches="tight"
        )
        plt.close()
        print("   ✅ Rolling Prediction分析图表已保存")

    def _plot_chunk_errors(self, ax, rolling_data: Dict):
        """绘制各分块的误差分析"""
        chunk_errors = rolling_data.get("chunk_errors", [])
        if not chunk_errors:
            ax.text(
                0.5,
                0.5,
                "No chunk error data",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("Chunk Error Analysis")
            return

        # 🆕 安全处理None值
        chunk_ids = []
        avg_errors = []
        for chunk in chunk_errors:
            if chunk and isinstance(chunk, dict):
                chunk_id = chunk.get("chunk_id")
                avg_error = chunk.get("avg_error")
                if chunk_id is not None and avg_error is not None:
                    chunk_ids.append(chunk_id + 1)
                    avg_errors.append(avg_error)

        if not chunk_ids or not avg_errors:
            ax.text(
                0.5,
                0.5,
                "No valid chunk error data",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("Chunk Error Analysis")
            return

        bars = ax.bar(
            chunk_ids, avg_errors, color="lightblue", alpha=0.7, edgecolor="navy"
        )
        ax.set_title("Average Error per Chunk")
        ax.set_xlabel("Chunk ID")
        ax.set_ylabel("Average Error")
        ax.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, error in zip(bars, avg_errors):
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2.0,
                height + height * 0.01,
                f"{error:.4f}",
                ha="center",
                va="bottom",
                fontsize=9,
            )

    def _plot_strategy_comparison(self, ax, strategy_comparison: Dict):
        """绘制策略对比图"""
        if not strategy_comparison:
            ax.text(
                0.5,
                0.5,
                "No strategy comparison data",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("Strategy Comparison")
            return

        # 🆕 安全处理None值
        standard_error = strategy_comparison.get("standard_avg_error")
        rolling_error = strategy_comparison.get("rolling_avg_error")

        if standard_error is None:
            standard_error = 0
        if rolling_error is None:
            rolling_error = 0

        strategies = ["Standard\nPrediction", "Rolling\nPrediction"]
        errors = [standard_error, rolling_error]
        colors = ["lightcoral", "lightgreen"]

        bars = ax.bar(strategies, errors, color=colors, alpha=0.7, edgecolor="black")
        ax.set_title("Prediction Strategy Comparison")
        ax.set_ylabel("Average Error")
        ax.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, error in zip(bars, errors):
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2.0,
                height + height * 0.01,
                f"{error:.4f}",
                ha="center",
                va="bottom",
                fontsize=10,
                fontweight="bold",
            )

        # 添加改进信息
        if standard_error > 0 and rolling_error > 0:
            improvement = strategy_comparison.get("error_reduction")
            if improvement is not None:
                ax.text(
                    0.5,
                    0.95,
                    f"Improvement: {improvement:.1f}%",
                    transform=ax.transAxes,
                    ha="center",
                    va="top",
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                    fontsize=10,
                    fontweight="bold",
                )

    def _plot_rolling_error_timeline(self, ax, rolling_data: Dict):
        """绘制滚动预测误差时间序列"""
        rolling_stats = rolling_data.get("rolling_stats", {})
        step_errors = rolling_stats.get("step_errors", [])

        if not step_errors:
            ax.text(
                0.5,
                0.5,
                "No timeline data",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("Rolling Prediction Error Timeline")
            return

        steps = list(range(len(step_errors)))
        ax.plot(steps, step_errors, "o-", color="blue", linewidth=2, markersize=4)
        ax.set_title("Rolling Prediction Error Over Time")
        ax.set_xlabel("Time Step")
        ax.set_ylabel("Prediction Error")
        ax.grid(True, alpha=0.3)

        # 添加统计信息
        avg_error = rolling_stats.get("avg_error", 0)
        ax.axhline(
            y=avg_error,
            color="red",
            linestyle="--",
            alpha=0.7,
            label=f"Average: {avg_error:.4f}",
        )
        ax.legend()

    def _plot_performance_improvement(self, ax, strategy_comparison: Dict):
        """绘制性能改进可视化"""
        if not strategy_comparison:
            ax.text(
                0.5,
                0.5,
                "No improvement data",
                ha="center",
                va="center",
                transform=ax.transAxes,
            )
            ax.set_title("Performance Improvement")
            return

        # 🆕 安全处理None值
        improvement_ratio = strategy_comparison.get("improvement_ratio")
        error_reduction = strategy_comparison.get("error_reduction")

        if improvement_ratio is None:
            improvement_ratio = 1.0
        if error_reduction is None:
            error_reduction = 0

        # 创建改进比率的可视化
        categories = ["Error Ratio", "Improvement %"]
        values = [improvement_ratio, error_reduction]
        colors = [
            "lightblue" if improvement_ratio < 1 else "lightcoral",
            "lightgreen" if error_reduction > 0 else "lightcoral",
        ]

        bars = ax.bar(categories, values, color=colors, alpha=0.7, edgecolor="black")
        ax.set_title("Performance Improvement Metrics")
        ax.set_ylabel("Value")
        ax.grid(True, alpha=0.3)

        # 添加参考线
        if "Error Ratio" in categories:
            ax.axhline(
                y=1.0,
                color="red",
                linestyle="--",
                alpha=0.5,
                label="No improvement line",
            )

        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2.0,
                height + abs(height) * 0.01,
                f"{value:.3f}",
                ha="center",
                va="bottom",
                fontsize=10,
                fontweight="bold",
            )

        ax.legend()

    def _plot_aggregated_rolling_comparison(
        self,
        all_predictions: List[Dict],
        save_path: Path,
        model_name: str = "Unknown Model",
        dataset_name: str = "Unknown Dataset",
    ):
        """绘制所有轨迹的Rolling Prediction汇总对比分析"""
        print("   📊 生成Rolling Prediction汇总对比分析...")

        # 收集所有轨迹的rolling prediction数据
        rolling_comparisons = []
        for pred in all_predictions:
            if "strategy_comparison" in pred and pred["strategy_comparison"]:
                rolling_comparisons.append(pred["strategy_comparison"])

        if not rolling_comparisons:
            print("   ⚠️ 没有找到strategy comparison数据")
            return

        num_trajectories = len(rolling_comparisons)
        # 🆕 检测样本数量并选择可视化模式
        use_statistical_mode = num_trajectories > 100
        if use_statistical_mode:
            print(
                f"   🎯 检测到大样本数量 ({num_trajectories} > 100)，启用统计聚合显示模式"
            )

        # 创建2x2布局的汇总对比图
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))  # 恢复并调大figsize
        fig.suptitle(
            f"🔄 Aggregated Rolling Prediction Analysis - {model_name}\n({num_trajectories} Trajectories with Rolling Prediction)"
            + (" - Statistical Mode" if use_statistical_mode else " - Detailed Mode"),
            fontsize=16,
            fontweight="bold",
        )

        # 收集所有对比数据
        standard_errors = []
        rolling_errors = []
        improvement_ratios = []
        error_reductions = []

        for comp in rolling_comparisons:
            if comp.get("standard_avg_error") is not None:
                standard_errors.append(comp["standard_avg_error"])
            if comp.get("rolling_avg_error") is not None:
                rolling_errors.append(comp["rolling_avg_error"])
            if comp.get("improvement_ratio") is not None:
                improvement_ratios.append(comp["improvement_ratio"])
            if comp.get("error_reduction") is not None:
                error_reductions.append(comp["error_reduction"])

        # 1. 误差对比箱线图
        if standard_errors and rolling_errors:
            error_data = [standard_errors, rolling_errors]
            box_plot = axes[0, 0].boxplot(
                error_data,
                tick_labels=["Standard\nPrediction", "Rolling\nPrediction"],
                patch_artist=True,
            )
            box_plot["boxes"][0].set_facecolor("lightcoral")
            box_plot["boxes"][1].set_facecolor("lightgreen")
            axes[0, 0].set_title("Prediction Error Comparison")
            axes[0, 0].set_ylabel("Average Error")
            axes[0, 0].grid(True, alpha=0.3)

            # 添加统计信息
            std_mean = np.mean(standard_errors)
            roll_mean = np.mean(rolling_errors)
            axes[0, 0].text(
                0.02,
                0.98,
                f"Standard Mean: {std_mean:.4f}\nRolling Mean: {roll_mean:.4f}",
                transform=axes[0, 0].transAxes,
                va="top",
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
            )

        # 2. 改进比率分布
        if improvement_ratios:
            axes[0, 1].hist(
                improvement_ratios,
                bins=10,
                alpha=0.7,
                color="skyblue",
                edgecolor="black",
            )
            axes[0, 1].axvline(
                x=1.0, color="red", linestyle="--", alpha=0.7, label="No improvement"
            )
            axes[0, 1].set_title("Improvement Ratio Distribution")
            axes[0, 1].set_xlabel("Improvement Ratio (Rolling/Standard)")
            axes[0, 1].set_ylabel("Number of Trajectories")
            axes[0, 1].grid(True, alpha=0.3)
            axes[0, 1].legend()

            # 添加统计信息
            mean_ratio = np.mean(improvement_ratios)
            better_count = sum(1 for r in improvement_ratios if r < 1.0)
            axes[0, 1].text(
                0.02,
                0.98,
                f"Mean Ratio: {mean_ratio:.3f}\nBetter: {better_count}/{len(improvement_ratios)}",
                transform=axes[0, 1].transAxes,
                va="top",
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
            )

        # 3. 误差减少百分比分布
        if error_reductions:
            axes[1, 0].hist(
                error_reductions,
                bins=10,
                alpha=0.7,
                color="lightgreen",
                edgecolor="black",
            )
            axes[1, 0].axvline(
                x=0, color="red", linestyle="--", alpha=0.7, label="No improvement"
            )
            axes[1, 0].set_title("Error Reduction Distribution")
            axes[1, 0].set_xlabel("Error Reduction (%)")
            axes[1, 0].set_ylabel("Number of Trajectories")
            axes[1, 0].grid(True, alpha=0.3)
            axes[1, 0].legend()

            # 添加统计信息
            mean_reduction = np.mean(error_reductions)
            positive_count = sum(1 for r in error_reductions if r > 0)
            axes[1, 0].text(
                0.02,
                0.98,
                f"Mean Reduction: {mean_reduction:.1f}%\nImproved: {positive_count}/{len(error_reductions)}",
                transform=axes[1, 0].transAxes,
                va="top",
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
            )

        # 4. 轨迹级别的改进对比 - 🆕 自适应可视化
        if (
            standard_errors
            and rolling_errors
            and len(standard_errors) == len(rolling_errors)
        ):
            if use_statistical_mode:
                # 大样本：统计分布对比
                # 计算改进差值
                improvements = [
                    (s - r) for s, r in zip(standard_errors, rolling_errors)
                ]

                # 绘制改进分布直方图
                n_bins = min(30, max(10, num_trajectories // 100))
                axes[1, 1].hist(
                    improvements,
                    bins=n_bins,
                    alpha=0.7,
                    color="lightgreen",
                    edgecolor="black",
                    linewidth=0.5,
                    label="Error Improvement",
                )

                # 添加统计线
                mean_improvement = np.mean(improvements)
                median_improvement = np.median(improvements)
                axes[1, 1].axvline(
                    0,
                    color="red",
                    linestyle="-",
                    linewidth=2,
                    alpha=0.8,
                    label="No Improvement",
                )
                axes[1, 1].axvline(
                    mean_improvement,
                    color="blue",
                    linestyle="--",
                    linewidth=2,
                    label=f"Mean: {mean_improvement:.4f}",
                )
                axes[1, 1].axvline(
                    median_improvement,
                    color="green",
                    linestyle=":",
                    linewidth=2,
                    label=f"Median: {median_improvement:.4f}",
                )

                # 计算改进统计
                improved_count = sum(1 for imp in improvements if imp > 0)
                improvement_rate = improved_count / len(improvements) * 100

                # 统计信息
                std_improvement = np.std(improvements)
                stats_text = f"Improved: {improved_count}/{len(improvements)} ({improvement_rate:.1f}%)\n"
                stats_text += f"Std: {std_improvement:.4f}\nRange: [{min(improvements):.4f}, {max(improvements):.4f}]"

                axes[1, 1].text(
                    0.98,
                    0.98,
                    stats_text,
                    transform=axes[1, 1].transAxes,
                    fontsize=9,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                    verticalalignment="top",
                    horizontalalignment="right",
                )

                axes[1, 1].set_title(
                    "Error Improvement Distribution\n(Rolling vs Standard)"
                )
                axes[1, 1].set_xlabel("Error Improvement (Standard - Rolling)")
                axes[1, 1].set_ylabel("Frequency")

            else:
                # 小样本：保持原有线图
                trajectory_indices = range(1, len(standard_errors) + 1)
                axes[1, 1].plot(
                    trajectory_indices,
                    standard_errors,
                    "o-",
                    label="Standard",
                    color="red",
                    alpha=0.7,
                )
                axes[1, 1].plot(
                    trajectory_indices,
                    rolling_errors,
                    "s-",
                    label="Rolling",
                    color="green",
                    alpha=0.7,
                )
                axes[1, 1].set_title("Trajectory-wise Error Comparison")
                axes[1, 1].set_xlabel("Trajectory Index")
                axes[1, 1].set_ylabel("Average Error")

                # 添加整体改进信息
                overall_improvement = (
                    (np.mean(standard_errors) - np.mean(rolling_errors))
                    / np.mean(standard_errors)
                    * 100
                )
                axes[1, 1].text(
                    0.02,
                    0.98,
                    f"Overall Improvement: {overall_improvement:.1f}%",
                    transform=axes[1, 1].transAxes,
                    va="top",
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.8),
                    fontweight="bold",
                )

            axes[1, 1].legend(fontsize=8)
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        self._add_model_dataset_info(fig, model_name, dataset_name)

        plt.savefig(
            save_path / "aggregated_rolling_comparison.png",
            dpi=300,
            bbox_inches="tight",
        )
        plt.close()

        # 🆕 用户提示信息
        if use_statistical_mode:
            print(
                f"   🎯 已使用统计聚合显示模式优化大样本可视化 ({num_trajectories} 个轨迹)"
            )
            print("      - 轨迹对比图 (右下): 从线图切换为改进分布直方图")
        else:
            print(f"   📈 使用标准详细显示模式 ({num_trajectories} 个轨迹)")

    def _plot_training_style_sequence_loss(
        self,
        all_predictions: List[Dict],
        save_path: Path,
        model_name: str = "Unknown Model",
        dataset_name: str = "Unknown Dataset",
    ):
        """制训练时风格的序列损失分析图"""
        if not all_predictions:
            error_msg = "❌ 没有预测数据进行训练时风格序列损失分析"
            print(f"   {error_msg}")
            raise ValueError(error_msg)

        # 收集训练时风格的序列损失数据
        training_losses = []
        trajectory_indices = []

        for i, pred in enumerate(all_predictions):
            if isinstance(pred, dict) and "training_style_sequence_loss" in pred:
                loss_value = pred["training_style_sequence_loss"]
                if loss_value is not None:
                    training_losses.append(loss_value)
                    trajectory_indices.append(i + 1)

        if not training_losses:
            print("   ⚠️ 没有找到训练时风格的序列损失数据")
            return

        num_trajectories = len(training_losses)
        print(f"📊 基于 {num_trajectories} 个轨迹生成训练时风格序列损失分析...")

        # 检测样本数量并选择可视化模式
        use_statistical_mode = num_trajectories > 50
        if use_statistical_mode:
            print(
                f"🎯 检测到大样本数量 ({num_trajectories} > 50)，启用统计聚合显示模式"
            )

        # 创建2x2布局的图表
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))  # 恢复并调大figsize

        # 设置自适应标题位置，避免覆盖
        fig.suptitle(
            f"Training-Style Sequence Loss Analysis - {model_name}\n({num_trajectories} Trajectories)",
            fontsize=16,
            fontweight="bold",
            y=0.95,  # 调整标题位置，避免覆盖
        )

        # 添加说明文字
        fig.text(
            0.5,
            0.91,
            "Analysis of training-style sequence L1 loss (Original Domain)",
            ha="center",
            va="top",
            fontsize=12,
            style="italic",
            transform=fig.transFigure,
        )

        # 1. 序列损失分布直方图
        if use_statistical_mode:
            # 大样本：使用统计分布
            n_bins = min(30, max(10, num_trajectories // 20))
            axes[0, 0].hist(
                training_losses,
                bins=n_bins,
                alpha=0.7,
                color="lightblue",
                edgecolor="black",
                linewidth=0.5,
            )

            # 添加统计线
            mean_loss = np.mean(training_losses)
            median_loss = np.median(training_losses)
            std_loss = np.std(training_losses)

            axes[0, 0].axvline(
                mean_loss,
                color="red",
                linestyle="--",
                linewidth=2,
                label=f"Mean: {mean_loss:.6f}",
            )
            axes[0, 0].axvline(
                median_loss,
                color="green",
                linestyle=":",
                linewidth=2,
                label=f"Median: {median_loss:.6f}",
            )

            # 添加统计信息文本框
            stats_text = f"Statistics:\n"
            stats_text += f"Mean: {mean_loss:.6f}\n"
            stats_text += f"Median: {median_loss:.6f}\n"
            stats_text += f"Std: {std_loss:.6f}\n"
            stats_text += f"Min: {min(training_losses):.6f}\n"
            stats_text += f"Max: {max(training_losses):.6f}\n"
            axes[0, 0].text(
                0.02,
                0.98,
                stats_text,
                transform=axes[0, 0].transAxes,
                va="top",
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                fontsize=9,
            )
        else:
            # 小样本：使用条形图
            bars = axes[0, 0].bar(
                trajectory_indices,
                training_losses,
                alpha=0.7,
                color="lightblue",
                edgecolor="black",
            )

            # 添加数值标签
            for bar, loss in zip(bars, training_losses):
                height = bar.get_height()
                axes[0, 0].text(
                    bar.get_x() + bar.get_width() / 2.0,
                    height + max(training_losses) * 0.01,
                    f"{loss:.4f}",
                    ha="center",
                    va="bottom",
                    fontsize=8,
                    rotation=45,
                )

        axes[0, 0].set_title("Training-Style Sequence Loss Distribution")
        axes[0, 0].set_xlabel(
            "Trajectory Index" if not use_statistical_mode else "Loss Value"
        )
        axes[0, 0].set_ylabel(
            "Number of Trajectories" if use_statistical_mode else "Sequence L1 Loss"
        )
        axes[0, 0].grid(True, alpha=0.3)
        if use_statistical_mode:
            axes[0, 0].legend(fontsize=8)

        # 2损失箱线图
        axes[0, 1].boxplot(
            training_losses,
            patch_artist=True,
            boxprops=dict(facecolor="lightgreen", alpha=0.7),
            medianprops=dict(color="red", linewidth=2),
        )
        axes[0, 1].set_title("Training-Style Sequence Loss Box Plot")
        axes[0, 1].set_ylabel("Sequence L1 Loss")
        axes[0, 1].grid(True, alpha=0.3)

        # 添加统计信息
        q1, q3 = np.percentile(training_losses, [25, 75])
        iqr = q3 - q1
        axes[0, 1].text(
            0.2,
            0.98,
            f"Q1: {q1:.6f}\nQ3: {q3:.6f}\nIQR: {iqr:.6f}",
            transform=axes[0, 1].transAxes,
            va="top",
            bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
            fontsize=9,
        )

        # 3. 序列损失与轨迹长度的关系（如果有数据）
        sequence_lengths = []
        for pred in all_predictions:
            if isinstance(pred, dict) and "step_errors" in pred:
                seq_len = len(pred["step_errors"])
                sequence_lengths.append(seq_len)

        if len(sequence_lengths) == len(training_losses):
            axes[1, 0].scatter(
                sequence_lengths,
                training_losses,
                alpha=0.6,
                color="orange",
                s=30,
            )
            axes[1, 0].set_title("Sequence Loss vs Sequence Length")
            axes[1, 0].set_xlabel("Sequence Length")
            axes[1, 0].set_ylabel("Training-Style Sequence L1 Loss")
            axes[1, 0].grid(True, alpha=0.3)
            # 添加趋势线
            if len(sequence_lengths) > 1:
                z = np.polyfit(sequence_lengths, training_losses, 1)
                p = np.poly1d(z)
                axes[1, 0].plot(
                    sequence_lengths,
                    p(sequence_lengths),
                    "r--",
                    alpha=0.8,
                    label=f"Trend: y={z[0]:.6}x + {z[1]:.6}",
                )
                axes[1, 0].legend(fontsize=8)
        else:
            axes[1, 0].text(
                0.5,
                0.5,
                "Sequence Length Data\nNot Available",
                ha="center",
                va="center",
                transform=axes[1, 0].transAxes,
                fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"),
            )
            axes[1, 0].set_title("Sequence Loss vs Sequence Length")
            axes[1, 0].axis("off")

        # 4统计汇总
        stats_text = f"📊 Training-Style Loss Summary\n\n"
        stats_text += f"Total Trajectories: {num_trajectories}\n"
        stats_text += f"Loss Domain: Original Domain\n"
        stats_text += f"Mean Loss: {np.mean(training_losses):.6f}\n"
        stats_text += f"Median Loss: {np.median(training_losses):.6f}\n"
        stats_text += f"Std Loss: {np.std(training_losses):.6f}\n"
        stats_text += f"Min Loss: {min(training_losses):.6f}\n"
        stats_text += f"Max Loss: {max(training_losses):.6f}\n"
        # 添加分位数信息
        percentiles = [10, 25, 50, 75, 90]
        for p in percentiles:
            value = np.percentile(training_losses, p)
            stats_text += f"{p}th Percentile: {value:.6f}\n"
        # 添加异常值检测
        q1, q3 = np.percentile(training_losses, [25, 75])
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        outliers = [x for x in training_losses if x < lower_bound or x > upper_bound]
        stats_text += f"\nOutliers (IQR method): {len(outliers)}/{num_trajectories}\n"

        axes[1, 1].text(
            0.5,
            0.95,
            stats_text,
            fontsize=9,
            verticalalignment="top",
            transform=axes[1, 1].transAxes,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8),
        )
        axes[1, 1].set_title("Performance Summary")
        axes[1, 1].axis("off")

        # 使用自适应布局，避免标题覆盖
        plt.tight_layout(rect=[0, 0, 1, 0.90])

        # 保存图表
        plt.savefig(
            save_path / "training_style_sequence_loss.png",
            dpi=300,
            bbox_inches="tight",
        )
        plt.close()

        print(
            f"   ✅ 训练时风格序列损失分析图已保存: {save_path / 'training_style_sequence_loss.png'}"
        )

        # 用户提示信息
        if use_statistical_mode:
            print(
                f"🎯 已使用统计聚合显示模式优化大样本可视化 ({num_trajectories} 个轨迹)"
            )
        else:
            print(f"   📈 使用标准详细显示模式 ({num_trajectories} 个轨迹)")

        print(
            f"   📊 分析完成: 基于 {len(training_losses)} 个有效轨迹的训练时风格序列损失数据"
        )
