"""
轨迹数据分析评估模块

这个包提供了完整的轨迹数据分析功能，包括：
- 模型评估和分析
- 数据加载和处理
- 可视化生成
- 命令行接口

主要组件：
- core: 核心分析器和数据加载器
- utils: 工具函数和辅助类
- data: 数据迭代器和处理
- visualization: 可视化和图表生成
- cli: 命令行接口

"""

from .core.analyzers import (
    TrajectoryAnalyzer,
    MemoryOptimizedTrajectoryAnalyzer,
    StreamingTrajectoryAnalyzer,
)
from .utils.file_utils import (
    extract_model_name_from_path,
    create_model_info_file,
    check_existing_evaluation,
    cleanup_temporary_files,
)

__version__ = "2.0.0"
__author__ = "AI Assistant"

__all__ = [
    "TrajectoryAnalyzer",
    "MemoryOptimizedTrajectoryAnalyzer",
    "StreamingTrajectoryAnalyzer",
    "extract_model_name_from_path",
    "create_model_info_file",
    "check_existing_evaluation",
    "cleanup_temporary_files",
]
