#!/usr/bin/env python3
"""
单手分析器模块化功能测试脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有模块的导入"""
    print("🧪 测试模块导入...")
    
    try:
        # 测试工具类导入
        from eval.utils.colors import Colors
        print("✅ Colors工具类导入成功")
        
        from eval.utils.json_encoder import NumpyEncoder
        print("✅ NumpyEncoder工具类导入成功")
        
        from eval.utils.schedule_utils import ConstantSchedulePicklable
        print("✅ ConstantSchedulePicklable工具类导入成功")
        
        # 测试数据模块导入
        from eval.data.dataset_sampler import DatasetSampler
        print("✅ DatasetSampler数据模块导入成功")
        
        # 测试轨迹加载函数导入
        from eval.data.trajectory_loader import extract_trajectory_poses
        print("✅ extract_trajectory_poses函数导入成功")
        
        # 测试IsaacSim初始化器导入
        from eval.core.isaac_initializer import IsaacInitializer
        print("✅ IsaacInitializer导入成功")
        
        # 测试单手分析器导入
        from eval.core.single_hand_analyzer import SingleHandAnalyzer
        print("✅ SingleHandAnalyzer导入成功")
        
        print("🎉 所有模块导入测试通过!")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analyzer_initialization():
    """测试分析器初始化"""
    print("\n🧪 测试分析器初始化...")
    
    try:
        from eval.core.single_hand_analyzer import SingleHandAnalyzer
        
        # 创建测试配置
        test_config = {
            "args": {
            "model_path": "/path/to/test/model.pth",
            "dataset_path": "/path/to/test/dataset.h5",
            "hand_type": "right",
            "episodes": 2,
            "num_envs": 1,
            "debug": True,
            "detailed": True,
            "save_trajectories": False,
            "use_object_centric": False,
            "enable_action_scaling": True,
            "enable_separated_normalization": False,
            "action_selection_strategy": "first",
            "position_tolerance": 0.05,
            "joint_tolerance": 0.1,
            "enable_debug_table": False,
            "debug_table_interval": 0,
            "chunked_execution_enabled": False,
            "output_dir": "./test_output"
            }
        }
        
        # 初始化分析器（不实际运行，只测试初始化）
        analyzer = SingleHandAnalyzer(test_config)
        print("✅ 分析器初始化成功")
        
        # 测试配置验证
        print(f"🔍 调试信息:")
        print(f"   analyzer.model_path: '{analyzer.model_path}'")
        print(f"   test_config['args']['model_path']: '{test_config['args']['model_path']}'")
        print(f"   analyzer.evaluation_results['hand_type']: '{analyzer.evaluation_results['hand_type']}'")
        print(f"   test_config['args']['hand_type']: '{test_config['args']['hand_type']}'")
        
        assert analyzer.model_path == test_config["args"]["model_path"]
        assert analyzer.evaluation_results["hand_type"] == test_config["args"]["hand_type"]
        assert analyzer.evaluation_results["episodes"] == test_config["args"]["episodes"]
        print("✅ 配置验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_utility_functions():
    """测试工具函数"""
    print("\n🧪 测试工具函数...")
    
    try:
        from eval.utils.colors import Colors
        
        # 测试颜色输出
        print(f"{Colors.GREEN}绿色文本{Colors.RESET}")
        print(f"{Colors.RED}红色文本{Colors.RESET}")
        print(f"{Colors.BLUE}蓝色文本{Colors.RESET}")
        print("✅ 颜色工具函数测试通过")
        
        from eval.utils.json_encoder import NumpyEncoder
        import json
        import numpy as np
        
        # 测试JSON编码器
        test_data = {
            "array": np.array([1, 2, 3]),
            "float": np.float32(3.14),
            "int": np.int64(42),
            "string": "test"
        }
        
        json_str = json.dumps(test_data, cls=NumpyEncoder)
        print("✅ JSON编码器测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始单手分析器模块化功能测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("模块导入测试", test_imports),
        ("分析器初始化测试", test_analyzer_initialization),
        ("工具函数测试", test_utility_functions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 模块化功能正常")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关模块")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 