"""
训练脚本的CLI主入口
处理命令行参数、AppLauncher初始化和Hydra配置
整合模块化的训练组件
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from omegaconf import DictConfig
import hydra
import torch
from stable_baselines3.common.utils import get_device

# 早期导入AppLauncher（必须在其他Isaac Lab导入之前）
from isaaclab.app import AppLauncher

# 全局变量
simulation_app = None
logger = logging.getLogger(__name__)


def setup_app_launcher():
    """设置AppLauncher和仿真应用"""
    global simulation_app

    # 解析AppLauncher参数
    parser = argparse.ArgumentParser(description="Isaac Sim Training Script")
    AppLauncher.add_app_launcher_args(parser)
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument(
        "--video_early", action="store_true", help="Enable cameras early"
    )

    args, remaining = parser.parse_known_args()

    # 处理视频录制的相机启用
    if hasattr(args, "video_early") and args.video_early:
        if hasattr(args, "enable_cameras"):
            args.enable_cameras = True
        else:
            setattr(args, "enable_cameras", True)

    # 初始化AppLauncher
    app_launcher = AppLauncher(args)
    simulation_app = app_launcher.app

    # 重置sys.argv供Hydra使用
    sys.argv = [sys.argv[0]] + remaining

    return args


def should_initialize_environment(cfg: DictConfig) -> bool:
    """判断当前训练阶段是否需要初始化环境"""
    training_phase = cfg.training.training_phase

    # 从配置中获取环境需求映射
    env_requirements = cfg.training_phases.environment_requirements

    # 检查当前训练阶段是否需要环境
    requires_env = env_requirements.get(training_phase, True)  # 默认需要环境

    logger.info(
        f"训练阶段 '{training_phase}' {'需要' if requires_env else '不需要'}环境初始化"
    )

    return requires_env


def setup_app_launcher_conditional(cfg: DictConfig):
    """根据训练阶段条件性设置AppLauncher"""
    global simulation_app

    if should_initialize_environment(cfg):
        logger.info("🚀 初始化IsaacSim环境...")

        # 获取预先解析的AppLauncher参数
        args = getattr(cli_main, "_app_args", None)
        if args is None:
            raise RuntimeError("AppLauncher参数未正确预解析")

        # 处理视频录制的相机启用
        if hasattr(args, "video_early") and args.video_early:
            if hasattr(args, "enable_cameras"):
                args.enable_cameras = True
            else:
                setattr(args, "enable_cameras", True)

        # 初始化AppLauncher
        app_launcher = AppLauncher(args)
        simulation_app = app_launcher.app

        return args
    else:
        logger.info("⚡ 跳过IsaacSim环境初始化 - 当前为离线训练阶段")
        simulation_app = None
        return None


def setup_logging(debug_mode: bool = False):
    """设置日志配置"""
    from px_janus_learnsim.utils.logger import (
        setup_logger,
        smart_disable_duplicate_logging,
        check_colorlog_status,
    )

    print("🔧 正在初始化日志配置...")
    check_colorlog_status()
    smart_disable_duplicate_logging()
    print("✅ 日志配置完成")

    # 设置debug级别
    if debug_mode:
        logging.getLogger("tora_learning").setLevel(logging.DEBUG)
        logging.getLogger(
            "px_janus_learnsim.learning.trainers.conditional_bc"
        ).setLevel(logging.DEBUG)
        logging.getLogger("action_scaling_utils").setLevel(logging.DEBUG)
        logger.info("🔍 Debug模式已启用 - 将显示详细的训练信息")
    else:
        logger.info("ℹ️  使用INFO日志级别 - 如需详细信息请使用 --debug 参数")


def setup_omegaconf_resolvers():
    """设置OmegaConf自定义resolver"""
    from omegaconf import OmegaConf
    from px_janus_learnsim.utils.name_generator import generate_human_readable_run_name

    # 注册运行名称生成器
    OmegaConf.register_new_resolver(
        "generate_run_name", generate_human_readable_run_name
    )

    # 注册模型类型resolver
    def get_model_type(training_phase: str) -> str:
        """根据训练阶段返回对应的模型类型，用于目录组织"""
        model_type_mapping = {
            # Execution Layer Training
            "execution_bc_actor_train": "execution_bc_actor",
            "execution_bc_critic_train": "execution_bc_critic",
            "execution_joint_rl": "execution_rl_actor_critic",
            # Decision Layer Training
            "decision_bc_train": "decision_bc_scoring",
            "decision_rl_train": "decision_rl_policy",
            # Reward Model Training
            "reward_model_pretrain": "reward_model",
            # Full System Training
            "joint_rl": "hierarchical_rl_full_system",
            # Legacy/Backward Compatibility
            "execution_bc_train": "execution_bc_actor",  # 向后兼容
        }
        return model_type_mapping.get(training_phase, "unknown_model_type")

    OmegaConf.register_new_resolver("get_model_type", get_model_type)

    logger.info("✅ OmegaConf自定义resolver注册完成")


def register_environments():
    """注册所有环境"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(current_dir))

    if project_root not in sys.path:
        sys.path.append(project_root)

    from env.env_registry import register_all_environments

    register_all_environments()
    logger.info("环境注册完成")


def setup_swanlab(config_manager) -> bool:
    """设置SwanLab日志"""
    if not config_manager.is_swanlab_enabled():
        return False

    try:
        import swanlab

        swanlab_config = config_manager.get_swanlab_config()

        swanlab.init(
            project=swanlab_config["project"],
            workspace=swanlab_config["workspace"],
            entity=swanlab_config["entity"],
            experiment_name=swanlab_config["experiment_name"],
            description=swanlab_config["description"],
            config=config_manager.resolved_cfg,
            logdir=str(Path(config_manager.output_dir) / "swanlog"),
            mode="cloud",
        )

        logger.info(f"SwanLab初始化成功: {swanlab_config['experiment_name']}")
        return True

    except Exception as e:
        logger.error(f"SwanLab初始化失败: {e}", exc_info=True)
        return False


def _execute_bc_training(
    cfg, env, data_loader, output_dir, device, swanlab_initialized
):
    """执行BC训练 - 支持自适应学习率（简化版）

    Args:
        env: 环境对象，对于BC训练可以为None（离线训练）
    """
    from ..core.trainers import ExecutionBCTrainer
    from ..utils.helpers import ConstantSchedulePicklable, save_complete_training_config
    from ..utils.adaptive_lr import (
        create_adaptive_optimizer_and_scheduler,
        validate_adaptive_config,
        LearningRateMonitor,
    )
    from ..integrations.swanlab_integration import SwanlabBCLogger
    import numpy as np
    import json
    from pathlib import Path
    from ..utils.schedules import create_schedule

    # 解析是否强制重算统计
    cfg_exec_policy_kwargs = cfg.agent.execution_policy.get("policy_kwargs", {})
    force_regenerate_stats_flag = cfg_exec_policy_kwargs.get(
        "force_regenerate_stats", False
    )

    # 加载数据
    train_dataloader, val_dataloader, stats, is_sim = data_loader.load_dataloaders(
        batch_size_train=cfg.training.get("batch_size_load", cfg.training.batch_size),
        batch_size_val=max(1, cfg.training.batch_size // 2),
        # 透传强制重算统计标志
        force_regenerate_stats=force_regenerate_stats_flag,
    )

    # 创建策略
    from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
        ExecutionBCActorPolicy,
    )
    from ..utils.helpers import merge_policy_kwargs

    exec_policy_kwargs = merge_policy_kwargs(
        cfg.agent.get("policy_kwargs", {}),
        cfg.agent.execution_policy.get("policy_kwargs", {}),
    )
    exec_policy_kwargs.setdefault("num_queries", cfg.env.seq_length)
    # Set tactile_shape parameter with correct default value matching SharedBaseEncoders
    exec_policy_kwargs.setdefault("tactile_shape", (16, 1))

    # 🔍 智能查找统计文件 - 实现fallback策略
    scaling_stats_path = exec_policy_kwargs.get("scaling_stats_path", None)
    if not scaling_stats_path:
        logger.info("🔍 未指定scaling_stats_path，启动智能查找...")

        # 使用改进的查找函数
        from px_janus_learnsim.utils.action_scaling_utils import (
            auto_find_separated_scaling_stats,
        )

        # 构造一个临时的模型路径用于查找
        temp_model_path = os.path.join(output_dir, "execution_bc_policy.pth")
        dataset_dir = cfg.training.get("dataset_dir", None)

        found_stats_path = auto_find_separated_scaling_stats(
            model_path=temp_model_path, dataset_dir=dataset_dir
        )

        if found_stats_path:
            logger.info(f"✅ 智能查找成功，使用统计文件: {found_stats_path}")
            exec_policy_kwargs["scaling_stats_path"] = found_stats_path
        else:
            logger.warning("⚠️ 智能查找失败，将使用默认统计处理")
    else:
        logger.info(f"📋 使用配置指定的统计文件: {scaling_stats_path}")

    # 检查单手模式并调整动作空间 - 遵照原始脚本
    enable_single_hand = exec_policy_kwargs.get("enable_single_hand", False)
    single_hand_config = exec_policy_kwargs.get("single_hand_config", {})

    # 处理env为None的情况（BC训练时）
    if env is None:
        logger.info(
            "🔧 环境为None（BC训练模式），从配置构造action_space和observation_space"
        )
        import gymnasium as gym
        import numpy as np

        # 根据配置计算action维度
        if enable_single_hand:
            action_dim = 25  # 16关节 + 9位姿
        else:
            action_dim = 50  # 双手

        env_action_space = gym.spaces.Box(
            low=-1.0, high=1.0, shape=(action_dim,), dtype=np.float32
        )

        # 创建一个简单的observation_space（从配置推断）
        # 这里使用一个合理的默认值，实际会从数据加载器推断
        env_observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(512,), dtype=np.float32
        )

        logger.info(f"构造的动作空间: {env_action_space.shape}")
        logger.info(f"构造的观察空间: {env_observation_space.shape}")

    elif enable_single_hand and cfg.env.name != "Isaac-SingleHand-DexH13-Direct-v0":
        logger.info("🔧 单手训练模式已启用，修改环境动作空间从50维到25维")
        import gymnasium as gym

        env_action_space = gym.spaces.Box(
            low=-1.0, high=1.0, shape=(25,), dtype=np.float32
        )
        env_observation_space = env.observation_space
        logger.info(
            f"动作空间已修改: {env.action_space.shape} -> {env_action_space.shape}"
        )
    else:
        env_action_space = env.action_space
        env_observation_space = env.observation_space

    # 构建学习率调度器（支持可选 Warm-up）
    exec_policy_cfg = cfg.agent.execution_policy
    if "lr_schedule" in exec_policy_cfg:
        sch_cfg = exec_policy_cfg.lr_schedule
        # OmegaConf -> dict 迭代
        lr_schedule = create_schedule(
            schedule_type=sch_cfg.get("type"),
            **{k: v for k, v in sch_cfg.items() if k != "type"},
        )
    else:
        lr_schedule = ConstantSchedulePicklable(
            exec_policy_cfg.get("learning_rate", cfg.agent.learning_rate)
        )

    policy = ExecutionBCActorPolicy(
        observation_space=env_observation_space,
        action_space=env_action_space,
        lr_schedule=lr_schedule,
        policy_kwargs=exec_policy_kwargs,
    ).to(device)

    # 🆕 创建自适应优化器和调度器（简化版）
    adaptive_config = exec_policy_kwargs.get("adaptive_lr_config", {})
    use_adaptive_lr = (
        adaptive_config.get("enable_validation", False)
        and adaptive_config.get("scheduler_type", "none") != "none"
    )

    scheduler = None
    scheduler_type = "none"
    lr_monitor = None

    if use_adaptive_lr and validate_adaptive_config(adaptive_config):
        logger.info("🔧 启用自适应学习率训练")
        optimizer, scheduler, scheduler_type = create_adaptive_optimizer_and_scheduler(
            policy, cfg
        )
        policy.optimizer = optimizer

        # 创建学习率监控器
        initial_lr = optimizer.param_groups[0]["lr"]
        lr_monitor = LearningRateMonitor(initial_lr)

        logger.info(f"✅ 自适应学习率配置完成:")
        logger.info(f"   - 调度器类型: {scheduler_type}")
        logger.info(f"   - 初始学习率: {initial_lr:.2e}")
    else:
        # 回退到原始优化器创建逻辑
        weight_decay = single_hand_config.get("weight_decay", 0.0)
        if weight_decay > 0:
            logger.info(f"使用权重衰减: {weight_decay}")
            policy.optimizer = torch.optim.AdamW(
                policy.parameters(),
                lr=cfg.agent.execution_policy.get(
                    "learning_rate", cfg.agent.learning_rate
                ),
                weight_decay=weight_decay,
            )
            logger.info("优化器已更新为AdamW with权重衰减")

    # 创建自定义logger
    custom_logger = SwanlabBCLogger() if swanlab_initialized else None

    # 🆕 创建统一的训练器（支持自适应学习率）
    trainer = ExecutionBCTrainer(
        policy=policy,
        dataloader=train_dataloader,
        optimizer=policy.optimizer,
        device=device,
        config=cfg,  # 🔧 添加配置参数以支持checkpoint
        output_dir=output_dir,  # 🔧 添加output_dir参数
        custom_logger=custom_logger,
        enable_conditional=exec_policy_kwargs.get("conditional_pose_dim", 0) > 0,
        enable_single_hand=enable_single_hand,
        single_hand_config={
            **single_hand_config,
            "use_object_centric": exec_policy_kwargs.get("use_object_centric", False),
            "enable_pose_randomization": exec_policy_kwargs.get(
                "enable_pose_randomization", False
            ),
            "randomization_range": exec_policy_kwargs.get("randomization_range", 0.5),
        },
        enable_action_scaling=exec_policy_kwargs.get("enable_action_scaling", True),
        obs_normalization=exec_policy_kwargs.get("obs_normalization", "standard"),
        action_scaling=exec_policy_kwargs.get("action_scaling", "minmax"),
        action_clip_range=tuple(
            exec_policy_kwargs.get("action_clip_range", [-1.0, 1.0])
        ),
        enable_separated_normalization=exec_policy_kwargs.get(
            "enable_separated_normalization", True
        ),
        target_pose_normalization=exec_policy_kwargs.get(
            "target_pose_normalization", "standard"
        ),
        model_save_path=os.path.join(output_dir, "execution_bc_policy.pth"),
        # 🔧 传递已找到的统计文件路径，避免重复查找
        scaling_stats_path=exec_policy_kwargs.get("scaling_stats_path"),
        force_regenerate_stats=exec_policy_kwargs.get("force_regenerate_stats", False),
        debug=False,
    )

    # 🆕 定义自适应学习率回调
    def on_epoch_end_callback():
        # 原始回调逻辑
        if custom_logger:
            custom_logger.log_epoch_summary()

        # 🆕 自适应学习率逻辑
        if use_adaptive_lr and scheduler is not None:
            current_lr = policy.optimizer.param_groups[0]["lr"]

            # 对于非plateau类型的调度器，直接step
            if scheduler_type in ["cosine", "step"]:
                scheduler.step()
                new_lr = policy.optimizer.param_groups[0]["lr"]
                if abs(new_lr - current_lr) / current_lr > 0.01:  # 变化超过1%
                    logger.info(f"🔄 学习率调整: {current_lr:.2e} → {new_lr:.2e}")

            # 记录学习率历史（如果有监控器）
            if lr_monitor is not None:
                # 简化处理：记录基本信息
                epoch_count = getattr(on_epoch_end_callback, "epoch_count", 0)
                lr_monitor.record(
                    epoch=epoch_count,
                    current_lr=current_lr,
                    train_loss=0.0,  # 简化：不计算训练损失
                    val_loss=None,
                )

                # 更新epoch计数
                on_epoch_end_callback.epoch_count = epoch_count + 1

    # 执行训练
    logger.info(f"🚀 开始训练，总轮数: {cfg.training.execution_epochs}")
    if use_adaptive_lr:
        logger.info(f"   - 自适应学习率: 启用 ({scheduler_type})")

    training_stats = trainer.train(
        n_epochs=cfg.training.execution_epochs,
        log_interval=cfg.logging.get("bc_log_interval", 500),
        on_epoch_end=on_epoch_end_callback,
    )

    # 🆕 保存自适应学习率训练历史（如果启用）
    if use_adaptive_lr and lr_monitor is not None:
        lr_history_path = Path(output_dir) / "learning_rate_history.json"
        lr_monitor.save_history(str(lr_history_path))

        # 保存训练摘要
        summary = {
            "training_completed": True,
            "total_epochs": cfg.training.execution_epochs,
            "scheduler_type": scheduler_type,
            "initial_lr": lr_monitor.initial_lr,
            "final_lr": policy.optimizer.param_groups[0]["lr"],
            "lr_adjustments": lr_monitor.adjustments_count,
            "adaptive_lr_enabled": True,
        }

        summary_path = Path(output_dir) / "adaptive_lr_summary.json"
        with open(summary_path, "w") as f:
            json.dump(summary, f, indent=2)

        logger.info(f"📊 自适应学习率历史已保存到: {lr_history_path}")
        logger.info(f"📊 训练摘要已保存到: {summary_path}")

    # 🔧 保存完整配置 - 遵照原始脚本实现
    try:
        # 获取环境配置（如果可用）
        env_cfg = getattr(env, "_cfg", None) if hasattr(env, "_cfg") else None

        save_complete_training_config(
            output_dir=output_dir,
            cfg=cfg,
            env_cfg=env_cfg,
            source="bc_training_script",
            # 🆕 添加更多关键参数 - 遵照原始脚本
            enable_single_hand=enable_single_hand,
            conditional_pose_dim=exec_policy_kwargs.get("conditional_pose_dim", 0),
            execution_epochs=cfg.training.execution_epochs,
            use_object_centric=exec_policy_kwargs.get("use_object_centric", False),
            enable_pose_randomization=exec_policy_kwargs.get(
                "enable_pose_randomization", False
            ),
        )
    except Exception as config_save_error:
        logger.warning(f"保存BC配置信息失败: {config_save_error}")

    return training_stats


def _execute_decision_bc_training(cfg, env, data_loader, output_dir, device):
    """执行决策BC训练 - 遵照原始脚本实现"""
    from ..core.trainers import DecisionBCTrainer
    from ..utils.helpers import (
        ConstantSchedulePicklable,
        merge_policy_kwargs,
        save_complete_training_config,
    )

    # 加载数据
    train_dataloader, val_dataloader, stats, is_sim = data_loader.load_dataloaders(
        batch_size_train=cfg.training.get("batch_size_load", cfg.training.batch_size),
        batch_size_val=max(1, cfg.training.batch_size // 2),
    )

    # 创建决策策略
    from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
        DecisionBCScoringPolicy,
    )

    decision_policy_kwargs = merge_policy_kwargs(
        cfg.agent.get("policy_kwargs", {}),
        cfg.agent.decision_policy.get("policy_kwargs", {}),
    )
    decision_policy_kwargs.setdefault("num_queries", cfg.env.seq_length)
    # Set tactile_shape parameter with correct default value matching SharedBaseEncoders
    decision_policy_kwargs.setdefault("tactile_shape", (16, 1))

    # 处理env为None的情况（BC训练时）
    if env is None:
        logger.info("🔧 环境为None（决策BC训练模式），从配置构造spaces")
        import gymnasium as gym
        import numpy as np

        # 创建简单的spaces用于决策BC训练
        env_action_space = gym.spaces.Box(
            low=-1.0, high=1.0, shape=(50,), dtype=np.float32  # 决策层通常是双手
        )
        env_observation_space = gym.spaces.Box(
            low=-np.inf, high=np.inf, shape=(512,), dtype=np.float32
        )
    else:
        env_action_space = env.action_space
        env_observation_space = env.observation_space

    policy = DecisionBCScoringPolicy(
        observation_space=env_observation_space,
        action_space=env_action_space,
        lr_schedule=ConstantSchedulePicklable(
            cfg.agent.decision_policy.get("learning_rate", cfg.agent.learning_rate)
        ),
        **decision_policy_kwargs,
    ).to(device)

    # 🔧 创建训练器 - 添加output_dir参数
    trainer = DecisionBCTrainer(
        policy=policy,
        dataloader=train_dataloader,
        optimizer=policy.optimizer,
        device=device,
        num_candidates=cfg.agent.decision_policy.get("num_candidates", 10),
        seq_length=cfg.env.seq_length,
        target_hand=cfg.env.get("target_hand", "left"),
        output_dir=output_dir,  # 🔧 添加output_dir参数
    )

    # 执行训练
    training_stats = trainer.train(
        n_epochs=cfg.training.decision_epochs,
        log_interval=cfg.logging.get("bc_log_interval", 500),
    )

    # 🔧 保存完整配置 - 遵照原始脚本
    try:
        # 获取环境配置（如果可用）
        env_cfg = getattr(env, "_cfg", None) if hasattr(env, "_cfg") else None

        save_complete_training_config(
            output_dir=output_dir,
            cfg=cfg,
            env_cfg=env_cfg,
            source="decision_bc_training_script",
            # 添加决策BC特定的元数据
            num_candidates=cfg.agent.decision_policy.get("num_candidates", 10),
            decision_epochs=cfg.training.decision_epochs,
            target_hand=cfg.env.get("target_hand", "left"),
        )
    except Exception as config_save_error:
        logger.warning(f"保存决策BC配置信息失败: {config_save_error}")

    return training_stats


def _execute_reward_model_training(cfg, data_loader, output_dir, device):
    """执行奖励模型训练"""
    from ..core.trainers import RewardModelTrainer

    # 加载数据
    train_dataloader, val_dataloader, stats, is_sim = data_loader.load_dataloaders(
        batch_size_train=cfg.agent.reward_model.batch_size,
        batch_size_val=max(1, cfg.agent.reward_model.batch_size // 2),
    )

    # 创建训练器
    trainer = RewardModelTrainer(
        dataloader=train_dataloader,
        device=device,
        model_kwargs=cfg.agent.reward_model.get("model_kwargs", {}),
        seq_length=cfg.env.seq_length,
        target_hand=cfg.env.get("target_hand", "left"),
        batch_size=cfg.agent.reward_model.batch_size,
        learning_rate=cfg.agent.reward_model.model_kwargs.get("learning_rate", 0.001),
        output_dir=output_dir,
    )

    # 执行训练
    return trainer.train(n_epochs=cfg.agent.reward_model.epochs)


def _execute_rl_training(cfg, env, eval_env, output_dir, device):
    """执行RL训练"""
    from ..core.trainers import HierarchicalRLTrainer

    # 创建训练器
    trainer = HierarchicalRLTrainer(
        env=env,
        eval_env=eval_env,
        cfg=cfg,
        output_dir=output_dir,
        device=device,
    )

    # 执行训练
    return trainer.train()


@hydra.main(config_path="../../conf", config_name="config", version_base=None)
def main(cfg: DictConfig):
    """主训练函数"""

    # 获取输出目录
    output_dir = hydra.core.hydra_config.HydraConfig.get().runtime.output_dir
    logger.info(f"🚀 开始训练流程...")
    logger.info(f"输出目录: {output_dir}")

    # 1. 初始化配置管理器
    from train.config.manager import TrainingConfigManager

    config_manager = TrainingConfigManager(cfg, output_dir)

    # 2. 设置种子
    seed = config_manager.setup_seed()
    logger.info(f"训练阶段: {config_manager.get_training_phase()}")

    # 3. 验证训练阶段
    if not config_manager.validate_training_phase():
        logger.error("❌ 训练阶段配置验证失败")
        return

    # 4. 检查是否需要环境初始化
    requires_env = should_initialize_environment(cfg)

    # 5. 条件性初始化AppLauncher
    app_args = None
    if requires_env:
        app_args = setup_app_launcher_conditional(cfg)
        if app_args:
            # 重新设置日志（考虑debug模式）
            debug_mode = hasattr(app_args, "debug") and app_args.debug
            setup_logging(debug_mode)

    # 6. 条件性设置环境配置
    EnvCfg, env_cfg = None, None
    if requires_env:
        EnvCfg, env_cfg = config_manager.setup_environment_config()

    # 7. 解析并保存配置
    resolved_cfg = config_manager.resolve_and_save_configs()

    # 8. 设置SwanLab
    swanlab_initialized = setup_swanlab(config_manager)

    # 9. 获取设备
    device = get_device(config_manager.get_device_config())
    logger.info(f"使用设备: {device}")

    env_manager = None
    trainer = None

    try:
        # 10. 条件性创建环境管理器
        main_env = None
        if requires_env:
            # 导入环境相关组件（仅在需要时）
            from train.core.environment import EnvironmentManager

            logger.info("🔧 设置训练环境...")
            env_manager = EnvironmentManager(cfg.env.name, env_cfg, output_dir)

            # 创建主训练环境
            video_config = None
            if cfg.video.record:
                video_config = {
                    "interval": cfg.video.interval,
                    "length": cfg.video.length,
                }

            main_env = env_manager.create_main_environment(
                enable_video=cfg.video.record, video_config=video_config
            )
        else:
            logger.info("⚡ 跳过环境创建 - 使用离线训练模式")

        # 11. 创建数据加载器（如果需要）
        training_phase = config_manager.get_training_phase()
        data_loader = None

        if training_phase in [
            "decision_bc_train",
            "execution_bc_train",
            "execution_bc_actor_train",
            "reward_model_pretrain",
        ]:
            # 导入数据加载器（仅在需要时）
            from train.data.loader import ExpertDataLoader

            dataset_dir = config_manager.get_dataset_dir()
            if not dataset_dir:
                raise ValueError(f"训练阶段 {training_phase} 需要指定数据集目录")

            logger.info("📊 初始化数据加载器...")
            data_loader = ExpertDataLoader(
                dataset_dir=dataset_dir, device=device, seq_length=cfg.env.seq_length
            )

        # 12. 根据训练阶段分发到具体的训练逻辑
        logger.info("🎯 开始训练...")

        if training_phase in ["execution_bc_train", "execution_bc_actor_train"]:
            # BC训练不需要环境，传递None
            training_stats = _execute_bc_training(
                cfg, main_env, data_loader, output_dir, device, swanlab_initialized
            )
        elif training_phase == "decision_bc_train":
            # 决策BC训练不需要环境，传递None
            training_stats = _execute_decision_bc_training(
                cfg, main_env, data_loader, output_dir, device
            )
        elif training_phase == "reward_model_pretrain":
            # 奖励模型预训练不需要环境，传递None
            training_stats = _execute_reward_model_training(
                cfg, data_loader, output_dir, device
            )
        elif training_phase == "joint_rl":
            # RL训练需要环境
            if main_env is None:
                raise RuntimeError(
                    f"训练阶段 '{training_phase}' 需要环境，但环境未初始化"
                )
            eval_env = env_manager.create_eval_environment()
            training_stats = _execute_rl_training(
                cfg, main_env, eval_env, output_dir, device
            )
        else:
            raise ValueError(f"Unsupported training phase: {training_phase}")

        logger.info("✅ 训练完成！")
        logger.info(f"训练统计: {training_stats}")

        # 11. 保存训练元数据
        config_manager.save_training_metadata(
            training_phase.replace("_train", ""), **training_stats
        )

    except Exception as e:
        logger.error(f"❌ 训练失败: {e}", exc_info=True)
        raise

    finally:
        # 清理资源
        logger.info("🧹 清理资源...")

        if env_manager:
            env_manager.close_environments()

        if swanlab_initialized:
            try:
                import swanlab

                swanlab.finish()
                logger.info("SwanLab会话已结束")
            except Exception as e:
                logger.error(f"关闭SwanLab失败: {e}")

        logger.info("🏁 训练流程结束")


def cli_main():
    """CLI入口点，处理条件性AppLauncher初始化"""
    global simulation_app

    try:
        # 预设置日志（基础日志）
        setup_logging(debug_mode=False)

        # 设置OmegaConf自定义resolver（必须在Hydra初始化之前）
        setup_omegaconf_resolvers()

        # 注册环境（总是需要注册，即使不初始化）
        register_environments()

        # 预先解析AppLauncher参数，避免Hydra报错
        parser = argparse.ArgumentParser(description="Isaac Sim Training Script")
        AppLauncher.add_app_launcher_args(parser)
        parser.add_argument("--debug", action="store_true", help="Enable debug logging")
        parser.add_argument(
            "--video_early", action="store_true", help="Enable cameras early"
        )

        # 解析已知的AppLauncher参数，保留其余参数给Hydra
        app_args, remaining_args = parser.parse_known_args()

        # 将AppLauncher参数存储到全局变量，供main函数使用
        cli_main._app_args = app_args

        # 重置sys.argv，只保留Hydra需要的参数
        sys.argv = [sys.argv[0]] + remaining_args

        # 调用Hydra装饰的main函数
        # 注意：环境初始化将在main函数内部根据配置决定
        try:
            main()
        except Exception as hydra_error:
            # 捕获并详细显示Hydra相关的异常
            logger.error("=" * 80)
            logger.error("🚨 训练过程中发生异常:")
            logger.error("=" * 80)
            logger.error(f"异常类型: {type(hydra_error).__name__}")
            logger.error(f"异常信息: {str(hydra_error)}")
            logger.error("=" * 80)
            logger.error("详细堆栈跟踪:")
            import traceback

            traceback.print_exc()
            logger.error("=" * 80)
            raise hydra_error

    except Exception as outer_error:
        # 捕获AppLauncher或其他初始化阶段的异常
        logger.error("=" * 80)
        logger.error("🚨 初始化阶段发生异常:")
        logger.error("=" * 80)
        logger.error(f"异常类型: {type(outer_error).__name__}")
        logger.error(f"异常信息: {str(outer_error)}")
        logger.error("=" * 80)
        logger.error("详细堆栈跟踪:")
        import traceback

        traceback.print_exc()
        logger.error("=" * 80)
        raise outer_error

    finally:
        # 确保仿真应用正确关闭
        if simulation_app is not None:
            logger.info("关闭Isaac Sim...")
            simulation_app.close()
            logger.info("Isaac Sim已关闭")


# 兼容性：支持直接调用main()
if __name__ == "__main__":
    cli_main()
