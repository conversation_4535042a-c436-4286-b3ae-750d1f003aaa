"""训练环境管理模块

统一处理环境创建、包装、配置等功能。
从原始1862行脚本中抽取环境相关逻辑。
"""

import os
import logging
from typing import Optional, Dict, Any
from copy import deepcopy

import gymnasium as gym
from stable_baselines3.common.vec_env import VecNormalize, DummyVecEnv

# 延迟导入Isaac Lab相关模块，避免在测试时出现导入错误
try:
    from isaaclab.utils.dict import print_dict
    from isaaclab_rl.sb3 import Sb3VecEnvWrapper

    ISAAC_LAB_AVAILABLE = True
except ImportError:
    ISAAC_LAB_AVAILABLE = False

    # 提供备用实现
    def print_dict(d, nesting=0):
        """备用的字典打印函数"""
        for k, v in d.items():
            print(" " * nesting + f"{k}: {v}")

    class Sb3VecEnvWrapper:
        """备用的SB3包装器"""

        def __init__(self, env):
            self.env = env

        def __getattr__(self, name):
            return getattr(self.env, name)


logger = logging.getLogger(__name__)


class EnvironmentManager:
    """环境管理器

    负责统一处理所有环境相关逻辑，包括：
    - 主训练环境创建和配置
    - 评估环境创建
    - 环境包装器管理（VecNormalize等）
    - 视频录制配置
    """

    def __init__(self, env_name: str, env_cfg: Any, output_dir: str):
        self.env_name = env_name
        self.env_cfg = env_cfg
        self.output_dir = output_dir
        self.main_env = None
        self.eval_env = None

        logger.info(f"初始化环境管理器: {env_name}")

    def create_main_environment(
        self, enable_video: bool = False, video_config: Optional[Dict[str, Any]] = None
    ) -> gym.Env:
        """创建主训练环境

        Args:
            enable_video: 是否启用视频录制
            video_config: 视频配置参数

        Returns:
            包装后的训练环境
        """
        logger.info(f"创建主训练环境: {self.env_name}")

        # 创建基础环境
        render_mode = "rgb_array" if enable_video else None
        env = gym.make(
            self.env_name,
            cfg=self.env_cfg,
            render_mode=render_mode,
        )

        # 配置视频录制
        if enable_video and video_config:
            env = self._setup_video_recording(env, video_config)

        # 应用SB3包装器
        env = Sb3VecEnvWrapper(env)

        self.main_env = env
        logger.info("主训练环境创建完成")

        return env

    def create_evaluation_environment(self, num_eval_envs: int = 1) -> gym.Env:
        """创建评估环境

        Args:
            num_eval_envs: 评估环境数量

        Returns:
            包装后的评估环境
        """
        logger.info(f"创建评估环境，环境数量: {num_eval_envs}")

        # 创建评估环境配置（深拷贝避免修改原配置）
        eval_env_cfg = deepcopy(self.env_cfg)
        eval_env_cfg.scene.num_envs = num_eval_envs

        def make_eval_env():
            """评估环境工厂函数"""
            return gym.make(self.env_name, cfg=eval_env_cfg, render_mode=None)

        # 创建向量化环境
        eval_vec_env = DummyVecEnv([make_eval_env])
        eval_vec_env = Sb3VecEnvWrapper(eval_vec_env)

        self.eval_env = eval_vec_env
        logger.info("评估环境创建完成")

        return eval_vec_env

    def apply_vec_normalize(
        self,
        env: gym.Env,
        load_path: Optional[str] = None,
        gamma: float = 0.99,
        training: bool = True,
    ) -> VecNormalize:
        """应用VecNormalize包装器

        Args:
            env: 要包装的环境
            load_path: VecNormalize状态加载路径
            gamma: 折扣因子
            training: 是否为训练模式

        Returns:
            VecNormalize包装的环境
        """
        logger.info(f"应用VecNormalize包装器，训练模式: {training}")

        if load_path and os.path.exists(load_path):
            logger.info(f"从 {load_path} 加载VecNormalize状态")
            vec_env = VecNormalize.load(load_path, env)
        else:
            if load_path:
                logger.warning(f"VecNormalize文件不存在: {load_path}，创建新实例")

            vec_env = VecNormalize(
                env,
                training=training,
                norm_obs=True,
                norm_reward=training,  # 只在训练时归一化奖励
                gamma=gamma,
            )

        logger.info("VecNormalize包装器应用完成")
        return vec_env

    def sync_vec_normalize_stats(
        self, source_env: VecNormalize, target_env: VecNormalize
    ) -> VecNormalize:
        """同步VecNormalize统计信息

        Args:
            source_env: 源环境（通常是训练环境）
            target_env: 目标环境（通常是评估环境）

        Returns:
            更新统计信息后的目标环境
        """
        logger.info("同步VecNormalize统计信息")

        target_env.obs_rms = source_env.obs_rms
        target_env.ret_rms = source_env.ret_rms

        logger.info("VecNormalize统计信息同步完成")
        return target_env

    def _setup_video_recording(
        self, env: gym.Env, video_config: Dict[str, Any]
    ) -> gym.Env:
        """设置视频录制

        Args:
            env: 基础环境
            video_config: 视频配置

        Returns:
            配置视频录制的环境
        """
        video_save_dir = os.path.join(self.output_dir, "videos", "train")
        os.makedirs(video_save_dir, exist_ok=True)

        video_kwargs = {
            "video_folder": video_save_dir,
            "step_trigger": lambda step: step % video_config.get("interval", 1000) == 0,
            "video_length": video_config.get("length", 200),
            "disable_logger": True,
        }

        logger.info("配置视频录制")
        logger.info(f"视频保存目录: {video_save_dir}")
        print_dict(video_kwargs, nesting=4)

        return gym.wrappers.RecordVideo(env, **video_kwargs)

    def get_observation_space(self) -> gym.Space:
        """获取观察空间"""
        if self.main_env is None:
            raise RuntimeError("主环境尚未创建")
        return self.main_env.observation_space

    def get_action_space(self) -> gym.Space:
        """获取动作空间"""
        if self.main_env is None:
            raise RuntimeError("主环境尚未创建")
        return self.main_env.action_space

    def close_environments(self):
        """关闭所有环境"""
        logger.info("关闭所有环境")

        if self.main_env is not None:
            try:
                self.main_env.close()
                logger.info("主训练环境已关闭")
            except Exception as e:
                logger.error(f"关闭主训练环境时出错: {e}")

        if self.eval_env is not None:
            try:
                self.eval_env.close()
                logger.info("评估环境已关闭")
            except Exception as e:
                logger.error(f"关闭评估环境时出错: {e}")

    def save_vec_normalize_stats(self, vec_env: VecNormalize, save_path: str):
        """保存VecNormalize统计信息

        Args:
            vec_env: VecNormalize环境
            save_path: 保存路径
        """
        try:
            vec_env.save(save_path)
            logger.info(f"VecNormalize统计信息已保存到: {save_path}")
        except Exception as e:
            logger.error(f"保存VecNormalize统计信息失败: {e}")

    def modify_action_space_for_single_hand(
        self, original_space: gym.Space, single_hand_dim: int = 25
    ) -> gym.Space:
        """为单手训练修改动作空间

        Args:
            original_space: 原始动作空间
            single_hand_dim: 单手动作维度

        Returns:
            修改后的动作空间
        """
        logger.info(f"修改动作空间: {original_space.shape} -> ({single_hand_dim},)")

        modified_space = gym.spaces.Box(
            low=-1.0, high=1.0, shape=(single_hand_dim,), dtype=original_space.dtype
        )

        return modified_space
