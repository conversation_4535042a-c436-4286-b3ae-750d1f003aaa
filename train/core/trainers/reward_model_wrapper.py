"""
Reward Model Wrapper for Learned Reward Model Integration
奖励模型包装器，用于集成学习奖励模型到RL训练中
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Optional, Union, Tuple
import os
import logging

# 尝试导入ReconstructionRewardModel
try:
    from px_janus_learnsim.learning.models.reward_model import ReconstructionRewardModel
except ImportError:
    print(
        "Warning: Could not import ReconstructionRewardModel. Please ensure px_janus_learnsim is properly installed."
    )
    ReconstructionRewardModel = None

logger = logging.getLogger("tora_learning.reward_wrapper")


class RewardModelWrapper:
    """
    奖励模型包装器，用于在RL训练中集成预训练的奖励模型

    提供统一的接口来使用不同类型的奖励模型
    """

    def __init__(
        self,
        reward_model_type: str = "reconstruction",
        model_path: Optional[str] = None,
        observation_dim: Optional[int] = None,
        action_dim: Optional[int] = None,
        device: str = "auto",
        **model_kwargs,
    ):
        """
        初始化奖励模型包装器

        Args:
            reward_model_type: 奖励模型类型 ("reconstruction", "preference", etc.)
            model_path: 预训练模型路径
            observation_dim: 观察空间维度
            action_dim: 动作空间维度
            device: 设备 ("auto", "cuda", "cpu")
            **model_kwargs: 模型特定的参数
        """
        self.reward_model_type = reward_model_type
        self.model_path = model_path
        self.observation_dim = observation_dim
        self.action_dim = action_dim

        # 设置设备
        if device == "auto":
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)

        self.model_kwargs = model_kwargs
        self.reward_model = None
        self.is_loaded = False

        # 初始化奖励模型
        self._initialize_model()

        # 如果提供了模型路径，加载预训练权重
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)

    def _initialize_model(self):
        """初始化奖励模型"""
        if self.reward_model_type == "reconstruction":
            if ReconstructionRewardModel is None:
                raise ImportError("ReconstructionRewardModel not available")

            if self.observation_dim is None:
                raise ValueError(
                    "observation_dim is required for ReconstructionRewardModel"
                )

            # 创建ReconstructionRewardModel实例
            self.reward_model = ReconstructionRewardModel(
                input_dim=self.observation_dim, device=self.device, **self.model_kwargs
            )
            logger.info(
                f"Initialized ReconstructionRewardModel with input_dim={self.observation_dim}"
            )

        else:
            raise ValueError(f"Unsupported reward model type: {self.reward_model_type}")

    def load_model(self, model_path: str):
        """
        加载预训练的奖励模型

        Args:
            model_path: 模型文件路径
        """
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")

        try:
            self.reward_model.load(model_path)
            self.is_loaded = True
            logger.info(f"Successfully loaded reward model from {model_path}")
        except Exception as e:
            logger.error(f"Failed to load reward model from {model_path}: {e}")
            raise

    def get_reward(
        self,
        obs: Union[np.ndarray, torch.Tensor],
        actions: Union[np.ndarray, torch.Tensor],
        next_obs: Union[np.ndarray, torch.Tensor],
        dones: Union[np.ndarray, torch.Tensor],
    ) -> np.ndarray:
        """
        计算奖励值

        Args:
            obs: 当前观察 [batch_size, obs_dim]
            actions: 执行的动作 [batch_size, action_dim]
            next_obs: 下一步观察 [batch_size, obs_dim]
            dones: 完成标志 [batch_size]

        Returns:
            rewards: 计算得到的奖励 [batch_size]
        """
        if not self.is_loaded:
            logger.warning("Reward model not loaded, returning zero rewards")
            batch_size = len(obs) if hasattr(obs, "__len__") else 1
            return np.zeros(batch_size, dtype=np.float32)

        # 转换为torch.Tensor
        if isinstance(obs, np.ndarray):
            obs = torch.from_numpy(obs).float().to(self.device)
        if isinstance(actions, np.ndarray):
            actions = torch.from_numpy(actions).float().to(self.device)
        if isinstance(next_obs, np.ndarray):
            next_obs = torch.from_numpy(next_obs).float().to(self.device)
        if isinstance(dones, np.ndarray):
            dones = torch.from_numpy(dones).float().to(self.device)

        try:
            # 使用奖励模型计算奖励
            with torch.no_grad():
                rewards = self.reward_model.get_reward(obs, actions, next_obs, dones)

            # 转换为numpy并返回
            return rewards.cpu().numpy().flatten()

        except Exception as e:
            logger.error(f"Error computing rewards: {e}")
            batch_size = obs.shape[0] if obs.ndim > 1 else 1
            return np.zeros(batch_size, dtype=np.float32)

    def save_model(self, save_path: str):
        """
        保存当前奖励模型

        Args:
            save_path: 保存路径
        """
        if self.reward_model is None:
            raise ValueError("No reward model to save")

        try:
            self.reward_model.save(save_path)
            logger.info(f"Reward model saved to {save_path}")
        except Exception as e:
            logger.error(f"Failed to save reward model to {save_path}: {e}")
            raise

    def set_training_mode(self, training: bool = True):
        """设置训练/评估模式"""
        if self.reward_model is not None:
            if hasattr(self.reward_model, "train"):
                self.reward_model.train(training)
            elif hasattr(self.reward_model, "autoencoder"):
                self.reward_model.autoencoder.train(training)

    def get_model_info(self) -> dict:
        """获取模型信息"""
        return {
            "type": self.reward_model_type,
            "observation_dim": self.observation_dim,
            "action_dim": self.action_dim,
            "device": str(self.device),
            "is_loaded": self.is_loaded,
            "model_path": self.model_path,
            "model_kwargs": self.model_kwargs,
        }


# 工厂函数，用于方便创建奖励模型包装器
def create_reward_model_wrapper(
    config: dict, observation_space, action_space, device: str = "auto"
) -> RewardModelWrapper:
    """
    根据配置创建奖励模型包装器

    Args:
        config: 配置字典，应包含reward_model相关配置
        observation_space: 观察空间
        action_space: 动作空间
        device: 设备

    Returns:
        RewardModelWrapper实例
    """
    # 提取配置参数
    reward_model_type = config.get("type", "reconstruction")
    model_path = config.get("checkpoint_path", None)
    model_kwargs = config.get("model_kwargs", {})

    # 获取空间维度
    if hasattr(observation_space, "shape"):
        observation_dim = observation_space.shape[0]
    else:
        observation_dim = (
            observation_space.n if hasattr(observation_space, "n") else None
        )

    if hasattr(action_space, "shape"):
        action_dim = action_space.shape[0]
    else:
        action_dim = action_space.n if hasattr(action_space, "n") else None

    # 创建包装器
    wrapper = RewardModelWrapper(
        reward_model_type=reward_model_type,
        model_path=model_path,
        observation_dim=observation_dim,
        action_dim=action_dim,
        device=device,
        **model_kwargs,
    )

    return wrapper


if __name__ == "__main__":
    # 测试代码
    print("Testing RewardModelWrapper...")

    # 创建一个简单的测试实例
    try:
        wrapper = RewardModelWrapper(
            reward_model_type="reconstruction",
            observation_dim=100,
            action_dim=10,
            device="cpu",
            latent_dim=32,
            ae_learning_rate=0.001,
        )

        print("RewardModelWrapper created successfully!")
        print("Model info:", wrapper.get_model_info())

        # 测试奖励计算
        obs = np.random.randn(5, 100)
        actions = np.random.randn(5, 10)
        next_obs = np.random.randn(5, 100)
        dones = np.zeros(5)

        rewards = wrapper.get_reward(obs, actions, next_obs, dones)
        print(f"Test rewards shape: {rewards.shape}")
        print("Test completed successfully!")

    except Exception as e:
        print(f"Test failed: {e}")
