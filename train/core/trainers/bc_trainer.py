"""BC训练器模块"""

import os
import logging
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Any, Optional, Callable
from tqdm import tqdm
from omegaconf import OmegaConf

from .base import BaseTrainer
from ...utils.helpers import (
    ConstantSchedulePicklable,
    convert_dataloader_to_transitions,
)
from ...integrations.swanlab_integration import SwanlabBCLogger

logger = logging.getLogger(__name__)


class BCTrainer:
    """BC训练器基类"""

    def __init__(self, policy, dataloader, optimizer, device, **kwargs):
        self.policy = policy
        self.dataloader = dataloader
        self.optimizer = optimizer
        self.device = device
        self.custom_logger = kwargs.get("custom_logger")

    def train(
        self,
        n_epochs: int,
        log_interval: int = 500,
        on_epoch_end: Optional[Callable] = None,
    ) -> Dict[str, Any]:
        """训练基础方法"""
        raise NotImplementedError("Subclasses must implement train method")


class ExecutionBCTrainer(BCTrainer):
    """执行层BC训练器"""

    def __init__(self, policy, dataloader, optimizer, device, **kwargs):
        super().__init__(policy, dataloader, optimizer, device, **kwargs)

        # 条件BC训练配置
        self.enable_conditional = kwargs.get("enable_conditional", True)
        self.enable_single_hand = kwargs.get("enable_single_hand", False)
        self.single_hand_config = kwargs.get("single_hand_config", {})

        # 动作缩放配置
        self.enable_action_scaling = kwargs.get("enable_action_scaling", True)
        self.obs_normalization = kwargs.get("obs_normalization", "standard")
        self.action_scaling = kwargs.get("action_scaling", "minmax")
        self.action_clip_range = kwargs.get("action_clip_range", (-1.0, 1.0))

        # 归一化配置
        self.enable_separated_normalization = kwargs.get(
            "enable_separated_normalization", True
        )
        self.target_pose_normalization = kwargs.get(
            "target_pose_normalization", "standard"
        )
        self.force_regenerate_stats = kwargs.get("force_regenerate_stats", False)

        # 调试模式
        self.debug = kwargs.get("debug", False)

        # 模型保存路径
        self.model_save_path = kwargs.get("model_save_path")

        # 🔧 统计文件路径 - 避免重复查找
        self.scaling_stats_path = kwargs.get("scaling_stats_path")

        # 🆕 Checkpoint 配置
        self.output_dir = kwargs.get("output_dir", ".")
        cfg_from_factory = kwargs.get("config")
        if cfg_from_factory is not None and hasattr(cfg_from_factory, "training"):
            self.ckpt_cfg = cfg_from_factory.training.get("checkpoint", {})
        else:
            self.ckpt_cfg = {}

        logger.info(f"ExecutionBCTrainer initialized:")
        logger.info(f"  - Conditional training: {self.enable_conditional}")
        logger.info(f"  - Single hand mode: {self.enable_single_hand}")
        logger.info(f"  - Action scaling: {self.enable_action_scaling}")
        logger.info(f"  - Debug mode: {self.debug}")
        logger.info(f"  - Scaling stats path: {self.scaling_stats_path}")

    def train(
        self,
        n_epochs: int,
        log_interval: int = 500,
        on_epoch_end: Optional[Callable] = None,
    ) -> Dict[str, Any]:
        """执行BC训练"""

        if self.enable_conditional:
            return self._train_conditional_bc(n_epochs, log_interval, on_epoch_end)
        else:
            return self._train_standard_bc(n_epochs, log_interval, on_epoch_end)

    def _train_conditional_bc(
        self, n_epochs: int, log_interval: int, on_epoch_end: Optional[Callable]
    ) -> Dict[str, Any]:
        """条件BC训练 - 完全遵照原始脚本实现"""
        logger.info("🚀 开始条件BC训练")

        try:
            from px_janus_learnsim.learning.trainers import ConditionalBCTrainer
            from train.utils.callbacks import CheckpointCallback

            # 创建条件BC训练器
            callbacks = []
            # 如果配置中启用了 checkpoint 频率
            save_epoch_freq = int(self.ckpt_cfg.get("save_epoch_freq", 0))
            save_step_freq = int(self.ckpt_cfg.get("save_step_freq", 0))
            max_to_keep = int(self.ckpt_cfg.get("max_to_keep", 5))

            if save_epoch_freq > 0 or save_step_freq > 0:
                ckpt_cb = CheckpointCallback(
                    policy=self.policy,
                    output_dir=self.output_dir,
                    save_epoch_freq=save_epoch_freq,
                    save_step_freq=save_step_freq,
                    max_to_keep=max_to_keep,
                )
                callbacks.append(ckpt_cb)

            conditional_trainer = ConditionalBCTrainer(
                policy=self.policy,
                dataloader=self.dataloader,
                optimizer=self.optimizer,
                device=self.device,
                custom_logger=self.custom_logger,
                enable_single_hand=self.enable_single_hand,
                single_hand_config=self.single_hand_config,
                enable_action_scaling=self.enable_action_scaling,
                debug=self.debug,
                obs_normalization=self.obs_normalization,
                action_scaling=self.action_scaling,
                action_clip_range=self.action_clip_range,
                model_save_path=self.model_save_path,
                enable_separated_normalization=self.enable_separated_normalization,
                target_pose_normalization=self.target_pose_normalization,
                # 🔧 传递已找到的统计文件路径，避免重复查找
                scaling_stats_path=self.scaling_stats_path,
                force_regenerate_stats=self.force_regenerate_stats,
                callbacks=callbacks,
            )

            # 执行训练
            training_stats = conditional_trainer.train(
                n_epochs=n_epochs,
                log_interval=log_interval,
                on_epoch_end=on_epoch_end,
            )

            # 🔧 添加模型保存逻辑 - 遵照原始脚本
            if self.model_save_path:
                logger.info(
                    f"Saving trained Execution BC policy to {self.model_save_path}..."
                )
                try:
                    from imitation.util.util import save_policy

                    # 条件BC训练模式：保存conditional_trainer中的policy
                    save_policy(self.policy, self.model_save_path)
                    logger.info(f"Execution BC Policy saved to {self.model_save_path}")
                except Exception as e:
                    logger.error(
                        f"Failed to save Execution BC trained policy: {e}",
                        exc_info=True,
                    )

            logger.info("条件BC训练完成!")
            return training_stats

        except ImportError as e:
            logger.error(f"ConditionalBCTrainer not available: {e}")
            logger.info("Falling back to standard BC training")
            return self._train_standard_bc(n_epochs, log_interval, on_epoch_end)

    def _train_standard_bc(
        self, n_epochs: int, log_interval: int, on_epoch_end: Optional[Callable]
    ) -> Dict[str, Any]:
        """标准BC训练 - 遵照原始脚本实现"""
        logger.info("🔄 开始标准BC训练")

        from imitation.algorithms import bc
        from imitation.util.util import save_policy

        # 转换数据格式
        logger.info("转换数据格式为imitation.Transitions...")
        execution_transitions, _ = convert_dataloader_to_transitions(
            self.dataloader, self.device, seq_length=15  # 默认序列长度
        )

        # 创建BC训练器
        rng = np.random.default_rng(42)  # 默认种子
        bc_trainer = bc.BC(
            observation_space=self.policy.observation_space,
            action_space=self.policy.action_space,
            policy=self.policy,
            demonstrations=execution_transitions,
            rng=rng,
            batch_size=32,  # 默认批次大小
            optimizer_cls=torch.optim.AdamW,
            optimizer_kwargs={"lr": 1e-4},
            custom_logger=self.custom_logger,
        )

        # 执行训练
        bc_trainer.train(
            n_epochs=n_epochs,
            log_interval=log_interval,
            on_epoch_end=on_epoch_end,
        )

        # 🔧 保存模型 - 遵照原始脚本
        if self.model_save_path:
            logger.info(
                f"Saving trained Execution BC policy to {self.model_save_path}..."
            )
            try:
                # 标准BC训练模式：保存execution_bc_trainer.policy
                save_policy(bc_trainer.policy, self.model_save_path)
                logger.info(f"Execution BC Policy saved to {self.model_save_path}")
            except Exception as e:
                logger.error(
                    f"Failed to save Execution BC trained policy: {e}", exc_info=True
                )

        return {
            "total_epochs": n_epochs,
            "total_batches": len(self.dataloader) * n_epochs,
            "average_loss": 0.0,  # BC训练器不直接返回损失
        }


class DecisionBCTrainer(BCTrainer):
    """决策层BC训练器"""

    def __init__(self, policy, dataloader, optimizer, device, **kwargs):
        super().__init__(policy, dataloader, optimizer, device, **kwargs)

        self.num_candidates = kwargs.get("num_candidates", 10)
        self.seq_length = kwargs.get("seq_length", 15)
        self.target_hand = kwargs.get("target_hand", "left")
        self.output_dir = kwargs.get("output_dir", ".")

        # 决策BC特定配置
        self.criterion = torch.nn.CrossEntropyLoss()

        # 🔧 添加模型保存路径 - 遵照原始脚本
        self.decision_policy_save_path = os.path.join(
            self.output_dir, "decision_models", "decision_bc_scoring_policy.pth"
        )
        os.makedirs(os.path.dirname(self.decision_policy_save_path), exist_ok=True)

        logger.info(f"DecisionBCTrainer initialized:")
        logger.info(f"  - Num candidates: {self.num_candidates}")
        logger.info(f"  - Sequence length: {self.seq_length}")
        logger.info(f"  - Model save path: {self.decision_policy_save_path}")

    def train(
        self,
        n_epochs: int,
        log_interval: int = 500,
        on_epoch_end: Optional[Callable] = None,
    ) -> Dict[str, Any]:
        """决策BC训练 - 完全遵照原始脚本实现"""
        logger.info("🎯 开始决策BC训练")

        from px_janus_learnsim.learning.models.bc_utils import (
            process_imitation_batch,
            dummy_pose_generator,
        )

        total_loss = 0.0
        total_batches = 0
        total_correct = 0
        total_samples = 0

        for epoch in range(n_epochs):
            epoch_loss = 0.0
            epoch_batches = 0
            epoch_correct = 0
            epoch_samples = 0

            progress_bar = tqdm(
                self.dataloader,
                desc=f"Epoch {epoch+1}/{n_epochs}",
                leave=False,
            )

            for batch_idx, batch_data in enumerate(progress_bar):
                self.optimizer.zero_grad()

                try:
                    # 处理批次数据
                    obs_dict, p_expert_batch, _, _, _ = process_imitation_batch(
                        batch_data, self.device
                    )

                    current_batch_size = p_expert_batch.shape[0]

                    # 🚀 优化：减少tensor操作和内存分配
                    # 生成候选pose
                    P_candidates_batch = dummy_pose_generator(
                        current_batch_size, self.num_candidates - 1, 18, self.device
                    )

                    # 🚀 优化：使用更高效的tensor拼接
                    # 预分配内存而不是动态拼接
                    P_combined_batch = torch.empty(
                        current_batch_size,
                        self.num_candidates,
                        18,
                        dtype=p_expert_batch.dtype,
                        device=self.device,
                    )
                    P_combined_batch[:, 0, :] = p_expert_batch  # 专家pose放在第一位
                    P_combined_batch[:, 1:, :] = P_candidates_batch  # 候选pose

                    # 🚀 优化：复用target_indices tensor
                    if (
                        not hasattr(self, "_target_indices_cache")
                        or self._target_indices_cache.shape[0] != current_batch_size
                    ):
                        self._target_indices_cache = torch.zeros(
                            current_batch_size, dtype=torch.long, device=self.device
                        )
                    target_indices = self._target_indices_cache[:current_batch_size]

                    # 前向传播
                    scores = self.policy.evaluate_scores(obs_dict, P_combined_batch)
                    loss = self.criterion(scores, target_indices)

                    # 反向传播
                    loss.backward()
                    self.optimizer.step()

                    # 统计
                    epoch_loss += loss.item()
                    epoch_batches += 1
                    total_loss += loss.item()
                    total_batches += 1

                    # 计算准确率
                    predicted_indices = torch.argmax(scores, dim=1)
                    correct_in_batch = (
                        (predicted_indices == target_indices).sum().item()
                    )
                    epoch_correct += correct_in_batch
                    epoch_samples += current_batch_size
                    total_correct += correct_in_batch
                    total_samples += current_batch_size

                    # 更新进度条
                    running_accuracy = (
                        epoch_correct / epoch_samples if epoch_samples > 0 else 0
                    )
                    progress_bar.set_postfix(
                        {
                            "batch_loss": f"{loss.item():.4f}",
                            "epoch_acc": f"{running_accuracy:.3f}",
                        }
                    )

                    # 记录日志
                    if self.custom_logger and (batch_idx + 1) % log_interval == 0:
                        self.custom_logger.record("bc/loss", loss.item())
                        self.custom_logger.record("bc/accuracy", running_accuracy)
                        self.custom_logger.dump()

                except Exception as e:
                    logger.error(f"Error in batch {batch_idx} of epoch {epoch+1}: {e}")
                    continue

            # Epoch结束
            avg_epoch_loss = epoch_loss / epoch_batches if epoch_batches > 0 else 0
            epoch_accuracy = epoch_correct / epoch_samples if epoch_samples > 0 else 0

            logger.info(
                f"Epoch {epoch+1}/{n_epochs} completed. Avg Loss: {avg_epoch_loss:.6f}, Accuracy: {epoch_accuracy:.4f}"
            )

            # 执行epoch结束回调
            if on_epoch_end:
                on_epoch_end()

        # 🔧 训练完成后保存模型 - 完全遵照原始脚本
        logger.info("Decision BC training finished. Saving policy model...")
        try:
            torch.save(self.policy.state_dict(), self.decision_policy_save_path)
            logger.info(
                f"Decision policy model saved to {self.decision_policy_save_path}"
            )
        except Exception as e:
            logger.error(
                f"Failed to save Decision BC policy state_dict: {e}", exc_info=True
            )

        # 计算最终统计
        avg_total_loss = total_loss / total_batches if total_batches > 0 else 0
        final_accuracy = total_correct / total_samples if total_samples > 0 else 0

        logger.info("决策BC训练完成!")

        return {
            "total_epochs": n_epochs,
            "total_batches": total_batches,
            "average_loss": avg_total_loss,
            "final_accuracy": final_accuracy,
            "model_save_path": self.decision_policy_save_path,
        }
