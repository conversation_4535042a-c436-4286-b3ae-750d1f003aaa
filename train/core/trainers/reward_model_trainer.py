"""奖励模型训练器模块"""

import os
import logging
import torch
import numpy as np
from typing import Dict, Any, Optional, Callable
from tqdm import tqdm

from .base import BaseTrainer
from ...integrations.swanlab_integration import log_to_swanlab

logger = logging.getLogger(__name__)


class RewardModelTrainer(BaseTrainer):
    """奖励模型训练器"""

    def __init__(self, dataloader, device, **kwargs):
        super().__init__(**kwargs)
        self.dataloader = dataloader
        self.device = device

        # 模型配置
        self.model_kwargs = kwargs.get("model_kwargs", {})
        self.seq_length = kwargs.get("seq_length", 15)
        self.target_hand = kwargs.get("target_hand", "left")

        # 训练配置
        self.batch_size = kwargs.get("batch_size", 32)
        self.learning_rate = kwargs.get("learning_rate", 0.001)

        # 输出路径
        self.output_dir = kwargs.get("output_dir", ".")

        logger.info(f"RewardModelTrainer initialized:")
        logger.info(f"  - Batch size: {self.batch_size}")
        logger.info(f"  - Learning rate: {self.learning_rate}")
        logger.info(f"  - Sequence length: {self.seq_length}")

    def _get_observation_dimension(self) -> int:
        """获取观察维度"""
        logger.info("Analyzing observation dimensions from expert data...")

        sample_batch = next(iter(self.dataloader))
        try:
            # 尝试使用坐标转换包装器
            try:
                from px_janus_learnsim.utils.coordinate_transform_wrapper import (
                    process_expert_batch_with_transforms,
                )

                sample_obs_np, _, _, _, _ = process_expert_batch_with_transforms(
                    sample_batch,
                    self.device,
                    self.seq_length,
                    target_hand=self.target_hand,
                    enable_coord_transform=True,
                    enable_6d_rotation_fix=False,
                )
            except Exception as e:
                logger.warning(f"坐标转换包装器执行失败: {e}，使用原始处理函数")
                from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
                    _process_expert_batch_for_imitation,
                )

                sample_obs_np, _, _, _, _ = _process_expert_batch_for_imitation(
                    sample_batch, self.device, self.seq_length
                )

            actual_observation_dim = sample_obs_np.shape[1]  # [batch_size, obs_dim]
            logger.info(f"Actual processed observation dim: {actual_observation_dim}")
            return actual_observation_dim

        except Exception as e:
            logger.error(f"Failed to analyze observation dimensions: {e}")
            # 使用默认维度
            default_dim = 34  # 默认观察维度
            logger.info(f"Using default observation dimension: {default_dim}")
            return default_dim

    def _create_observation_dataset(self) -> torch.utils.data.DataLoader:
        """创建观察数据集"""

        class ObservationDataset(torch.utils.data.Dataset):
            def __init__(self, dataloader, device, seq_length, target_hand):
                self.observations = []
                logger.info(
                    "Extracting observations from expert data for reward model training..."
                )

                for batch_data in tqdm(dataloader, desc="Processing expert data"):
                    try:
                        # 尝试使用坐标转换包装器
                        try:
                            from px_janus_learnsim.utils.coordinate_transform_wrapper import (
                                process_expert_batch_with_transforms,
                            )

                            obs_np, _, _, _, _ = process_expert_batch_with_transforms(
                                batch_data,
                                device,
                                seq_length,
                                target_hand=target_hand,
                                enable_coord_transform=True,
                                enable_6d_rotation_fix=False,
                            )
                        except Exception as e:
                            logger.warning(
                                f"坐标转换包装器执行失败: {e}，使用原始处理函数"
                            )
                            from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
                                _process_expert_batch_for_imitation,
                            )

                            obs_np, _, _, _, _ = _process_expert_batch_for_imitation(
                                batch_data, device, seq_length
                            )

                        # 只保留observations用于autoencoder训练
                        self.observations.append(torch.from_numpy(obs_np).float())

                    except Exception as e:
                        logger.error(f"Error processing batch for observations: {e}")
                        continue

                # 合并所有观察数据
                if self.observations:
                    self.observations = torch.cat(self.observations, dim=0)
                    logger.info(
                        f"Total observations for reward model training: {len(self.observations)}"
                    )
                else:
                    raise ValueError("No observations extracted from expert data")

            def __len__(self):
                return len(self.observations)

            def __getitem__(self, idx):
                return self.observations[idx]

        # 创建观察数据集
        obs_dataset = ObservationDataset(
            self.dataloader, self.device, self.seq_length, self.target_hand
        )

        obs_dataloader = torch.utils.data.DataLoader(
            obs_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            drop_last=False,
        )

        logger.info(f"Created observation DataLoader with {len(obs_dataset)} samples")
        return obs_dataloader

    def train(self, n_epochs: int, **kwargs) -> Dict[str, Any]:
        """训练奖励模型"""
        logger.info("🏆 开始奖励模型训练")

        # 获取观察维度
        input_dim = self._get_observation_dimension()

        # 初始化ReconstructionRewardModel
        try:
            from px_janus_learnsim.learning.models.reward_model import (
                ReconstructionRewardModel,
            )

            # 处理配置参数映射
            model_kwargs = dict(self.model_kwargs)

            # 映射参数名
            if "encoder_dim" in model_kwargs:
                encoder_dim = model_kwargs.pop("encoder_dim")
                model_kwargs["encoder_hidden_dims"] = [encoder_dim, encoder_dim // 2]
                model_kwargs["decoder_hidden_dims"] = [encoder_dim // 2, encoder_dim]

            # 映射learning_rate参数名
            if "learning_rate" in model_kwargs:
                model_kwargs["ae_learning_rate"] = model_kwargs.pop("learning_rate")

            # 移除不支持的参数
            unsupported_params = ["weight_decay"]
            for param in unsupported_params:
                if param in model_kwargs:
                    removed_value = model_kwargs.pop(param)
                    logger.info(
                        f"Removed unsupported parameter '{param}': {removed_value}"
                    )

            reward_model = ReconstructionRewardModel(
                input_dim=input_dim,
                device=str(self.device),
                **model_kwargs,
            )
            logger.info(f"ReconstructionRewardModel initialized: input_dim={input_dim}")

        except Exception as e:
            logger.error(f"Failed to initialize ReconstructionRewardModel: {e}")
            raise

        # 创建观察数据集
        try:
            obs_dataloader = self._create_observation_dataset()
        except Exception as e:
            logger.error(f"Failed to create observation dataset: {e}")
            raise

        # 执行训练
        try:
            logger.info(f"Starting reward model training for {n_epochs} epochs...")
            epoch_losses, final_loss = reward_model.train_reward_model(
                expert_data_loader=obs_dataloader,
                epochs=n_epochs,
                learning_rate=self.learning_rate,
            )

            logger.info(
                f"Reward model training completed. Final loss: {final_loss:.6f}"
            )

            # 记录到SwanLab（如果启用）
            for epoch, loss in enumerate(epoch_losses):
                log_to_swanlab(
                    {
                        "reward_model/train_loss": loss,
                        "reward_model/epoch": epoch,
                    },
                    step=epoch,
                )

        except Exception as e:
            logger.error(f"Error during reward model training: {e}")
            raise

        # 保存模型
        try:
            final_model_path = os.path.join(self.output_dir, "final_reward_model.pth")
            reward_model.save(final_model_path)
            logger.info(f"Final reward model saved: {final_model_path}")

            # 保存备份
            best_model_path = os.path.join(self.output_dir, "best_reward_model.pth")
            reward_model.save(best_model_path)
            logger.info(f"Best reward model saved: {best_model_path}")

        except Exception as e:
            logger.error(f"Error saving reward model: {e}")

        logger.info("奖励模型训练完成!")

        return {
            "total_epochs": n_epochs,
            "epoch_losses": epoch_losses,
            "final_loss": final_loss,
            "model_path": final_model_path,
        }
