"""RL训练器模块"""

import os
import logging
import torch
from typing import Dict, Any, Optional, Callable
from copy import deepcopy

from .base import BaseTrainer
from ...utils.helpers import save_training_config
from ...integrations.swanlab_integration import log_to_swanlab

logger = logging.getLogger(__name__)


class HierarchicalRLTrainer(BaseTrainer):
    """层次化RL训练器"""

    def __init__(self, env, eval_env, cfg, output_dir, device, **kwargs):
        super().__init__(**kwargs)
        self.env = env
        self.eval_env = eval_env
        self.cfg = cfg
        self.output_dir = output_dir
        self.device = device

        # 训练配置
        self.n_timesteps = cfg.training.n_timesteps
        self.save_freq = cfg.training.save_freq
        self.eval_freq = cfg.training.eval_freq
        self.n_eval_episodes = cfg.training.n_eval_episodes

        # RL特定配置
        self.use_vec_normalize = cfg.training.get("use_vec_normalize", False)
        self.load_model_path = cfg.training.get("load_model", None)
        self.load_bc_model_path = cfg.training.get("load_execution_bc_model_path", None)

        # 专家数据配置
        self.expert_ratio = cfg.agent.get("expert_ratio", 0)
        self.dataset_dir = cfg.training.get("dataset_dir", None)

        self.agent = None
        self.current_env_for_agent = None

        logger.info(f"HierarchicalRLTrainer initialized:")
        logger.info(f"  - Timesteps: {self.n_timesteps}")
        logger.info(f"  - Expert ratio: {self.expert_ratio}")
        logger.info(f"  - Use VecNormalize: {self.use_vec_normalize}")

    def _setup_environment(self):
        """设置环境"""
        from stable_baselines3.common.vec_env import VecNormalize

        self.current_env_for_agent = self.env

        if self.use_vec_normalize:
            if self.load_model_path:
                vec_normalize_load_path = os.path.join(
                    os.path.dirname(self.load_model_path), "vec_normalize.pkl"
                )
                if os.path.exists(vec_normalize_load_path):
                    logger.info(
                        f"Loading VecNormalize state from {vec_normalize_load_path}"
                    )
                    self.current_env_for_agent = VecNormalize.load(
                        vec_normalize_load_path, self.current_env_for_agent
                    )
                else:
                    logger.warning(f"VecNormalize file not found, creating new")
                    self.current_env_for_agent = VecNormalize(
                        self.current_env_for_agent,
                        gamma=self.cfg.agent.get("gamma", 0.99),
                    )
            else:
                logger.info("Initializing new VecNormalize wrapper")
                self.current_env_for_agent = VecNormalize(
                    self.current_env_for_agent, gamma=self.cfg.agent.get("gamma", 0.99)
                )
            logger.info("VecNormalize is active for the agent")

    def _setup_agent(self):
        """设置智能体"""
        from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
            HierarchicalHybridSAC,
            ExecutionRLPolicy,
        )
        from omegaconf import OmegaConf

        agent_class = HierarchicalHybridSAC

        # 准备策略参数
        policy_kwargs_for_sac = OmegaConf.to_container(
            self.cfg.agent.get("policy_kwargs", {}), resolve=True
        )
        policy_kwargs_for_sac.setdefault("num_queries", self.cfg.env.seq_length)

        if self.load_model_path:
            logger.info(f"Loading agent model from: {self.load_model_path}")
            self.agent = agent_class.load(
                self.load_model_path,
                env=self.current_env_for_agent,
                device=self.cfg.app.get("device", "auto"),
            )
            logger.info("Agent model loaded successfully")
        else:
            logger.info("Creating new HierarchicalHybridSAC agent...")
            self.agent = agent_class(
                policy=ExecutionRLPolicy,
                env=self.current_env_for_agent,
                learning_rate=self.cfg.agent.learning_rate,
                buffer_size=self.cfg.agent.buffer_size,
                learning_starts=self.cfg.agent.learning_starts,
                batch_size=self.cfg.training.batch_size,
                tau=self.cfg.agent.tau,
                gamma=self.cfg.agent.gamma,
                train_freq=self.cfg.agent.get("train_freq", 1),
                gradient_steps=self.cfg.agent.get("gradient_steps", 1),
                optimize_memory_usage=self.cfg.agent.get(
                    "optimize_memory_usage", False
                ),
                ent_coef=self.cfg.agent.get("ent_coef", "auto"),
                target_update_interval=self.cfg.agent.get("target_update_interval", 1),
                target_entropy=self.cfg.agent.get("target_entropy", "auto"),
                use_sde=self.cfg.agent.get("use_sde", False),
                sde_sample_freq=self.cfg.agent.get("sde_sample_freq", -1),
                sde_eta=self.cfg.agent.get("sde_eta", 0.0),
                tensorboard_log=os.path.join(self.output_dir, "tensorboard_logs"),
                policy_kwargs=policy_kwargs_for_sac,
                verbose=self.cfg.agent.get("verbose", 1),
                seed=self.cfg.app.seed,
                device=self.cfg.app.get("device", "auto"),
                _init_setup_model=True,
                # HierarchicalHybridSAC特定参数
                expert_buffer_size=self.cfg.agent.get("expert_buffer_size", 100000),
                expert_ratio=self.expert_ratio,
                bc_weight=self.cfg.agent.get("bc_weight", 0.1),
                train_mode=self.cfg.training.hierarchical_mode,
                seq_length=self.cfg.env.seq_length,
            )
            logger.info("New agent created")

    def _load_pretrained_bc_policy(self):
        """加载预训练BC策略"""
        # 确定BC策略路径
        bc_policy_load_path = self.load_bc_model_path
        if not bc_policy_load_path:
            bc_policy_load_path = os.path.join(
                self.output_dir, "execution_bc_policy.pth"
            )

        if os.path.exists(bc_policy_load_path):
            logger.info(f"Loading pretrained BC policy from {bc_policy_load_path}")
            try:
                bc_state_dict = torch.load(
                    bc_policy_load_path, map_location=self.device
                )

                # 处理不同的保存格式
                if hasattr(bc_state_dict, "state_dict"):
                    bc_state_dict = bc_state_dict.state_dict()
                elif "policy_state_dict" in bc_state_dict:
                    bc_state_dict = bc_state_dict["policy_state_dict"]
                elif "model_state_dict" in bc_state_dict:
                    bc_state_dict = bc_state_dict["model_state_dict"]

                self.agent.policy.actor.load_state_dict(bc_state_dict, strict=False)
                logger.info(
                    "Successfully loaded BC policy weights into agent.policy.actor"
                )

            except Exception as e:
                logger.error(f"Failed to load BC policy weights: {e}")
                logger.warning("Proceeding with randomly initialized RL policy actor")
        else:
            logger.warning(f"No pretrained BC policy found at {bc_policy_load_path}")

    def _load_expert_data(self):
        """加载专家数据到重放缓冲区"""
        if (
            not isinstance(self.agent, type(None))
            and self.expert_ratio > 0
            and self.dataset_dir
        ):
            logger.info("Loading expert data into replay buffer for RL phase...")
            try:
                from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
                    HierarchicalHybridSAC,
                )

                expert_data_load_batch_size = self.cfg.training.get(
                    "expert_data_load_batch_size", self.cfg.training.batch_size
                )

                expert_train_dataloader, _, _, _ = HierarchicalHybridSAC.load_data(
                    self.dataset_dir,
                    batch_size_train=expert_data_load_batch_size,
                    batch_size_val=0,
                    seq_length=self.cfg.env.seq_length,
                )

                if expert_train_dataloader and hasattr(
                    self.agent, "load_expert_data_from_dataloader"
                ):
                    added_count = self.agent.load_expert_data_from_dataloader(
                        expert_train_dataloader
                    )
                    logger.info(
                        f"Added {added_count} expert samples to the replay buffer"
                    )
                else:
                    logger.warning(
                        "Could not load expert dataloader for RL phase buffer filling"
                    )

            except Exception as e:
                logger.error(f"Error loading expert data into buffer for RL: {e}")

    def _setup_callbacks(self):
        """设置回调"""
        from stable_baselines3.common.callbacks import (
            CheckpointCallback,
            EvalCallback,
            CallbackList,
        )
        from stable_baselines3.common.vec_env import VecNormalize

        # 检查点回调
        checkpoint_callback = CheckpointCallback(
            save_freq=max(1, self.save_freq // self.current_env_for_agent.num_envs),
            save_path=self.output_dir,
            name_prefix="rl_model",
            save_replay_buffer=self.cfg.training.get(
                "save_replay_buffer_on_checkpoint", False
            ),
            save_vecnormalize=self.use_vec_normalize,
            verbose=1,
        )

        # 评估回调
        eval_callback = EvalCallback(
            self.eval_env,
            best_model_save_path=self.output_dir,
            log_path=self.output_dir,
            eval_freq=max(1, self.eval_freq // self.current_env_for_agent.num_envs),
            n_eval_episodes=self.n_eval_episodes,
            deterministic=self.cfg.training.get("eval_deterministic", True),
            render=False,
            callback_on_new_best=None,
            warn=True,
        )

        return CallbackList([checkpoint_callback, eval_callback])

    def train(self, **kwargs) -> Dict[str, Any]:
        """执行RL训练"""
        logger.info("🎮 开始层次化RL训练")

        # 设置环境
        self._setup_environment()

        # 设置智能体
        self._setup_agent()

        # 加载预训练BC策略
        if not self.load_model_path:  # 只有在不加载完整模型时才加载BC策略
            self._load_pretrained_bc_policy()

        # 加载专家数据
        self._load_expert_data()

        # 设置回调
        callback_list = self._setup_callbacks()

        # 开始训练
        logger.info(f"Starting RL training for {self.n_timesteps} timesteps...")
        self.agent.learn(
            total_timesteps=self.n_timesteps,
            callback=callback_list,
            reset_num_timesteps=self.load_model_path is None,
            log_interval=self.cfg.logging.get("bc_log_interval", 4),
        )

        # 保存最终模型
        final_model_path = os.path.join(self.output_dir, "rl_model_final")
        self.agent.save(final_model_path)
        logger.info(f"Final agent model saved to {final_model_path}")

        # 保存VecNormalize状态
        if self.use_vec_normalize:
            from stable_baselines3.common.vec_env import VecNormalize

            if isinstance(self.current_env_for_agent, VecNormalize):
                vecnormalize_final_path = os.path.join(
                    self.output_dir, "vec_normalize_final.pkl"
                )
                self.current_env_for_agent.save(vecnormalize_final_path)
                logger.info(f"VecNormalize stats saved to {vecnormalize_final_path}")

        # 保存配置
        save_training_config(self.output_dir, self.cfg, source="rl_training_script")

        # 清理环境
        self.current_env_for_agent.close()
        self.eval_env.close()

        logger.info("层次化RL训练完成!")

        return {
            "total_timesteps": self.n_timesteps,
            "final_model_path": final_model_path,
            "training_completed": True,
        }
