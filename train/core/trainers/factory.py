"""训练器工厂模块

根据配置创建不同类型的训练器。
从原始1862行脚本中抽取训练器创建逻辑。
"""

import logging
from typing import Dict, Any, Type, Optional
import torch
from omegaconf import DictConfig

from .base import BaseTrainer
from .bc_trainer import ExecutionBCTrainer, DecisionBCTrainer
from .reward_model_trainer import RewardModelTrainer
from .rl_trainer import HierarchicalRLTrainer
from ...utils.constants import TrainingPhase

logger = logging.getLogger(__name__)


class TrainerFactory:
    """训练器工厂

    负责根据配置创建不同类型的训练器，包括：
    - BC训练器（Decision、Execution）
    - RL训练器
    - Reward Model训练器
    """

    # 注册的训练器类型
    _trainers: Dict[str, Type[BaseTrainer]] = {}

    @classmethod
    def register_trainer(cls, name: str, trainer_class: Type[BaseTrainer]):
        """注册训练器类型

        Args:
            name: 训练器名称
            trainer_class: 训练器类
        """
        cls._trainers[name] = trainer_class
        logger.info(f"注册训练器: {name} -> {trainer_class.__name__}")

    @classmethod
    def create_trainer(
        cls,
        training_phase: str,
        config: DictConfig,
        output_dir: str,
        device: torch.device,
        **kwargs,
    ) -> BaseTrainer:
        """创建训练器

        Args:
            training_phase: 训练阶段
            config: 配置对象
            output_dir: 输出目录
            device: 设备
            **kwargs: 额外参数

        Returns:
            训练器实例
        """
        logger.info(f"Creating trainer for phase: {training_phase}")

        if training_phase in [
            TrainingPhase.EXECUTION_BC_TRAIN.value,
            TrainingPhase.EXECUTION_BC_ACTOR_TRAIN.value,
        ]:
            return cls._create_execution_bc_trainer(
                config, output_dir, device, **kwargs
            )

        elif training_phase == TrainingPhase.DECISION_BC_TRAIN.value:
            return cls._create_decision_bc_trainer(config, output_dir, device, **kwargs)

        elif training_phase == TrainingPhase.REWARD_MODEL_PRETRAIN.value:
            return cls._create_reward_model_trainer(
                config, output_dir, device, **kwargs
            )

        elif training_phase == TrainingPhase.JOINT_RL.value:
            return cls._create_rl_trainer(config, output_dir, device, **kwargs)

        else:
            raise ValueError(f"Unsupported training phase: {training_phase}")

    @classmethod
    def _create_execution_bc_trainer(
        cls, config: DictConfig, output_dir: str, device: torch.device, **kwargs
    ) -> ExecutionBCTrainer:
        """创建执行层BC训练器"""
        required_params = ["policy", "dataloader", "optimizer"]
        cls._validate_required_params(required_params, kwargs)

        logger.info("Creating ExecutionBCTrainer")
        return ExecutionBCTrainer(
            config=config, output_dir=output_dir, device=device, **kwargs
        )

    @classmethod
    def _create_decision_bc_trainer(
        cls, config: DictConfig, output_dir: str, device: torch.device, **kwargs
    ) -> DecisionBCTrainer:
        """创建决策层BC训练器"""
        required_params = ["policy", "dataloader", "optimizer"]
        cls._validate_required_params(required_params, kwargs)

        logger.info("Creating DecisionBCTrainer")
        return DecisionBCTrainer(
            config=config, output_dir=output_dir, device=device, **kwargs
        )

    @classmethod
    def _create_reward_model_trainer(
        cls, config: DictConfig, output_dir: str, device: torch.device, **kwargs
    ) -> RewardModelTrainer:
        """创建奖励模型训练器"""
        required_params = ["dataloader"]
        cls._validate_required_params(required_params, kwargs)

        logger.info("Creating RewardModelTrainer")
        return RewardModelTrainer(
            config=config, output_dir=output_dir, device=device, **kwargs
        )

    @classmethod
    def _create_rl_trainer(
        cls, config: DictConfig, output_dir: str, device: torch.device, **kwargs
    ) -> HierarchicalRLTrainer:
        """创建RL训练器"""
        required_params = ["env", "eval_env", "cfg"]
        cls._validate_required_params(required_params, kwargs)

        logger.info("Creating HierarchicalRLTrainer")
        return HierarchicalRLTrainer(
            config=config, output_dir=output_dir, device=device, **kwargs
        )

    @classmethod
    def _validate_required_params(cls, required_params: list, kwargs: dict):
        """验证必需参数"""
        missing_params = [param for param in required_params if param not in kwargs]
        if missing_params:
            raise ValueError(f"Missing required parameters: {missing_params}")

    @classmethod
    def get_available_trainers(cls) -> Dict[str, Type[BaseTrainer]]:
        """获取可用的训练器类型

        Returns:
            训练器类型字典
        """
        return cls._trainers.copy()

    @classmethod
    def is_trainer_available(cls, trainer_type: str) -> bool:
        """检查训练器类型是否可用

        Args:
            trainer_type: 训练器类型

        Returns:
            是否可用
        """
        return trainer_type in cls._trainers


# 自动注册训练器的装饰器
def register_trainer(name: str):
    """训练器注册装饰器

    Args:
        name: 训练器名称
    """

    def decorator(trainer_class: Type[BaseTrainer]):
        TrainerFactory.register_trainer(name, trainer_class)
        return trainer_class

    return decorator


# 导入并注册具体的训练器实现
def _auto_register_trainers():
    """自动注册训练器"""
    try:
        # 导入BC训练器
        from .bc_trainer import ExecutionBCTrainer, DecisionBCTrainer

        TrainerFactory.register_trainer("execution_bc", ExecutionBCTrainer)
        TrainerFactory.register_trainer("decision_bc", DecisionBCTrainer)

    except ImportError as e:
        logger.warning(f"BC训练器导入失败: {e}")

    try:
        # 导入Reward Model训练器
        from .reward_trainer import RewardModelTrainer

        TrainerFactory.register_trainer("reward_model", RewardModelTrainer)

    except ImportError as e:
        logger.warning(f"Reward Model训练器导入失败: {e}")

    try:
        # 导入RL训练器
        from .rl_trainer import HierarchicalRLTrainer

        TrainerFactory.register_trainer("hierarchical_rl", HierarchicalRLTrainer)

    except ImportError as e:
        logger.warning(f"RL训练器导入失败: {e}")


# 在模块加载时自动注册
_auto_register_trainers()


class TrainerConfig:
    """训练器配置辅助类"""

    @staticmethod
    def get_bc_config(config: DictConfig, training_phase: str) -> Dict[str, Any]:
        """获取BC训练配置

        Args:
            config: 原始配置
            training_phase: 训练阶段

        Returns:
            BC训练配置
        """
        if training_phase.startswith("execution"):
            policy_config = config.agent.get("execution_policy", {})
            epochs = config.training.get("execution_epochs", 10)
            batch_size = config.training.get(
                "execution_batch_size", config.training.batch_size
            )
        elif training_phase.startswith("decision"):
            policy_config = config.agent.get("decision_policy", {})
            epochs = config.training.get("decision_epochs", 10)
            batch_size = config.training.get(
                "decision_batch_size", config.training.batch_size
            )
        else:
            raise ValueError(f"无效的BC训练阶段: {training_phase}")

        return {
            "policy_config": policy_config,
            "epochs": epochs,
            "batch_size": batch_size,
            "learning_rate": policy_config.get(
                "learning_rate", config.agent.learning_rate
            ),
        }

    @staticmethod
    def get_rl_config(config: DictConfig) -> Dict[str, Any]:
        """获取RL训练配置

        Args:
            config: 原始配置

        Returns:
            RL训练配置
        """
        return {
            "n_timesteps": config.training.n_timesteps,
            "save_freq": config.training.save_freq,
            "eval_freq": config.training.eval_freq,
            "n_eval_episodes": config.training.n_eval_episodes,
            "use_vec_normalize": config.training.get("use_vec_normalize", False),
        }

    @staticmethod
    def get_reward_model_config(config: DictConfig) -> Dict[str, Any]:
        """获取Reward Model训练配置

        Args:
            config: 原始配置

        Returns:
            Reward Model训练配置
        """
        reward_config = config.agent.get("reward_model", {})
        return {
            "epochs": reward_config.get("epochs", 100),
            "batch_size": reward_config.get("batch_size", 32),
            "learning_rate": reward_config.get("model_kwargs", {}).get(
                "learning_rate", 0.001
            ),
            "model_kwargs": reward_config.get("model_kwargs", {}),
        }

    @staticmethod
    def get_supported_phases() -> list:
        """获取支持的训练阶段"""
        return [phase.value for phase in TrainingPhase]

    @staticmethod
    def create_bc_trainer_from_config(
        cfg, policy, dataloader, optimizer, device, **kwargs
    ) -> BaseTrainer:
        """从配置创建BC训练器"""
        training_phase = cfg.training.training_phase

        # 准备训练器参数
        trainer_kwargs = {
            "policy": policy,
            "dataloader": dataloader,
            "optimizer": optimizer,
            "device": device,
            **kwargs,
        }

        # 添加配置特定参数
        if training_phase in [
            TrainingPhase.EXECUTION_BC_TRAIN.value,
            TrainingPhase.EXECUTION_BC_ACTOR_TRAIN.value,
        ]:
            # 执行层BC特定配置
            exec_policy_config = cfg.agent.get("execution_policy", {})
            trainer_kwargs.update(
                {
                    "enable_conditional": exec_policy_config.get(
                        "policy_kwargs", {}
                    ).get("conditional_pose_dim", 0)
                    > 0,
                    "enable_single_hand": exec_policy_config.get(
                        "policy_kwargs", {}
                    ).get("enable_single_hand", False),
                    "single_hand_config": exec_policy_config.get(
                        "policy_kwargs", {}
                    ).get("single_hand_config", {}),
                    "enable_action_scaling": exec_policy_config.get(
                        "policy_kwargs", {}
                    ).get("enable_action_scaling", True),
                    "obs_normalization": exec_policy_config.get(
                        "policy_kwargs", {}
                    ).get("obs_normalization", "standard"),
                    "action_scaling": exec_policy_config.get("policy_kwargs", {}).get(
                        "action_scaling", "minmax"
                    ),
                    "action_clip_range": tuple(
                        exec_policy_config.get("policy_kwargs", {}).get(
                            "action_clip_range", [-1.0, 1.0]
                        )
                    ),
                    "enable_separated_normalization": exec_policy_config.get(
                        "policy_kwargs", {}
                    ).get("enable_separated_normalization", True),
                    "target_pose_normalization": exec_policy_config.get(
                        "policy_kwargs", {}
                    ).get("target_pose_normalization", "standard"),
                }
            )

        elif training_phase == TrainingPhase.DECISION_BC_TRAIN.value:
            # 决策层BC特定配置
            decision_policy_config = cfg.agent.get("decision_policy", {})
            trainer_kwargs.update(
                {
                    "num_candidates": decision_policy_config.get("num_candidates", 10),
                    "seq_length": cfg.env.seq_length,
                    "target_hand": cfg.env.get("target_hand", "left"),
                }
            )

        return TrainerFactory.create_trainer(
            training_phase, cfg, cfg.output_dir, device, **trainer_kwargs
        )

    @staticmethod
    def create_reward_model_trainer_from_config(
        cfg, dataloader, device, output_dir, **kwargs
    ) -> RewardModelTrainer:
        """从配置创建奖励模型训练器"""
        reward_model_config = cfg.agent.get("reward_model", {})

        trainer_kwargs = {
            "dataloader": dataloader,
            "device": device,
            "output_dir": output_dir,
            "model_kwargs": reward_model_config.get("model_kwargs", {}),
            "seq_length": cfg.env.seq_length,
            "target_hand": cfg.env.get("target_hand", "left"),
            "batch_size": reward_model_config.get("batch_size", 32),
            "learning_rate": reward_model_config.get("model_kwargs", {}).get(
                "learning_rate", 0.001
            ),
            **kwargs,
        }

        return TrainerFactory.create_trainer(
            TrainingPhase.REWARD_MODEL_PRETRAIN.value,
            cfg,
            output_dir,
            device,
            **trainer_kwargs,
        )

    @staticmethod
    def create_rl_trainer_from_config(
        cfg, env, eval_env, output_dir, device, **kwargs
    ) -> HierarchicalRLTrainer:
        """从配置创建RL训练器"""
        trainer_kwargs = {
            "env": env,
            "eval_env": eval_env,
            "cfg": cfg,
            "output_dir": output_dir,
            "device": device,
            **kwargs,
        }

        return TrainerFactory.create_trainer(
            TrainingPhase.JOINT_RL.value, cfg, output_dir, device, **trainer_kwargs
        )
