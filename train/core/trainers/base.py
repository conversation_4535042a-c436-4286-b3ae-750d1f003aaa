"""训练器基类模块

定义统一的训练器接口和基础功能。
从原始1862行脚本中抽取训练器相关逻辑。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable
from pathlib import Path

import torch
from omegaconf import DictConfig

logger = logging.getLogger(__name__)


class BaseTrainer(ABC):
    """训练器基类

    定义所有训练器的统一接口，包括：
    - 训练流程控制
    - 模型保存和加载
    - 日志记录
    - 回调机制
    """

    def __init__(
        self,
        config: DictConfig,
        output_dir: str,
        device: torch.device,
        custom_logger: Optional[Any] = None,
    ):
        self.config = config
        self.output_dir = Path(output_dir)
        self.device = device
        self.custom_logger = custom_logger

        # 训练状态
        self.is_training = False
        self.current_epoch = 0
        self.total_epochs = 0

        # 回调函数
        self.callbacks = {}

        logger.info(f"初始化{self.__class__.__name__}")

    @abstractmethod
    def setup_model(self) -> None:
        """设置模型（子类必须实现）"""
        pass

    @abstractmethod
    def setup_data(self) -> None:
        """设置数据加载器（子类必须实现）"""
        pass

    @abstractmethod
    def train_epoch(self, epoch: int) -> Dict[str, float]:
        """训练一个epoch（子类必须实现）

        Args:
            epoch: 当前epoch编号

        Returns:
            训练指标字典
        """
        pass

    def train(
        self,
        n_epochs: int,
        log_interval: int = 100,
        save_interval: Optional[int] = None,
        on_epoch_end: Optional[Callable] = None,
    ) -> Dict[str, Any]:
        """执行完整训练流程

        Args:
            n_epochs: 训练轮数
            log_interval: 日志记录间隔
            save_interval: 模型保存间隔
            on_epoch_end: epoch结束回调函数

        Returns:
            训练统计信息
        """
        logger.info(f"开始训练: {n_epochs} epochs")

        self.total_epochs = n_epochs
        self.is_training = True

        # 设置模型和数据
        self.setup_model()
        self.setup_data()

        # 训练统计
        training_stats = {
            "total_epochs": n_epochs,
            "total_batches": 0,
            "average_loss": 0.0,
            "epoch_losses": [],
        }

        try:
            for epoch in range(n_epochs):
                self.current_epoch = epoch

                # 执行epoch训练
                epoch_metrics = self.train_epoch(epoch)

                # 记录统计信息
                if "loss" in epoch_metrics:
                    training_stats["epoch_losses"].append(epoch_metrics["loss"])

                # 执行回调
                if on_epoch_end:
                    on_epoch_end()

                # 保存检查点
                if save_interval and (epoch + 1) % save_interval == 0:
                    self.save_checkpoint(epoch)

                # 记录进度
                if (epoch + 1) % max(1, n_epochs // 10) == 0:
                    progress = (epoch + 1) / n_epochs * 100
                    logger.info(f"训练进度: {progress:.1f}% ({epoch + 1}/{n_epochs})")

        except KeyboardInterrupt:
            logger.warning("训练被用户中断")
        except Exception as e:
            logger.error(f"训练过程中出错: {e}", exc_info=True)
            raise
        finally:
            self.is_training = False

        # 计算最终统计
        if training_stats["epoch_losses"]:
            training_stats["average_loss"] = sum(training_stats["epoch_losses"]) / len(
                training_stats["epoch_losses"]
            )

        logger.info(f"训练完成: 平均损失={training_stats['average_loss']:.6f}")
        return training_stats

    def save_model(self, save_path: str) -> None:
        """保存模型（子类可重写）

        Args:
            save_path: 保存路径
        """
        logger.info(f"保存模型到: {save_path}")
        # 默认实现，子类应重写
        pass

    def load_model(self, load_path: str) -> None:
        """加载模型（子类可重写）

        Args:
            load_path: 加载路径
        """
        logger.info(f"从 {load_path} 加载模型")
        # 默认实现，子类应重写
        pass

    def save_checkpoint(self, epoch: int) -> None:
        """保存训练检查点

        Args:
            epoch: 当前epoch
        """
        checkpoint_dir = self.output_dir / "checkpoints"
        checkpoint_dir.mkdir(exist_ok=True)

        checkpoint_path = checkpoint_dir / f"checkpoint_epoch_{epoch}.pth"

        checkpoint_data = {
            "epoch": epoch,
            "config": self.config,
            "trainer_state": self.get_trainer_state(),
        }

        try:
            torch.save(checkpoint_data, checkpoint_path)
            logger.info(f"检查点已保存: {checkpoint_path}")
        except Exception as e:
            logger.error(f"保存检查点失败: {e}")

    def get_trainer_state(self) -> Dict[str, Any]:
        """获取训练器状态（子类可重写）

        Returns:
            训练器状态字典
        """
        return {
            "current_epoch": self.current_epoch,
            "total_epochs": self.total_epochs,
            "is_training": self.is_training,
        }

    def load_checkpoint(self, checkpoint_path: str) -> Dict[str, Any]:
        """加载训练检查点

        Args:
            checkpoint_path: 检查点路径

        Returns:
            检查点数据
        """
        try:
            checkpoint_data = torch.load(checkpoint_path, map_location=self.device)

            self.current_epoch = checkpoint_data.get("epoch", 0)
            trainer_state = checkpoint_data.get("trainer_state", {})

            self.restore_trainer_state(trainer_state)

            logger.info(f"检查点加载成功: {checkpoint_path}")
            return checkpoint_data

        except Exception as e:
            logger.error(f"加载检查点失败: {e}")
            raise

    def restore_trainer_state(self, state: Dict[str, Any]) -> None:
        """恢复训练器状态（子类可重写）

        Args:
            state: 训练器状态字典
        """
        self.current_epoch = state.get("current_epoch", 0)
        self.total_epochs = state.get("total_epochs", 0)
        self.is_training = state.get("is_training", False)

    def register_callback(self, name: str, callback: Callable) -> None:
        """注册回调函数

        Args:
            name: 回调名称
            callback: 回调函数
        """
        self.callbacks[name] = callback
        logger.debug(f"注册回调函数: {name}")

    def call_callback(self, name: str, *args, **kwargs) -> Any:
        """调用回调函数

        Args:
            name: 回调名称
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            回调函数返回值
        """
        if name in self.callbacks:
            try:
                return self.callbacks[name](*args, **kwargs)
            except Exception as e:
                logger.error(f"回调函数 {name} 执行失败: {e}")
        return None

    def log_metrics(
        self, metrics: Dict[str, float], step: Optional[int] = None
    ) -> None:
        """记录训练指标

        Args:
            metrics: 指标字典
            step: 训练步数
        """
        if self.custom_logger:
            for key, value in metrics.items():
                try:
                    self.custom_logger.record(key, value)
                except Exception as e:
                    logger.warning(f"记录指标 {key} 失败: {e}")

        # 同时记录到标准日志
        metrics_str = ", ".join([f"{k}={v:.6f}" for k, v in metrics.items()])
        step_str = f" (step {step})" if step is not None else ""
        logger.info(f"训练指标{step_str}: {metrics_str}")

    def get_training_progress(self) -> Dict[str, Any]:
        """获取训练进度信息

        Returns:
            训练进度字典
        """
        if self.total_epochs == 0:
            progress_percent = 0.0
        else:
            progress_percent = (self.current_epoch + 1) / self.total_epochs * 100

        return {
            "current_epoch": self.current_epoch,
            "total_epochs": self.total_epochs,
            "progress_percent": progress_percent,
            "is_training": self.is_training,
        }

    def validate(self) -> Dict[str, float]:
        """执行验证（子类可重写）

        Returns:
            验证指标字典
        """
        logger.info("执行验证...")
        # 默认实现，子类应重写
        return {}

    def cleanup(self) -> None:
        """清理资源（子类可重写）"""
        logger.info("清理训练器资源")
        self.is_training = False
