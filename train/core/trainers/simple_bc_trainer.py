"""简化版BC训练器"""

import os
import logging
import torch
import numpy as np
from typing import Dict, Any, Optional, Callable

logger = logging.getLogger(__name__)


class SimpleBCTrainer:
    """简化版BC训练器 - 去除不必要的继承和抽象"""

    def __init__(
        self,
        policy,
        dataloader,
        optimizer,
        device,
        custom_logger=None,
        model_save_path=None,
        **training_config,
    ):
        # 核心组件
        self.policy = policy
        self.dataloader = dataloader
        self.optimizer = optimizer
        self.device = device
        self.custom_logger = custom_logger
        self.model_save_path = model_save_path

        # 训练配置（扁平化，避免多层嵌套）
        self.enable_conditional = training_config.get("enable_conditional", True)
        self.enable_single_hand = training_config.get("enable_single_hand", False)
        self.debug = training_config.get("debug", False)

        logger.info(f"SimpleBCTrainer初始化完成")

    def train(self, n_epochs: int, log_interval: int = 500) -> Dict[str, Any]:
        """执行训练 - 统一的训练接口"""

        if self.enable_conditional:
            return self._train_conditional(n_epochs, log_interval)
        else:
            return self._train_standard(n_epochs, log_interval)

    def _train_conditional(self, n_epochs: int, log_interval: int) -> Dict[str, Any]:
        """条件BC训练"""
        try:
            from px_janus_learnsim.learning.trainers import ConditionalBCTrainer

            trainer = ConditionalBCTrainer(
                policy=self.policy,
                dataloader=self.dataloader,
                optimizer=self.optimizer,
                device=self.device,
                custom_logger=self.custom_logger,
                enable_single_hand=self.enable_single_hand,
                debug=self.debug,
                model_save_path=self.model_save_path,
            )

            return trainer.train(n_epochs=n_epochs, log_interval=log_interval)

        except ImportError:
            logger.warning("ConditionalBCTrainer不可用，回退到标准BC训练")
            return self._train_standard(n_epochs, log_interval)

    def _train_standard(self, n_epochs: int, log_interval: int) -> Dict[str, Any]:
        """标准BC训练"""
        from imitation.algorithms import bc
        from imitation.util.util import save_policy
        from ...utils.helpers import convert_dataloader_to_transitions

        # 转换数据
        execution_transitions, _ = convert_dataloader_to_transitions(
            self.dataloader, self.device, seq_length=15
        )

        # 创建并执行训练
        bc_trainer = bc.BC(
            observation_space=self.policy.observation_space,
            action_space=self.policy.action_space,
            policy=self.policy,
            demonstrations=execution_transitions,
            rng=np.random.default_rng(42),
            batch_size=32,
            custom_logger=self.custom_logger,
        )

        bc_trainer.train(n_epochs=n_epochs, log_interval=log_interval)

        # 保存模型
        if self.model_save_path:
            save_policy(bc_trainer.policy, self.model_save_path)

        return {"total_epochs": n_epochs, "average_loss": 0.0}
