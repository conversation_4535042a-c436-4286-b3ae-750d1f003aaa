"""训练配置管理模块

统一处理Hydra配置、环境配置、训练参数验证等功能。
从原始1862行脚本中抽取配置相关逻辑。
"""

import os
import time
import random
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import yaml

import torch
from omegaconf import DictConfig, OmegaConf
from stable_baselines3.common.utils import set_random_seed
from isaaclab.utils.io import dump_yaml

# 使用相对导入，从项目根目录导入env模块
import sys
import os

# 确保项目根目录在Python路径中
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from env.env_config_utils import (
    get_environment_config,
    apply_training_config_overrides,
    save_evaluation_config,
)

logger = logging.getLogger(__name__)


class TrainingConfigManager:
    """训练配置管理器

    负责统一处理所有配置相关逻辑，包括：
    - Hydra配置验证和解析
    - 环境配置加载和应用
    - 训练参数验证
    - 设备和种子设置
    - 配置保存和恢复
    """

    def __init__(self, cfg: DictConfig, output_dir: str):
        self.cfg = cfg
        self.output_dir = output_dir
        self.resolved_cfg = None
        self.env_cfg = None
        self.actual_seed = None

        logger.info("初始化训练配置管理器")

    def setup_seed(self) -> int:
        """设置随机种子"""
        self.actual_seed = self.cfg.app.seed
        if self.cfg.app.seed == -1:
            self.actual_seed = random.randint(0, 10000)
            logger.info(f"生成随机种子: {self.actual_seed}")

        set_random_seed(self.actual_seed)
        logger.info(f"使用种子: {self.actual_seed}")
        return self.actual_seed

    def setup_environment_config(self) -> Tuple[Any, Any]:
        """设置环境配置

        Returns:
            Tuple[EnvCfg_class, env_cfg_instance]: 环境配置类和实例
        """
        logger.info("设置环境配置...")

        # 动态导入环境配置
        env_name = self.cfg.env.name
        EnvCfg, env_cfg = get_environment_config(env_name, self.cfg)

        logger.info(f"使用环境: {env_name} 配置类: {EnvCfg.__name__}")

        # 应用训练配置覆盖
        env_cfg = apply_training_config_overrides(env_cfg, self.cfg)

        # 设置环境参数
        env_cfg.scene.num_envs = (
            self.cfg.env.num_envs
            if self.cfg.env.num_envs is not None
            else env_cfg.scene.num_envs
        )
        env_cfg.seed = self.actual_seed
        env_cfg.sim.device = (
            self.cfg.app.device
            if self.cfg.app.device is not None
            else env_cfg.sim.device
        )

        self.env_cfg = env_cfg

        logger.info(
            f"环境配置: frames_to_stack={env_cfg.num_frames_to_stack}, "
            f"base_obs={env_cfg.base_observation_space}, "
            f"total_obs={env_cfg.observation_space}"
        )

        return EnvCfg, env_cfg

    def validate_training_phase(self) -> bool:
        """验证训练阶段配置的一致性"""
        training_phase = self.cfg.training.training_phase
        training_mode = self.cfg.training.get("training_mode", "bc")

        # 检查不一致的配置
        if training_phase == "joint_rl" and training_mode == "bc":
            logger.error(
                f"配置不一致: training_phase='{training_phase}' "
                f"但 training_mode='{training_mode}'"
            )
            return False

        # 验证BC训练阶段
        bc_phases = [
            "decision_bc_train",
            "execution_bc_train",
            "execution_bc_actor_train",
        ]
        if training_phase in bc_phases:
            if not self.cfg.training.get("dataset_dir"):
                logger.error(f"BC训练阶段 '{training_phase}' 需要指定 dataset_dir")
                return False

        logger.info(f"训练阶段验证通过: {training_phase}")
        return True

    def resolve_and_save_configs(self) -> Dict[str, Any]:
        """解析并保存所有配置"""
        try:
            # 解析所有配置引用（如${env.seq_length}）为实际数值
            self.resolved_cfg = OmegaConf.to_container(
                self.cfg, resolve=True, throw_on_missing=True
            )

            # 保存解析后的配置
            resolved_config_path = os.path.join(self.output_dir, "resolved_config.yaml")
            with open(resolved_config_path, "w") as f:
                yaml.dump(self.resolved_cfg, f, default_flow_style=False, indent=2)
            logger.info(f"保存完全解析的配置到: {resolved_config_path}")

            # 保存原始配置
            original_config_path = os.path.join(self.output_dir, "original_config.yaml")
            OmegaConf.save(config=self.cfg, f=original_config_path)
            logger.info(f"保存原始配置到: {original_config_path}")

            # 保存环境配置
            if self.env_cfg is not None:
                env_cfg_save_path = os.path.join(
                    self.output_dir, "env_config_applied.yaml"
                )
                dump_yaml(env_cfg_save_path, self.env_cfg)
                logger.info(f"保存应用的环境配置到: {env_cfg_save_path}")

            # 打印关键解析信息
            self._log_key_config_info()

            return self.resolved_cfg

        except Exception as e:
            logger.error(f"保存解析配置失败: {e}")
            # 降级保存原始配置
            fallback_path = os.path.join(self.output_dir, "effective_config.yaml")
            OmegaConf.save(config=self.cfg, f=fallback_path)
            logger.info(f"降级保存原始配置到: {fallback_path}")
            return OmegaConf.to_container(self.cfg, resolve=False)

    def _log_key_config_info(self):
        """记录关键配置信息"""
        if not self.resolved_cfg:
            return

        logger.info("🔍 关键配置解析结果:")
        logger.info(f"  - env.seq_length: {self.resolved_cfg['env']['seq_length']}")

        # 记录policy_kwargs信息
        agent_config = self.resolved_cfg.get("agent", {})
        if "policy_kwargs" in agent_config:
            num_queries = agent_config["policy_kwargs"].get("num_queries", "N/A")
            logger.info(f"  - agent.policy_kwargs.num_queries: {num_queries}")

        # 记录execution_policy信息
        if "execution_policy" in agent_config:
            exec_policy = agent_config["execution_policy"]
            if "policy_kwargs" in exec_policy:
                exec_num_queries = exec_policy["policy_kwargs"].get(
                    "num_queries", "N/A"
                )
                logger.info(
                    f"  - agent.execution_policy.policy_kwargs.num_queries: {exec_num_queries}"
                )

    def save_training_metadata(self, training_type: str, **extra_metadata):
        """保存训练元数据"""
        if not self.env_cfg or not self.resolved_cfg:
            logger.warning("环境配置或解析配置未准备好，跳过元数据保存")
            return

        metadata = {
            "source": f"{training_type}_training_script",
            "env_name": self.cfg.env.name,
            "seq_length": self.resolved_cfg["env"]["seq_length"],
            "frames_to_stack": self.env_cfg.num_frames_to_stack,
            "observation_space": self.env_cfg.observation_space,
            "base_observation_space": self.env_cfg.base_observation_space,
            "num_envs": self.env_cfg.scene.num_envs,
            "timestamp": time.strftime("%Y-%m-%d_%H-%M-%S"),
            "seed": self.actual_seed,
            **extra_metadata,
        }

        try:
            save_evaluation_config(self.env_cfg, self.output_dir, metadata)
            logger.info(f"保存{training_type}训练元数据成功")
        except Exception as e:
            logger.warning(f"保存{training_type}训练元数据失败: {e}")

    def get_device_config(self) -> str:
        """获取设备配置"""
        return self.cfg.app.get("device", "auto")

    def get_training_phase(self) -> str:
        """获取训练阶段"""
        return self.cfg.training.training_phase

    def get_dataset_dir(self) -> Optional[str]:
        """获取数据集目录"""
        return self.cfg.training.get("dataset_dir")

    def is_swanlab_enabled(self) -> bool:
        """检查是否启用SwanLab"""
        return self.cfg.logging.get("use_swanlab", False)

    def get_swanlab_config(self) -> Dict[str, Any]:
        """获取SwanLab配置"""
        return {
            "project": self.cfg.logging.get(
                "swanlab_project_name", "px_tactrix_project"
            ),
            "workspace": self.cfg.logging.get("swanlab_workspace", None),
            "entity": self.cfg.logging.get("swanlab_entity", None),
            "experiment_name": self.cfg.logging.current_run_id,
            "description": self.cfg.logging.get(
                "experiment_description",
                "Behavioral Cloning training run for px_tactrix execution layer.",
            ),
        }
