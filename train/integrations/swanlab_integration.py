"""SwanLab集成模块"""

import os
import logging
import tempfile
from typing import Optional, Dict, Any
from pathlib import Path

try:
    import swanlab
    from stable_baselines3.common import logger as sb3_base_logger
    from imitation.util import logger as imitation_logger

    SWANLAB_AVAILABLE = True
except ImportError:
    SWANLAB_AVAILABLE = False
    swanlab = None
    sb3_base_logger = None
    imitation_logger = None

logger = logging.getLogger(__name__)


class SwanlabBCLogger(imitation_logger.HierarchicalLogger):
    """自定义SwanLab BC Logger"""

    def __init__(self, *args, **kwargs):
        if not SWANLAB_AVAILABLE:
            raise ImportError("SwanLab dependencies not available")

        logger.info("SwanlabBCLogger.__init__: Entered")

        # 创建临时目录用于默认logger
        temp_dir_for_default_logger = None
        if "folder" not in kwargs or kwargs["folder"] is None:
            temp_dir_for_default_logger = tempfile.mkdtemp(
                prefix="swanlab_bc_logger_default_"
            )
            actual_folder = temp_dir_for_default_logger
            logger.info(f"Created temporary folder for default_logger: {actual_folder}")
        else:
            actual_folder = kwargs["folder"]

        actual_format_strs = kwargs.get("format_strs", ["stdout"])

        # 创建HierarchicalLogger期望的default_logger
        sb3_output_formats = [
            imitation_logger.make_output_format(f, str(actual_folder))
            for f in actual_format_strs
            if f != "wandb"
        ]

        internal_default_sb3_logger = sb3_base_logger.Logger(
            folder=str(actual_folder), output_formats=sb3_output_formats
        )

        # 初始化父类
        super().__init__(
            default_logger=internal_default_sb3_logger,
            format_strs=actual_format_strs,
        )

        self._target_summary_provider_logger = self.default_logger
        self._swanlab_actual_batch_count = 0
        self._epoch_losses = []
        self._internal_epoch_count = 0
        self._interval_losses = []

    def record(self, key: str, value: Any):
        """记录训练指标"""
        logger.debug(
            f"SwanlabBCLogger.record: key='{key}', value='{value}' (type: {type(value)})"
        )

        # 记录所有"bc/"前缀的键到SwanLab
        if key.startswith("bc/") and swanlab is not None:
            log_val_for_swanlab = None
            try:
                if hasattr(value, "item"):  # torch.Tensor
                    log_val_for_swanlab = value.item()
                elif isinstance(value, (float, int)):
                    log_val_for_swanlab = float(value)
                else:
                    logger.debug(
                        f"Value for key '{key}' not convertible to float for SwanLab"
                    )

                if log_val_for_swanlab is not None:
                    swanlab.log(
                        {key: log_val_for_swanlab},
                        step=self._swanlab_actual_batch_count,
                    )
                    logger.debug(
                        f"Logged to SwanLab '{key}': {log_val_for_swanlab:.4f}"
                    )

            except Exception as e:
                logger.error(f"Error logging key '{key}' to SwanLab: {e}")

        # 特殊处理"bc/loss"用于聚合
        if key == "bc/loss":
            try:
                if hasattr(value, "item"):
                    loss_value = value.item()
                elif isinstance(value, (float, int)):
                    loss_value = float(value)
                else:
                    loss_value = None

                if loss_value is not None:
                    self._epoch_losses.append(loss_value)
                    self._interval_losses.append(loss_value)

                if swanlab is not None:
                    self._swanlab_actual_batch_count += 1
            except Exception as e:
                logger.warning(f"Could not extract loss value for aggregation: {e}")

        super().record(key, value)

    def record_mean(self, key: str, value: Any, exclude: Any = None):
        """记录平均值"""
        logger.debug(f"SwanlabBCLogger.record_mean: key='{key}', value='{value}'")
        super().record_mean(key, value, exclude)

    def dump(self, step: int = 0):
        """输出日志"""
        super().dump(step)
        if swanlab is not None and self._interval_losses:
            self._interval_losses = []  # 重置间隔损失

    def log_epoch_summary(self):
        """记录epoch摘要"""
        if swanlab is None:
            return

        if self._epoch_losses:
            epoch_avg_loss = sum(self._epoch_losses) / len(self._epoch_losses)
            logger.info(
                f"Epoch {self._internal_epoch_count} summary - Avg Loss: {epoch_avg_loss:.4f}"
            )

        self._epoch_losses = []
        self._internal_epoch_count += 1


def initialize_swanlab(cfg: Any, output_dir: str) -> bool:
    """
    初始化SwanLab

    Args:
        cfg: 配置对象
        output_dir: 输出目录

    Returns:
        是否成功初始化
    """
    if not SWANLAB_AVAILABLE:
        logger.warning("SwanLab not available, skipping initialization")
        return False

    if not cfg.logging.get("use_swanlab", False):
        logger.info("SwanLab disabled in config")
        return False

    try:
        logger.info("Initializing SwanLab...")

        current_run_name = cfg.logging.current_run_id
        logger.info(f"SwanLab experiment name: {current_run_name}")

        swanlab.init(
            project=cfg.logging.get("swanlab_project_name", "px_tactrix_project"),
            workspace=cfg.logging.get("swanlab_workspace", None),
            entity=cfg.logging.get("swanlab_entity", None),
            experiment_name=current_run_name,
            description=cfg.logging.get(
                "experiment_description",
                "Behavioral Cloning training run for px_tactrix execution layer.",
            ),
            config=cfg if hasattr(cfg, "__dict__") else dict(cfg),
            logdir=str(Path(output_dir) / "swanlog"),
            mode="cloud",
        )

        logger.info(
            f"SwanLab initialized successfully for project: {cfg.logging.get('swanlab_project_name', 'px_tactrix_project')}"
        )
        return True

    except Exception as e:
        logger.error(f"Failed to initialize SwanLab: {e}")
        return False


def finish_swanlab():
    """结束SwanLab会话"""
    if SWANLAB_AVAILABLE and swanlab is not None:
        try:
            swanlab.finish()
            logger.info("SwanLab session finished successfully")
        except Exception as e:
            logger.error(f"Error finishing SwanLab session: {e}")


def log_to_swanlab(metrics: Dict[str, Any], step: Optional[int] = None):
    """记录指标到SwanLab"""
    if SWANLAB_AVAILABLE and swanlab is not None:
        try:
            swanlab.log(metrics, step=step)
        except Exception as e:
            logger.error(f"Error logging to SwanLab: {e}")
