"""
Train模块 - 模型训练框架

提供统一的训练接口，支持多种训练模式：
- BC训练 (Behavioral Cloning)
- RL训练 (Reinforcement Learning)
- Reward模型训练

特点：
- 模块化架构，易于扩展
- 统一的配置管理
- 完整的日志和监控系统
- SwanLab实验跟踪集成
"""

__version__ = "1.0.0"
__author__ = "AI Assistant & 用户"

# 导出主要接口
from .cli.main import main, cli_main

# 导出配置管理
from .config.manager import TrainingConfigManager

# 导出工具类
from .utils.schedules import (
    ConstantSchedulePicklable,
    LinearSchedulePicklable,
    ExponentialSchedulePicklable,
    create_schedule,
)

__all__ = [
    # 主要接口
    "main",
    "cli_main",
    # 配置管理
    "TrainingConfigManager",
    # 工具类
    "ConstantSchedulePicklable",
    "LinearSchedulePicklable",
    "ExponentialSchedulePicklable",
    "create_schedule",
]
