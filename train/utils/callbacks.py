# 新建回调工具
from __future__ import annotations

import os
from collections import deque
from pathlib import Path
from typing import List, Optional

from imitation.util.util import save_policy


class BaseCallback:
    """训练过程通用回调基类

    子类可重写对应钩子方法。Trainer 应在合适的时机调用。
    """

    def on_epoch_end(self, trainer, epoch: int):  # noqa: D401
        """Called at the end of every epoch."""

    def on_batch_end(self, trainer, global_step: int):  # noqa: D401
        """Called at the end of every optimisation step (batch)."""


class CheckpointCallback(BaseCallback):
    """按 epoch / step 周期性保存策略权重的回调。

    只保存 `trainer.policy`（等价于 `save_policy()` 行为），避免过大磁盘占用。
    若需保存 optimiser/scheduler，请在后续扩展。
    """

    def __init__(
        self,
        policy,
        output_dir: str | Path,
        save_epoch_freq: int = 0,
        save_step_freq: int = 0,
        max_to_keep: int = 5,
    ) -> None:
        self.policy = policy
        self.save_epoch_freq = int(save_epoch_freq)
        self.save_step_freq = int(save_step_freq)
        self.saved_paths: deque[Path] = deque(maxlen=max(1, max_to_keep))

        self.ckpt_dir = Path(output_dir).expanduser().resolve() / "checkpoints_bc"
        self.ckpt_dir.mkdir(parents=True, exist_ok=True)

    # ---------------------------------------------------------------------
    # Hook implementations
    # ---------------------------------------------------------------------
    def on_epoch_end(self, trainer, epoch: int):  # noqa: D401
        if self.save_epoch_freq <= 0:
            return
        if (epoch + 1) % self.save_epoch_freq == 0:
            tag = f"epoch_{epoch + 1:04d}"
            self._save(tag)

    def on_batch_end(self, trainer, global_step: int):  # noqa: D401
        if self.save_step_freq <= 0:
            return
        if global_step % self.save_step_freq == 0:
            tag = f"step_{global_step:07d}"
            self._save(tag)

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------
    def _save(self, tag: str):
        ckpt_path = self.ckpt_dir / f"ckpt_{tag}.pth"
        try:
            save_policy(self.policy, str(ckpt_path))
        except Exception as e:
            # 不因保存失败中断训练，打印警告
            import logging

            logging.getLogger(__name__).warning(
                f"Checkpoint save failed at {ckpt_path}: {e}"
            )
            return

        self.saved_paths.append(ckpt_path)
        # 滚动删除最早的文件
        if (
            len(self.saved_paths) == self.saved_paths.maxlen
            and len(self.saved_paths) > 0
        ):
            oldest = self.saved_paths[0]
            if oldest.exists():
                try:
                    oldest.unlink()
                except Exception:
                    pass
