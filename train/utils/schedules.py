"""
学习率调度器工具
提供可序列化的学习率调度器类
"""


class ConstantSchedulePicklable:
    """
    可序列化的常数学习率调度器

    用于支持pickle序列化，解决SB3等库的序列化问题
    """

    def __init__(self, value: float):
        """
        初始化常数调度器

        Args:
            value: 常数学习率值
        """
        self.value = value

    def __call__(self, progress_remaining: float) -> float:
        """
        返回学习率值

        Args:
            progress_remaining: 剩余训练进度（0-1之间）

        Returns:
            学习率值
        """
        return self.value

    def __repr__(self):
        return f"ConstantSchedulePicklable(value={self.value})"


class LinearSchedulePicklable:
    """可序列化的线性学习率调度器"""

    def __init__(self, initial_value: float, final_value: float = 0.0):
        """
        初始化线性调度器

        Args:
            initial_value: 初始学习率
            final_value: 最终学习率
        """
        self.initial_value = initial_value
        self.final_value = final_value

    def __call__(self, progress_remaining: float) -> float:
        """
        计算当前学习率

        Args:
            progress_remaining: 剩余训练进度（1.0到0.0）

        Returns:
            当前学习率
        """
        return (
            self.final_value
            + (self.initial_value - self.final_value) * progress_remaining
        )

    def __repr__(self):
        return f"LinearSchedulePicklable(initial={self.initial_value}, final={self.final_value})"


class ExponentialSchedulePicklable:
    """可序列化的指数学习率调度器"""

    def __init__(self, initial_value: float, decay_rate: float = 0.99):
        """
        初始化指数调度器

        Args:
            initial_value: 初始学习率
            decay_rate: 衰减率
        """
        self.initial_value = initial_value
        self.decay_rate = decay_rate

    def __call__(self, progress_remaining: float) -> float:
        """
        计算当前学习率

        Args:
            progress_remaining: 剩余训练进度（1.0到0.0）

        Returns:
            当前学习率
        """
        progress_done = 1.0 - progress_remaining
        return self.initial_value * (self.decay_rate**progress_done)

    def __repr__(self):
        return f"ExponentialSchedulePicklable(initial={self.initial_value}, decay={self.decay_rate})"


# === Warm-up 调度器 ===
class LinearWarmupSchedulePicklable:
    """可序列化的线性 Warm-up 学习率调度器

    先在训练前期 (warmup_fraction) 将学习率从 init_value 线性提升到 target_value，
    其后交给指定的 after_schedule（可为常数/线性/指数）继续调度。

    Attributes
    ----------
    init_value : float
        Warm-up 起始学习率。
    target_value : float
        Warm-up 结束时的学习率，也作为后续调度器的初始值。
    warmup_fraction : float
        Warm-up 所占训练进度比例 (0-1)。
    after_schedule : Callable[[float], float]
        Warm-up 结束后调用的调度器，可由 ``create_schedule`` 创建。
    """

    def __init__(
        self,
        init_value: float,
        target_value: float,
        warmup_fraction: float = 0.05,
        after_type: str = "constant",
        after_kwargs: dict | None = None,
    ):
        self.init_value = float(init_value)
        self.target_value = float(target_value)
        # 确保 warmup_fraction 合理
        self.warmup_fraction = max(1e-9, min(float(warmup_fraction), 1.0))

        after_kwargs = after_kwargs or {}
        # 创建后续调度器（递归调用工厂函数）
        self.after_schedule = create_schedule(after_type, **after_kwargs)

    # pylint: disable=unused-argument
    def __call__(self, progress_remaining: float) -> float:
        """根据剩余进度返回当前学习率"""
        progress_done = 1.0 - progress_remaining

        if progress_done < self.warmup_fraction:
            alpha = progress_done / self.warmup_fraction
            return self.init_value + alpha * (self.target_value - self.init_value)

        # Warm-up 完成后，进度重新映射到 0-1 供 after_schedule 使用
        # 计算剩余部分的 progress_remaining_for_after
        remaining_total = 1.0 - self.warmup_fraction
        if remaining_total <= 0:
            return self.target_value

        # progress_remaining_for_after: 从 1 → 0
        # 当 progress_remaining == 1 - warmup_fraction 时应为 1
        progress_remaining_for_after = (1.0 - progress_done) / remaining_total
        return self.after_schedule(progress_remaining_for_after)

    def __repr__(self):
        return (
            "LinearWarmupSchedulePicklable(init="
            f"{self.init_value}, target={self.target_value}, warmup_fraction={self.warmup_fraction})"
        )


def create_schedule(schedule_type: str, **kwargs):
    """
    创建学习率调度器的工厂函数

    Args:
        schedule_type: 调度器类型 ('constant', 'linear', 'exponential', 'linear_warmup')
        **kwargs: 调度器参数

    Returns:
        调度器实例
    """
    if schedule_type == "constant":
        return ConstantSchedulePicklable(kwargs.get("value", 1e-3))
    elif schedule_type == "linear":
        return LinearSchedulePicklable(
            kwargs.get("initial_value", 1e-3), kwargs.get("final_value", 0.0)
        )
    elif schedule_type == "exponential":
        return ExponentialSchedulePicklable(
            kwargs.get("initial_value", 1e-3), kwargs.get("decay_rate", 0.99)
        )
    elif schedule_type == "linear_warmup":
        return LinearWarmupSchedulePicklable(
            kwargs.get("init_value", 0.0),
            kwargs.get("target_value", 1e-3),
            kwargs.get("warmup_fraction", 0.05),
            kwargs.get("after_type", "constant"),
            kwargs.get("after_kwargs", {}),
        )
    else:
        raise ValueError(f"不支持的调度器类型: {schedule_type}")
