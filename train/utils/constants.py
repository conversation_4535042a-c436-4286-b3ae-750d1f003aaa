"""训练相关的常量和枚举定义"""

from enum import Enum
from typing import Dict, Any


class TrainingPhase(Enum):
    """训练阶段枚举"""

    DECISION_BC_TRAIN = "decision_bc_train"
    EXECUTION_BC_TRAIN = "execution_bc_train"
    EXECUTION_BC_ACTOR_TRAIN = "execution_bc_actor_train"
    REWARD_MODEL_PRETRAIN = "reward_model_pretrain"
    JOINT_RL = "joint_rl"


class TrainingMode(Enum):
    """训练模式枚举"""

    BC = "bc"
    RL = "rl"


# 全局状态变量
SWANLAB_INITIALIZED = False

# 默认配置
DEFAULT_POLICY_KWARGS = {
    "tactile_shape": (16, 1),
    "num_queries": 15,  # 默认序列长度
}

# 模型类型映射
MODEL_TYPE_MAPPING = {
    "execution_bc_actor_train": "execution_bc_actor",
    "execution_bc_critic_train": "execution_bc_critic",
    "execution_joint_rl": "execution_rl_actor_critic",
    "decision_bc_train": "decision_bc_scoring",
    "decision_rl_train": "decision_rl_policy",
    "reward_model_pretrain": "reward_model",
    "joint_rl": "hierarchical_rl_full_system",
    "execution_bc_train": "execution_bc_actor",  # 向后兼容
}

# 日志配置
LOG_COLORS = {
    "DEBUG": "cyan",
    "INFO": "green",
    "WARNING": "yellow",
    "ERROR": "red",
    "CRITICAL": "red,bg_white",
}

# 训练器默认配置
TRAINER_DEFAULTS = {
    "bc_log_interval": 500,
    "checkpoint_save_freq": 10000,
    "eval_freq": 5000,
    "n_eval_episodes": 10,
}
