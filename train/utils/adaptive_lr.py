"""
自适应学习率工具模块

包含：
- 自适应优化器和调度器创建
- 学习率历史记录
- 验证损失监控
"""

import torch
import numpy as np
from typing import Tuple, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


def create_adaptive_optimizer_and_scheduler(
    policy, cfg
) -> Tuple[torch.optim.Optimizer, Optional[torch.optim.lr_scheduler._LRScheduler], str]:
    """
    创建自适应学习率优化器和调度器

    Args:
        policy: 要优化的策略模型
        cfg: 配置对象

    Returns:
        optimizer: 优化器
        scheduler: 学习率调度器（可能为None）
        scheduler_type: 调度器类型
    """
    # 获取基础学习率
    base_lr = cfg.agent.execution_policy.get("learning_rate", 1e-4)

    # 获取自适应配置
    adaptive_config = cfg.agent.execution_policy.policy_kwargs.get(
        "adaptive_lr_config", {}
    )

    # 获取权重衰减配置
    single_hand_config = cfg.agent.execution_policy.policy_kwargs.get(
        "single_hand_config", {}
    )
    weight_decay = single_hand_config.get("weight_decay", 1e-5)

    logger.info(
        f"🔧 创建自适应优化器: 初始学习率={base_lr:.2e}, 权重衰减={weight_decay:.2e}"
    )

    # 创建AdamW优化器
    optimizer = torch.optim.AdamW(
        policy.parameters(),
        lr=base_lr,
        weight_decay=weight_decay,
        betas=(0.9, 0.999),
        eps=1e-8,
    )

    # 获取调度器类型
    scheduler_type = adaptive_config.get("scheduler_type", "reduce_on_plateau")

    if scheduler_type == "reduce_on_plateau":
        scheduler = create_plateau_scheduler(optimizer, adaptive_config)
        logger.info("✅ 创建ReduceLROnPlateau调度器")
        return optimizer, scheduler, "plateau"

    elif scheduler_type == "cosine_annealing":
        scheduler = create_cosine_scheduler(optimizer, adaptive_config, cfg)
        logger.info("✅ 创建CosineAnnealingWarmRestarts调度器")
        return optimizer, scheduler, "cosine"

    elif scheduler_type == "step":
        scheduler = create_step_scheduler(optimizer, adaptive_config)
        logger.info("✅ 创建StepLR调度器")
        return optimizer, scheduler, "step"

    else:
        logger.info("ℹ️ 使用固定学习率（无调度器）")
        return optimizer, None, "none"


def create_plateau_scheduler(optimizer, adaptive_config):
    """创建基于验证损失plateau的调度器"""
    from torch.optim.lr_scheduler import ReduceLROnPlateau

    return ReduceLROnPlateau(
        optimizer,
        mode="min",
        factor=adaptive_config.get("factor", 0.7),
        patience=adaptive_config.get("patience", 5),
        min_lr=adaptive_config.get("min_lr", 1e-6),
        threshold=adaptive_config.get("threshold", 1e-4),
        threshold_mode="rel",
        cooldown=adaptive_config.get("cooldown", 2),
        verbose=True,
    )


def create_cosine_scheduler(optimizer, adaptive_config, cfg):
    """创建余弦退火调度器"""
    from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

    # 获取总epoch数
    total_epochs = cfg.training.get("execution_epochs", 100)
    T_0 = adaptive_config.get("T_0", max(10, total_epochs // 10))

    return CosineAnnealingWarmRestarts(
        optimizer,
        T_0=T_0,
        T_mult=adaptive_config.get("T_mult", 2),
        eta_min=adaptive_config.get("min_lr", 1e-6),
        last_epoch=-1,
    )


def create_step_scheduler(optimizer, adaptive_config):
    """创建阶梯式调度器"""
    from torch.optim.lr_scheduler import StepLR

    return StepLR(
        optimizer,
        step_size=adaptive_config.get("step_size", 30),
        gamma=adaptive_config.get("gamma", 0.7),
        last_epoch=-1,
    )


class LearningRateMonitor:
    """学习率变化监控器"""

    def __init__(self, initial_lr: float):
        self.initial_lr = initial_lr
        self.lr_history = []
        self.adjustments_count = 0

    def record(
        self, epoch: int, current_lr: float, train_loss: float, val_loss: float = None
    ):
        """记录学习率和损失历史"""
        record = {
            "epoch": epoch,
            "lr": current_lr,
            "train_loss": train_loss,
            "val_loss": val_loss,
            "lr_ratio": current_lr / self.initial_lr,
        }
        self.lr_history.append(record)

        # 检测学习率是否发生变化
        if len(self.lr_history) > 1:
            prev_lr = self.lr_history[-2]["lr"]
            if abs(current_lr - prev_lr) / prev_lr > 0.01:  # 变化超过1%
                self.adjustments_count += 1
                logger.info(
                    f"🔄 学习率调整 #{self.adjustments_count}: {prev_lr:.2e} → {current_lr:.2e}"
                )

    def get_stats(self) -> Dict[str, Any]:
        """获取学习率统计信息"""
        if not self.lr_history:
            return {}

        lrs = [record["lr"] for record in self.lr_history]
        return {
            "initial_lr": self.initial_lr,
            "final_lr": lrs[-1],
            "min_lr": min(lrs),
            "max_lr": max(lrs),
            "adjustments_count": self.adjustments_count,
            "total_epochs": len(self.lr_history),
        }

    def save_history(self, filepath: str):
        """保存学习率历史到文件"""
        import json

        with open(filepath, "w") as f:
            json.dump(
                {"lr_history": self.lr_history, "stats": self.get_stats()}, f, indent=2
            )


def validate_adaptive_config(adaptive_config: Dict[str, Any]) -> bool:
    """验证自适应学习率配置的有效性"""
    required_keys = ["scheduler_type"]

    for key in required_keys:
        if key not in adaptive_config:
            logger.error(f"❌ 自适应学习率配置缺少必需参数: {key}")
            return False

    # 验证调度器特定参数
    scheduler_type = adaptive_config["scheduler_type"]

    if scheduler_type == "reduce_on_plateau":
        if adaptive_config.get("patience", 0) <= 0:
            logger.warning("⚠️ patience应该大于0，使用默认值5")
            adaptive_config["patience"] = 5

        if not 0 < adaptive_config.get("factor", 0.7) < 1:
            logger.warning("⚠️ factor应该在(0,1)区间，使用默认值0.7")
            adaptive_config["factor"] = 0.7

    logger.info("✅ 自适应学习率配置验证通过")
    return True
