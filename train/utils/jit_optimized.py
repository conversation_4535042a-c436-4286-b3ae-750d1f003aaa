"""
JIT编译优化模块

使用torch.jit.script编译关键的数学运算函数，提供显著的性能提升。
专注于计算密集的操作，如坐标转换、矩阵运算等。

使用方法:
    from train.utils.jit_optimized import optimized_coordinate_transform, optimized_rotation_conversion

    # 使用JIT编译的函数
    result = optimized_coordinate_transform(positions, rotations)
"""

import torch
import torch.nn.functional as F
from typing import Tuple


@torch.jit.script
def optimized_batch_normalize(
    data: torch.Tensor, mean: torch.Tensor, std: torch.Tensor
) -> torch.Tensor:
    """
    JIT编译的批量归一化函数

    Args:
        data: 输入数据 [batch_size, features]
        mean: 均值 [features]
        std: 标准差 [features]

    Returns:
        归一化后的数据
    """
    return (data - mean) / (std + 1e-8)


@torch.jit.script
def optimized_rotation_6d_to_matrix(rot_6d: torch.Tensor) -> torch.Tensor:
    """
    JIT编译的6D旋转转换为旋转矩阵

    Args:
        rot_6d: 6D旋转表示 [..., 6]

    Returns:
        旋转矩阵 [..., 3, 3]
    """
    batch_shape = rot_6d.shape[:-1]

    # 提取前两列
    col1 = rot_6d[..., :3]  # [..., 3]
    col2 = rot_6d[..., 3:6]  # [..., 3]

    # 归一化第一列
    col1_norm = F.normalize(col1, dim=-1)

    # 计算第三列（叉积）
    col3 = torch.cross(col1_norm, col2, dim=-1)
    col3_norm = F.normalize(col3, dim=-1)

    # 重新计算第二列以确保正交性
    col2_corrected = torch.cross(col3_norm, col1_norm, dim=-1)

    # 组合成旋转矩阵
    rotation_matrix = torch.stack([col1_norm, col2_corrected, col3_norm], dim=-1)

    return rotation_matrix


@torch.jit.script
def optimized_quaternion_to_6d(quat: torch.Tensor) -> torch.Tensor:
    """
    JIT编译的四元数转6D旋转表示

    Args:
        quat: 四元数 [..., 4] (w, x, y, z)

    Returns:
        6D旋转表示 [..., 6]
    """
    # 四元数归一化
    quat_norm = F.normalize(quat, dim=-1)

    w, x, y, z = (
        quat_norm[..., 0],
        quat_norm[..., 1],
        quat_norm[..., 2],
        quat_norm[..., 3],
    )

    # 转换为旋转矩阵的前两列
    r11 = 1 - 2 * (y * y + z * z)
    r12 = 2 * (x * y - w * z)
    r13 = 2 * (x * z + w * y)

    r21 = 2 * (x * y + w * z)
    r22 = 1 - 2 * (x * x + z * z)
    r23 = 2 * (y * z - w * x)

    # 返回前两列（6D表示）
    return torch.stack([r11, r21, r13, r12, r22, r23], dim=-1)


@torch.jit.script
def optimized_batch_coordinate_transform(
    positions: torch.Tensor,
    rotations: torch.Tensor,
    object_pos: torch.Tensor,
    object_quat: torch.Tensor,
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    JIT编译的批量坐标转换函数

    Args:
        positions: 位置 [batch_size, 3]
        rotations: 旋转（6D表示）[batch_size, 6]
        object_pos: 物体位置 [batch_size, 3]
        object_quat: 物体四元数 [batch_size, 4]

    Returns:
        转换后的位置和旋转
    """
    batch_size = positions.shape[0]

    # 将物体四元数转换为旋转矩阵
    obj_rot_matrix = optimized_quaternion_to_rotation_matrix(object_quat)

    # 位置转换：相对于物体坐标系
    relative_pos = positions - object_pos
    transformed_pos = torch.bmm(
        obj_rot_matrix.transpose(-2, -1), relative_pos.unsqueeze(-1)  # 逆旋转
    ).squeeze(-1)

    # 旋转转换
    hand_rot_matrix = optimized_rotation_6d_to_matrix(rotations)
    transformed_rot_matrix = torch.bmm(
        obj_rot_matrix.transpose(-2, -1), hand_rot_matrix
    )

    # 转换回6D表示
    transformed_rot_6d = optimized_rotation_matrix_to_6d(transformed_rot_matrix)

    return transformed_pos, transformed_rot_6d


@torch.jit.script
def optimized_quaternion_to_rotation_matrix(quat: torch.Tensor) -> torch.Tensor:
    """
    JIT编译的四元数转旋转矩阵

    Args:
        quat: 四元数 [..., 4] (w, x, y, z)

    Returns:
        旋转矩阵 [..., 3, 3]
    """
    # 归一化四元数
    quat_norm = F.normalize(quat, dim=-1)
    w, x, y, z = (
        quat_norm[..., 0],
        quat_norm[..., 1],
        quat_norm[..., 2],
        quat_norm[..., 3],
    )

    # 计算旋转矩阵元素
    r00 = 1 - 2 * (y * y + z * z)
    r01 = 2 * (x * y - w * z)
    r02 = 2 * (x * z + w * y)

    r10 = 2 * (x * y + w * z)
    r11 = 1 - 2 * (x * x + z * z)
    r12 = 2 * (y * z - w * x)

    r20 = 2 * (x * z - w * y)
    r21 = 2 * (y * z + w * x)
    r22 = 1 - 2 * (x * x + y * y)

    # 组装旋转矩阵
    row1 = torch.stack([r00, r01, r02], dim=-1)
    row2 = torch.stack([r10, r11, r12], dim=-1)
    row3 = torch.stack([r20, r21, r22], dim=-1)

    return torch.stack([row1, row2, row3], dim=-2)


@torch.jit.script
def optimized_rotation_matrix_to_6d(rot_matrix: torch.Tensor) -> torch.Tensor:
    """
    JIT编译的旋转矩阵转6D表示

    Args:
        rot_matrix: 旋转矩阵 [..., 3, 3]

    Returns:
        6D旋转表示 [..., 6]
    """
    # 提取前两列
    col1 = rot_matrix[..., :, 0]  # [..., 3]
    col2 = rot_matrix[..., :, 1]  # [..., 3]

    # 展平并连接
    return torch.cat([col1, col2], dim=-1)


@torch.jit.script
def optimized_tensor_concatenate(
    tensors: list[torch.Tensor], dim: int = 0
) -> torch.Tensor:
    """
    JIT编译的tensor拼接函数

    Args:
        tensors: tensor列表
        dim: 拼接维度

    Returns:
        拼接后的tensor
    """
    return torch.cat(tensors, dim=dim)


@torch.jit.script
def optimized_action_scaling(
    actions: torch.Tensor,
    action_mean: torch.Tensor,
    action_std: torch.Tensor,
    clip_range: float = 3.0,
) -> torch.Tensor:
    """
    JIT编译的动作缩放函数

    Args:
        actions: 原始动作 [batch_size, action_dim]
        action_mean: 动作均值 [action_dim]
        action_std: 动作标准差 [action_dim]
        clip_range: 裁剪范围

    Returns:
        缩放后的动作
    """
    # 反归一化
    scaled_actions = actions * action_std + action_mean

    # 裁剪到合理范围
    clipped_actions = torch.clamp(scaled_actions, -clip_range, clip_range)

    return clipped_actions


@torch.jit.script
def optimized_observation_preprocessing(
    obs: torch.Tensor, obs_mean: torch.Tensor, obs_std: torch.Tensor
) -> torch.Tensor:
    """
    JIT编译的观察预处理函数

    Args:
        obs: 原始观察 [batch_size, obs_dim]
        obs_mean: 观察均值 [obs_dim]
        obs_std: 观察标准差 [obs_dim]

    Returns:
        预处理后的观察
    """
    return (obs - obs_mean) / (obs_std + 1e-8)


class JITOptimizedFunctions:
    """
    JIT优化函数的管理类

    提供统一的接口来使用JIT编译的函数，并包含性能监控功能。
    """

    def __init__(self, device: torch.device):
        self.device = device
        self.performance_stats = {
            "coordinate_transform_calls": 0,
            "rotation_conversion_calls": 0,
            "action_scaling_calls": 0,
            "total_time_saved_ms": 0.0,
        }

    def batch_coordinate_transform(self, positions, rotations, object_pos, object_quat):
        """批量坐标转换"""
        self.performance_stats["coordinate_transform_calls"] += 1
        return optimized_batch_coordinate_transform(
            positions, rotations, object_pos, object_quat
        )

    def rotation_6d_to_matrix(self, rot_6d):
        """6D旋转转矩阵"""
        self.performance_stats["rotation_conversion_calls"] += 1
        return optimized_rotation_6d_to_matrix(rot_6d)

    def action_scaling(self, actions, action_mean, action_std, clip_range=3.0):
        """动作缩放"""
        self.performance_stats["action_scaling_calls"] += 1
        return optimized_action_scaling(actions, action_mean, action_std, clip_range)

    def get_performance_stats(self):
        """获取性能统计"""
        return self.performance_stats.copy()

    def reset_stats(self):
        """重置性能统计"""
        for key in self.performance_stats:
            if isinstance(self.performance_stats[key], (int, float)):
                self.performance_stats[key] = 0


# 全局JIT优化函数实例
_global_jit_functions = None


def get_jit_functions(device: torch.device) -> JITOptimizedFunctions:
    """获取全局JIT优化函数实例"""
    global _global_jit_functions
    if _global_jit_functions is None:
        _global_jit_functions = JITOptimizedFunctions(device)
    return _global_jit_functions


# 便捷函数
def enable_jit_optimization(device: torch.device) -> JITOptimizedFunctions:
    """启用JIT优化"""
    jit_funcs = get_jit_functions(device)
    print(f"🚀 JIT优化已启用，设备: {device}")
    return jit_funcs
