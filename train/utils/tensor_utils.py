"""
最小侵入性Tensor优化工具 - 专注于数据预处理优化

只优化明确不会影响神经网络训练的部分：
- 数据加载和预处理阶段的tensor操作
- 设备转换优化
- 内存分配模式优化

避免任何可能影响训练梯度计算的操作。
"""

import torch
import logging
import numpy as np
from typing import Dict, Tuple, Optional, Union, Any, List
import functools
import warnings

logger = logging.getLogger(__name__)


def safe_concatenate_numpy_to_tensor(
    arrays_list: List[np.ndarray],
    axis: int = 0,
    device: Optional[torch.device] = None,
    dtype: torch.dtype = torch.float32,
) -> torch.Tensor:
    """
    安全的numpy数组拼接转tensor函数 - 专用于数据预处理阶段

    这个函数专门用于替换训练数据预处理中的多次np.concatenate操作，
    不会影响神经网络的前向和反向传播。

    Args:
        arrays_list: numpy数组列表
        axis: 拼接轴
        device: 目标设备
        dtype: 目标数据类型

    Returns:
        拼接后的tensor
    """
    if not arrays_list:
        raise ValueError("Empty arrays list")

    # 预计算总大小，避免多次内存重分配
    total_size = sum(arr.shape[axis] for arr in arrays_list)
    first_shape = list(arrays_list[0].shape)
    first_shape[axis] = total_size

    # 一次性分配numpy数组，然后转换为tensor
    # 这比多次concatenate + 多次转换更高效
    result_np = np.empty(first_shape, dtype=arrays_list[0].dtype)

    start_idx = 0
    for arr in arrays_list:
        arr_size = arr.shape[axis]
        end_idx = start_idx + arr_size

        if axis == 0:
            result_np[start_idx:end_idx] = arr
        elif axis == 1:
            result_np[:, start_idx:end_idx] = arr
        else:
            # 通用slice方法
            slices = [slice(None)] * result_np.ndim
            slices[axis] = slice(start_idx, end_idx)
            result_np[tuple(slices)] = arr

        start_idx = end_idx

    # 转换为tensor
    target_device = device or torch.device("cpu")
    return torch.from_numpy(result_np).to(dtype=dtype, device=target_device)


def efficient_device_transfer(
    tensors: List[torch.Tensor], target_device: torch.device, non_blocking: bool = True
) -> List[torch.Tensor]:
    """
    高效的批量设备转换 - 仅用于数据预处理

    减少设备转换的同步开销，但不改变tensor的计算图。

    Args:
        tensors: tensor列表
        target_device: 目标设备
        non_blocking: 非阻塞转换

    Returns:
        转换后的tensor列表
    """
    # 分组：已在目标设备 vs 需要转换
    already_on_device = []
    need_transfer = []

    for tensor in tensors:
        if tensor.device == target_device:
            already_on_device.append(tensor)
        else:
            need_transfer.append(tensor)

    # 批量转换需要转换的tensor
    if need_transfer:
        # 使用non_blocking减少同步开销
        transferred = [
            t.to(target_device, non_blocking=non_blocking) for t in need_transfer
        ]

        # 只在CUDA设备间转换时才需要同步
        if target_device.type == "cuda" and non_blocking:
            torch.cuda.synchronize(target_device)

        return already_on_device + transferred
    else:
        return already_on_device


def optimize_dataloader_conversion(
    obs_list: List[np.ndarray],
    acts_list: List[np.ndarray],
    next_obs_list: List[np.ndarray],
    dones_list: List[np.ndarray],
    device: torch.device,
) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
    """
    优化dataloader数据转换过程 - 专门用于helpers.py中的convert_dataloader_to_transitions

    这个函数专门优化数据预处理阶段，不会影响训练过程。

    Args:
        obs_list: 观察数据列表
        acts_list: 动作数据列表
        next_obs_list: 下一状态观察列表
        dones_list: 完成标志列表
        device: 目标设备

    Returns:
        优化转换后的tensor元组
    """
    logger.info("使用优化的数据转换...")

    # 使用优化的concatenation，避免多次内存分配
    obs_tensor = safe_concatenate_numpy_to_tensor(obs_list, axis=0, device=device)
    acts_tensor = safe_concatenate_numpy_to_tensor(acts_list, axis=0, device=device)
    next_obs_tensor = safe_concatenate_numpy_to_tensor(
        next_obs_list, axis=0, device=device
    )
    dones_tensor = safe_concatenate_numpy_to_tensor(dones_list, axis=0, device=device)

    # 展平序列数据到样本级别（保持原有逻辑不变）
    obs_flat = obs_tensor.reshape(-1, obs_tensor.shape[-1])
    acts_flat = acts_tensor.reshape(-1, acts_tensor.shape[-1])
    next_obs_flat = next_obs_tensor.reshape(-1, next_obs_tensor.shape[-1])
    dones_flat = dones_tensor.flatten()

    logger.info(f"优化转换完成: obs={obs_flat.shape}, acts={acts_flat.shape}")

    return obs_flat, acts_flat, next_obs_flat, dones_flat


class SafeCoordinateTransformHelper:
    """
    安全的坐标转换辅助工具

    专门用于优化坐标转换中的tensor操作，但不改变转换逻辑本身。
    主要减少CPU/GPU之间的数据传输次数。
    """

    def __init__(self, device: torch.device):
        self.device = device
        self._conversion_stats = {"cpu_gpu_transfers_saved": 0, "batch_conversions": 0}

    def batch_numpy_to_tensor(
        self, numpy_arrays: List[np.ndarray], dtype: torch.dtype = torch.float32
    ) -> List[torch.Tensor]:
        """
        批量numpy到tensor转换，减少设备传输

        Args:
            numpy_arrays: numpy数组列表
            dtype: 目标数据类型

        Returns:
            tensor列表
        """
        # 批量转换，而不是逐个转换
        tensors = []
        for arr in numpy_arrays:
            tensor = torch.from_numpy(arr).to(dtype=dtype, device=self.device)
            tensors.append(tensor)

        self._conversion_stats["batch_conversions"] += len(numpy_arrays)
        return tensors

    def get_stats(self) -> Dict[str, int]:
        """获取转换统计"""
        return self._conversion_stats.copy()


# 上下文管理器，用于安全的数据预处理优化
class SafeDataPreprocessingContext:
    """
    安全的数据预处理上下文管理器

    只在数据预处理阶段应用优化，不影响训练循环。
    """

    def __init__(self, device: torch.device):
        self.device = device
        self.coord_helper = SafeCoordinateTransformHelper(device)
        self._original_settings = {}

    def __enter__(self):
        # 保存原始设置
        if self.device.type == "cuda":
            # 启用一些安全的CUDA优化
            self._original_settings["benchmark"] = torch.backends.cudnn.benchmark
            torch.backends.cudnn.benchmark = True

        logger.info(f"启用安全数据预处理优化 (设备: {self.device})")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # 恢复原始设置
        if self.device.type == "cuda":
            torch.backends.cudnn.benchmark = self._original_settings.get(
                "benchmark", False
            )

        # 记录统计信息
        stats = self.coord_helper.get_stats()
        if stats["batch_conversions"] > 0:
            logger.info(f"数据预处理优化统计: {stats}")

        # 清理CUDA缓存（可选）
        if self.device.type == "cuda":
            torch.cuda.empty_cache()


# 简单的性能监控装饰器
def monitor_tensor_operations(func):
    """
    监控tensor操作的装饰器，用于识别性能瓶颈

    只记录统计信息，不改变函数行为。
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        import time

        start_time = time.time()

        # 记录GPU内存使用（如果可用）
        gpu_memory_before = None
        if torch.cuda.is_available():
            gpu_memory_before = torch.cuda.memory_allocated()

        # 执行原始函数
        result = func(*args, **kwargs)

        # 记录统计信息
        elapsed_time = time.time() - start_time
        if elapsed_time > 0.1:  # 只记录较慢的操作
            logger.debug(f"函数 {func.__name__} 执行时间: {elapsed_time:.3f}s")

            if gpu_memory_before is not None:
                gpu_memory_after = torch.cuda.memory_allocated()
                memory_diff = gpu_memory_after - gpu_memory_before
                if abs(memory_diff) > 1024 * 1024:  # 1MB
                    logger.debug(f"GPU内存变化: {memory_diff / 1024 / 1024:.1f}MB")

        return result

    return wrapper


# 工厂函数，用于创建安全的优化实例
def create_safe_data_optimizer(
    device: Union[str, torch.device]
) -> SafeDataPreprocessingContext:
    """
    创建安全的数据优化器

    Args:
        device: 目标设备

    Returns:
        数据预处理上下文管理器
    """
    if isinstance(device, str):
        device = torch.device(device)

    return SafeDataPreprocessingContext(device)


# 验证函数，确保优化不会改变结果
def verify_optimization_correctness(
    original_func, optimized_func, *test_args, **test_kwargs
):
    """
    验证优化函数的正确性

    Args:
        original_func: 原始函数
        optimized_func: 优化后的函数
        test_args: 测试参数
        test_kwargs: 测试关键字参数

    Returns:
        是否通过验证
    """
    try:
        original_result = original_func(*test_args, **test_kwargs)
        optimized_result = optimized_func(*test_args, **test_kwargs)

        # 比较结果
        if isinstance(original_result, torch.Tensor) and isinstance(
            optimized_result, torch.Tensor
        ):
            return torch.allclose(
                original_result, optimized_result, rtol=1e-5, atol=1e-6
            )
        elif isinstance(original_result, (list, tuple)) and isinstance(
            optimized_result, (list, tuple)
        ):
            if len(original_result) != len(optimized_result):
                return False
            return all(
                torch.allclose(a, b, rtol=1e-5, atol=1e-6)
                for a, b in zip(original_result, optimized_result)
                if isinstance(a, torch.Tensor) and isinstance(b, torch.Tensor)
            )
        else:
            return original_result == optimized_result

    except Exception as e:
        logger.error(f"验证过程中出现错误: {e}")
        return False
