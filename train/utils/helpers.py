"""训练相关的工具函数和辅助类"""

import os
import logging
import torch
import numpy as np
from typing import Tuple, Optional, Dict, Any
from tqdm import tqdm
from imitation.data import types as imitation_types
import time
import yaml
from omegaconf import OmegaConf, DictConfig
from pathlib import Path

logger = logging.getLogger(__name__)


class ConstantSchedulePicklable:
    """可序列化的常量调度器"""

    def __init__(self, value: float):
        self.value = value

    def __call__(self, progress_remaining: float) -> float:
        return self.value


def convert_dataloader_to_transitions(
    dataloader: torch.utils.data.DataLoader,
    device: torch.device,
    seq_length: int,
    target_hand: str = "left",
) -> Tuple[imitation_types.Transitions, imitation_types.Transitions]:
    """
    将DataLoader数据转换为imitation.types.Transitions对象 - 优化版本

    Args:
        dataloader: 数据加载器
        device: 设备
        seq_length: 序列长度
        target_hand: 目标手部

    Returns:
        执行层和决策层的Transitions对象
    """
    logger.info(
        "Converting DataLoader data to imitation.types.Transitions (优化版本)..."
    )

    # 🚀 使用安全的数据预处理优化
    from .tensor_utils import create_safe_data_optimizer, optimize_dataloader_conversion

    obs_list, acts_list, infos_list, next_obs_list, dones_list = [], [], [], [], []
    target_pose_list = []

    # 创建安全的数据优化器上下文
    with create_safe_data_optimizer(device) as optimizer_context:
        batch_num = 0
        for batch in tqdm(dataloader, desc="Converting expert data"):
            batch_num += 1
            try:
                # 尝试使用坐标转换包装器
                try:
                    from px_janus_learnsim.utils.coordinate_transform_wrapper import (
                        process_expert_batch_with_transforms,
                    )

                    obs_np, acts_np, next_obs_np, dones_np, target_pose_np = (
                        process_expert_batch_with_transforms(
                            batch,
                            device,
                            seq_length,
                            target_hand=target_hand,
                            enable_coord_transform=True,
                            enable_6d_rotation_fix=False,
                        )
                    )
                except Exception as e:
                    logger.warning(f"坐标转换包装器执行失败: {e}，使用原始处理函数")
                    from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
                        _process_expert_batch_for_imitation,
                    )

                    obs_np, acts_np, next_obs_np, dones_np, target_pose_np = (
                        _process_expert_batch_for_imitation(
                            batch, device, seq_length, target_hand=target_hand
                        )
                    )

                obs_list.append(obs_np)
                acts_list.append(acts_np)
                next_obs_list.append(next_obs_np)
                dones_list.append(dones_np)
                infos_list.append([{} for _ in range(len(obs_np))])
                target_pose_list.append(target_pose_np)

            except Exception as e:
                logger.error(f"Error processing batch {batch_num}: {e}")
                continue

        if not obs_list:
            raise ValueError("No data successfully converted from DataLoader.")

        # 🚀 使用优化的数据转换，避免多次内存分配
        logger.info("使用优化的tensor拼接...")
        obs_flat, acts_flat, next_obs_flat, dones_flat = optimize_dataloader_conversion(
            obs_list, acts_list, next_obs_list, dones_list, device
        )

        # 处理target_pose（保持原有逻辑）
        logger.info("处理target_pose数据...")
        target_poses_concatenated = np.concatenate(target_pose_list, axis=0)
        seq_length_actual = obs_list[0].shape[1]
        np_target_pose_all = np.repeat(
            target_poses_concatenated, seq_length_actual, axis=0
        )

        # 扩展infos以匹配展平后的样本数
        total_samples = obs_flat.shape[0]
        np_infos_all = np.array([{} for _ in range(total_samples)], dtype=object)

        logger.info(f"优化转换完成. 处理了 {total_samples} 个样本.")
        logger.info(f"  - Observations shape: {obs_flat.shape}")
        logger.info(f"  - Actions shape: {acts_flat.shape}")
        logger.info(f"  - Target Poses shape: {np_target_pose_all.shape}")

        # 转换为numpy数组（如果需要）
        obs_np = (
            obs_flat.cpu().numpy() if isinstance(obs_flat, torch.Tensor) else obs_flat
        )
        acts_np = (
            acts_flat.cpu().numpy()
            if isinstance(acts_flat, torch.Tensor)
            else acts_flat
        )
        next_obs_np = (
            next_obs_flat.cpu().numpy()
            if isinstance(next_obs_flat, torch.Tensor)
            else next_obs_flat
        )
        dones_np = (
            dones_flat.cpu().numpy()
            if isinstance(dones_flat, torch.Tensor)
            else dones_flat
        )

        execution_transitions = imitation_types.Transitions(
            obs=obs_np,
            acts=acts_np,
            infos=np_infos_all,
            next_obs=next_obs_np,
            dones=dones_np,
        )

        decision_transitions = imitation_types.Transitions(
            obs=obs_np,
            acts=np_target_pose_all,
            infos=np_infos_all,
            next_obs=next_obs_np,
            dones=dones_np,
        )

        return execution_transitions, decision_transitions


def get_device_config() -> str:
    """获取设备配置

    Returns:
        设备配置字符串，如 'cpu', 'cuda', 'cuda:0' 等
    """
    if torch.cuda.is_available():
        return "cuda"
    else:
        return "cpu"


def setup_device(device_str: str = "auto") -> torch.device:
    """设置训练设备"""
    from stable_baselines3.common.utils import get_device

    return get_device(device_str)


def setup_seed(seed: int) -> int:
    """设置随机种子"""
    import random
    from stable_baselines3.common.utils import set_random_seed

    if seed == -1:
        seed = random.randint(0, 10000)
        logger.info(f"Generated random seed: {seed}")

    set_random_seed(seed)
    logger.info(f"Using seed: {seed}")
    return seed


def merge_policy_kwargs(
    base_kwargs: Dict[str, Any], specific_kwargs: Dict[str, Any]
) -> Dict[str, Any]:
    """合并策略关键字参数"""
    from ..utils.constants import DEFAULT_POLICY_KWARGS

    merged = {**DEFAULT_POLICY_KWARGS, **base_kwargs, **specific_kwargs}
    return merged


def save_training_config(
    output_dir: str, cfg: DictConfig, source: str = "training_script", **extra_metadata
):
    """保存训练配置 - 简化版本"""
    try:
        # 保存原始配置
        original_config_path = os.path.join(output_dir, "original_config.yaml")
        OmegaConf.save(cfg, original_config_path)
        logger.info(f"原始配置已保存到: {original_config_path}")

    except Exception as e:
        logger.warning(f"保存配置失败: {e}")


def create_output_directories(output_dir: str) -> Dict[str, str]:
    """创建输出目录结构"""
    directories = {
        "models": os.path.join(output_dir, "models"),
        "logs": os.path.join(output_dir, "logs"),
        "videos": os.path.join(output_dir, "videos"),
        "tensorboard": os.path.join(output_dir, "tensorboard_logs"),
        "checkpoints": os.path.join(output_dir, "checkpoints"),
    }

    for name, path in directories.items():
        os.makedirs(path, exist_ok=True)
        logger.debug(f"Created directory: {path}")

    return directories


def save_complete_training_config(
    output_dir: str,
    cfg: DictConfig,
    env_cfg: Any = None,
    source: str = "training_script",
    **extra_metadata,
) -> None:
    """
    完整的配置保存功能 - 完全遵照原始脚本实现

    Args:
        output_dir: 输出目录
        cfg: Hydra配置对象
        env_cfg: 环境配置对象
        source: 配置来源标识
        **extra_metadata: 额外的元数据
    """
    try:
        # 🆕 保存完全解析的配置（包含命令行覆盖和引用解析）
        logger.info("保存完全解析的配置...")

        # 解析所有配置引用（如${env.seq_length}）为实际数值
        resolved_cfg = OmegaConf.to_container(cfg, resolve=True, throw_on_missing=True)

        # 保存解析后的配置
        resolved_config_path = os.path.join(output_dir, "resolved_config.yaml")
        with open(resolved_config_path, "w") as f:
            yaml.dump(resolved_cfg, f, default_flow_style=False, indent=2)
        logger.info(f"保存完全解析的配置到: {resolved_config_path}")

        # 同时保存原始配置（用于调试对比）
        original_config_path = os.path.join(output_dir, "original_config.yaml")
        OmegaConf.save(config=cfg, f=original_config_path)
        logger.info(f"保存原始配置到: {original_config_path}")

        # 🔍 打印关键解析信息
        logger.info("🔍 关键配置解析结果:")
        logger.info(f"  - env.seq_length: {resolved_cfg['env']['seq_length']}")
        if "policy_kwargs" in resolved_cfg.get("agent", {}):
            logger.info(
                f"  - agent.policy_kwargs.num_queries: {resolved_cfg['agent']['policy_kwargs'].get('num_queries', 'N/A')}"
            )
        if "execution_policy" in resolved_cfg.get("agent", {}):
            exec_policy = resolved_cfg["agent"]["execution_policy"]
            if "policy_kwargs" in exec_policy:
                logger.info(
                    f"  - agent.execution_policy.policy_kwargs.num_queries: {exec_policy['policy_kwargs'].get('num_queries', 'N/A')}"
                )

        # 🔧 使用统一配置管理工具保存环境配置，使用解析后的数值
        if env_cfg is not None:
            save_evaluation_config(
                env_cfg,
                output_dir,
                {
                    "source": source,
                    "env_name": resolved_cfg.get("env", {}).get("name", "unknown"),
                    "seq_length": resolved_cfg["env"]["seq_length"],  # 使用解析后的数值
                    "frames_to_stack": getattr(env_cfg, "num_frames_to_stack", None),
                    "observation_space": getattr(env_cfg, "observation_space", None),
                    "base_observation_space": getattr(
                        env_cfg, "base_observation_space", None
                    ),
                    "num_envs": (
                        getattr(env_cfg.scene, "num_envs", None)
                        if hasattr(env_cfg, "scene")
                        else None
                    ),
                    "timestamp": time.strftime("%Y-%m-%d_%H-%M-%S"),
                    **extra_metadata,
                },
            )

    except Exception as e:
        logger.error(f"保存解析配置失败: {e}")
        # 降级保存原始配置
        try:
            fallback_path = os.path.join(output_dir, "effective_config.yaml")
            OmegaConf.save(config=cfg, f=fallback_path)
            logger.info(f"降级保存原始配置到: {fallback_path}")
        except Exception as fallback_error:
            logger.error(f"降级保存也失败: {fallback_error}")


def save_evaluation_config(
    env_cfg: Any, output_dir: str, metadata: Dict[str, Any]
) -> None:
    """
    保存环境评估配置 - 遵照原始脚本实现

    Args:
        env_cfg: 环境配置对象
        output_dir: 输出目录
        metadata: 元数据字典
    """
    try:
        from isaaclab.utils.io import dump_yaml

        # 保存环境配置
        env_cfg_save_path = os.path.join(output_dir, "env_config_applied.yaml")
        dump_yaml(env_cfg_save_path, env_cfg)
        logger.info(f"Saved applied {type(env_cfg).__name__} to {env_cfg_save_path}")

        # 保存评估元数据
        eval_metadata_path = os.path.join(output_dir, "evaluation_metadata.yaml")
        with open(eval_metadata_path, "w") as f:
            yaml.dump(metadata, f, default_flow_style=False, indent=2)
        logger.info(f"保存评估元数据到: {eval_metadata_path}")

    except Exception as e:
        logger.warning(f"保存环境评估配置失败: {e}")


def create_model_save_paths(output_dir: str, training_phase: str) -> Dict[str, str]:
    """
    创建模型保存路径 - 遵照原始脚本的命名规范

    Args:
        output_dir: 输出目录
        training_phase: 训练阶段

    Returns:
        包含各种模型保存路径的字典
    """
    paths = {}

    if training_phase in ["execution_bc_train", "execution_bc_actor_train"]:
        paths["execution_bc_policy"] = os.path.join(
            output_dir, "execution_bc_policy.pth"
        )

    elif training_phase == "decision_bc_train":
        decision_models_dir = os.path.join(output_dir, "decision_models")
        os.makedirs(decision_models_dir, exist_ok=True)
        paths["decision_bc_policy"] = os.path.join(
            decision_models_dir, "decision_bc_scoring_policy.pth"
        )

    elif training_phase == "reward_model_pretrain":
        paths["final_reward_model"] = os.path.join(output_dir, "final_reward_model.pth")
        paths["best_reward_model"] = os.path.join(output_dir, "best_reward_model.pth")

    elif training_phase == "joint_rl":
        paths["rl_model_final"] = os.path.join(output_dir, "rl_model_final")
        paths["vec_normalize_final"] = os.path.join(
            output_dir, "vec_normalize_final.pkl"
        )

    return paths
