#!/usr/bin/env python3
"""
Comprehensive validation script for coordinate transformation analysis.
This script provides detailed analysis to verify if the world->object coordinate transformation is correct.
"""

import h5py
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from pathlib import Path
import argparse
import sys
import torch
sys.path.append('/home/<USER>/workspace/px_LearningSim_Janus/src')
from px_janus_learnsim.utils.math import rot6d_to_matrix_gram_schmidt, matrix_to_rot6d


def analyze_transformation_correctness(original_file, transformed_file, obj_path, handpose_path):
    """
    Analyze transformation correctness by comparing original and transformed data
    """
    print("🔍 Loading data files...")
    
    # Load original data
    with h5py.File(original_file, 'r') as f:
        obj_orig = f[obj_path][:]
        hand_orig = f[handpose_path][:]
    
    # Load transformed data
    with h5py.File(transformed_file, 'r') as f:
        obj_trans = f[obj_path][:]
        hand_trans = f[handpose_path][:]
    
    print(f"📊 Data shapes: obj={obj_orig.shape}, hand={hand_orig.shape}")
    
    # Extract positions and rotations
    obj_pos_orig = obj_orig[:, :3]
    obj_rot_orig = obj_orig[:, 3:9]
    hand_pos_orig = hand_orig[:, :3]
    hand_rot_orig = hand_orig[:, 3:9]
    
    obj_pos_trans = obj_trans[:, :3]
    obj_rot_trans = obj_trans[:, 3:9]
    hand_pos_trans = hand_trans[:, :3]
    hand_rot_trans = hand_trans[:, 3:9]
    
    # Validation checks
    validation_results = {}
    
    # Check 1: Object should be at origin in transformed data
    obj_pos_trans_mean = np.mean(obj_pos_trans, axis=0)
    obj_pos_trans_std = np.std(obj_pos_trans, axis=0)
    
    validation_results['object_at_origin'] = {
        'mean_position': obj_pos_trans_mean,
        'std_position': obj_pos_trans_std,
        'max_deviation': np.max(np.abs(obj_pos_trans_mean)),
        'is_valid': np.max(np.abs(obj_pos_trans_mean)) < 1e-10
    }
    
    # Check 2: Verify transformation reversibility
    # Manually reconstruct world coordinates from object coordinates
    reconstructed_positions = []
    for i in range(len(hand_pos_trans)):
        # Get object pose in world
        obj_pos_world = obj_pos_orig[i]
        obj_rot_world = obj_rot_orig[i]
        
        # Convert 6D rotation to matrix
        R_obj = rot6d_to_matrix_gram_schmidt(torch.tensor(obj_rot_world, dtype=torch.float32)).numpy()
        
        # Transform back to world: p_world = R_obj * p_obj + t_obj
        hand_pos_world_reconstructed = R_obj @ hand_pos_trans[i] + obj_pos_world
        reconstructed_positions.append(hand_pos_world_reconstructed)
    
    reconstructed_positions = np.array(reconstructed_positions)
    reconstruction_error = np.linalg.norm(reconstructed_positions - hand_pos_orig, axis=1)
    
    validation_results['reconstruction_accuracy'] = {
        'mean_error': np.mean(reconstruction_error),
        'max_error': np.max(reconstruction_error),
        'std_error': np.std(reconstruction_error),
        'is_valid': np.max(reconstruction_error) < 1e-6
    }
    
    # Check 3: Distance preservation
    # Distances between hand and object should be preserved
    orig_distances = np.linalg.norm(hand_pos_orig - obj_pos_orig, axis=1)
    trans_distances = np.linalg.norm(hand_pos_trans - obj_pos_trans, axis=1)
    distance_error = np.abs(orig_distances - trans_distances)
    
    validation_results['distance_preservation'] = {
        'mean_error': np.mean(distance_error),
        'max_error': np.max(distance_error),
        'std_error': np.std(distance_error),
        'is_valid': np.max(distance_error) < 1e-6
    }
    
    # Check 4: Rotation analysis
    # Object rotation should be identity in transformed data
    obj_rot_trans_matrices = []
    for i in range(len(obj_rot_trans)):
        R = rot6d_to_matrix_gram_schmidt(torch.tensor(obj_rot_trans[i], dtype=torch.float32)).numpy()
        obj_rot_trans_matrices.append(R)
    
    obj_rot_trans_matrices = np.array(obj_rot_trans_matrices)
    identity_deviations = []
    for R in obj_rot_trans_matrices:
        identity_error = np.linalg.norm(R - np.eye(3), 'fro')
        identity_deviations.append(identity_error)
    
    validation_results['object_rotation_identity'] = {
        'mean_deviation': np.mean(identity_deviations),
        'max_deviation': np.max(identity_deviations),
        'is_valid': np.max(identity_deviations) < 1e-6
    }
    
    # Check 5: Data concentration improvement
    orig_hand_std = np.std(hand_pos_orig, axis=0)
    trans_hand_std = np.std(hand_pos_trans, axis=0)
    concentration_improvement = np.mean(orig_hand_std) / np.mean(trans_hand_std)
    
    validation_results['concentration_improvement'] = {
        'original_std': orig_hand_std,
        'transformed_std': trans_hand_std,
        'improvement_factor': concentration_improvement,
        'is_valid': concentration_improvement > 1.0
    }
    
    return validation_results, {
        'obj_pos_orig': obj_pos_orig,
        'hand_pos_orig': hand_pos_orig,
        'obj_pos_trans': obj_pos_trans,
        'hand_pos_trans': hand_pos_trans,
        'reconstructed_positions': reconstructed_positions,
        'reconstruction_error': reconstruction_error
    }


def create_validation_plots(validation_results, data_dict, output_dir):
    """Create comprehensive validation plots"""
    
    fig = plt.figure(figsize=(20, 15))
    
    # Plot 1: 3D scatter of original vs transformed
    ax1 = fig.add_subplot(3, 4, 1, projection='3d')
    ax1.scatter(data_dict['hand_pos_orig'][:, 0], 
               data_dict['hand_pos_orig'][:, 1], 
               data_dict['hand_pos_orig'][:, 2], 
               c='blue', s=20, alpha=0.6, label='Hand (Original)')
    ax1.scatter(data_dict['obj_pos_orig'][:, 0], 
               data_dict['obj_pos_orig'][:, 1], 
               data_dict['obj_pos_orig'][:, 2], 
               c='green', s=30, alpha=0.8, label='Object (Original)')
    ax1.set_title('Original World Coordinates')
    ax1.legend()
    
    ax2 = fig.add_subplot(3, 4, 2, projection='3d')
    ax2.scatter(data_dict['hand_pos_trans'][:, 0], 
               data_dict['hand_pos_trans'][:, 1], 
               data_dict['hand_pos_trans'][:, 2], 
               c='red', s=20, alpha=0.6, label='Hand (Transformed)')
    ax2.scatter(data_dict['obj_pos_trans'][:, 0], 
               data_dict['obj_pos_trans'][:, 1], 
               data_dict['obj_pos_trans'][:, 2], 
               c='orange', s=30, alpha=0.8, label='Object (Transformed)')
    ax2.set_title('Transformed Object Coordinates')
    ax2.legend()
    
    # Plot 3: Reconstruction error
    ax3 = fig.add_subplot(3, 4, 3)
    ax3.hist(data_dict['reconstruction_error'], bins=30, alpha=0.7, color='purple')
    ax3.set_title('Reconstruction Error Distribution')
    ax3.set_xlabel('Error (m)')
    ax3.set_ylabel('Count')
    ax3.axvline(validation_results['reconstruction_accuracy']['mean_error'], 
                color='red', linestyle='--', label='Mean Error')
    ax3.legend()
    
    # Plot 4: Distance preservation
    orig_distances = np.linalg.norm(data_dict['hand_pos_orig'] - data_dict['obj_pos_orig'], axis=1)
    trans_distances = np.linalg.norm(data_dict['hand_pos_trans'] - data_dict['obj_pos_trans'], axis=1)
    
    ax4 = fig.add_subplot(3, 4, 4)
    ax4.scatter(orig_distances, trans_distances, alpha=0.6, s=10)
    ax4.plot([orig_distances.min(), orig_distances.max()], 
             [orig_distances.min(), orig_distances.max()], 'r--', label='Perfect Preservation')
    ax4.set_title('Distance Preservation')
    ax4.set_xlabel('Original Distance')
    ax4.set_ylabel('Transformed Distance')
    ax4.legend()
    
    # Plot 5: Object position deviation from origin
    ax5 = fig.add_subplot(3, 4, 5)
    obj_pos_norms = np.linalg.norm(data_dict['obj_pos_trans'], axis=1)
    ax5.hist(obj_pos_norms, bins=30, alpha=0.7, color='green')
    ax5.set_title('Object Position Deviation from Origin')
    ax5.set_xlabel('Distance from Origin')
    ax5.set_ylabel('Count')
    ax5.axvline(validation_results['object_at_origin']['max_deviation'], 
                color='red', linestyle='--', label='Max Deviation')
    ax5.legend()
    
    # Plot 6: Coordinate range comparison
    ax6 = fig.add_subplot(3, 4, 6)
    orig_ranges = np.ptp(data_dict['hand_pos_orig'], axis=0)
    trans_ranges = np.ptp(data_dict['hand_pos_trans'], axis=0)
    x = np.arange(3)
    width = 0.35
    ax6.bar(x - width/2, orig_ranges, width, label='Original', alpha=0.7)
    ax6.bar(x + width/2, trans_ranges, width, label='Transformed', alpha=0.7)
    ax6.set_title('Coordinate Range Comparison')
    ax6.set_xlabel('Axis')
    ax6.set_ylabel('Range')
    ax6.set_xticks(x)
    ax6.set_xticklabels(['X', 'Y', 'Z'])
    ax6.legend()
    
    # Plot 7: Reconstruction error over time
    ax7 = fig.add_subplot(3, 4, 7)
    ax7.plot(data_dict['reconstruction_error'], alpha=0.7)
    ax7.set_title('Reconstruction Error Over Time')
    ax7.set_xlabel('Sample Index')
    ax7.set_ylabel('Error (m)')
    ax7.axhline(validation_results['reconstruction_accuracy']['mean_error'], 
                color='red', linestyle='--', label='Mean Error')
    ax7.legend()
    
    # Plot 8: Standard deviation comparison
    ax8 = fig.add_subplot(3, 4, 8)
    orig_std = validation_results['concentration_improvement']['original_std']
    trans_std = validation_results['concentration_improvement']['transformed_std']
    x = np.arange(3)
    width = 0.35
    ax8.bar(x - width/2, orig_std, width, label='Original', alpha=0.7)
    ax8.bar(x + width/2, trans_std, width, label='Transformed', alpha=0.7)
    ax8.set_title('Standard Deviation Comparison')
    ax8.set_xlabel('Axis')
    ax8.set_ylabel('Standard Deviation')
    ax8.set_xticks(x)
    ax8.set_xticklabels(['X', 'Y', 'Z'])
    ax8.legend()
    
    # Plot 9-12: Validation summary text
    ax9 = fig.add_subplot(3, 4, 9)
    ax9.axis('off')
    
    summary_text = f"""
VALIDATION RESULTS:

1. Object at Origin:
   Max deviation: {validation_results['object_at_origin']['max_deviation']:.2e}
   Status: {'✓ PASS' if validation_results['object_at_origin']['is_valid'] else '✗ FAIL'}

2. Reconstruction Accuracy:
   Mean error: {validation_results['reconstruction_accuracy']['mean_error']:.2e}
   Max error: {validation_results['reconstruction_accuracy']['max_error']:.2e}
   Status: {'✓ PASS' if validation_results['reconstruction_accuracy']['is_valid'] else '✗ FAIL'}

3. Distance Preservation:
   Mean error: {validation_results['distance_preservation']['mean_error']:.2e}
   Max error: {validation_results['distance_preservation']['max_error']:.2e}
   Status: {'✓ PASS' if validation_results['distance_preservation']['is_valid'] else '✗ FAIL'}
    """
    
    ax9.text(0.05, 0.95, summary_text, transform=ax9.transAxes, 
            fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    ax10 = fig.add_subplot(3, 4, 10)
    ax10.axis('off')
    
    summary_text2 = f"""
4. Object Rotation Identity:
   Mean deviation: {validation_results['object_rotation_identity']['mean_deviation']:.2e}
   Max deviation: {validation_results['object_rotation_identity']['max_deviation']:.2e}
   Status: {'✓ PASS' if validation_results['object_rotation_identity']['is_valid'] else '✗ FAIL'}

5. Concentration Improvement:
   Factor: {validation_results['concentration_improvement']['improvement_factor']:.2f}x
   Status: {'✓ PASS' if validation_results['concentration_improvement']['is_valid'] else '✗ FAIL'}

OVERALL STATUS:
{'✓ TRANSFORMATION CORRECT' if all([
    validation_results['object_at_origin']['is_valid'],
    validation_results['reconstruction_accuracy']['is_valid'],
    validation_results['distance_preservation']['is_valid'],
    validation_results['object_rotation_identity']['is_valid'],
    validation_results['concentration_improvement']['is_valid']
]) else '✗ TRANSFORMATION ISSUES DETECTED'}
    """
    
    ax10.text(0.05, 0.95, summary_text2, transform=ax10.transAxes, 
             fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    output_path = Path(output_dir) / "comprehensive_validation.png"
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    return output_path


def main():
    parser = argparse.ArgumentParser(description='Validate coordinate transformation')
    parser.add_argument('original_file', help='Path to original HDF5 file')
    parser.add_argument('transformed_file', help='Path to transformed HDF5 file')
    parser.add_argument('--obj_path', default='dataset/observation/state/obj1/data',
                       help='Path to object data in HDF5')
    parser.add_argument('--handpose_path', default='dataset/action/lefthand/handpose/data',
                       help='Path to handpose data in HDF5')
    parser.add_argument('--output_dir', default='.',
                       help='Output directory for plots')
    
    args = parser.parse_args()
    
    print("🚀 Starting comprehensive transformation validation...")
    
    # Perform validation
    validation_results, data_dict = analyze_transformation_correctness(
        args.original_file, args.transformed_file, args.obj_path, args.handpose_path
    )
    
    # Create plots
    output_path = create_validation_plots(validation_results, data_dict, args.output_dir)
    
    print(f"📊 Validation complete! Results saved to: {output_path}")
    
    # Print summary
    print("\n" + "="*60)
    print("VALIDATION SUMMARY")
    print("="*60)
    
    for test_name, result in validation_results.items():
        status = "✓ PASS" if result.get('is_valid', False) else "✗ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    overall_pass = all(result.get('is_valid', False) for result in validation_results.values())
    print(f"\nOVERALL: {'✓ TRANSFORMATION CORRECT' if overall_pass else '✗ ISSUES DETECTED'}")
    
    if not overall_pass:
        print("\n🔍 RECOMMENDATIONS:")
        if not validation_results['object_at_origin']['is_valid']:
            print("- Check object coordinate transformation logic")
        if not validation_results['reconstruction_accuracy']['is_valid']:
            print("- Verify rotation matrix operations and coordinate system definitions")
        if not validation_results['distance_preservation']['is_valid']:
            print("- Check for numerical precision issues in transformation")
        if not validation_results['concentration_improvement']['is_valid']:
            print("- Transformation may not be providing expected benefits")


if __name__ == "__main__":
    main() 