#!/usr/bin/env python3
"""
单手控制模型评估脚本 -评估工具

专门用于评估单手DexH13控制模型的目标导向运动生成能力。

功能特点：
1. 随机生成目标位姿（手指关节+手腕位置）
2. 随机设置初始位姿
3. 评估模型是否能在规定时间内达到目标
4. 计算详细的评估指标：位置精度、关节精度、成功率等
5. 生成可视化的评估报告
6. 支持ACT模型序列动作输出的多种选择策略
7. 智能检测ExecutionBCActorPolicy并提供target_pose条件支持
8. 支持34维观察空间和25维动作空间的统一处理

使用示例：
    # 评估右手模型
    python evaluate_single_hand_model.py --model_path ./model.pth --hand_type right --episodes 20

    # 评估左手模型，使用更严格的阈值和时间加权动作选择
    python evaluate_single_hand_model.py --model_path ./model.pth --hand_type left --episodes 10 --position_tolerance 0.03 --action_selection_strategy weighted_avg

    # 评估ExecutionBCActorPolicy模型
    python evaluate_single_hand_model.py --model_path ./execution_bc_model.pth --hand_type right --action_selection_strategy first

版本：2.0 - 支持ACT序列动作和ExecutionBCActorPolicy
"""

import argparse
import os
import json
import time
from typing import Dict
import numpy as np
import torch
from pathlib import Path
from prettytable import PrettyTable
from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
    HierarchicalHybridSAC,
)


# 终端颜色常量 - 已移至 eval.utils.colors
from eval.utils.colors import Colors


# Isaac Lab相关导入
from isaaclab.app import AppLauncher


# 数据集采样器 - 已移至 eval.data.dataset_sampler
from eval.data.dataset_sampler import DatasetSampler


# 调度器和编码器 - 已移至 eval.utils
from eval.utils.schedule_utils import ConstantSchedulePicklable
from eval.utils.json_encoder import NumpyEncoder


# 参数解析器 - 已移至 eval.cli.argument_parser
def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="单手DexH13控制模型评估脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法：
  python evaluate_single_hand_model.py --model_path ./outputs/single_hand_model.pth --hand_type right
  python evaluate_single_hand_model.py --model_path ./model.pth --hand_type left --episodes 20 --detailed
        """,
    )

    # === 基础配置 ===
    parser.add_argument(
        "--model_path", type=str, required=True, help="单手控制模型路径"
    )
    parser.add_argument(
        "--hand_type",
        type=str,
        choices=["left", "right"],
        default="right",
        help="手部类型",
    )
    parser.add_argument("--episodes", type=int, default=10, help="评估的episode数量")
    parser.add_argument(
        "--max_steps", type=int, default=500, help="每个episode的最大步数"
    )

    # === 环境配置 ===
    parser.add_argument("--num_envs", type=int, default=1, help="并行环境数量")

    # === 评估配置 ===
    parser.add_argument(
        "--position_tolerance", type=float, default=0.05, help="位置达到阈值 (米)"
    )
    parser.add_argument(
        "--joint_tolerance", type=float, default=0.1, help="关节角度达到阈值 (弧度)"
    )
    parser.add_argument(
        "--success_hold_time", type=int, default=10, help="成功保持时间步数"
    )

    # === 随机化配置 ===
    parser.add_argument(
        "--joint_range",
        type=float,
        nargs=2,
        default=[-0.5, 0.5],
        help="关节角度随机范围",
    )
    parser.add_argument(
        "--position_range_x",
        type=float,
        nargs=2,
        default=[-0.2, 0.2],
        help="X轴位置随机范围",
    )
    parser.add_argument(
        "--position_range_y",
        type=float,
        nargs=2,
        default=[-0.2, 0.2],
        help="Y轴位置随机范围",
    )
    parser.add_argument(
        "--position_range_z",
        type=float,
        nargs=2,
        default=[0.8, 1.2],
        help="Z轴位置随机范围",
    )

    # === 输出配置 ===
    parser.add_argument("--output_dir", type=str, default=None, help="结果输出目录")
    parser.add_argument("--save_trajectories", action="store_true", help="保存轨迹数据")
    parser.add_argument("--detailed", action="store_true", help="详细输出模式")
    parser.add_argument("--disable_render", action="store_true", help="禁用渲染")

    # === 调试配置 ===
    parser.add_argument(
        "--debug_table_interval",
        type=int,
        default=50,
        help="调试表格打印间隔步数 (设为1表示每步都打印，设为0表示禁用)",
    )
    parser.add_argument(
        "--enable_debug_table", action="store_true", help="启用详细调试表格输出"
    )

    # === 物体中心坐标系配置 ===
    parser.add_argument(
        "--use_object_centric",
        action="store_true",
        default=True,
        help="启用物体中心坐标系转换（默认启用，与训练时保持一致）",
    )
    parser.add_argument(
        "--disable_object_centric",
        action="store_true",
        help="禁用物体中心坐标系转换（用于调试世界坐标系模式）",
    )

    # === 动作缩放配置 ===
    parser.add_argument(
        "--enable_action_scaling",
        action="store_true",
        default=True,
        help="启用动作缩放（默认启用）",
    )
    parser.add_argument(
        "--disable_action_scaling", action="store_true", help="禁用动作缩放"
    )
    parser.add_argument(
        "--scaling_stats_path", type=str, help="动作缩放统计文件路径(.pkl格式)"
    )

    # === 调试配置 ===
    parser.add_argument("--debug", action="store_true", help="启用调试输出")
    parser.add_argument(
        "--debug_table",
        action="store_true",
        help="启用详细的调试表格输出（显示手指语义信息和动作处理流程）",
    )

    # === ACT模型序列动作选择配置 ===
    parser.add_argument(
        "--action_selection_strategy",
        type=str,
        choices=["first", "last", "weighted_avg"],
        default="first",
        help="ACT模型序列动作选择策略",
    )
    parser.add_argument("--dataset_path", type=str, help="数据集路径，用于加载真实轨迹")
    parser.add_argument(
        "--use_dataset_poses", action="store_true", help="使用数据集中的物体位姿"
    )
    # Isaac Lab应用启动参数
    AppLauncher.add_app_launcher_args(parser)

    return parser

from px_janus_learnsim.utils.math import rot6d_to_matrix_gram_schmidt, matrix_to_rot6d


class SingleHandModelEvaluator:
    """单手控制模型评估器"""

    def __init__(self, args):
        """
        初始化评估器

        Args:
            args: 命令行参数
        """
        self.args = args
        # 从AppLauncher获取device信息，如果没有设置则使用CUDA（如果可用）
        device_str = getattr(
            args, "device", "cuda" if torch.cuda.is_available() else "cpu"
        )
        self.device = torch.device(device_str if torch.cuda.is_available() else "cpu")

        # 物体中心坐标系支持
        self.use_object_centric = getattr(
            args, "use_object_centric", True
        )  # 默认启用，与训练时保持一致
        self.object_centric_transformer = None

        # 如果用户明确指定禁用，则禁用
        if getattr(args, "disable_object_centric", False):
            self.use_object_centric = False

        # 动作缩放支持 - 默认禁用以匹配训练配置
        self.enable_action_scaling = getattr(
            args, "enable_action_scaling", False
        )  # 默认禁用，匹配训练配置

        # 如果用户明确指定启用，则启用
        if getattr(args, "enable_action_scaling", False):
            self.enable_action_scaling = True

        self.action_scaling_processor = None
        self.enable_separated_normalization = False

        if self.enable_action_scaling:
            try:
                from px_janus_learnsim.utils.action_scaling_utils import (
                    ActionScalingProcessor,
                    load_separated_scaling_processor,
                )

                scaling_stats_path = getattr(args, "scaling_stats_path", None)

                # 如果没有指定统计文件路径，自动在模型目录下查找
                if not scaling_stats_path:
                    model_dir = os.path.dirname(args.model_path)

                    # 🆕 优先查找分离归一化统计文件
                    separated_patterns = [
                        "separated_scaling_stats.pkl",
                        "conditional_scaling_stats.pkl",
                        "separated_scaling_stats.npz",
                        "conditional_scaling_stats.npz",
                    ]

                    for pattern in separated_patterns:
                        test_path = os.path.join(model_dir, pattern)
                        if os.path.exists(test_path):
                            scaling_stats_path = test_path
                            break

                    # 如果没找到分离归一化文件，查找传统统计文件
                    if not scaling_stats_path:
                        traditional_patterns = [
                            "scaling_stats.pkl",
                            "action_scaling_stats.pkl",
                            "scaling_stats.npz",
                            "data_stats.pkl",
                        ]

                        for pattern in traditional_patterns:
                            test_path = os.path.join(model_dir, pattern)
                            if os.path.exists(test_path):
                                scaling_stats_path = test_path
                                break

                if scaling_stats_path and os.path.exists(scaling_stats_path):
                    print(f"🔍 找到统计文件: {scaling_stats_path}")

                    # 🆕 根据文件类型和内容判断归一化模式
                    file_name = os.path.basename(scaling_stats_path)
                    is_separated_file = (
                        "separated" in file_name or "conditional" in file_name
                    )

                    # 尝试分离归一化加载
                    try:
                        if is_separated_file:
                            print("🆕 尝试加载分离归一化处理器...")
                            self.action_scaling_processor = (
                                load_separated_scaling_processor(scaling_stats_path)
                            )
                            self.enable_separated_normalization = True
                            print("✅ 分离归一化处理器已加载")
                        else:
                            print("📚 尝试加载传统归一化处理器...")
                            # 加载传统处理器
                            self.action_scaling_processor = ActionScalingProcessor()
                            self.action_scaling_processor.load_stats(scaling_stats_path)
                            self.enable_separated_normalization = False
                            print("✅ 传统归一化处理器已加载")

                        # 显示统计信息
                        if self.enable_separated_normalization:
                            if (
                                hasattr(
                                    self.action_scaling_processor, "separated_stats"
                                )
                                and self.action_scaling_processor.separated_stats
                                is not None
                            ):
                                separated_stats = (
                                    self.action_scaling_processor.separated_stats
                                )
                                print("📊 分离归一化统计信息:")
                                print(f"   观察空间维度: {separated_stats.obs_dim}")
                                print(
                                    f"   目标位姿维度: {separated_stats.target_pose_dim}"
                                )
                                # 🆕 显示动作维度和缩放方式
                                if hasattr(separated_stats, "action_dim"):
                                    print(
                                        f"   动作空间维度: {separated_stats.action_dim}"
                                    )
                                if hasattr(separated_stats, "action_scaling_type"):
                                    print(
                                        f"   动作缩放方式: {separated_stats.action_scaling_type}"
                                    )
                                print(
                                    f"   观察归一化方式: {separated_stats.obs_normalization_type}"
                                )
                                print(
                                    f"   目标位姿归一化方式: {separated_stats.target_pose_normalization_type}"
                                )
                        else:
                            if (
                                hasattr(self.action_scaling_processor, "stats")
                                and self.action_scaling_processor.stats is not None
                            ):
                                expected_obs_dim = (
                                    self.action_scaling_processor.stats.obs_dim
                                )
                                print("📊 传统归一化统计信息:")
                                print(f"   统计文件期望观察维度: {expected_obs_dim}")

                    except Exception as e:
                        print(f"⚠️ 分离归一化加载失败，尝试传统模式: {e}")
                        try:
                            # 降级到传统处理器
                            self.action_scaling_processor = ActionScalingProcessor()
                            self.action_scaling_processor.load_stats(scaling_stats_path)
                            self.enable_separated_normalization = False
                            print("✅ 降级到传统归一化处理器")
                        except Exception as e2:
                            print(f"❌ 传统归一化加载也失败: {e2}")
                            self.enable_action_scaling = False
                            self.enable_separated_normalization = False

                else:
                    print("⚠️ 动作缩放统计文件不存在，将禁用动作缩放功能")
                    if scaling_stats_path:
                        print(f"   尝试的路径: {scaling_stats_path}")
                    else:
                        print(f"   模型目录: {os.path.dirname(args.model_path)}")
                    self.enable_action_scaling = False
                    self.enable_separated_normalization = False

            except ImportError as e:
                print(f"❌ 无法导入动作缩放模块: {e}")
                self.enable_action_scaling = False
                self.enable_separated_normalization = False

        if self.use_object_centric:
            try:
                import sys

                sys.path.append("/home/<USER>/workspace/px_LearningSim_Janus/src")
                from px_janus_learnsim.utils.object_centric_transforms import (
                    ObjectCentricTransformer,
                )

                self.object_centric_transformer = ObjectCentricTransformer(
                    device=str(self.device)
                )
                print("✅ 物体中心坐标系转换器已启用（在环境文件中计算观察中已处理）")

            except ImportError as e:
                print(f"❌ 无法导入物体中心坐标系转换模块: {e}")
                self.use_object_centric = False

        # 创建输出目录
        self.output_dir = self._create_output_directory()
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 🆕 序列长度配置（将在setup_environment中设置）
        self.seq_length = None

        # 调试配置
        self.debug_table_interval = getattr(args, "debug_table_interval", 50)
        self.enable_debug_table = getattr(args, "enable_debug_table", False)

        # 如果启用了detailed模式，也启用调试表格
        if getattr(args, "detailed", False):
            self.enable_debug_table = True

        # 评估结果存储
        self.evaluation_results = {
            "model_path": args.model_path,
            "hand_type": args.hand_type,
            "episodes": args.episodes,
            "max_steps": args.max_steps,
            "use_object_centric": self.use_object_centric,
            "enable_action_scaling": self.enable_action_scaling,
            "enable_separated_normalization": self.enable_separated_normalization,
            "evaluation_config": self._get_evaluation_config(),
            "episodes_data": [],
            "summary": {},
        }

        print(f"📁 评估结果将保存到: {self.output_dir}")
        if self.use_object_centric:
            print("🎯 使用物体中心坐标系模式（与训练时保持一致）")
        else:
            print("🌍 使用世界坐标系模式")

        if self.enable_action_scaling:
            normalization_mode = (
                "分离归一化" if self.enable_separated_normalization else "传统归一化"
            )
            print(f"📏 动作缩放已启用: {normalization_mode}模式")
        else:
            print("📏 动作缩放已禁用（匹配训练配置）")

        # ✅ 新增：ACT Chunked Execution状态管理
        self.chunked_execution_enabled = False  # 默认关闭，基于模型序列长度动态启用
        self.action_buffer = None  # 存储预测的动作序列
        self.buffer_index = 0  # 当前执行到的动作索引
        self.last_prediction_step = -1  # 上次进行模型预测的步数
        self.chunk_size = None  # 动作块大小，从模型配置中获取

        # 获取模型序列长度
        self.seq_length = self._get_seq_length_from_training_config()

        # 数据集采样器
        self.dataset_sampler = None
        self.dataset_path = getattr(args, "dataset_path", None)

        # ✅ 如果模型输出序列长度 > 1，则启用chunked execution
        if self.seq_length is not None and self.seq_length > 1:
            self.chunked_execution_enabled = True
            self.chunk_size = self.seq_length
            print(f"✅ 启用ACT Chunked Execution: chunk_size={self.chunk_size}")
        else:
            print("📝 使用单步动作预测（非ACT或序列长度=1）")

    def _create_output_directory(self):
        """创建输出目录"""
        if self.args.output_dir:
            return Path(self.args.output_dir)

        # 自动生成目录名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        model_name = Path(self.args.model_path).stem
        dir_name = f"single_hand_eval_{self.args.hand_type}_{model_name}_{timestamp}"

        return Path("evaluation_results") / "single_hand" / dir_name

    def _get_seq_length_from_training_config(self):
        """从训练配置中读取序列长度"""
        model_dir = os.path.dirname(self.args.model_path)

        # 🆕 优先读取解析后的配置
        resolved_config_path = os.path.join(model_dir, "resolved_config.yaml")
        if os.path.exists(resolved_config_path):
            try:
                import yaml

                with open(resolved_config_path, "r") as f:
                    config = yaml.safe_load(f)
                seq_length = config.get("env", {}).get("seq_length", 15)
                print(f"   📖 从resolved_config.yaml读取seq_length: {seq_length}")
                return seq_length
            except Exception as e:
                print(f"   ⚠️ 读取resolved_config.yaml失败: {e}")

        # 🔄 降级读取evaluation_config.yaml
        eval_config_path = os.path.join(model_dir, "evaluation_config.yaml")
        if os.path.exists(eval_config_path):
            try:
                import yaml

                with open(eval_config_path, "r") as f:
                    config = yaml.safe_load(f)
                seq_length = config.get("seq_length", 15)
                print(f"   📖 从evaluation_config.yaml读取seq_length: {seq_length}")
                return seq_length
            except Exception as e:
                print(f"   ⚠️ 读取evaluation_config.yaml失败: {e}")

        # 🔄 再次降级读取原始配置
        original_config_path = os.path.join(model_dir, "original_config.yaml")
        if os.path.exists(original_config_path):
            try:
                with open(original_config_path, "r") as f:
                    # 这里需要解析OmegaConf格式，简单的正则表达式提取seq_length
                    config_content = f.read()
                    import re

                    match = re.search(r"seq_length:\s*(\d+)", config_content)
                    if match:
                        seq_length = int(match.group(1))
                        print(
                            f"   📖 从original_config.yaml解析seq_length: {seq_length}"
                        )
                        return seq_length
            except Exception as e:
                print(f"   ⚠️ 读取original_config.yaml失败: {e}")

        # 🔄 最后降级：使用默认值
        default_seq_length = 15
        print(f"   ⚠️ 未找到训练配置，使用默认seq_length: {default_seq_length}")
        print("   📁 搜索的配置文件:")
        print(
            f"      - {resolved_config_path} {'✅' if os.path.exists(resolved_config_path) else '❌'}"
        )
        print(
            f"      - {eval_config_path} {'✅' if os.path.exists(eval_config_path) else '❌'}"
        )
        print(
            f"      - {original_config_path} {'✅' if os.path.exists(original_config_path) else '❌'}"
        )
        return default_seq_length

    def _get_evaluation_config(self):
        """获取评估配置字典"""
        return {
            "position_tolerance": self.args.position_tolerance,
            "joint_tolerance": self.args.joint_tolerance,
            "success_hold_time": self.args.success_hold_time,
            "joint_range": self.args.joint_range,
            "position_ranges": {
                "x": self.args.position_range_x,
                "y": self.args.position_range_y,
                "z": self.args.position_range_z,
            },
            "device": str(self.device),
            "num_envs": self.args.num_envs,
            "use_object_centric": self.use_object_centric,
            "enable_action_scaling": self.enable_action_scaling,
            "enable_separated_normalization": self.enable_separated_normalization,
            "action_selection_strategy": self.args.action_selection_strategy,
            "debug": getattr(self.args, "debug", False),
            "seq_length": self._get_seq_length_from_training_config(),  # 🆕 添加seq_length
        }

    def run_evaluation(self):
        """运行完整评估流程"""
        print("🚀 开始单手控制模型评估")
        print("=" * 50)

        # 1. 设置环境
        print("🌍 设置单手DexH13环境...")
        self.setup_environment()

        # 2. 加载模型
        print("🤖 加载单手控制模型...")
        self.load_model()

        # 3. 运行评估
        print("🎯 开始评估测试...")

        start_time = time.time()
        # 加载数据集数据
        if self.dataset_path:
            print(f"✅ 加载数据集=======]]]]]]]]]]]]]]]]]]]: {self.dataset_path}")
            # 🔧 修复：确保评估时只使用训练时见过的数据
            self.dataset_sampler = DatasetSampler(
                self.dataset_path,
                self.seq_length,
                use_train_split_only=True,  # 只使用训练集，不包含验证集
            )
            print("✅ 加载数据集成功")

        # 运行所有episodes
        for episode_idx in range(self.args.episodes):
            episode_data = self.evaluate_episode(episode_idx)
            self.evaluation_results["episodes_data"].append(episode_data)

        end_time = time.time()
        self.evaluation_results["evaluation_time"] = end_time - start_time

        # 4. 计算汇总统计
        print("📊 计算评估统计...")
        self.calculate_summary_statistics()

        # 5. 保存结果
        print("💾 保存评估结果...")
        self.save_results()

        # 6. 打印汇总报告
        self.print_summary_report()

        print("✅ 评估完成!")

    def setup_environment(self):
        """设置单手DexH13环境"""
        from env.IsaacSim_direct_task_table_set_env_DexH13_single_hand import (
            SingleHandDexH13DirectEnvCfg,
        )

        # 创建环境配置
        env_cfg = SingleHandDexH13DirectEnvCfg()

        # === 🔧 重要理解修正：seq_length的真实含义 ===
        # seq_length=15 指的是模型输出序列长度，不是观察帧堆叠！
        # - 训练时：模型从34维观察预测15个动作序列
        # - 评估时：环境提供34维观察，模型预测15个动作，取第一个执行
        self.seq_length = self._get_seq_length_from_training_config()
        print(f"   📊 训练配置序列长度: {self.seq_length} (模型输出动作序列长度)")

        # ✅ 不设置 num_frames_to_stack，保持环境默认的34维观察空间
        # env_cfg.num_frames_to_stack = self.seq_length  # ❌ 错误的理解

        # 基础环境配置
        env_cfg.scene.num_envs = self.args.num_envs
        device_str = str(self.device)
        if "cuda" in device_str:
            env_cfg.sim.device = "cuda"
        else:
            env_cfg.sim.device = device_str

        # 设置手部类型
        env_cfg.hand_type = self.args.hand_type

        # === 动作缩放配置 ===
        # 如果启用了外部动作缩放，禁用环境内置的动作缩放
        if self.enable_action_scaling and self.action_scaling_processor is not None:
            env_cfg.disable_builtin_action_scaling = True
            print("   🔧 已禁用环境内置动作缩放（使用外部统计缩放）")
        else:
            env_cfg.disable_builtin_action_scaling = False
            print("   🔧 使用环境内置动作缩放")

        # === 启用评估模式配置 ===
        env_cfg.evaluation_mode = True  # 启用评估模式
        
        # 根据是否使用数据集轨迹来设置随机生成选项
        if hasattr(self.args, 'use_dataset_poses') and self.args.use_dataset_poses:
            env_cfg.random_target_generation = False  # 使用数据集轨迹时禁用随机目标生成
            env_cfg.random_initial_pose = False       # 使用数据集轨迹时禁用随机初始位姿
            print("   🎯 数据集轨迹模式: 禁用随机生成，使用轨迹起点和终点")
        else:
            env_cfg.random_target_generation = True   # 启用随机目标生成
            env_cfg.random_initial_pose = True        # 启用随机初始位姿
            print("   🎲 随机模式: 启用随机目标和初始位姿生成")

        # === 启用目标可视化 ===
        env_cfg.enable_target_visualization = True  # 启用环境内置的目标可视化
        env_cfg.target_hand_color = [0.0, 0.8, 1.0]  # 蓝绿色
        env_cfg.target_hand_transparency = 0.6  # 半透明
        env_cfg.target_hand_offset = [1.0, 0.0, 0.0]  # 在右侧显示目标手

        # 评估阈值配置
        env_cfg.position_tolerance = self.args.position_tolerance
        env_cfg.joint_tolerance = self.args.joint_tolerance
        env_cfg.success_hold_time = self.args.success_hold_time

        # 随机范围配置
        env_cfg.joint_random_range = self.args.joint_range
        env_cfg.position_random_range = [
            self.args.position_range_x,
            self.args.position_range_y,
            self.args.position_range_z,
        ]

        # === 新增：物体中心坐标系配置传递 ===
        # 确保环境使用与评估脚本一致的坐标系设置
        env_cfg.use_object_centric = self.use_object_centric

        print("   ✅ 环境配置完成")
        print(f"      - 手部类型: {env_cfg.hand_type}")
        print(f"      - 评估模式: {env_cfg.evaluation_mode}")
        print(f"      - 环境数量: {env_cfg.scene.num_envs}")
        print(f"      - 位置阈值: {env_cfg.position_tolerance}m")
        print(f"      - 关节阈值: {env_cfg.joint_tolerance}rad")
        print(f"      - 目标可视化: {env_cfg.enable_target_visualization}")
        print(f"      - 物体中心坐标系: {env_cfg.use_object_centric}")
        print(
            f"      - 动作缩放状态: {'外部统计缩放' if self.enable_action_scaling and self.action_scaling_processor else '环境内置缩放'}"
        )
        print(f"      - 禁用内置缩放: {env_cfg.disable_builtin_action_scaling}")

        # 创建环境
        import gymnasium as gym

        # 🆕 确保已注册自定义Isaac环境
        try:
            from env.env_registry import register_all_environments

            register_all_environments()
        except Exception as e:
            print(f"⚠️ 环境注册失败或已注册: {e}")

        self.env = gym.make("Isaac-SingleHand-DexH13-Direct-v0", cfg=env_cfg)

        # === 新增：调试episode长度配置 ===
        print("   🔍 环境episode长度调试:")
        print(f"      - env_cfg.episode_length_s: {env_cfg.episode_length_s}")
        print(f"      - env_cfg.decimation: {env_cfg.decimation}")
        print(f"      - env_cfg.sim.dt: {env_cfg.sim.dt}")
        if hasattr(self.env.unwrapped, "max_episode_length"):
            print(
                f"      - 实际max_episode_length: {self.env.unwrapped.max_episode_length}"
            )

        # 验证环境配置
        print("   📊 环境创建成功:")
        print(f"      - 观察空间: {self.env.observation_space}")
        print(f"      - 动作空间: {self.env.action_space}")
        print(f"      - 设备: {self.env.unwrapped.device}")

        # === ✅ 验证观察空间配置 ===
        expected_obs_dim = 34  # 固定34维：手部25维 + 物体9维
        actual_obs_dim = self.env.observation_space.shape[0]
        print("   🔍 观察维度验证:")
        print(f"      - 期望观察维度: {expected_obs_dim}维 (手部25 + 物体9)")
        print(f"      - 实际观察维度: {actual_obs_dim}维")
        print(f"      - 模型输出序列长度: {self.seq_length}个动作")
        if actual_obs_dim == expected_obs_dim:
            print("      - ✅ 观察维度正确，符合seq2seq架构")
        else:
            print("      - ❌ 观察维度异常，期望34维")
            print("      - 🔧 请检查环境配置")

    def load_model(self):
        """加载单手控制模型"""
        if not os.path.exists(self.args.model_path):
            print(f"❌ 模型文件不存在: {self.args.model_path}")
            print("将使用随机动作进行基准测试")
            self.model = None
            return

        try:
            print(f"🔄 加载模型: {self.args.model_path}")

            # 加载模型文件
            model_data = torch.load(
                self.args.model_path, map_location=self.device, weights_only=False
            )

            print(f"   📝 模型数据类型: {type(model_data)}")

            if isinstance(model_data, dict):
                print(f"   🔍 模型字典包含键: {list(model_data.keys())}")

                # 尝试提取policy对象
                if "policy" in model_data:
                    self.model = model_data["policy"]
                    print("   ✅ 从字典中提取policy对象")
                else:
                    print("   ❌ 字典中未找到policy键")
                    self.model = None
                    return
            else:
                # 直接是policy对象
                self.model = model_data
                print("   ✅ 直接加载policy对象")

            # 设置模型为评估模式
            if hasattr(self.model, "eval"):
                self.model.eval()

            # 确保模型在正确设备上
            if hasattr(self.model, "to"):
                self.model = self.model.to(self.device)

            print(f"   ✅ 模型加载成功，类型: {type(self.model)}")
            print(f"   🔧 设备: {self.device}")

        except Exception as e:
            print(f"   ❌ 模型加载失败: {e}")
            import traceback

            traceback.print_exc()
            print("将使用随机动作进行基准测试")
            self.model = None

    def _select_action_from_sequence(self, action_sequence, strategy="first"):
        """
        从ACT模型输出的动作序列中选择单个动作执行

        Args:
            action_sequence: 动作序列张量 [batch_size, seq_length, action_dim] 或 [seq_length, action_dim]
                           可以是numpy.ndarray或torch.Tensor
            strategy: 选择策略 - "first", "last", "weighted_avg"

        Returns:
            选择的动作 [batch_size, action_dim] 或 [action_dim]
        """
        # 🔧 类型转换：确保输入是torch.Tensor
        if isinstance(action_sequence, np.ndarray):
            print(f"🔄 转换numpy到tensor: {action_sequence.shape}")
            action_sequence = torch.from_numpy(action_sequence).float().to(self.device)
        elif not isinstance(action_sequence, torch.Tensor):
            raise TypeError(f"不支持的action_sequence类型: {type(action_sequence)}")

        # print(f"🔄 处理动作序列: {action_sequence.shape}")

        # 确保是3D张量: [batch_size, seq_length, action_dim]
        if action_sequence.dim() == 2:
            # [seq_length, action_dim] -> [1, seq_length, action_dim]
            action_sequence = action_sequence.unsqueeze(0)
        elif action_sequence.dim() == 1:
            # [action_dim] -> [1, 1, action_dim]
            action_sequence = action_sequence.unsqueeze(0).unsqueeze(0)

        batch_size, seq_length, action_dim = action_sequence.shape

        # 🆕 验证序列长度是否与配置匹配
        if self.seq_length is not None and seq_length != self.seq_length:
            print(f"⚠️ 模型输出序列长度({seq_length})与配置({self.seq_length})不匹配")

        if strategy == "first":
            # 使用序列的第一个动作
            selected_action = action_sequence[:, 0, :]
        elif strategy == "last":
            # 使用序列的最后一个动作
            selected_action = action_sequence[:, -1, :]
        elif strategy == "weighted_avg":
            # 使用时间加权平均，越近的动作权重越高
            weights = torch.exp(
                -0.1 * torch.arange(seq_length, device=action_sequence.device)
            )
            weights = weights / weights.sum()
            weights = weights.view(1, seq_length, 1)  # 广播维度
            selected_action = (action_sequence * weights).sum(dim=1)
        else:
            # 默认使用第一个动作
            selected_action = action_sequence[:, 0, :]
        # print(
        #     f"🔄 ==========================================================选择动作: {selected_action}"
        # )

        return selected_action

    def _extract_target_pose_from_obs(self, obs):
        """
        从观察字典中提取完整的目标姿态信息（简化版）

        Args:
            obs: 环境观察字典

        Returns:
            target_pose: 完整目标状态 [1, 25] 或 None
        """
        if not isinstance(obs, dict):
            return None

        # 优先方法：直接从evaluation_info获取预构建的25维目标状态
        if "evaluation_info" in obs:
            eval_info = obs["evaluation_info"]
            if "target_full_state_25d" in eval_info:
                target_full_state = eval_info["target_full_state_25d"]

                # 统一类型转换
                if isinstance(target_full_state, (list, np.ndarray)):
                    target_full_state = (
                        torch.from_numpy(np.array(target_full_state))
                        .float()
                        .to(self.device)
                    )
                elif isinstance(target_full_state, torch.Tensor):
                    target_full_state = target_full_state.float().to(self.device)
                else:
                    return None

                # 统一维度处理
                if target_full_state.dim() == 1:
                    target_full_state = target_full_state.unsqueeze(0)  # [1, 25]

                # 维度验证
                if target_full_state.shape[-1] == 25:
                    return target_full_state
                else:
                    print(f"⚠️ target_full_state_25d维度错误: {target_full_state.shape}")
            else:
                print("⚠️ Env返回的Obs中，未找到target_full_state_25d")

        return None

    def _reset_chunked_execution_state(self):
        """重置chunked execution状态（每个episode开始时调用）"""
        self.action_buffer = None
        self.buffer_index = 0
        self.last_prediction_step = -1
        if self.chunked_execution_enabled:
            print(f"🔄 重置ACT Chunked Execution状态 (chunk_size={self.chunk_size})")

    def _should_request_new_prediction(self, current_step):
        """判断是否应该请求新的动作预测"""
        if not self.chunked_execution_enabled:
            return True  # 非chunked模式，每步都预测

        # 第一次预测
        if self.action_buffer is None:
            return True

        # buffer已用尽，需要新预测
        if (
            self.buffer_index >= self.action_buffer.shape[1]
        ):  # shape: [batch, seq, action_dim]
            return True

        return False

    def _get_action_from_buffer(self):
        """从动作buffer中获取当前步的动作"""
        if self.action_buffer is None:
            return None

        # 检查buffer是否用尽
        if self.action_buffer.dim() >= 2:
            max_index = self.action_buffer.shape[1]
        else:
            max_index = self.action_buffer.shape[0]

        if self.buffer_index >= max_index:
            return None

        # 获取当前动作
        if self.action_buffer.dim() >= 2:
            action = self.action_buffer[
                0, self.buffer_index
            ]  # [batch, seq, action] -> [action]
        else:
            action = self.action_buffer[
                self.buffer_index
            ]  # [seq, action] -> [action] (不太可能)

        # 递增索引
        self.buffer_index += 1

        # 确保返回的action是2维tensor [1, action_dim]，与predict_action的其他分支保持一致
        if action.dim() == 1:
            action = action.unsqueeze(0)  # [action_dim] -> [1, action_dim]

        return action

    def _store_predicted_actions(self, predicted_actions):
        """存储预测的动作序列到buffer"""
        self.action_buffer = predicted_actions
        self.buffer_index = 0  # 重置索引，准备从第一个动作开始

        if hasattr(self.args, "debug") and self.args.debug:
            print(f"💾 存储动作序列到buffer: {self.action_buffer.shape}")
            print("🔄 重置buffer索引为0")

    def predict_action(self, obs, current_step=None):
        """预测动作 - 集成ACT Chunked Execution机制"""
        if self.model is None:
            # 使用随机动作
            action_shape = (self.args.num_envs, self.env.action_space.shape[0])
            random_action = (
                2.0 * torch.rand(action_shape, device=self.env.unwrapped.device) - 1.0
            )
            return random_action

        # ✅ ACT Chunked Execution逻辑
        action_from_buffer = None
        is_using_buffer = False

        if self.chunked_execution_enabled and current_step is not None:
            # 首先尝试从buffer获取动作
            action_from_buffer = self._get_action_from_buffer()
            if action_from_buffer is not None:
                if hasattr(self.args, "debug") and self.args.debug:
                    print(
                        f"📋 从buffer执行动作 {self.buffer_index}/{self.action_buffer.shape[1] if self.action_buffer is not None else 0}"
                    )
                is_using_buffer = True
                # 不直接返回，继续执行后续的处理和调试逻辑
            else:
                # Buffer为空或用尽，需要新预测
                if hasattr(self.args, "debug") and self.args.debug:
                    print(f"🧠 请求新的动作序列预测 (step {current_step})")

                # 记录新预测的步数（不重置buffer状态，因为_store_predicted_actions会处理）
                self.last_prediction_step = current_step

        try:
            with torch.no_grad():
                # === 观察和目标状态处理 === (保持原有逻辑)
                if isinstance(obs, dict):
                    obs_for_prediction = obs.get("policy", obs)
                    target_pose = self._extract_target_pose_from_obs(obs)
                    print(
                        f"\r{Colors.CYAN}🎯 目标位姿: {target_pose[0][16:19]}{Colors.RESET}",
                        end="",
                        flush=True,
                    )
                else:
                    obs_for_prediction = obs
                    target_pose = None

                # 🚀 优化：使用高效的tensor转换
                if not isinstance(obs_for_prediction, torch.Tensor):
                    # 使用from_numpy + to() 比 tensor() 更高效
                    obs_tensor = torch.from_numpy(obs_for_prediction).to(
                        dtype=torch.float32, device=self.device
                    )
                else:
                    obs_tensor = obs_for_prediction.to(self.device)

                # 如果使用buffer，跳过模型预测
                if is_using_buffer:
                    predicted_output = None  # buffer执行时不需要模型输出
                    action_tensor_selected = action_from_buffer
                    # 跳过模型相关的处理，直接进入后续的动作处理流程
                else:
                    # === 进行模型预测流程 ===
                    # ✅ 正确理解：seq2seq架构期望34维观察输入
                    if obs_tensor.shape[-1] == 34:  # 期望的34维观察
                        obs_for_model = obs_tensor
                        if hasattr(self.args, "debug") and self.args.debug:
                            print("✅ 使用34维观察输入（seq2seq架构）")
                    else:
                        print(f"⚠️ 观察维度异常: 期望34维，实际{obs_tensor.shape[-1]}维")
                        print(
                            f"   模型训练时期望34维输入，输出{self.seq_length}个动作序列"
                        )
                        obs_for_model = obs_tensor

                    # 应用归一化（保持原有逻辑）
                    if (
                        self.enable_action_scaling
                        and self.action_scaling_processor is not None
                    ):
                        if self.enable_separated_normalization:
                            # 分离归一化模式（保持原有逻辑）
                            try:
                                # 🚀 优化：延迟CPU转换，尽量保持tensor操作
                                obs_numpy = (
                                    obs_for_model.cpu().numpy()
                                    if isinstance(obs_for_model, torch.Tensor)
                                    else obs_for_model
                                )

                                if hasattr(
                                    self.action_scaling_processor,
                                    "normalize_observations_separated",
                                ):
                                    normalized_obs_34d = self.action_scaling_processor.normalize_observations_separated(
                                        obs_numpy
                                    )
                                elif hasattr(
                                    self.action_scaling_processor,
                                    "normalize_observations",
                                ):
                                    if (
                                        hasattr(
                                            self.action_scaling_processor,
                                            "separated_stats",
                                        )
                                        and self.action_scaling_processor.separated_stats
                                        is not None
                                    ):
                                        obs_stats = (
                                            self.action_scaling_processor.separated_stats.observations
                                        )
                                        obs_mean = obs_stats.get("mean", 0.0)
                                        obs_std = obs_stats.get("std", 1.0)
                                        normalized_obs_34d = (obs_numpy - obs_mean) / (
                                            obs_std + 1e-8
                                        )
                                    else:
                                        print("⚠️ 缺少分离归一化统计信息，使用原始观察")
                                        normalized_obs_34d = obs_numpy
                                else:
                                    print("⚠️ 未找到分离归一化方法，使用原始观察")
                                    normalized_obs_34d = obs_numpy

                                obs_tensor = (
                                    torch.from_numpy(normalized_obs_34d)
                                    .float()
                                    .to(obs_tensor.device)
                                )

                                # 处理target_pose归一化
                                if target_pose is not None:
                                    # 🚀 优化：批量处理target_pose转换
                                    target_pose_np = (
                                        target_pose.cpu().numpy()
                                        if isinstance(target_pose, torch.Tensor)
                                        else np.array(target_pose)
                                    )

                                    if (
                                        hasattr(
                                            self.action_scaling_processor,
                                            "separated_stats",
                                        )
                                        and self.action_scaling_processor.separated_stats
                                        is not None
                                    ):
                                        target_stats = (
                                            self.action_scaling_processor.separated_stats.target_pose
                                        )
                                        target_mean = target_stats.get("mean", 0.0)
                                        target_std = target_stats.get("std", 1.0)
                                        normalized_target_pose = (
                                            target_pose_np - target_mean
                                        ) / (target_std + 1e-8)
                                    else:
                                        print("⚠️ 缺少目标位姿归一化统计，使用原始数据")
                                        normalized_target_pose = target_pose_np

                                    target_pose = (
                                        torch.from_numpy(normalized_target_pose)
                                        .float()
                                        .to(obs_tensor.device)
                                    )
                                else:
                                    print("⚠️ 分离归一化模式下未找到target_pose")

                            except Exception as e:
                                print(f"⚠️ 分离归一化应用失败: {e}")
                                import traceback

                                traceback.print_exc()
                        else:
                            # 传统归一化模式
                            obs_tensor = (
                                self.action_scaling_processor.normalize_observations(
                                    obs_for_model
                                )
                            )

                            if target_pose is not None:
                                target_pose = self.action_scaling_processor.normalize_observations(
                                    target_pose
                                )

                    # 使用处理后的观察调用predict方法
                    if (
                        self.enable_action_scaling
                        and self.action_scaling_processor is not None
                    ):
                        model_input = obs_tensor
                    else:
                        model_input = obs_for_model

                    # ✅ 模型预测 - 直接调用ACT模型获取完整序列
                    if (
                        self.chunked_execution_enabled
                        and hasattr(self.model, "act_model")
                        and hasattr(self.model.act_model, "sample_actions")
                    ):
                        # 直接使用ACT模型的sample_actions方法获取完整序列
                        if hasattr(self.args, "debug") and self.args.debug:
                            print(
                                f"🧠 直接调用ACT模型获取完整序列 (chunk_size={self.seq_length})"
                            )
                        try:
                            with torch.no_grad():
                                # 提取特征
                                extracted_features = self.model._extract_features(
                                    model_input, target_pose=target_pose
                                )
                                # 获取完整动作序列
                                sequence_output = self.model.act_model.sample_actions(
                                    extracted_features,
                                    target_pose=target_pose,
                                    deterministic=True,
                                )
                                predicted_output = sequence_output[
                                    "actions"
                                ]  # [B, seq_length, action_dim]
                                if hasattr(self.args, "debug") and self.args.debug:
                                    print(
                                        f"✅ 获取完整ACT序列: {predicted_output.shape}"
                                    )
                        except Exception as e:
                            print(f"⚠️ ACT序列获取失败: {e}，回退到标准方法")
                            predicted_output = self.model.predict(
                                model_input, target_pose=target_pose
                            )

                    else:
                        # 标准方法：单步预测
                        predicted_output = self.model.predict(
                            model_input, target_pose=target_pose
                        )

                    # ✅ ACT Chunked Execution: 存储完整序列
                    if self.chunked_execution_enabled:
                        # 对于ACT模型，predicted_output应该是 [batch, seq_length, action_dim]
                        # print(f"🔍 模型输出维度调试: {predicted_output.shape}")
                        self._store_predicted_actions(predicted_output)
                        # 立即获取第一个动作执行
                        action_tensor_selected = self._get_action_from_buffer()
                        if action_tensor_selected is None:
                            print("⚠️ Buffer为空，使用降级处理")
                            # 降级处理：使用原有的选择策略
                            action_tensor_selected = self._select_action_from_sequence(
                                predicted_output,
                                strategy=self.args.action_selection_strategy,
                            )
                    else:
                        # 非chunked模式：使用原有的选择策略
                        action_tensor_selected = self._select_action_from_sequence(
                            predicted_output,
                            strategy=self.args.action_selection_strategy,
                        )

            # 其余处理逻辑保持不变（6D旋转正交化、动作反缩放等）
            action_tensor_corrected = None
            try:
                if (
                    action_tensor_selected.shape[-1] == 25
                ):  # 单手模式：16关节 + 9位姿(3位置+6旋转)
                    action_joints = action_tensor_selected[:, :16]
                    action_position = action_tensor_selected[:, 16:19]
                    action_rotation_6d = action_tensor_selected[:, 19:25]

                    # 正交化6D旋转
                    action_rotation_matrices = rot6d_to_matrix_gram_schmidt(
                        action_rotation_6d, validate=True
                    )

                    action_rotation_6d_corrected = matrix_to_rot6d(
                        action_rotation_matrices
                    )

                    action_tensor_corrected = torch.cat(
                        [
                            action_joints,
                            action_position,
                            action_rotation_6d_corrected,
                        ],
                        dim=-1,
                    )

                    # invalid_rotations = (~action_rotation_valid).sum().item()
                    # if (
                    #     invalid_rotations > 0
                    #     and hasattr(self.args, "debug")
                    #     and self.args.debug
                    # ):
                    #     print(f"🔧 6D旋转正交化: 修正了{invalid_rotations}个无效旋转")
                else:
                    action_tensor_corrected = action_tensor_selected.clone()

            except Exception as e:
                print(f"⚠️ 6D旋转正交化失败: {e}")
                action_tensor_corrected = action_tensor_selected.clone()

            # 应用动作反缩放
            action_tensor_final = action_tensor_corrected
            if self.enable_action_scaling and self.action_scaling_processor is not None:
                try:
                    if hasattr(self.action_scaling_processor, "unscale_actions"):
                        action_tensor_final, scaling_method = (
                            self.action_scaling_processor.unscale_actions(
                                action_tensor_corrected
                            )
                        )
                    elif hasattr(self.action_scaling_processor, "denormalize_actions"):
                        action_tensor_final = (
                            self.action_scaling_processor.denormalize_actions(
                                action_tensor_corrected
                            )
                        )
                    else:
                        print("⚠️ 动作缩放处理器缺少反缩放方法，使用原始动作")
                        action_tensor_final = action_tensor_corrected
                except Exception as e:
                    print(f"⚠️ 动作反缩放失败: {e}")
                    action_tensor_final = action_tensor_corrected

            # === ✅ 新增：动作反向坐标转换（物体中心坐标系 → 世界坐标系）===
            action_tensor_world = action_tensor_final
            if self.use_object_centric and self.object_centric_transformer is not None:
                try:
                    action_tensor_world = self._transform_action_to_world_frame(
                        action_tensor_final
                    )

                    if hasattr(self.args, "debug") and self.args.debug:
                        print("🔄 动作坐标转换完成: 物体中心坐标系 → 世界坐标系")
                except Exception as e:
                    print(f"⚠️ 动作坐标转换失败: {e}")
                    # 如果转换失败，使用原始动作
                    action_tensor_world = action_tensor_final

            # 打印详细调试表格
            if self.enable_debug_table:
                step_idx = getattr(self, "_debug_step_counter", 0)
                self._debug_step_counter = getattr(self, "_debug_step_counter", 0) + 1

                # 判断是否为新序列预测（如果使用了chunked模式且刚才进行了模型预测）
                is_new_sequence = (
                    self.chunked_execution_enabled
                    and hasattr(self, "buffer_index")
                    and self.buffer_index == 1
                    and hasattr(self, "last_prediction_step")
                    and self.last_prediction_step == current_step
                )

                # 准备buffer信息
                buffer_info = None
                if self.chunked_execution_enabled and hasattr(self, "action_buffer"):
                    if self.action_buffer is not None:
                        # action_buffer应该是tensor，使用shape获取维度
                        total_actions = (
                            self.action_buffer.shape[1]
                            if self.action_buffer.dim() >= 2
                            else self.action_buffer.shape[0]
                        )
                        current_index = getattr(self, "buffer_index", 0)
                        remaining_actions = total_actions - current_index
                    else:
                        total_actions = 0
                        current_index = 0
                        remaining_actions = 0

                    buffer_info = {
                        "total_actions": total_actions,
                        "current_index": current_index,
                        "remaining_actions": remaining_actions,
                    }

                    # 添加当前动作预览
                    if action_tensor_selected is not None:
                        if isinstance(action_tensor_selected, torch.Tensor):
                            current_preview = (
                                action_tensor_selected.cpu().numpy().flatten()
                            )
                        else:
                            current_preview = np.array(action_tensor_selected).flatten()
                        buffer_info["current_action_preview"] = current_preview

                    # 添加下一个动作预览（如果有）
                    if (
                        self.action_buffer is not None
                        and hasattr(self, "buffer_index")
                        and self.buffer_index < total_actions
                    ):
                        # buffer的形状应该是 [batch, seq, action_dim]
                        next_action = (
                            self.action_buffer[0, self.buffer_index]
                            if self.action_buffer.dim() >= 2
                            else self.action_buffer[self.buffer_index]
                        )
                        if isinstance(next_action, torch.Tensor):
                            next_preview = next_action.cpu().numpy().flatten()
                        else:
                            next_preview = np.array(next_action).flatten()
                        buffer_info["next_action_preview"] = next_preview

                self._print_detailed_debug_table(
                    obs=obs,
                    predicted_output=predicted_output if is_new_sequence else None,
                    action_tensor_selected=action_tensor_selected,
                    action_tensor_corrected=action_tensor_corrected,
                    action_tensor_final=action_tensor_final,
                    action_tensor_world=action_tensor_world,
                    step_idx=step_idx,
                    is_new_sequence=is_new_sequence,
                    buffer_info=buffer_info,
                )

            # 验证动作维度
            expected_action_dim = 25
            if action_tensor_world.shape[-1] != expected_action_dim:
                print(
                    f"⚠️ 动作维度异常: 期望{expected_action_dim}维，实际{action_tensor_world.shape[-1]}维"
                )

            # 最终维度检查和修正
            if action_tensor_world.dim() == 1:
                # 如果是1维，扩展为2维 [action_dim] -> [1, action_dim]
                action_tensor_world = action_tensor_world.unsqueeze(0)
            elif action_tensor_world.dim() > 2:
                # 如果超过2维，取第一个batch [batch, seq, action_dim] -> [batch, action_dim]
                action_tensor_world = action_tensor_world[:, 0, :]

            # 确保batch size正确
            if action_tensor_world.shape[0] != self.args.num_envs:
                if action_tensor_world.shape[0] == 1 and self.args.num_envs > 1:
                    # 扩展到正确的环境数量
                    action_tensor_world = action_tensor_world.repeat(
                        self.args.num_envs, 1
                    )

            return action_tensor_world

        except Exception as e:
            print(f"⚠️ 动作预测失败: {e}")
            import traceback

            traceback.print_exc()
            # 返回随机动作作为后备
            action_shape = (self.args.num_envs, self.env.action_space.shape[0])
            return (
                2.0 * torch.rand(action_shape, device=self.env.unwrapped.device) - 1.0
            )

    def _transform_action_to_world_frame(
        self, action_object_frame: torch.Tensor
    ) -> torch.Tensor:
        """
        将模型输出的物体中心坐标系动作转换回世界坐标系

        这是评估阶段坐标系一致性的关键步骤：
        1. 模型接收物体中心坐标系的观察
        2. 模型输出物体中心坐标系的动作
        3. 需要将动作转换回世界坐标系给环境执行

        Args:
            action_object_frame: 物体中心坐标系下的动作 [batch_size, 25]

        Returns:
            action_world_frame: 世界坐标系下的动作 [batch_size, 25]
        """
        try:
            # 验证输入维度
            if action_object_frame.dim() != 2:
                if action_object_frame.dim() == 1:
                    # 如果是1维，扩展为2维 [action_dim] -> [1, action_dim]
                    print(
                        f"🔧 动作坐标转换：将1维tensor扩展为2维，shape: {action_object_frame.shape} -> {action_object_frame.unsqueeze(0).shape}"
                    )
                    action_object_frame = action_object_frame.unsqueeze(0)
                else:
                    print(
                        f"⚠️ 动作坐标转换：期望2维tensor，实际{action_object_frame.dim()}维，shape: {action_object_frame.shape}"
                    )
                    return action_object_frame

            if action_object_frame.shape[-1] != 25:
                print(
                    f"⚠️ 动作坐标转换：期望25维动作，实际{action_object_frame.shape[-1]}维"
                )
                return action_object_frame

            # 获取物体的世界坐标系状态
            object_pos_world, object_quat_world = self._get_object_world_state()

            if object_pos_world is None or object_quat_world is None:
                print("⚠️ 无法获取物体世界坐标系状态，使用原始动作")
                return action_object_frame

            # 分解动作为关节、位置、旋转组件
            batch_size = action_object_frame.shape[0]
            action_joints = action_object_frame[:, :16]  # 关节动作不需要坐标转换
            action_position_obj = action_object_frame[:, 16:19]  # 物体中心坐标系位置
            action_rotation_6d_obj = action_object_frame[:, 19:25]  # 物体中心坐标系旋转

            # 导入坐标转换工具
            from px_janus_learnsim.utils.coordinate_transform_utils import (
                batch_transform_hand_states_from_object_frame,
            )

            # 将位置和旋转从物体中心坐标系转换回世界坐标系
            action_position_world, action_rotation_6d_world = (
                batch_transform_hand_states_from_object_frame(
                    action_position_obj,  # 物体坐标系下的位置
                    action_rotation_6d_obj,  # 物体坐标系下的6D旋转
                    object_pos_world,  # 物体世界位置
                    object_quat_world,  # 物体世界四元数
                )
            )

            # 重新组合动作
            action_world_frame = torch.cat(
                [
                    action_joints,  # [batch_size, 16] 关节动作
                    action_position_world,  # [batch_size, 3] 世界坐标系位置
                    action_rotation_6d_world,  # [batch_size, 6] 世界坐标系6D旋转
                ],
                dim=-1,
            )  # [batch_size, 25]

            return action_world_frame

        except Exception as e:
            print(f"⚠️ 动作坐标转换详细错误: {e}")
            import traceback

            traceback.print_exc()
            return action_object_frame

    def _display_dict_item_details(self, item_dict, index):
        """显示字典项目的详细信息"""
        # 🔍 深入探索字典内容
        for key, value in item_dict.items():
            if hasattr(value, "shape"):
                print(f"         🗝️ {key}: shape={value.shape}, dtype={value.dtype}")
            elif hasattr(value, "__len__") and not isinstance(value, str):
                print(f"         🗝️ {key}: len={len(value)}, type={type(value)}")
            else:
                # 如果是小的值，显示内容；如果太大则只显示类型
                if isinstance(value, (int, float, bool)) or (
                    isinstance(value, str) and len(value) < 50
                ):
                    print(f"         🗝️ {key}: {value} (type={type(value)})")
                else:
                    print(f"         🗝️ {key}: type={type(value)}")

    def _convert_to_single_hand_policy_format(
        self, obs_dict, actions_dict, target_pose, hand_type
    ):
        """
        将双手训练数据转换为单手Policy期望的格式

        Args:
            obs_dict: 训练时的观察字典
            actions_dict: 训练时的动作字典
            target_pose: 训练时的目标位姿
            hand_type: 'left' 或 'right'

        Returns:
            policy_obs_dict: Policy期望的观察格式 (34维向量或obs_dict)
            policy_actions: Policy期望的动作格式 [B, 25]
            policy_target_pose: Policy期望的目标位姿 [B, 25]
        """
        try:
            # 1. 提取指定手的数据
            if hand_type not in ["left", "right"]:
                print(f"   ⚠️ 不支持的手部类型: {hand_type}")
                return None, None, None

            target_hand_key = f"{hand_type}hand"

            if target_hand_key not in obs_dict:
                print(f"   ⚠️ 观察数据中未找到{target_hand_key}")
                return None, None, None

            # 2. 提取手部观察数据 (16关节 + 9位姿 = 25维)
            hand_joints = obs_dict[target_hand_key]["joints"]  # [B, 16]
            hand_pose = obs_dict[target_hand_key]["handpose"]  # [B, 9]

            # 3. 提取主要物体数据 (通常是obj1)
            object_data = None
            for obj_key in ["obj1", "object", "obj2"]:  # 按优先级查找
                if obj_key in obs_dict:
                    object_data = obs_dict[obj_key]  # [B, 9]
                    print(
                        f"      📦 使用物体数据: {obj_key}, shape={object_data.shape}"
                    )
                    break

            if object_data is None:
                print("   ⚠️ 未找到物体数据")
                return None, None, None

            # 4. 组合34维观察向量 [手部25维 + 物体9维]
            policy_obs_34d = torch.cat(
                [hand_joints, hand_pose, object_data], dim=-1
            )  # [B, 34]

            # 5. 构建Policy期望的obs_dict格式（如果需要）
            policy_obs_dict = {"policy": policy_obs_34d}  # [B, 34]

            # 6. 提取对应手的动作数据
            if target_hand_key in actions_dict:
                hand_actions = actions_dict[target_hand_key]

                # 检查动作数据结构
                if isinstance(hand_actions, dict):
                    print(
                        f"      🔍 {target_hand_key}动作数据是字典，键: {list(hand_actions.keys())}"
                    )

                    # 显示所有动作组件
                    for key, value in hand_actions.items():
                        if hasattr(value, "shape"):
                            print(f"         📊 动作组件[{key}]: {value.shape}")

                    # 优先查找完整的动作tensor
                    if "actions" in hand_actions:
                        policy_actions = hand_actions["actions"]
                        print("      ✅ 使用完整动作tensor: actions")

                    else:
                        # 🆕 组合joints和handpose形成25维动作
                        if "joints" in hand_actions and "handpose" in hand_actions:
                            action_joints = hand_actions["joints"]  # [B, T, 16]
                            action_handpose = hand_actions["handpose"]  # [B, T, 9]

                            print(
                                f"      🔧 组合动作: joints{action_joints.shape} + handpose{action_handpose.shape}"
                            )

                            # 检查维度兼容性
                            if action_joints.shape[:-1] == action_handpose.shape[:-1]:
                                # 在最后一维拼接: [B, T, 16] + [B, T, 9] -> [B, T, 25]
                                policy_actions = torch.cat(
                                    [action_joints, action_handpose], dim=-1
                                )
                                print(
                                    f"      ✅ 成功组合为25维动作: {policy_actions.shape}"
                                )
                            else:
                                print("      ⚠️ joints和handpose维度不兼容，无法组合")
                                policy_actions = action_joints  # 降级使用joints
                        else:
                            # 查找第一个tensor作为后备
                            policy_actions = None
                            for key, value in hand_actions.items():
                                if hasattr(value, "shape") and policy_actions is None:
                                    policy_actions = value
                                    print(f"      ⚠️ 降级使用单一组件: {key}")
                                    break

                            if policy_actions is None:
                                print(
                                    f"   ⚠️ 在{target_hand_key}动作字典中未找到tensor数据"
                                )
                                return None, None, None

                elif hasattr(hand_actions, "shape"):
                    # 直接是tensor
                    policy_actions = hand_actions
                    print(f"      ✅ 使用直接动作tensor: {policy_actions.shape}")
                else:
                    print(f"   ⚠️ 不支持的动作数据类型: {type(hand_actions)}")
                    return None, None, None
            else:
                print(f"   ⚠️ 动作数据中未找到{target_hand_key}")
                return None, None, None

            # 7. 处理目标位姿
            if target_pose is not None:
                if target_pose.shape[-1] == 18:  # 双手目标位姿
                    # 假设前9维是左手，后9维是右手（需要根据实际数据结构调整）
                    if hand_type == "left":
                        # 左手：取前9维位姿，需要组合16维关节目标
                        hand_pose_target = target_pose[:, :9]  # [B, 9]
                        # 从当前手部关节提取目标（或使用零向量）
                        hand_joints_target = torch.zeros_like(hand_joints)  # [B, 16]
                        policy_target_pose = torch.cat(
                            [hand_joints_target, hand_pose_target], dim=-1
                        )  # [B, 25]
                    else:  # right
                        # 右手：取后9维位姿
                        hand_pose_target = target_pose[:, 9:18]  # [B, 9]
                        hand_joints_target = torch.zeros_like(hand_joints)  # [B, 16]
                        policy_target_pose = torch.cat(
                            [hand_joints_target, hand_pose_target], dim=-1
                        )  # [B, 25]
                elif target_pose.shape[-1] == 25:  # 已经是单手格式
                    policy_target_pose = target_pose
                else:
                    print(f"   ⚠️ 不支持的目标位姿维度: {target_pose.shape[-1]}")
                    policy_target_pose = None
            else:
                policy_target_pose = None

            print(f"      ✅ 成功转换为{hand_type}手Policy格式")
            print(f"         观察维度: {policy_obs_34d.shape} (期望34维)")
            print(f"         动作维度: {policy_actions.shape}")
            if policy_target_pose is not None:
                print(f"         目标位姿维度: {policy_target_pose.shape} (期望25维)")

            return policy_obs_dict, policy_actions, policy_target_pose

        except Exception as e:
            print(f"   ❌ 转换为单手格式失败: {e}")
            import traceback

            traceback.print_exc()
            return None, None, None

    def _get_object_world_state(self) -> tuple[torch.Tensor, torch.Tensor]:
        """
        从环境获取物体的世界坐标系位置和旋转

        Returns:
            object_pos_world: 物体世界位置 [batch_size, 3]
            object_quat_world: 物体世界四元数 [batch_size, 4] (w,x,y,z)
        """
        try:
            # 通过环境获取物体状态
            env_unwrapped = self.env.unwrapped

            if hasattr(env_unwrapped, "object1") and env_unwrapped.object1 is not None:
                # 从环境的object1获取世界坐标系状态
                object_pos_world = (
                    env_unwrapped.object1.data.root_pos_w.clone()
                )  # [num_envs, 3]
                object_quat_world = (
                    env_unwrapped.object1.data.root_quat_w.clone()
                )  # [num_envs, 4]

                # 确保在正确的设备上
                object_pos_world = object_pos_world.to(self.device)
                object_quat_world = object_quat_world.to(self.device)

                return object_pos_world, object_quat_world
            else:
                print("⚠️ 环境中未找到object1或object1数据不可用")
                return None, None

        except Exception as e:
            print(f"⚠️ 获取物体世界状态失败: {e}")
            return None, None

    def evaluate_episode(self, episode_idx: int) -> Dict:
        """评估单个episode"""
        print(f"🎮 评估Episode {episode_idx + 1}/{self.args.episodes}")

        episode_data = {
            "episode_idx": episode_idx,
            "success": False,
            "total_steps": 0,
            "total_reward": 0.0,
            "target_achieved_steps": [],  # 记录达到目标的步数
            "final_position_error": None,
            "final_joint_error": None,
            "initial_state": None,
            "target_state": None,
            "trajectory": [] if self.args.save_trajectories else None,
        }

        # 重置环境（会自动生成随机目标和初始位姿）
        obs, info = self.env.reset()

        print("✅ 轨迹已加载到环境")

        # ✅ 重置ACT Chunked Execution状态
        self._reset_chunked_execution_state()

        # 重置调试计数器（每个episode开始时重置）
        self._debug_step_counter = 0

        # 🧪 数据集轨迹处理 - 按训练时格式处理数据
        trajectory_data = None
        if self.dataset_sampler is not None:
            try:
                print(f"\n📊 从数据集提取一条轨迹 (Episode {episode_idx + 1}):")
                sample = self.dataset_sampler.get_sample()
                print("   ✅ 成功提取数据样本")
                print(f"   📋 样本类型: {type(sample)}")

                # 🔄 处理训练数据格式，按照conditional_bc.py的逻辑
                if isinstance(sample, list) and len(sample) == 2:
                    # 训练格式：[state_dict, actions_dict]
                    state_dict, actions_dict = sample
                    print("   📦 训练格式数据: state_dict, actions_dict")
                    print(f"      - state_dict: type={type(state_dict)}")
                    print(f"      - actions_dict: type={type(actions_dict)}")

                    # 使用训练时相同的处理逻辑
                    try:
                        # 导入训练时的数据处理函数
                        from px_janus_learnsim.learning.models.bc_utils import (
                            process_imitation_batch,
                        )

                        # 按照训练时逻辑处理数据
                        obs_dict, target_pose, _, _, _ = process_imitation_batch(
                            sample, self.device
                        )

                        print("   ✅ 数据处理成功:")
                        print(f"      - obs_dict键: {list(obs_dict.keys())}")
                        for key, value in obs_dict.items():
                            if isinstance(value, dict):
                                for subkey, subvalue in value.items():
                                    if hasattr(subvalue, "shape"):
                                        print(
                                            f"         📊 obs_dict[{key}][{subkey}]: {subvalue.shape}"
                                        )
                            elif hasattr(value, "shape"):
                                print(f"         📊 obs_dict[{key}]: {value.shape}")

                        if target_pose is not None and hasattr(target_pose, "shape"):
                            print(f"      - target_pose: {target_pose.shape}")

                        # 提取动作数据
                        if isinstance(actions_dict, dict):
                            print(
                                f"      - actions_dict键: {list(actions_dict.keys())}"
                            )
                            for key, value in actions_dict.items():
                                if hasattr(value, "shape"):
                                    print(
                                        f"         📊 actions_dict[{key}]: {value.shape}"
                                    )

                        # 🆕 转换为单手Policy格式
                        policy_obs_dict, policy_actions, policy_target_pose = (
                            self._convert_to_single_hand_policy_format(
                                obs_dict, actions_dict, target_pose, self.args.hand_type
                            )
                        )

                        print("   🎯 转换为单手Policy格式:")
                        if policy_obs_dict is not None:
                            if isinstance(policy_obs_dict, dict):
                                print(
                                    f"      - policy_obs_dict键: {list(policy_obs_dict.keys())}"
                                )
                                for key, value in policy_obs_dict.items():
                                    if hasattr(value, "shape"):
                                        print(
                                            f"         📊 policy_obs_dict[{key}]: {value.shape}"
                                        )
                            elif hasattr(policy_obs_dict, "shape"):
                                print(
                                    f"      - policy_obs_dict: {policy_obs_dict.shape}"
                                )

                        if policy_actions is not None and hasattr(
                            policy_actions, "shape"
                        ):
                            print(f"      - policy_actions: {policy_actions.shape}")

                        if policy_target_pose is not None and hasattr(
                            policy_target_pose, "shape"
                        ):
                            print(
                                f"      - policy_target_pose: {policy_target_pose.shape}"
                            )

                        trajectory_data = {
                            "obs_dict": policy_obs_dict,  # 直接使用Policy格式
                            "actions": policy_actions,  # 直接使用Policy格式
                            "target_pose": policy_target_pose,  # 直接使用Policy格式
                            "format": "policy_format",
                        }

                        # 🔍 调试模式下保留原始数据
                        if getattr(self.args, "debug", False):
                            trajectory_data["debug_original_obs"] = obs_dict
                            trajectory_data["debug_original_actions"] = actions_dict

                    except ImportError as e:
                        print(f"   ⚠️ 无法导入训练数据处理函数: {e}")

                else:
                    print(f"   📄 其他格式样本: {sample}")

                print("   🔄 注意: 这条轨迹在迭代器耗尽时会重新出现（循环迭代）")
                print("   " + "=" * 70)

            except Exception as e:
                print(f"   ❌ 数据集采样失败: {e}")
                import traceback

                traceback.print_exc()
        else:
            print("   📊 数据集采样器未启用")

        # 🎯 如果成功处理了轨迹数据，用于设置环境初始状态

        if trajectory_data and self.args.use_dataset_poses:
            print("🎬 加载数据集轨迹到环境...")
            self.env.unwrapped.load_trajectory(trajectory_data)
            print("✅ 轨迹已加载到环境")
            print("   📍 初始状态: 轨迹第0步的手部姿态")
            print("   🎯 目标状态: 轨迹最后一步的手部姿态")
        elif self.args.use_dataset_poses and trajectory_data is None:
            print("⚠️ 启用了数据集轨迹模式但未能获取轨迹数据")
        else:
            print("🎲 使用随机生成的目标和初始位姿")

        # 记录初始状态和目标状态
        if "evaluation_info" in obs:
            eval_info = obs["evaluation_info"]
            episode_data["initial_state"] = eval_info["current_hand_state"]
            episode_data["target_state"] = {
                "joints": eval_info["target_hand_joints"],
                "position": eval_info["target_hand_position"],
            }

        step_rewards = []
        step = 0

        # 循环直到环境自然终结，不使用max_steps限制
        while True:
            # ✅ 预测动作 - 传递当前步数以支持ACT Chunked Execution
            action = self.predict_action(obs, current_step=step)

            # 执行动作
            next_obs, reward, terminated, truncated, info = self.env.step(action)

            # 记录奖励
            if isinstance(reward, torch.Tensor):
                step_reward = reward.mean().item()
            else:
                step_reward = float(reward)
            step_rewards.append(step_reward)

            # 检查是否达到目标
            if "evaluation_info" in next_obs:
                eval_info = next_obs["evaluation_info"]
                target_achieved = eval_info["target_achieved"]

                # 如果任何一个环境达到目标
                if any(target_achieved):
                    episode_data["target_achieved_steps"].append(step)

                # 检查episode成功
                if eval_info["episode_success"]:
                    episode_data["success"] = True
                    episode_data["final_position_error"] = min(
                        eval_info["position_distances"]
                    )
                    episode_data["final_joint_error"] = min(
                        eval_info["joint_distances"]
                    )
                    print(f"   ✅ 成功! 步数: {step}")
                    break

            # 保存轨迹数据
            if self.args.save_trajectories:
                episode_data["trajectory"].append(
                    {
                        "step": step,
                        "obs": obs.copy() if isinstance(obs, np.ndarray) else obs,
                        "action": (
                            action.cpu().numpy()
                            if isinstance(action, torch.Tensor)
                            else action
                        ),
                        "reward": step_reward,
                        "info": info,
                    }
                )

            # 检查是否环境自然终结
            if terminated.any() or truncated.any():
                break

            obs = next_obs
            step += 1
            episode_data["total_steps"] = step

            # 安全保护：防止无限循环（使用一个很大的数）
            if step >= 10000:
                print(f"⚠️ Episode {episode_idx + 1} 达到安全步数限制 {step}")
                break

        # 计算最终指标
        episode_data["total_reward"] = sum(step_rewards)
        episode_data["average_reward"] = np.mean(step_rewards) if step_rewards else 0.0

        # 如果没有成功，记录最终误差
        if not episode_data["success"] and "evaluation_info" in obs:
            eval_info = obs["evaluation_info"]
            episode_data["final_position_error"] = min(eval_info["position_distances"])
            episode_data["final_joint_error"] = min(eval_info["joint_distances"])

        if self.args.detailed:
            print(f"   📊 Episode {episode_idx + 1} 结果:")
            print(f"      成功: {'✅' if episode_data['success'] else '❌'}")
            print(f"      步数: {episode_data['total_steps']}")
            print(f"      总奖励: {episode_data['total_reward']:.4f}")
            print(f"      平均奖励: {episode_data['average_reward']:.4f}")
            if episode_data["final_position_error"] is not None:
                print(
                    f"      最终位置误差: {episode_data['final_position_error']:.6f}m"
                )
            if episode_data["final_joint_error"] is not None:
                print(f"      最终关节误差: {episode_data['final_joint_error']:.6f}rad")

        return episode_data

    def calculate_summary_statistics(self):
        """计算汇总统计数据"""
        episodes = self.evaluation_results["episodes_data"]

        if not episodes:
            print("⚠️ 没有可用的episode数据")
            return

        # 基本统计
        success_count = sum(1 for ep in episodes if ep["success"])
        total_rewards = [ep["total_reward"] for ep in episodes]
        average_rewards = [ep["average_reward"] for ep in episodes]
        total_steps = [ep["total_steps"] for ep in episodes]

        # 位置和关节误差统计
        position_errors = [
            ep["final_position_error"]
            for ep in episodes
            if ep["final_position_error"] is not None
        ]
        joint_errors = [
            ep["final_joint_error"]
            for ep in episodes
            if ep["final_joint_error"] is not None
        ]

        # 成功episode的步数统计
        success_steps = [ep["total_steps"] for ep in episodes if ep["success"]]

        summary = {
            "total_episodes": len(episodes),
            "successful_episodes": success_count,
            "success_rate": success_count / len(episodes),
            # 奖励统计
            "average_total_reward": np.mean(total_rewards),
            "std_total_reward": np.std(total_rewards),
            "average_episode_reward": np.mean(average_rewards),
            "std_episode_reward": np.std(average_rewards),
            # 步数统计
            "average_episode_length": np.mean(total_steps),
            "std_episode_length": np.std(total_steps),
            "max_episode_length": np.max(total_steps),
            "min_episode_length": np.min(total_steps),
            # 成功相关统计
            "average_success_steps": np.mean(success_steps) if success_steps else None,
            "std_success_steps": np.std(success_steps) if success_steps else None,
            # 误差统计
            "average_position_error": (
                np.mean(position_errors) if position_errors else None
            ),
            "std_position_error": np.std(position_errors) if position_errors else None,
            "min_position_error": np.min(position_errors) if position_errors else None,
            "max_position_error": np.max(position_errors) if position_errors else None,
            "average_joint_error": np.mean(joint_errors) if joint_errors else None,
            "std_joint_error": np.std(joint_errors) if joint_errors else None,
            "min_joint_error": np.min(joint_errors) if joint_errors else None,
            "max_joint_error": np.max(joint_errors) if joint_errors else None,
        }

        self.evaluation_results["summary"] = summary

    def save_results(self):
        """保存评估结果到文件"""

        # 保存JSON结果
        json_file = self.output_dir / "evaluation_results.json"
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(
                self.evaluation_results,
                f,
                indent=2,
                ensure_ascii=False,
                cls=NumpyEncoder,
            )

        print(f"📄 JSON结果已保存: {json_file}")

        # 保存CSV摘要
        csv_file = self.output_dir / "evaluation_summary.csv"
        summary = self.evaluation_results["summary"]

        csv_data = {
            "手部类型": [self.args.hand_type],
            "总episodes": [summary["total_episodes"]],
            "成功episodes": [summary["successful_episodes"]],
            "成功率": [f"{summary['success_rate']:.2%}"],
            "平均奖励": [f"{summary['average_total_reward']:.4f}"],
            "平均位置误差": [f"{summary['average_position_error']:.4f}m"],
            "平均关节误差": [f"{summary['average_joint_error']:.4f}rad"],
            "平均步数": [f"{summary['average_episode_length']:.1f}"],
            "评估时间": [f"{self.evaluation_results['evaluation_time']:.2f}s"],
        }

        import pandas as pd

        df = pd.DataFrame(csv_data)
        df.to_csv(csv_file, index=False, encoding="utf-8")

        print(f"📊 CSV摘要已保存: {csv_file}")
        print(f"💾 所有评估结果已保存到: {self.output_dir}")

    def generate_summary_text(self):
        """生成汇总文本报告"""
        summary = self.evaluation_results["summary"]

        lines = [
            "=" * 60,
            "🤖 单手DexH13控制模型评估报告",
            "=" * 60,
            f"📁 模型路径: {self.args.model_path}",
            f"🤚 手部类型: {self.args.hand_type}",
            f"📊 评估episodes: {self.args.episodes}",
            f"⏱️ 评估时间: {self.evaluation_results['evaluation_time']:.2f}秒",
            f"🎯 位置阈值: {self.args.position_tolerance}m",
            f"🔧 关节阈值: {self.args.joint_tolerance}rad",
            "",
            "🏆 评估结果:",
            "-" * 40,
            f"✅ 成功率: {summary['success_rate']:.1%} ({summary['successful_episodes']}/{summary['total_episodes']})",
            f"🏆 平均总奖励: {summary['average_total_reward']:.4f} ± {summary['std_total_reward']:.4f}",
            f"📈 平均episode奖励: {summary['average_episode_reward']:.4f} ± {summary['std_episode_reward']:.4f}",
            f"⏳ 平均episode长度: {summary['average_episode_length']:.1f} ± {summary['std_episode_length']:.1f}",
            "",
            "📏 精度分析:",
            "-" * 40,
        ]

        if summary["average_position_error"] is not None:
            lines.extend(
                [
                    f"📍 平均位置误差: {summary['average_position_error']:.6f}m ± {summary['std_position_error']:.6f}m",
                    f"📍 位置误差范围: [{summary['min_position_error']:.6f}, {summary['max_position_error']:.6f}]m",
                ]
            )

        if summary["average_joint_error"] is not None:
            lines.extend(
                [
                    f"🔧 平均关节误差: {summary['average_joint_error']:.6f}rad ± {summary['std_joint_error']:.6f}rad",
                    f"🔧 关节误差范围: [{summary['min_joint_error']:.6f}, {summary['max_joint_error']:.6f}]rad",
                ]
            )

        if summary["average_success_steps"] is not None:
            lines.extend(
                [
                    "",
                    "⚡ 成功统计:",
                    "-" * 40,
                    f"⏱️ 平均成功步数: {summary['average_success_steps']:.1f} ± {summary['std_success_steps']:.1f}",
                ]
            )

        lines.extend(
            ["", f"📅 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}", "=" * 60]
        )

        return "\n".join(lines)

    def print_summary_report(self):
        """打印汇总报告"""
        summary = self.evaluation_results["summary"]

        print("\n" + "=" * 50)
        print("📊 单手DexH13控制模型评估汇总")
        print("=" * 50)
        print(f"🤚 手部类型: {self.args.hand_type}")
        print(f"📁 模型路径: {self.args.model_path}")
        print(f"🎯 位置阈值: {self.args.position_tolerance}m")
        print(f"🔧 关节阈值: {self.args.joint_tolerance}rad")
        print(f"🌐 物体中心坐标系: {'启用' if self.use_object_centric else '禁用'}")
        print(f"🎬 动作选择策略: {self.args.action_selection_strategy}")
        print(f"⏱️ 评估时间: {self.evaluation_results['evaluation_time']:.2f}秒")
        print("-" * 50)
        print(
            f"✅ 成功率: {summary['success_rate']:.1%} ({summary['successful_episodes']}/{summary['total_episodes']})"
        )
        print(
            f"🏆 平均总奖励: {summary['average_total_reward']:.4f} ± {summary['std_total_reward']:.4f}"
        )
        print(
            f"⏳ 平均episode长度: {summary['average_episode_length']:.1f} ± {summary['std_episode_length']:.1f}"
        )

        if summary["average_position_error"] is not None:
            print(f"📍 平均位置误差: {summary['average_position_error']:.6f}m")
        if summary["average_joint_error"] is not None:
            print(f"🔧 平均关节误差: {summary['average_joint_error']:.6f}rad")
        if summary["average_success_steps"] is not None:
            print(f"⚡ 平均成功步数: {summary['average_success_steps']:.1f}")

        print("=" * 50)

    def _print_detailed_debug_table(
        self,
        obs,
        predicted_output,
        action_tensor_selected,
        action_tensor_corrected,
        action_tensor_final,
        action_tensor_world,
        step_idx=None,
        is_new_sequence=False,
        buffer_info=None,
    ):
        """
        打印详细的调试表格，显示目标手、当前手的语义信息以及模型动作处理的各个阶段
        支持ACT Chunked Execution机制的调试信息显示

        Args:
            obs: 环境观察
            predicted_output: 模型原始输出（新序列时为完整序列，buffer执行时为None）
            action_tensor_selected: 选择后的动作（当前执行的单个动作）
            action_tensor_corrected: 6D旋转正交化后的动作
            action_tensor_final: 最终缩放后的动作
            action_tensor_world: 世界坐标系下的动作
            step_idx: 当前步数（可选）
            is_new_sequence: 是否为新序列预测（True）还是从buffer执行（False）
            buffer_info: buffer状态信息字典
        """
        try:
            # 检查是否启用调试表格
            if not self.enable_debug_table:
                return

            # 检查调试表格打印间隔
            if self.debug_table_interval <= 0:
                return  # 间隔为0表示禁用

            # 只在特定步数打印，避免过多输出
            if step_idx is not None and step_idx % self.debug_table_interval != 0:
                return

            print("\n" + "=" * 120)
            if step_idx is not None:
                if is_new_sequence:
                    execution_mode = "🆕 新序列预测"
                else:
                    execution_mode = "📋 Buffer执行"
                print(
                    f"{Colors.SPECIAL_HEADER} 🔍 详细调试信息 (第 {step_idx} 步, {execution_mode}, 间隔: {self.debug_table_interval}) {Colors.RESET}"
                )
            else:
                print(f"{Colors.SPECIAL_HEADER} 🔍 详细调试信息 {Colors.RESET}")
            print("=" * 120)

            # === 1. 解析观察空间中的语义信息 ===
            current_obs = obs.get("policy", obs) if isinstance(obs, dict) else obs

            # 转换为numpy便于处理
            if isinstance(current_obs, torch.Tensor):
                current_obs_np = current_obs.cpu().numpy()
            else:
                current_obs_np = np.array(current_obs)

            # 确保是一维数组（取第一个环境的数据）
            if current_obs_np.ndim > 1:
                current_obs_np = current_obs_np[0]

            # DexH13手部语义映射（基于34维观察空间）
            finger_names = ["拇指", "食指", "中指", "无名指", "小指"]
            finger_joint_indices = {
                "拇指": [0, 1, 2, 3],  # 4个关节
                "食指": [4, 5, 6, 7],  # 4个关节
                "中指": [8, 9, 10, 11],  # 4个关节
                "无名指": [12, 13, 14, 15],  # 4个关节
            }

            # 手腕位置和6D旋转在观察空间中的位置
            wrist_pos_indices = [16, 17, 18]  # XYZ位置
            wrist_rot6d_indices = [19, 20, 21, 22, 23, 24]  # 6D旋转

            # 📊 当前手部状态表格
            current_state_table = PrettyTable()
            current_state_table.field_names = ["部位", "关节值", "单位"]
            current_state_table.align["部位"] = "l"
            current_state_table.align["关节值"] = "r"
            current_state_table.align["单位"] = "c"

            # 添加手指关节数据
            for finger_name, joint_indices in finger_joint_indices.items():
                joint_values = [current_obs_np[i] for i in joint_indices]
                joint_str = ", ".join([f"{val:.4f}" for val in joint_values])
                current_state_table.add_row(
                    [f"🖐️ {finger_name}", f"[{joint_str}]", "rad"]
                )

            # 添加手腕位置和旋转数据
            wrist_pos = [current_obs_np[i] for i in wrist_pos_indices]
            wrist_rot6d = [current_obs_np[i] for i in wrist_rot6d_indices]
            current_state_table.add_row(
                [
                    "🤚 手腕位置",
                    f"[{wrist_pos[0]:.4f}, {wrist_pos[1]:.4f}, {wrist_pos[2]:.4f}]",
                    "m",
                ]
            )
            current_state_table.add_row(
                [
                    "🔄 手腕6D旋转",
                    f"[{', '.join([f'{v:.4f}' for v in wrist_rot6d])}]",
                    "-",
                ]
            )

            # 确定坐标系说明
            coord_system = "物体中心坐标系" if self.use_object_centric else "世界坐标系"
            print(
                f"\n{Colors.BLUE}{Colors.BOLD}📊 当前手部状态 (从观察空间 - {coord_system}){Colors.RESET}"
            )
            print(current_state_table)

            # === 2. 目标手信息 ===
            target_pose = self._extract_target_pose_from_obs(obs)
            if target_pose is not None:
                if isinstance(target_pose, torch.Tensor):
                    target_pose_np = target_pose.cpu().numpy()
                else:
                    target_pose_np = np.array(target_pose)

                # 确保是一维数组
                if target_pose_np.ndim > 1:
                    target_pose_np = target_pose_np[0]

                # 🎯 目标手部状态表格
                target_state_table = PrettyTable()
                target_state_table.field_names = ["部位", "目标值", "单位"]
                target_state_table.align["部位"] = "l"
                target_state_table.align["目标值"] = "r"
                target_state_table.align["单位"] = "c"

                # 添加目标手指关节数据 (前16维)
                for finger_name, joint_indices in finger_joint_indices.items():
                    joint_values = [target_pose_np[i] for i in joint_indices]
                    joint_str = ", ".join([f"{val:.4f}" for val in joint_values])
                    target_state_table.add_row(
                        [f"🖐️ {finger_name}", f"[{joint_str}]", "rad"]
                    )

                # 添加目标手腕位置和6D旋转 (后9维)
                target_wrist_pos = target_pose_np[16:19]
                target_wrist_rot6d = target_pose_np[19:25]
                target_state_table.add_row(
                    [
                        "🤚 手腕位置",
                        f"[{target_wrist_pos[0]:.4f}, {target_wrist_pos[1]:.4f}, {target_wrist_pos[2]:.4f}]",
                        "m",
                    ]
                )
                target_state_table.add_row(
                    [
                        "🔄 手腕6D旋转",
                        f"[{', '.join([f'{v:.4f}' for v in target_wrist_rot6d])}]",
                        "-",
                    ]
                )

                print(
                    f"\n{Colors.GREEN}{Colors.BOLD}🎯 目标手部状态 (25维目标位姿 - {coord_system}){Colors.RESET}"
                )
                print(target_state_table)
            else:
                print("\n🎯 目标手部状态: 未找到目标位姿信息")

            # === 3. 模型动作输出的各个阶段 ===
            print(f"\n{Colors.YELLOW}{Colors.BOLD}🤖 模型动作处理流程{Colors.RESET}")

            # 根据是否为新序列预测显示不同信息
            if is_new_sequence and predicted_output is not None:
                # 新序列预测：显示完整序列信息
                if isinstance(predicted_output, torch.Tensor):
                    raw_output_np = predicted_output.cpu().numpy()
                else:
                    raw_output_np = np.array(predicted_output)

                raw_output_table = PrettyTable()
                raw_output_table.field_names = ["属性", "值", "描述"]
                raw_output_table.align["属性"] = "l"
                raw_output_table.align["值"] = "c"
                raw_output_table.align["描述"] = "l"

                raw_output_table.add_row(
                    ["🔢 输出维度", f"{raw_output_np.shape}", "模型原始输出张量形状"]
                )

                # 🆕 添加配置的序列长度信息
                if self.seq_length is not None:
                    raw_output_table.add_row(
                        ["⚙️ 配置序列长度", f"{self.seq_length}", "训练配置中的序列长度"]
                    )

                if raw_output_np.ndim == 3:  # [batch, seq, action_dim]
                    raw_output_table.add_row(
                        ["📏 序列长度", f"{raw_output_np.shape[1]}", "时间步数量"]
                    )
                    raw_output_table.add_row(
                        ["🎯 动作维度", f"{raw_output_np.shape[2]}", "单个动作的特征数"]
                    )

                    # 显示第一个和最后一个时间步的动作
                    first_action = raw_output_np[0, 0, :]
                    last_action = raw_output_np[0, -1, :]
                    first_str = (
                        f"[{', '.join([f'{v:.4f}' for v in first_action[:5]])}...]"
                    )
                    last_str = (
                        f"[{', '.join([f'{v:.4f}' for v in last_action[:5]])}...]"
                    )
                    raw_output_table.add_row(
                        ["⏮️ 首步动作", first_str, "序列第一个时间步(前5维)"]
                    )
                    raw_output_table.add_row(
                        ["⏭️ 末步动作", last_str, "序列最后一个时间步(前5维)"]
                    )
                else:
                    single_action_str = f"[{', '.join([f'{v:.4f}' for v in raw_output_np.flatten()[:5]])}...]"
                    raw_output_table.add_row(
                        ["🎯 单步动作", single_action_str, "直接输出动作(前5维)"]
                    )

                print(
                    f"\n{Colors.PURPLE}{Colors.BOLD}1️⃣ 🆕 新序列预测输出 (训练时 - {coord_system}){Colors.RESET}"
                )
                print(raw_output_table)

            else:
                # Buffer执行：显示buffer状态信息
                buffer_table = PrettyTable()
                buffer_table.field_names = ["Buffer状态", "值", "描述"]
                buffer_table.align["Buffer状态"] = "l"
                buffer_table.align["值"] = "c"
                buffer_table.align["描述"] = "l"

                if buffer_info:
                    buffer_table.add_row(
                        [
                            "📋 Buffer大小",
                            f"{buffer_info.get('total_actions', 'N/A')}",
                            "总动作序列长度",
                        ]
                    )
                    buffer_table.add_row(
                        [
                            "📍 当前位置",
                            f"{buffer_info.get('current_index', 'N/A')}",
                            "当前执行的动作索引",
                        ]
                    )
                    buffer_table.add_row(
                        [
                            "⏳ 剩余动作",
                            f"{buffer_info.get('remaining_actions', 'N/A')}",
                            "Buffer中剩余动作数",
                        ]
                    )

                    # 显示当前动作的预览
                    if "current_action_preview" in buffer_info:
                        current_preview = buffer_info["current_action_preview"]
                        preview_str = f"[{', '.join([f'{v:.4f}' for v in current_preview[:5]])}...]"
                        buffer_table.add_row(
                            ["🎯 当前动作", preview_str, "正在执行的动作(前5维)"]
                        )

                    # 显示下一个动作的预览（如果有）
                    if "next_action_preview" in buffer_info:
                        next_preview = buffer_info["next_action_preview"]
                        next_str = (
                            f"[{', '.join([f'{v:.4f}' for v in next_preview[:5]])}...]"
                        )
                        buffer_table.add_row(
                            ["⏭️ 下一动作", next_str, "下一步将执行的动作(前5维)"]
                        )
                else:
                    buffer_table.add_row(["⚠️ Buffer状态", "未知", "Buffer信息不可用"])

                print(
                    f"\n{Colors.PURPLE}{Colors.BOLD}1️⃣ 📋 Buffer执行状态 ({coord_system}){Colors.RESET}"
                )
                print(buffer_table)

            # 选择后的动作表格
            if isinstance(action_tensor_selected, torch.Tensor):
                selected_np = action_tensor_selected.cpu().numpy()
            else:
                selected_np = np.array(action_tensor_selected)

            if selected_np.ndim > 1:
                selected_np = selected_np[0]

            selected_action_table = PrettyTable()
            selected_action_table.field_names = ["部位", "动作值", "单位"]
            selected_action_table.align["部位"] = "l"
            selected_action_table.align["动作值"] = "r"
            selected_action_table.align["单位"] = "c"

            # 分解选择后的动作
            for finger_name, joint_indices in finger_joint_indices.items():
                joint_values = [selected_np[i] for i in joint_indices]
                joint_str = ", ".join([f"{val:.4f}" for val in joint_values])
                selected_action_table.add_row(
                    [f"🖐️ {finger_name}", f"[{joint_str}]", "rad"]
                )

            selected_wrist_pos = selected_np[16:19]
            selected_wrist_rot6d = selected_np[19:25]
            selected_action_table.add_row(
                [
                    "🤚 手腕位置",
                    f"[{selected_wrist_pos[0]:.4f}, {selected_wrist_pos[1]:.4f}, {selected_wrist_pos[2]:.4f}]",
                    "m",
                ]
            )
            selected_action_table.add_row(
                [
                    "🔄 手腕6D旋转",
                    f"[{', '.join([f'{v:.4f}' for v in selected_wrist_rot6d])}]",
                    "-",
                ]
            )

            print(
                f"\n{Colors.CYAN}{Colors.BOLD}2️⃣ 序列选择结果 (当前执行动作) (策略: {self.args.action_selection_strategy}, 维度: {selected_np.shape} - {coord_system}){Colors.RESET}"
            )
            print(selected_action_table)

            # 6D旋转正交化后的动作表格
            if action_tensor_corrected is not None:
                if isinstance(action_tensor_corrected, torch.Tensor):
                    corrected_np = action_tensor_corrected.cpu().numpy()
                else:
                    corrected_np = np.array(action_tensor_corrected)

                if corrected_np.ndim > 1:
                    corrected_np = corrected_np[0]

                corrected_action_table = PrettyTable()
                corrected_action_table.field_names = ["项目", "修正后值", "变化"]
                corrected_action_table.align["项目"] = "l"
                corrected_action_table.align["修正后值"] = "r"
                corrected_action_table.align["变化"] = "c"

                corrected_wrist_pos = corrected_np[16:19]
                corrected_wrist_rot6d = corrected_np[19:25]

                # 计算变化幅度
                pos_change = np.linalg.norm(corrected_wrist_pos - selected_wrist_pos)
                rot_change = np.linalg.norm(
                    corrected_wrist_rot6d - selected_wrist_rot6d
                )

                corrected_action_table.add_row(
                    [
                        "🤚 手腕位置",
                        f"[{corrected_wrist_pos[0]:.4f}, {corrected_wrist_pos[1]:.4f}, {corrected_wrist_pos[2]:.4f}]",
                        f"Δ{pos_change:.6f}",
                    ]
                )
                corrected_action_table.add_row(
                    [
                        "🔄 手腕6D旋转",
                        f"[{', '.join([f'{v:.4f}' for v in corrected_wrist_rot6d])}]",
                        f"{'🔧' if rot_change > 1e-6 else '✅'} Δ{rot_change:.6f}",
                    ]
                )

                print(
                    f"\n{Colors.ORANGE}{Colors.BOLD}3️⃣ 6D旋转正交化后的动作 ({coord_system}){Colors.RESET}"
                )
                print(corrected_action_table)

            # 最终缩放后的动作表格
            if isinstance(action_tensor_final, torch.Tensor):
                final_np = action_tensor_final.cpu().numpy()
            else:
                final_np = np.array(action_tensor_final)

            if final_np.ndim > 1:
                final_np = final_np[0]

            final_action_table = PrettyTable()
            final_action_table.field_names = ["部位", "最终值", "单位", "缩放变化"]
            final_action_table.align["部位"] = "l"
            final_action_table.align["最终值"] = "r"
            final_action_table.align["单位"] = "c"
            final_action_table.align["缩放变化"] = "c"

            # 分解最终动作
            for finger_name, joint_indices in finger_joint_indices.items():
                joint_values = [final_np[i] for i in joint_indices]
                joint_str = ", ".join([f"{val:.4f}" for val in joint_values])

                # 计算与选择后动作的变化（如果有6D旋转正交化，则与修正后比较）
                if action_tensor_corrected is not None:
                    selected_joint_values = [corrected_np[i] for i in joint_indices]
                else:
                    selected_joint_values = [selected_np[i] for i in joint_indices]

                joint_change = np.linalg.norm(
                    np.array(joint_values) - np.array(selected_joint_values)
                )
                final_action_table.add_row(
                    [
                        f"🖐️ {finger_name}",
                        f"[{joint_str}]",
                        "rad",
                        f"Δ{joint_change:.6f}",
                    ]
                )

            final_wrist_pos = final_np[16:19]
            final_wrist_rot6d = final_np[19:25]

            # 计算缩放前后的变化
            if action_tensor_corrected is not None:
                pos_change = np.linalg.norm(final_wrist_pos - corrected_wrist_pos)
                rot_change = np.linalg.norm(final_wrist_rot6d - corrected_wrist_rot6d)
            else:
                pos_change = np.linalg.norm(final_wrist_pos - selected_wrist_pos)
                rot_change = np.linalg.norm(final_wrist_rot6d - selected_wrist_rot6d)

            final_action_table.add_row(
                [
                    "🤚 手腕位置",
                    f"[{final_wrist_pos[0]:.4f}, {final_wrist_pos[1]:.4f}, {final_wrist_pos[2]:.4f}]",
                    "m",
                    f"Δ{pos_change:.6f}",
                ]
            )
            final_action_table.add_row(
                [
                    "🔄 手腕6D旋转",
                    f"[{', '.join([f'{v:.4f}' for v in final_wrist_rot6d])}]",
                    "-",
                    f"Δ{rot_change:.6f}",
                ]
            )

            print(
                f"\n{Colors.RED}{Colors.BOLD}4️⃣ 最终缩放后的动作 (应用到仿真 - 绝对位置){Colors.RESET}"
            )
            print(final_action_table)

            # === 5. 世界坐标系动作表格 (如果启用物体中心坐标系转换) ===
            if (
                self.use_object_centric
                and action_tensor_world is not None
                and not torch.equal(action_tensor_final, action_tensor_world)
            ):
                if isinstance(action_tensor_world, torch.Tensor):
                    world_np = action_tensor_world.cpu().numpy()
                else:
                    world_np = np.array(action_tensor_world)

                if world_np.ndim > 1:
                    world_np = world_np[0]

                world_action_table = PrettyTable()
                world_action_table.field_names = [
                    "部位",
                    "世界坐标系值",
                    "单位",
                    "坐标转换变化",
                ]
                world_action_table.align["部位"] = "l"
                world_action_table.align["世界坐标系值"] = "r"
                world_action_table.align["单位"] = "c"
                world_action_table.align["坐标转换变化"] = "c"

                # 分解世界坐标系动作
                for finger_name, joint_indices in finger_joint_indices.items():
                    joint_values = [world_np[i] for i in joint_indices]
                    joint_str = ", ".join([f"{val:.4f}" for val in joint_values])

                    # 关节动作不进行坐标转换，所以变化应该为0
                    final_joint_values = [final_np[i] for i in joint_indices]
                    joint_change = np.linalg.norm(
                        np.array(joint_values) - np.array(final_joint_values)
                    )

                    world_action_table.add_row(
                        [
                            f"🖐️ {finger_name}",
                            f"[{joint_str}]",
                            "rad",
                            f"{'✅' if joint_change < 1e-6 else '🔧'} Δ{joint_change:.6f}",
                        ]
                    )

                world_wrist_pos = world_np[16:19]
                world_wrist_rot6d = world_np[19:25]

                # 计算坐标转换的变化
                pos_change = np.linalg.norm(world_wrist_pos - final_wrist_pos)
                rot_change = np.linalg.norm(world_wrist_rot6d - final_wrist_rot6d)

                world_action_table.add_row(
                    [
                        "🤚 手腕位置",
                        f"[{world_wrist_pos[0]:.4f}, {world_wrist_pos[1]:.4f}, {world_wrist_pos[2]:.4f}]",
                        "m",
                        f"{'🔧' if pos_change > 1e-6 else '✅'} Δ{pos_change:.6f}",
                    ]
                )
                world_action_table.add_row(
                    [
                        "🔄 手腕6D旋转",
                        f"[{', '.join([f'{v:.4f}' for v in world_wrist_rot6d])}]",
                        "-",
                        f"{'🔧' if rot_change > 1e-6 else '✅'} Δ{rot_change:.6f}",
                    ]
                )

                print(
                    f"\n{Colors.CYAN}{Colors.BOLD}5️⃣ 世界坐标系动作 (坐标转换后 - 最终执行){Colors.RESET}"
                )
                print(world_action_table)

            # === 6. 坐标系信息 ===
            system_config_table = PrettyTable()
            system_config_table.field_names = ["配置项", "状态", "描述"]
            system_config_table.align["配置项"] = "l"
            system_config_table.align["状态"] = "c"
            system_config_table.align["描述"] = "l"

            system_config_table.add_row(
                [
                    "🌐 物体中心坐标系",
                    "✅ 启用" if self.use_object_centric else "❌ 禁用",
                    "观察空间和目标位姿的坐标系转换",
                ]
            )
            system_config_table.add_row(
                [
                    "🔄 动作坐标转换",
                    (
                        "✅ 启用"
                        if (
                            self.use_object_centric
                            and self.object_centric_transformer is not None
                        )
                        else "❌ 禁用"
                    ),
                    "物体中心坐标系动作转换回世界坐标系",
                ]
            )
            system_config_table.add_row(
                [
                    "📏 动作缩放",
                    "✅ 启用" if self.enable_action_scaling else "❌ 禁用",
                    "模型输出到仿真动作的缩放处理",
                ]
            )
            system_config_table.add_row(
                ["🎯 位置控制模式", "绝对位置", "手腕位置动作作为绝对目标位置应用"]
            )

            # ACT Chunked Execution相关配置
            system_config_table.add_row(
                [
                    "🧠 ACT Chunked Execution",
                    "✅ 启用" if self.chunked_execution_enabled else "❌ 禁用",
                    "动作序列分块执行机制",
                ]
            )

            if self.chunked_execution_enabled:
                system_config_table.add_row(
                    [
                        "📏 序列长度",
                        f"{self.seq_length}" if self.seq_length else "未知",
                        "每次预测的动作序列长度",
                    ]
                )

                # 显示当前buffer状态
                if buffer_info:
                    buffer_status = f"{buffer_info.get('current_index', 0)}/{buffer_info.get('total_actions', 0)}"
                    system_config_table.add_row(
                        ["📋 Buffer状态", buffer_status, "当前执行进度/总序列长度"]
                    )

            if self.enable_action_scaling:
                normalization_mode = (
                    "分离归一化"
                    if self.enable_separated_normalization
                    else "传统归一化"
                )
                system_config_table.add_row(
                    ["🔧 归一化模式", normalization_mode, "数据归一化策略"]
                )

            print(f"\n{Colors.WHITE}{Colors.BOLD}🌐 系统配置状态{Colors.RESET}")
            print(system_config_table)

            # 调试信息结束分割线
            print("\n" + "=" * 120)
            print(f"{Colors.SPECIAL_HEADER} 🏁 调试信息结束 {Colors.RESET}")
            print("=" * 120)

            print("=" * 100)

        except Exception as e:
            print(f"⚠️ 调试表格生成失败: {e}")
            import traceback

            traceback.print_exc()


if __name__ == "__main__":
    # 解析命令行参数
    parser = create_parser()
    args = parser.parse_args()

    print("🤖 单手DexH13控制模型评估器")
    print("=" * 50)
    print(f"📁 模型路径: {args.model_path}")
    print(f"🤚 手部类型: {args.hand_type}")
    print(f"📊 评估episodes: {args.episodes}")
    print(f"🎯 位置阈值: {args.position_tolerance}m")
    print(f"🔧 关节阈值: {args.joint_tolerance}rad")
    print(f"🌐 物体中心坐标系: {'启用' if args.use_object_centric else '禁用'}")
    print(f"🎬 动作选择策略: {args.action_selection_strategy}")
    print(f"🔄 动作缩放: {'启用' if args.enable_action_scaling else '禁用'}")
    if getattr(args, "enable_debug_table", False):
        interval = getattr(args, "debug_table_interval", 50)
        if interval > 0:
            print(f"🔍 详细调试表格: 启用 (每{interval}步输出一次)")
        else:
            print("🔍 详细调试表格: 禁用")
    print("=" * 50)

    # 启动Isaac Lab应用
    app_launcher = AppLauncher(args)
    simulation_app = app_launcher.app

    try:
        # 现在可以导入Isaac Lab相关模块

        # 导入单手环境

        # 导入模型相关类
        try:
            from px_janus_learnsim.learning.algorithms.Hierarchical_VTLA_clerk import (
                ExecutionBCActorPolicy,
            )

            print("✅ 成功导入模型类")
        except ImportError as e:
            print(f"⚠️ 警告：无法导入模型类: {e}")
            print("将使用随机动作进行基准测试")

        # 创建评估器并运行评估
        evaluator = SingleHandModelEvaluator(args)
        evaluator.run_evaluation()

    except KeyboardInterrupt:
        print("\n⚠️ 评估被用户中断")
    except Exception as e:
        print(f"❌ 评估过程中发生错误: {e}")
        import traceback

        traceback.print_exc()
    finally:
        # 清理资源
        if "evaluator" in locals() and hasattr(evaluator, "env"):
            print("🔄 关闭环境...")
            evaluator.env.close()
        print("🔄 关闭仿真应用...")
        simulation_app.close()
