#!/bin/bash
# PX-Tactrix 分发打包脚本

set -e  # 遇到错误时退出

echo "🚀 开始打包 PX-Tactrix 项目..."

# 1. 清理旧的构建文件
echo "🧹 清理旧的构建文件..."
rm -rf dist/
rm -rf build/
rm -rf *.egg-info/

# 2. 确保uv已安装
if ! command -v uv &> /dev/null; then
    echo "❌ uv 未安装，请先安装 uv"
    echo "安装命令: curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

# 3. 锁定依赖
echo "🔒 锁定依赖版本..."
uv lock

# 4. 构建项目
echo "📦 构建项目..."
uv build

# 5. 创建分发目录
DIST_DIR="px_tactrix_distribution_$(date +%Y%m%d_%H%M%S)"
echo "📁 创建分发目录: $DIST_DIR"
mkdir -p "$DIST_DIR"

# 6. 复制必要文件
echo "📋 复制项目文件..."
cp -r train/ "$DIST_DIR/"
cp -r eval/ "$DIST_DIR/"
cp -r conf/ "$DIST_DIR/"
cp -r docs/ "$DIST_DIR/" 2>/dev/null || echo "⚠️  docs/ 目录不存在，跳过"
cp -r scripts/ "$DIST_DIR/" 2>/dev/null || echo "⚠️  scripts/ 目录不存在，跳过"
cp -r env/ "$DIST_DIR/" 2>/dev/null || echo "⚠️  env/ 目录不存在，跳过"

# 复制配置和说明文件
cp pyproject.toml "$DIST_DIR/"
cp uv.lock "$DIST_DIR/"
cp README.md "$DIST_DIR/"
cp INSTALLATION.md "$DIST_DIR/"

# 复制.python-version文件（如果存在）
if [ -f .python-version ]; then
    cp .python-version "$DIST_DIR/"
    echo "✅ 复制 .python-version 文件"
else
    echo "⚠️  .python-version 文件不存在，将在安装时创建"
fi


# 复制主要脚本
echo "📄 复制主要脚本..."
cp train_model.py "$DIST_DIR/"
cp eval_model_vis.py "$DIST_DIR/"
cp vis_hdf5.py "$DIST_DIR/"
cp eval_model_performance.py "$DIST_DIR/"
cp main.py "$DIST_DIR/"

# 复制构建的wheel包
echo "🎡 复制构建的wheel包..."
if ls dist/*.whl >/dev/null 2>&1; then
    cp dist/*.whl "$DIST_DIR/"
    echo "✅ wheel包复制完成"
else
    echo "⚠️  未找到wheel包，请先运行 uv build"
fi

# 7. 创建快速安装脚本
cat > "$DIST_DIR/quick_install.sh" << 'EOF'
#!/bin/bash
# PX-Tactrix 环境检查脚本

echo "🚀 PX-Tactrix 环境检查..."

# 检查uv是否已安装
if command -v uv &> /dev/null; then
    echo "✅ uv 已安装"
    uv_version=$(uv --version 2>/dev/null || echo "未知版本")
    echo "   版本: $uv_version"
else
    echo "❌ uv 未安装"
    echo ""
    echo "🔧 请安装 uv (Python包管理器):"
    echo ""
    echo "方法1 - 官方安装脚本 (推荐):"
    echo "  curl -LsSf https://astral.sh/uv/install.sh | sh"
    echo ""
    echo "方法2 - 使用pip:"
    echo "  pip install uv"
    echo ""
    echo "方法3 - 使用包管理器:"
    echo "  # Ubuntu/Debian"
    echo "  sudo apt install python3-pip && pip install uv"
    echo "  # macOS"
    echo "  brew install uv"
    echo ""
    echo "安装完成后，请重新启动终端或运行:"
    echo "  source ~/.bashrc  # 或 source ~/.zshrc"
    echo ""
    echo "然后重新运行此脚本验证安装"
    exit 1
fi

# 检查Python版本支持
echo ""
echo "🐍 Python环境检查..."
if uv python list >/dev/null 2>&1; then
    echo "✅ uv Python管理功能正常"

    # 检查是否有Python 3.12+
    if uv python list | grep -E "3\.1[2-9]|3\.[2-9][0-9]" >/dev/null 2>&1; then
        echo "✅ 检测到兼容的Python版本"
    else
        echo "⚠️  建议安装Python 3.12+："
        echo "   uv python install 3.12"
    fi
else
    echo "⚠️  uv Python管理功能异常，但可能不影响使用"
fi

echo ""
echo "✅ 环境检查完成！"
echo ""
echo "🎯 下一步操作："
echo ""
echo "1️⃣  创建虚拟环境并安装依赖:"
echo "   uv sync"
echo ""
echo "2️⃣  激活虚拟环境:"
echo "   source .venv/bin/activate"
echo ""
echo "3️⃣  开始使用:"
echo "   python train_model.py        # 训练模型"
echo "   python eval_model_vis.py     # 评估模型"
echo "   python vis_hdf5.py           # 可视化数据"
echo ""
echo "📖 详细说明请查看 INSTALLATION.md 和 README.md"
echo ""
echo "💡 提示:"
echo "- uv会自动管理Python版本和依赖"
echo "- 首次运行uv sync可能需要几分钟下载依赖"
echo "- 如果网络较慢，可以配置国内镜像源"
echo ""
echo "🌏 配置国内镜像源 (可选):"
echo "   export UV_INDEX_URL='https://pypi.tuna.tsinghua.edu.cn/simple'"
echo ""
echo "🔧 常用命令:"
echo "   uv sync                      # 安装/更新依赖"
echo "   uv add <package>             # 添加新依赖"
echo "   uv run python <script>       # 在虚拟环境中运行脚本"
echo "   uv shell                     # 进入虚拟环境shell"
EOF

chmod +x "$DIST_DIR/quick_install.sh"

# 8. 创建requirements.txt（兼容性）
echo "📄 生成 requirements.txt..."
uv export --format requirements-txt > "$DIST_DIR/requirements.txt"

# 9. 创建分发说明
cat > "$DIST_DIR/DISTRIBUTION_README.md" << EOF
# PX-Tactrix 分发包

## 📦 包含内容

- \`train/\` - 训练模块
- \`eval/\` - 评估模块
- \`conf/\` - 配置文件
- \`*.py\` - 主要脚本
- \`*.whl\` - 预构建的wheel包
- \`uv.lock\` - 锁定的依赖版本
- \`requirements.txt\` - pip兼容的依赖文件

## 🚀 快速开始

### 方法1: 使用环境检查脚本 (推荐)
\`\`\`bash
./quick_install.sh
\`\`\`
该脚本会检查uv是否已安装，并提供下一步操作指导。

### 方法2: 直接使用uv
\`\`\`bash
# 确保已安装uv
uv sync                    # 创建虚拟环境并安装依赖
source .venv/bin/activate  # 激活环境
python train_model.py      # 开始使用
\`\`\`

### 方法3: 使用pip (兼容性)
\`\`\`bash
pip install -r requirements.txt
pip install *.whl
\`\`\`

## 📖 详细说明

请查看 \`INSTALLATION.md\` 获取完整的安装和使用说明。

---
生成时间: $(date)
版本: $(grep version pyproject.toml | cut -d'"' -f2)
EOF

# 10. 验证分发包完整性
echo "🔍 验证分发包完整性..."
missing_files=()

# 检查关键文件
for file in "pyproject.toml" "uv.lock" "README.md" "INSTALLATION.md" "quick_install.sh"; do
    if [ ! -f "$DIST_DIR/$file" ]; then
        missing_files+=("$file")
    fi
done

# 检查关键目录
for dir in "train" "eval" "conf"; do
    if [ ! -d "$DIST_DIR/$dir" ]; then
        missing_files+=("$dir/")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo "✅ 分发包完整性检查通过"
else
    echo "❌ 分发包缺少以下文件/目录:"
    printf '   - %s\n' "${missing_files[@]}"
    echo "请检查复制过程是否有错误"
    exit 1
fi

# 11. 压缩分发包
echo "🗜️  压缩分发包..."
tar -czf "${DIST_DIR}.tar.gz" "$DIST_DIR"

echo "✅ 打包完成！"
echo ""

# 显示分发包统计信息
echo "📊 分发包统计:"
echo "  - 目录: $DIST_DIR/"
echo "  - 压缩包: ${DIST_DIR}.tar.gz"

# 计算大小
if command -v du &> /dev/null; then
    dir_size=$(du -sh "$DIST_DIR" | cut -f1)
    tar_size=$(du -sh "${DIST_DIR}.tar.gz" | cut -f1)
    echo "  - 目录大小: $dir_size"
    echo "  - 压缩包大小: $tar_size"
fi

# 统计文件数量
file_count=$(find "$DIST_DIR" -type f | wc -l)
echo "  - 文件数量: $file_count"

echo ""
echo "🎯 分发给用户:"
echo "  1. 发送压缩包: ${DIST_DIR}.tar.gz"
echo "  2. 用户解压: tar -xzf ${DIST_DIR}.tar.gz"
echo "  3. 用户检查环境: cd $DIST_DIR && ./quick_install.sh"
echo "  4. 用户安装依赖: uv sync"
echo "  5. 用户开始使用: source .venv/bin/activate && python train_model.py"
echo ""
echo "💡 提示:"
echo "  - 压缩包包含完整的项目代码和依赖信息"
echo "  - 环境检查脚本会指导用户完成uv安装"
echo "  - 用户可以自由选择环境管理方式（uv、conda、pip等）"
echo "  - 支持离线安装（如果用户有uv缓存）"
