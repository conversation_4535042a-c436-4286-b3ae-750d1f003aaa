"""Tora机器人的DirectRLEnv实现"""

import gymnasium as gym
import torch
from collections import deque
import numpy as np

# from tlpy.drivers.isaac_tora_driver import Isaac<PERSON>oraDriver
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.sim.spawners.from_files import GroundPlaneCfg, spawn_ground_plane
from isaaclab.utils import configclass
from isaaclab.utils.math import sample_uniform
from px_janus_learnsim.robot.tora.tora import TORA_CFG

# 添加DexH13配置导入
from px_janus_learnsim.robot.DexH13.DexH13_right import DexH13_right_CFG
from px_janus_learnsim.robot.DexH13.DexH13_left import DexH13_left_CFG
import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation, ArticulationCfg
from typing import Sequence
from isaaclab.assets import ArticulationCfg, AssetBaseCfg
from px_janus_learnsim.config.paths import ROBOT_MODELS_PATH, ASSET_PATH
from isaaclab.assets import (
    Articulation,
    ArticulationCfg,
    AssetBaseCfg,
    RigidObject,
    RigidObjectCfg,
)
from isaaclab.managers import SceneEntityCfg
from px_janus_learnsim.utils.math import quat_to_rot6d


# from tlpy.interfaces.robot import ToraJoints
from dataclasses import MISSING


@configclass
class DexH13DirectEnvCfg(DirectRLEnvCfg):
    # env
    num_frames_to_stack: int = 1  # 默认值，实际使用时会被Hydra配置覆盖

    # 方案A：渐进式优化 - 观察空间降维
    base_observation_space: int = 59  # 优化后: 左手25维 + 右手25维 + 单物体9维

    decimation = 2
    episode_length_s = 5.0
    action_scale = 1.0  # [N]
    action_space = 50
    observation_space: int = (
        MISSING  # base_observation_space * num_frames_to_stack，会在运行时重新计算
    )

    # --- 优化后的双手灵巧手观察空间 (Total Dimension = 59) ---
    # 基于数据质量分析，从110维优化到59维，移除无效填充数据

    # 左手本体感受信息 (25 dims) - 当前环境用零填充
    # ----------------------------------------
    # left_hand_joints:       16,  # 左手16个关节角度 (零填充)
    # left_hand_pose:         9,   # 左手位姿: 3位置 + 6旋转 (零填充)

    # 右手本体感受信息 (25 dims) - 实际DexH13数据
    # ----------------------------------------
    # right_hand_joints:      16,  # 右手16个关节角度 (拇指4 + 食指4 + 中指4 + 无名指4)
    # right_hand_pose:        9,   # 右手位姿: 3位置 + 6旋转

    # 单物体状态信息 (Object States - 9 dims, 只使用obj1有效数据)
    # --------------------------------------------------------
    # 优化原因：
    # 1. 专家数据质量分析显示：
    #    - obj1: 19维数据中只有前9维[x,y,z, r11,r12,r21,r22,r31,r32]是真实的
    #    - obj2-obj5: 完全是填充数据(零值)
    #    - obj1后10维: [length,width,height, bbox_*]也是填充数据
    # 2. 方案A优势：
    #    - 46%降维 (110→59)：训练效率显著提升
    #    - 100%有效数据：避免填充噪声干扰
    #    - 稳定性提升：明确的obj1键名识别
    # 3. 后续扩展：可根据数据质量改善逐步增加物体支持
    #
    # object_1_state:         9,   # 物体1有效数据: 3位置 + 6旋转 (前9维)

    # --- 总计: 25(左手) + 25(右手) + 9(单物体) = 59 维 ---
    state_space = 0

    # simulation
    sim: SimulationCfg = SimulationCfg(dt=1 / 120, render_interval=decimation)

    # robot - 使用DexH13右手配置
    dexh13_right_cfg: ArticulationCfg = DexH13_right_CFG.replace(
        prim_path="/World/envs/env_.*/dexh13_right",
        init_state=ArticulationCfg.InitialStateCfg(
            pos=(0.0, 0.0, 1.0),  # 调整初始位置
            rot=(0.0, 0.0, 0.0, 1.0),  # 默认旋转
        ),
    )
    dexh13_left_cfg: ArticulationCfg = DexH13_left_CFG.replace(
        prim_path="/World/envs/env_.*/dexh13_left",
        init_state=ArticulationCfg.InitialStateCfg(
            pos=(0.5, 0.0, 1.0),  # 调整初始位置
            rot=(0.0, 0.0, 0.0, 1.0),  # 默认旋转
        ),
    )

    # scene
    scene: InteractiveSceneCfg = InteractiveSceneCfg(
        num_envs=1, env_spacing=4.0, replicate_physics=True
    )

    table: RigidObjectCfg = RigidObjectCfg(
        prim_path="/World/envs/env_.*/table",
        spawn=sim_utils.UsdFileCfg(
            usd_path=f"{ASSET_PATH}/table_white.usd",
            scale=[0.1, 0.15, 0.1],  # 改为Python列表
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                kinematic_enabled=True,
                disable_gravity=True,
            ),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(
            pos=(0.0, -0.4, 0.0), rot=(0.7071, 0.7071, 0.0, 0.0)
        ),
    )

    object1: RigidObjectCfg = RigidObjectCfg(
        prim_path="/World/envs/env_.*/object1",
        spawn=sim_utils.UsdFileCfg(
            usd_path=f"{ASSET_PATH}/ImageToStl.com_obj_000030.usd",
            scale=[0.001, 0.001, 0.001],  # 改为Python列表
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                kinematic_enabled=False,
                disable_gravity=False,
                enable_gyroscopic_forces=True,
            ),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(
            pos=(-0.2, -0.25, 1.0343), rot=(0.707, 0.707, 0.0, 0.0)
        ),
    )

    # reset
    max_joint_pos = 3.0
    initial_joint_pos_range = [-0.25, 0.25]

    # reward scales
    rew_scale_alive = 1.0
    rew_scale_terminated = -2.0
    rew_scale_joint_pos = -1.0
    rew_scale_joint_vel = -0.01


class DexH13DirectEnv(DirectRLEnv):
    cfg: DexH13DirectEnvCfg

    def __init__(
        self, cfg: DexH13DirectEnvCfg, render_mode: str | None = None, **kwargs
    ):
        super().__init__(cfg, render_mode, **kwargs)

        self.action_scale = self.cfg.action_scale

        # 初始化关节状态变量（在_setup_scene之后会被正确设置）
        self.dexh13_right_joint_pos = None
        self.dexh13_right_joint_vel = None
        self.dexh13_left_joint_pos = None
        self.dexh13_left_joint_vel = None

        if hasattr(self, "dexh13_right") and self.dexh13_right is not None:
            self.dexh13_right_joint_pos = self.dexh13_right.data.joint_pos
            self.dexh13_right_joint_vel = self.dexh13_right.data.joint_vel

            self.dexh13_left_joint_pos = self.dexh13_left.data.joint_pos
            self.dexh13_left_joint_vel = self.dexh13_left.data.joint_vel

            self.dexh13_right_entity_cfg = SceneEntityCfg(
                "dexh13_right",
                joint_names=list(self.dexh13_right.data.joint_names),
                body_names=list(self.dexh13_right.data.body_names),
            )
            self.dexh13_right_entity_cfg.resolve(self.scene)

            self.dexh13_left_entity_cfg = SceneEntityCfg(
                "dexh13_left",
                joint_names=list(self.dexh13_left.data.joint_names),
                body_names=list(self.dexh13_left.data.body_names),
            )
            self.dexh13_left_entity_cfg.resolve(self.scene)
        else:
            print(
                "[Warning] self.dexh13_right or self.dexh13_left not initialized yet in DexH13DirectEnv.__init__"
            )

        if hasattr(self, "object1") and self.object1 is not None:
            self.object1_entity_cfg = SceneEntityCfg(
                "object1", body_names=list(self.object1.data.body_names)
            )
            self.object1_entity_cfg.resolve(self.scene)
        else:
            print(
                "[Warning] self.object1 not initialized yet in DexH13DirectEnv.__init__"
            )

        self.base_obs_dim = self.cfg.base_observation_space
        self.num_stack = self.cfg.num_frames_to_stack

        self.observation_space = gym.spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(self.cfg.observation_space,),
            dtype=np.float32,
        )
        print(
            f"[Info] Environment gym.space.Box created with shape: {self.observation_space.shape}"
        )

        self._obs_history = [deque(maxlen=self.num_stack) for _ in range(self.num_envs)]

        self.action_space = gym.spaces.Box(
            low=-1.0, high=1.0, shape=(self.cfg.action_space,), dtype=np.float32
        )

        self.action_range = self.cfg.action_scale
        self.action_center = 0.0

        if hasattr(self, "dexh13_right"):
            self.dexh13_right_entity_cfg.resolve(self.scene)
        if hasattr(self, "dexh13_left"):
            self.dexh13_left_entity_cfg.resolve(self.scene)
        if hasattr(self, "object1_entity_cfg"):
            self.object1_entity_cfg.resolve(self.scene)
        self.last_actions = torch.zeros(
            (self.num_envs, self.cfg.action_space), device=self.device
        )
        self.episode_length_s = self.cfg.episode_length_s

    def _setup_scene(self):
        # 第一步：创建基础场景
        # 创建机器人
        self.dexh13_right = Articulation(self.cfg.dexh13_right_cfg)
        self.dexh13_left = Articulation(self.cfg.dexh13_left_cfg)

        # 添加地面
        spawn_ground_plane(prim_path="/World/ground", cfg=GroundPlaneCfg())

        # 添加机器人到场景
        self.scene.articulations["dexh13_right"] = self.dexh13_right
        self.scene.articulations["dexh13_left"] = self.dexh13_left

        # 第二步：克隆环境
        # 这一步会复制所有已经创建的物体（机器人和地面）
        self.scene.clone_environments(copy_from_source=False)

        # 第三步：创建和添加其他物体
        # 在克隆环境后创建桌子和物体，这样它们会在每个环境中都被正确创建
        self.table = RigidObject(self.cfg.table)
        self.object1 = RigidObject(self.cfg.object1)

        # 将物体添加到场景
        self.scene.rigid_objects["table"] = self.table
        self.scene.rigid_objects["object1"] = self.object1

        # 添加灯光（灯光通常是全局的，不需要复制）
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

        # 初始化关节状态变量（现在dexh13已经被创建）
        if hasattr(self.dexh13_right, "data"):
            self.dexh13_right_joint_pos = self.dexh13_right.data.joint_pos
            self.dexh13_right_joint_vel = self.dexh13_right.data.joint_vel
        if hasattr(self.dexh13_left, "data"):
            self.dexh13_left_joint_pos = self.dexh13_left.data.joint_pos
            self.dexh13_left_joint_vel = self.dexh13_left.data.joint_vel

            # 初始化DexH13关节映射（移至此处以确保在首次step前完成）
            self._initialize_joint_mapping()

        # 标记需要延迟初始化双手关节状态变量
        # 将在第一次调用_ensure_joint_state_initialized时初始化
        self._joint_state_initialized = False

    def _ensure_joint_state_initialized(self):
        """延迟初始化双手关节状态变量，确保物理视图已完全初始化"""
        if not self._joint_state_initialized:
            try:
                # 现在物理视图应该已经可用，可以安全访问num_joints
                total_joints = (
                    self.dexh13_right.num_joints + self.dexh13_left.num_joints
                )
                self.joint_pos = torch.zeros(
                    (self.num_envs, total_joints), device=self.device
                )
                self.joint_vel = torch.zeros(
                    (self.num_envs, total_joints), device=self.device
                )
                self._joint_state_initialized = True
                print(f"✅ 双手关节状态初始化完成: 总计{total_joints}个关节")
            except AttributeError as e:
                print(f"⚠️ 关节状态初始化失败，物理视图未就绪: {e}")
                # 使用默认关节数量作为后备方案 (DexH13每只手16个关节)
                total_joints = 32  # 16 + 16
                self.joint_pos = torch.zeros(
                    (self.num_envs, total_joints), device=self.device
                )
                self.joint_vel = torch.zeros(
                    (self.num_envs, total_joints), device=self.device
                )
                self._joint_state_initialized = True
                print(f"✅ 使用默认关节数量初始化: {total_joints}个关节")

    def _pre_physics_step(self, actions: torch.Tensor) -> None:
        # 从[-1,1]扩展到实际需要的范围 [-action_scale, action_scale]
        scaled_actions = actions * self.action_range + self.action_center
        self.actions = scaled_actions.clone()
        self.last_actions = self.actions.clone()

    def _initialize_joint_mapping(self):
        """初始化DexH13关节名称和索引映射"""
        # 定义DexH13关节名称顺序：拇指、食指、中指、无名指
        self.dexh13_right_joint_names = [
            # 拇指 (4个关节) - 按训练时顺序
            "mzcb",  # 拇指掌长本节
            "mzjdxz",  # 拇指近端旋转
            "mzzd",  # 拇指中段
            "mzydxz",  # 拇指远端旋转
            # 食指 (4个关节)
            "szcb",  # 食指掌长本节
            "szjdxz",  # 食指近端旋转
            "szzdxz",  # 食指中段旋转
            "szydxz",  # 食指远端旋转
            # 中指 (4个关节)
            "zzcb",  # 中指掌长本节
            "zzjdxz",  # 中指近端旋转
            "zzzdxz",  # 中指中段旋转
            "zzydxz",  # 中指远端旋转
            # 无名指 (4个关节)
            "wmzcb",  # 无名指掌长本节
            "wmzjdxz",  # 无名指近端旋转
            "wmzzdxz",  # 无名指中段旋转
            "wmzydxz",  # 无名指远端旋转
        ]
        self.dexh13_left_joint_names = [
            "mzcb",
            "mzjdxz",
            "mzzdxz",
            "mzyd",
            "szcb",
            "szjdxz",
            "szzdxz",
            "szydxz",
            "zzcb",
            "zzjdxz",
            "zzzdxz",
            "zzydxz",
            "wmzcb",
            "wmzjdxz",
            "wmzzdxz",
            "wmzydxz",
        ]
        # 动态查找关节索引
        self.dexh13_right_joint_indices = []
        for joint_name in self.dexh13_right_joint_names:
            joint_ids, _ = self.dexh13_right.find_joints(joint_name)
            if joint_ids is not None and len(joint_ids) > 0:
                self.dexh13_right_joint_indices.append(joint_ids[0])
            else:
                print(f"⚠️ 未找到关节: {joint_name}")

        self.dexh13_right_joint_indices = torch.tensor(
            self.dexh13_right_joint_indices, dtype=torch.long, device=self.device
        )

        print(f"✅ DexH13右手关节映射完成:")
        print(f"   关节名称: {self.dexh13_right_joint_names}")
        print(f"   关节索引: {self.dexh13_right_joint_indices.tolist()}")

        self.dexh13_left_joint_indices = []
        for joint_name in self.dexh13_left_joint_names:
            joint_ids, _ = self.dexh13_left.find_joints(joint_name)
            if joint_ids is not None and len(joint_ids) > 0:
                self.dexh13_left_joint_indices.append(joint_ids[0])
            else:
                print(f"⚠️ 未找到关节: {joint_name}")

        self.dexh13_left_joint_indices = torch.tensor(
            self.dexh13_left_joint_indices, dtype=torch.long, device=self.device
        )

        print(f"✅ DexH13左手关节映射完成:")
        print(f"   关节名称: {self.dexh13_left_joint_names}")

    def _apply_action(self) -> None:
        """
        应用BC模型输出的50维动作到DexH13灵巧手

        训练时50维动作空间分解：
        - action_left_joints:  [0:16]   左手关节 (拇指4 + 食指4 + 中指4 + 无名指4)
        - action_left_pose:    [16:25]  左手位姿 (3位置 + 6旋转)
        - action_right_joints: [25:41]  右手关节 (拇指4 + 食指4 + 中指4 + 无名指4)
        - action_right_pose:   [41:50]  右手位姿 (3位置 + 6旋转)

        现在支持真正的双手DexH13控制
        """

        # 确保关节映射已初始化（在_setup_scene中应该已完成）
        if not hasattr(self, "dexh13_right_joint_indices"):
            print("⚠️ 关节映射未初始化，执行紧急初始化")
            self._initialize_joint_mapping()

        num_envs = self.actions.shape[0]
        num_joints_right = len(self.dexh13_right.data.joint_names)
        num_joints_left = len(self.dexh13_left.data.joint_names)

        # 验证动作维度
        if self.actions.shape[1] != 50:
            print(f"⚠️ 动作维度不匹配: 期望50维，实际{self.actions.shape[1]}维")
            return

        # 创建完整的关节目标位置张量
        full_actions_right = torch.zeros(
            (num_envs, num_joints_right),
            device=self.actions.device,
            dtype=self.actions.dtype,
        )
        full_actions_left = torch.zeros(
            (num_envs, num_joints_left),
            device=self.actions.device,
            dtype=self.actions.dtype,
        )

        # 为每个环境设置关节目标
        for env_idx in range(num_envs):
            action = self.actions[env_idx]  # 50维动作向量

            # 解析50维动作空间 (根据训练数据顺序)
            # [0:16]左手关节, [16:25]左手位姿(3pos+6rot), [25:41]右手关节, [41:50]右手位姿(3pos+6rot)
            left_hand_joints = action[0:16]  # 左手16个关节 - 修正索引
            right_hand_joints = action[25:41]  # 右手16个关节

            # 按训练时顺序解析右手：拇指、食指、中指、无名指
            right_thumb_joints = right_hand_joints[0:4]  # 拇指关节 [0:4]
            right_index_joints = right_hand_joints[4:8]  # 食指关节 [4:8]
            right_middle_joints = right_hand_joints[8:12]  # 中指关节 [8:12]
            right_ring_joints = right_hand_joints[12:16]  # 无名指关节 [12:16]

            # 按训练时顺序解析左手：拇指、食指、中指、无名指
            left_thumb_joints = left_hand_joints[0:4]  # 拇指关节 [0:4]
            left_index_joints = left_hand_joints[4:8]  # 食指关节 [4:8]
            left_middle_joints = left_hand_joints[8:12]  # 中指关节 [8:12]
            left_ring_joints = left_hand_joints[12:16]  # 无名指关节 [12:16]

            # 组合右手所有关节目标 (保持训练时的顺序)
            right_target_joints = torch.cat(
                [
                    right_thumb_joints,
                    right_index_joints,
                    right_middle_joints,
                    right_ring_joints,
                ]
            )

            # 组合左手所有关节目标 (保持训练时的顺序)
            left_target_joints = torch.cat(
                [
                    left_thumb_joints,
                    left_index_joints,
                    left_middle_joints,
                    left_ring_joints,
                ]
            )

            # 确保右手关节索引数量匹配
            if len(self.dexh13_right_joint_indices) == len(right_target_joints):
                # 将目标关节角度映射到完整的关节空间
                full_actions_right[env_idx, self.dexh13_right_joint_indices] = (
                    right_target_joints
                )

                if env_idx == 0:  # 只为第一个环境打印调试信息
                    print(f"🎯 从50维动作中提取右手关节 [25:41]:")
                    print(f"   拇指: {right_thumb_joints.cpu().numpy()}")
                    print(f"   食指: {right_index_joints.cpu().numpy()}")
                    print(f"   中指: {right_middle_joints.cpu().numpy()}")
                    print(f"   无名指: {right_ring_joints.cpu().numpy()}")
            else:
                print(
                    f"❌ 右手关节索引数量({len(self.dexh13_right_joint_indices)})与目标关节数量({len(right_target_joints)})不匹配"
                )

            # 确保左手关节索引数量匹配
            if len(self.dexh13_left_joint_indices) == len(left_target_joints):
                # 将目标关节角度映射到完整的关节空间
                full_actions_left[env_idx, self.dexh13_left_joint_indices] = (
                    left_target_joints
                )

                if env_idx == 0:  # 只为第一个环境打印调试信息
                    print(f"🎯 从50维动作中提取左手关节 [0:16]:")
                    print(f"   拇指: {left_thumb_joints.cpu().numpy()}")
                    print(f"   食指: {left_index_joints.cpu().numpy()}")
                    print(f"   中指: {left_middle_joints.cpu().numpy()}")
                    print(f"   无名指: {left_ring_joints.cpu().numpy()}")
            else:
                print(
                    f"❌ 左手关节索引数量({len(self.dexh13_left_joint_indices)})与目标关节数量({len(left_target_joints)})不匹配"
                )

        # 应用动作到仿真
        self.dexh13_right.set_joint_position_target(full_actions_right)
        self.dexh13_left.set_joint_position_target(full_actions_left)

    def _get_observations(self) -> dict:
        # 调用基础观察计算函数
        current_base_obs = self._compute_base_observations()

        # 处理观察历史和帧堆叠
        stacked_obs_list = []
        for env_idx in range(self.num_envs):
            obs_to_store = current_base_obs[env_idx].clone().detach()

            if len(self._obs_history[env_idx]) == 0:
                for _ in range(self.num_stack):
                    self._obs_history[env_idx].append(obs_to_store)
            else:
                self._obs_history[env_idx].append(obs_to_store)

            stacked_env_tensor = torch.cat(list(self._obs_history[env_idx]), dim=0)
            stacked_obs_list.append(stacked_env_tensor)

        final_stacked_obs = torch.stack(stacked_obs_list, dim=0)
        return {"policy": final_stacked_obs}

    def _get_rewards(self) -> torch.Tensor:
        total_reward = compute_rewards(
            self.cfg.rew_scale_alive,
            self.cfg.rew_scale_terminated,
            self.cfg.rew_scale_joint_pos,
            self.cfg.rew_scale_joint_vel,
            self.joint_pos,
            self.joint_vel,
            self.reset_terminated,
        )
        return total_reward

    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        # 确保关节状态变量已初始化
        self._ensure_joint_state_initialized()

        # 获取双手关节状态进行边界检查
        right_joint_pos = self.dexh13_right.data.joint_pos
        right_joint_vel = self.dexh13_right.data.joint_vel
        left_joint_pos = self.dexh13_left.data.joint_pos
        left_joint_vel = self.dexh13_left.data.joint_vel

        # 合并双手关节数据用于奖励计算
        self.joint_pos = torch.cat([right_joint_pos, left_joint_pos], dim=1)
        self.joint_vel = torch.cat([right_joint_vel, left_joint_vel], dim=1)

        time_out = self.episode_length_buf >= self.max_episode_length - 1

        # 检查双手是否超出关节限制
        right_out_of_bounds = torch.any(
            torch.abs(right_joint_pos) > self.cfg.max_joint_pos, dim=1
        )
        left_out_of_bounds = torch.any(
            torch.abs(left_joint_pos) > self.cfg.max_joint_pos, dim=1
        )
        out_of_bounds = right_out_of_bounds | left_out_of_bounds

        return out_of_bounds, time_out

    def _reset_idx(self, env_ids: Sequence[int] | None):
        if env_ids is None:
            env_ids = list(range(self.num_envs))
        super()._reset_idx(env_ids)

        # 确保关节状态变量已初始化
        self._ensure_joint_state_initialized()

        # 重置右手
        right_joint_pos = self.dexh13_right.data.default_joint_pos[env_ids]
        right_joint_pos += sample_uniform(
            self.cfg.initial_joint_pos_range[0],
            self.cfg.initial_joint_pos_range[1],
            (len(env_ids), self.dexh13_right.num_joints),
            right_joint_pos.device,
        )
        right_joint_vel = self.dexh13_right.data.default_joint_vel[env_ids]
        right_default_root_state = self.dexh13_right.data.default_root_state[env_ids]
        right_default_root_state[:, :3] += self.scene.env_origins[env_ids]

        # 重置左手
        left_joint_pos = self.dexh13_left.data.default_joint_pos[env_ids]
        left_joint_pos += sample_uniform(
            self.cfg.initial_joint_pos_range[0],
            self.cfg.initial_joint_pos_range[1],
            (len(env_ids), self.dexh13_left.num_joints),
            left_joint_pos.device,
        )
        left_joint_vel = self.dexh13_left.data.default_joint_vel[env_ids]
        left_default_root_state = self.dexh13_left.data.default_root_state[env_ids]
        left_default_root_state[:, :3] += self.scene.env_origins[env_ids]

        # 合并双手状态用于奖励计算
        self.joint_pos[env_ids] = torch.cat([right_joint_pos, left_joint_pos], dim=1)
        self.joint_vel[env_ids] = torch.cat([right_joint_vel, left_joint_vel], dim=1)

        # 应用右手状态到仿真
        self.dexh13_right.write_root_pose_to_sim(
            right_default_root_state[:, :7], env_ids
        )
        self.dexh13_right.write_root_velocity_to_sim(
            right_default_root_state[:, 7:], env_ids
        )
        self.dexh13_right.write_joint_state_to_sim(
            right_joint_pos, right_joint_vel, None, env_ids
        )

        # 应用左手状态到仿真
        self.dexh13_left.write_root_pose_to_sim(left_default_root_state[:, :7], env_ids)
        self.dexh13_left.write_root_velocity_to_sim(
            left_default_root_state[:, 7:], env_ids
        )
        self.dexh13_left.write_joint_state_to_sim(
            left_joint_pos, left_joint_vel, None, env_ids
        )

        # 重置其他物体
        table_state = self.table.data.default_root_state[env_ids].clone()
        object1_state = self.object1.data.default_root_state[env_ids].clone()
        table_state[:, :3] += self.scene.env_origins[env_ids]
        object1_state[:, :3] += self.scene.env_origins[env_ids]
        self.table.write_root_state_to_sim(table_state, env_ids)
        self.object1.write_root_state_to_sim(object1_state, env_ids)

        # 重置观察历史
        initial_base_obs = self._compute_base_observations()
        for env_idx in env_ids:
            self._obs_history[env_idx].clear()
            obs_to_fill = initial_base_obs[env_idx].clone().detach()
            for _ in range(self.num_stack):
                self._obs_history[env_idx].append(obs_to_fill)

        self.last_actions[env_ids] = 0.0

    def _compute_base_observations(self) -> torch.Tensor:
        # 获取右手DexH13的16个关节状态和位姿
        if not hasattr(self, "dexh13_right_joint_names"):
            # 如果还没有初始化关节名称，使用默认顺序
            self.dexh13_right_joint_names = [
                # 拇指 (4个关节)
                "mzcb",
                "mzjdxz",
                "mzzd",
                "mzydxz",
                # 食指 (4个关节)
                "szcb",
                "szjdxz",
                "szzdxz",
                "szydxz",
                # 中指 (4个关节)
                "zzcb",
                "zzjdxz",
                "zzzdxz",
                "zzydxz",
                # 无名指 (4个关节)
                "wmzcb",
                "wmzjdxz",
                "wmzzdxz",
                "wmzydxz",
            ]

        # 确保左手关节名称也被初始化
        if not hasattr(self, "dexh13_left_joint_names"):
            self.dexh13_left_joint_names = [
                "mzcb",
                "mzjdxz",
                "mzzdxz",
                "mzyd",
                "szcb",
                "szjdxz",
                "szzdxz",
                "szydxz",
                "zzcb",
                "zzjdxz",
                "zzzdxz",
                "zzydxz",
                "wmzcb",
                "wmzjdxz",
                "wmzzdxz",
                "wmzydxz",
            ]

        # 获取右手DexH13关节位置
        right_joint_indices = []
        for joint_name in self.dexh13_right_joint_names:
            joint_ids, _ = self.dexh13_right.find_joints(joint_name)
            if joint_ids is not None and len(joint_ids) > 0:
                right_joint_indices.append(joint_ids[0])

        if len(right_joint_indices) == 16:
            right_joint_pos = self.dexh13_right.data.joint_pos[..., right_joint_indices]
        else:
            print(
                f"⚠️ 右手DexH13关节数量不匹配: 期望16个，找到{len(right_joint_indices)}个"
            )
            right_joint_pos = torch.zeros((self.num_envs, 16), device=self.device)

        # 获取右手DexH13位姿信息 (9维: 3位置 + 6旋转)
        right_root_pos = self.dexh13_right.data.root_pos_w  # [num_envs, 3] 位置
        right_root_quat = self.dexh13_right.data.root_quat_w  # [num_envs, 4] 四元数
        right_root_rot_6d = quat_to_rot6d(right_root_quat)  # [num_envs, 6] 6D旋转表示
        right_pose = torch.cat([right_root_pos, right_root_rot_6d], dim=1)

        # 右手本体感受信息: 16维关节 + 9维位姿 = 25维
        right_hand_proprio = torch.cat([right_joint_pos, right_pose], dim=1)

        # 获取左手DexH13关节位置和位姿
        left_joint_indices = []
        for joint_name in self.dexh13_left_joint_names:
            joint_ids, _ = self.dexh13_left.find_joints(joint_name)
            if joint_ids is not None and len(joint_ids) > 0:
                left_joint_indices.append(joint_ids[0])

        if len(left_joint_indices) == 16:
            left_joint_pos = self.dexh13_left.data.joint_pos[..., left_joint_indices]
        else:
            print(
                f"⚠️ 左手DexH13关节数量不匹配: 期望16个，找到{len(left_joint_indices)}个"
            )
            left_joint_pos = torch.zeros((self.num_envs, 16), device=self.device)

        # 获取左手DexH13位姿信息 (9维: 3位置 + 6旋转)
        left_root_pos = self.dexh13_left.data.root_pos_w  # [num_envs, 3] 位置
        left_root_quat = self.dexh13_left.data.root_quat_w  # [num_envs, 4] 四元数
        left_root_rot_6d = quat_to_rot6d(left_root_quat)  # [num_envs, 6] 6D旋转表示
        left_pose = torch.cat([left_root_pos, left_root_rot_6d], dim=1)

        # 左手本体感受信息: 16维关节 + 9维位姿 = 25维
        left_hand_proprio = torch.cat([left_joint_pos, left_pose], dim=1)

        # 组合双手本体感受信息: 左手25维 + 右手25维 = 50维
        robot_proprio = torch.cat([left_hand_proprio, right_hand_proprio], dim=1)

        # 方案A：单物体状态处理 - 只使用object1的前9维有效数据
        # 获取object1位置和旋转信息
        obj1_pos = self.object1.data.root_pos_w  # [num_envs, 3] 位置
        obj1_quat = self.object1.data.root_quat_w  # [num_envs, 4] 四元数
        obj1_rot_6d = quat_to_rot6d(obj1_quat)  # [num_envs, 6] 6D旋转表示

        # 组合单物体状态: 3位置 + 6旋转 = 9维 (对应专家数据中的有效部分)
        single_object_state = torch.cat([obj1_pos, obj1_rot_6d], dim=1)

        # 最终观察向量: 50维(双手) + 9维(单物体) = 59维
        base_obs = torch.cat([robot_proprio, single_object_state], dim=1)

        # 验证观察维度
        expected_dim = 59  # 方案A优化后的观察维度
        if base_obs.shape[1] != expected_dim:
            print(
                f"⚠️ [观察维度错误] 计算得到{base_obs.shape[1]}维，期望{expected_dim}维"
            )
            print(f"   - 机器人本体感受: {robot_proprio.shape[1]}维")
            print(f"   - 单物体状态: {single_object_state.shape[1]}维")
        else:
            # 成功时使用debug日志避免过多输出
            if hasattr(self, "_debug_obs_count"):
                self._debug_obs_count += 1
                if self._debug_obs_count % 1000 == 0:  # 每1000次输出一次
                    print(
                        f"✅ [观察维度正确] {base_obs.shape[1]}维 = {robot_proprio.shape[1]}(双手) + {single_object_state.shape[1]}(物体)"
                    )
            else:
                self._debug_obs_count = 1
                print(
                    f"✅ [观察维度正确] {base_obs.shape[1]}维 = {robot_proprio.shape[1]}(双手) + {single_object_state.shape[1]}(物体)"
                )

        return base_obs


@torch.jit.script
def compute_rewards(
    rew_scale_alive: float,
    rew_scale_terminated: float,
    rew_scale_joint_pos: float,
    rew_scale_joint_vel: float,
    joint_pos: torch.Tensor,
    joint_vel: torch.Tensor,
    reset_terminated: torch.Tensor,
):
    rew_alive = rew_scale_alive * (1.0 - reset_terminated.float())
    rew_termination = rew_scale_terminated * reset_terminated.float()
    rew_joint_pos = rew_scale_joint_pos * torch.sum(torch.square(joint_pos), dim=-1)
    rew_joint_vel = rew_scale_joint_vel * torch.sum(torch.abs(joint_vel), dim=-1)
    total_reward = rew_alive + rew_termination + rew_joint_pos + rew_joint_vel
    return total_reward


# 注册环境

gym.register(
    id="Isaac-DexH13-Direct-v0",
    entry_point="IsaacSim_direct_task_table_set_env_DexH13:DexH13DirectEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": "IsaacSim_direct_task_table_set_env_DexH13:DexH13DirectEnvCfg",
        "rl_games_cfg_entry_point": "agents:rl_games_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": "agents:rsl_rl_ppo_cfg.yaml",
        "skrl_cfg_entry_point": "agents:skrl_ppo_cfg.yaml",
        "skrl_sac_cfg_entry_point": "agents:skrl_sac_cfg.yaml",  # 添加这一行
        "sb3_cfg_entry_point": "agents:sb3_ppo_cfg.yaml",
    },
)
