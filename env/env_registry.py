"""
统一的环境注册中心
用于管理所有可用的机器人仿真环境
"""

import gymnasium as gym


def register_all_environments():
    """注册所有可用的环境"""

    # 注册TORA全身机器人环境
    gym.register(
        id="Isaac-TORA-Direct-v0",
        entry_point="env.IsaacSim_direct_task_table_set_env_TORA:ToraDirectEnv",
        disable_env_checker=True,
        kwargs={
            "env_cfg_entry_point": "env.<PERSON><PERSON><PERSON>_direct_task_table_set_env_TORA:ToraDirectEnvCfg",
            "rl_games_cfg_entry_point": "agents:rl_games_ppo_cfg.yaml",
            "rsl_rl_cfg_entry_point": "agents:rsl_rl_ppo_cfg.yaml",
            "skrl_cfg_entry_point": "agents:skrl_ppo_cfg.yaml",
            "skrl_sac_cfg_entry_point": "agents:skrl_sac_cfg.yaml",
            "sb3_cfg_entry_point": "agents:sb3_ppo_cfg.yaml",
        },
    )

    # 注册DexH13灵巧手环境
    gym.register(
        id="Isaac-DexH13-Direct-v0",
        entry_point="env.IsaacSim_direct_task_table_set_env_DexH13:DexH13DirectEnv",
        disable_env_checker=True,
        kwargs={
            "env_cfg_entry_point": "env.IsaacSim_direct_task_table_set_env_DexH13:DexH13DirectEnvCfg",
            "rl_games_cfg_entry_point": "agents:rl_games_ppo_cfg.yaml",
            "rsl_rl_cfg_entry_point": "agents:rsl_rl_ppo_cfg.yaml",
            "skrl_cfg_entry_point": "agents:skrl_ppo_cfg.yaml",
            "skrl_sac_cfg_entry_point": "agents:skrl_sac_cfg.yaml",
            "sb3_cfg_entry_point": "agents:sb3_ppo_cfg.yaml",
        },
    )

    # 注册单手DexH13灵巧手环境
    gym.register(
        id="Isaac-SingleHand-DexH13-Direct-v0",
        entry_point="env.IsaacSim_direct_task_table_set_env_DexH13_single_hand:SingleHandDexH13DirectEnv",
        disable_env_checker=True,
        kwargs={
            "env_cfg_entry_point": "env.IsaacSim_direct_task_table_set_env_DexH13_single_hand:SingleHandDexH13DirectEnvCfg",
        },
    )

    print("✅ 已注册所有可用环境:")
    print("   - Isaac-TORA-Direct-v0 (全身机器人)")
    print("   - Isaac-DexH13-Direct-v0 (DexH13灵巧手 - 双手)")
    print("   - Isaac-SingleHand-DexH13-Direct-v0 (单手DexH13灵巧手)")


# 支持的环境映射
SUPPORTED_ENVIRONMENTS = {
    "Isaac-TORA-Direct-v0": {
        "description": "TORA全身机器人环境",
        "config_class": "ToraDirectEnvCfg",
        "module": "IsaacSim_direct_task_table_set_env",
        "action_space": 50,
        "observation_space": "动态计算",
    },
    "Isaac-DexH13-Direct-v0": {
        "description": "DexH13灵巧手环境",
        "config_class": "DexH13DirectEnvCfg",
        "module": "IsaacSim_direct_task_table_set_env_DexH13",
        "action_space": 50,
        "observation_space": "动态计算",
    },
}


def get_env_info(env_name: str) -> dict:
    """获取环境信息"""
    if env_name not in SUPPORTED_ENVIRONMENTS:
        raise ValueError(
            f"不支持的环境: {env_name}. 可用环境: {list(SUPPORTED_ENVIRONMENTS.keys())}"
        )
    return SUPPORTED_ENVIRONMENTS[env_name]


def list_available_environments():
    """列出所有可用环境"""
    print("\n🌍 可用的仿真环境:")
    for env_name, info in SUPPORTED_ENVIRONMENTS.items():
        print(f"   {env_name}: {info['description']}")
        print(f"      动作空间: {info['action_space']}")
        print(f"      配置类: {info['config_class']}")
        print()


if __name__ == "__main__":
    register_all_environments()
    list_available_environments()
