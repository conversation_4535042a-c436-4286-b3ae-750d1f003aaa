#!/usr/bin/env python3
"""
环境配置工具模块
提供训练和评估脚本之间的统一配置管理
"""

import os
import logging
from typing import Any, Optional, Tuple
from omegaconf import DictConfig, OmegaConf
from isaaclab.utils.io import load_yaml, dump_yaml


logger = logging.getLogger(__name__)


def get_environment_config(env_name: str, hydra_cfg: Optional[DictConfig] = None):
    """
    根据环境名称获取环境配置类

    Args:
        env_name: 环境名称 (e.g., "Isaac-TORA-Direct-v0", "Isaac-DexH13-Direct-v0")
        hydra_cfg: Hydra配置对象（可选）

    Returns:
        tuple: (EnvCfg class, env_cfg instance)
    """
    if env_name == "Isaac-TORA-Direct-v0":
        from .IsaacSim_direct_task_table_set_env_TORA import ToraDirectEnvCfg

        EnvCfg = ToraDirectEnvCfg
        env_cfg = ToraDirectEnvCfg()
    elif env_name == "Isaac-DexH13-Direct-v0":
        from .IsaacSim_direct_task_table_set_env_DexH13 import DexH13DirectEnvCfg

        EnvCfg = DexH13DirectEnvCfg
        env_cfg = DexH13DirectEnvCfg()
    elif env_name == "Isaac-SingleHand-DexH13-Direct-v0":
        from .IsaacSim_direct_task_table_set_env_DexH13_single_hand import (
            SingleHandDexH13DirectEnvCfg,
        )

        EnvCfg = SingleHandDexH13DirectEnvCfg
        env_cfg = SingleHandDexH13DirectEnvCfg()
    else:
        raise ValueError(f"Unsupported environment: {env_name}")

    logger.info(f"Loaded environment config: {EnvCfg.__name__}")
    return EnvCfg, env_cfg


def apply_training_config_overrides(env_cfg, hydra_cfg: DictConfig):
    """
    应用训练时的配置覆盖逻辑（与tora_learning_train_sb3_hierarchical.py保持一致）

    Args:
        env_cfg: 环境配置实例
        hydra_cfg: Hydra配置对象

    Returns:
        env_cfg: 应用覆盖后的环境配置
    """
    original_frames = env_cfg.num_frames_to_stack
    original_obs_space = env_cfg.observation_space

    # 始终应用seq_length配置，确保observation_space被正确计算
    # 即使seq_length等于默认值也要重新计算，因为observation_space可能是MISSING
    logger.info(
        f"应用帧堆叠配置: {env_cfg.num_frames_to_stack} -> {hydra_cfg.env.seq_length}"
    )
    env_cfg.num_frames_to_stack = hydra_cfg.env.seq_length

    # 重新计算观察空间（即使值相同也重新计算，确保observation_space被设置）
    stacked_obs_dim = env_cfg.num_frames_to_stack * env_cfg.base_observation_space
    env_cfg.observation_space = stacked_obs_dim

    logger.info(
        f"重新计算观察空间: {original_obs_space} -> {env_cfg.observation_space}"
    )

    # 应用其他环境参数
    if hasattr(hydra_cfg.env, "num_envs") and hydra_cfg.env.num_envs is not None:
        env_cfg.scene.num_envs = hydra_cfg.env.num_envs

    logger.info(f"最终环境配置:")
    logger.info(f"  - 环境数量: {env_cfg.scene.num_envs}")
    logger.info(f"  - 帧堆叠数量: {env_cfg.num_frames_to_stack}")
    logger.info(f"  - 基础观察维度: {env_cfg.base_observation_space}")
    logger.info(f"  - 总观察维度: {env_cfg.observation_space}")

    return env_cfg


def load_training_config_from_model_dir(model_path: str) -> Optional[DictConfig]:
    """
    从模型目录加载训练时的配置

    Args:
        model_path: 模型文件路径

    Returns:
        DictConfig or None: 训练时的配置，如果未找到则返回None
    """
    model_dir = os.path.dirname(model_path)
    logger.info(f"在目录中搜索训练配置: {model_dir}")

    # 优先尝试加载Hydra配置
    effective_config_path = os.path.join(model_dir, "effective_config.yaml")
    if os.path.exists(effective_config_path):
        logger.info(f"找到训练时Hydra配置: {effective_config_path}")
        try:
            training_cfg = OmegaConf.load(effective_config_path)
            logger.info("成功加载训练时Hydra配置")
            # 验证关键配置存在
            if hasattr(training_cfg, "env") and hasattr(training_cfg.env, "seq_length"):
                logger.info(f"验证配置: seq_length={training_cfg.env.seq_length}")
                return training_cfg
            else:
                logger.warning("Hydra配置缺少关键的env.seq_length参数")
        except Exception as e:
            logger.error(f"加载Hydra配置失败: {e}")

    # 备用：尝试加载环境配置文件
    env_config_path = os.path.join(model_dir, "env_config_applied.yaml")
    if os.path.exists(env_config_path):
        logger.info(f"找到训练时环境配置: {env_config_path}")
        try:
            env_config = load_yaml(env_config_path)
            logger.info(
                f"环境配置内容预览: {list(env_config.keys()) if isinstance(env_config, dict) else type(env_config)}"
            )

            # 构造一个简化的配置结构
            frames_to_stack = env_config.get("num_frames_to_stack", 15)
            scene_config = env_config.get("scene", {})
            num_envs = (
                scene_config.get("num_envs", 4) if isinstance(scene_config, dict) else 4
            )

            pseudo_cfg = OmegaConf.create(
                {"env": {"seq_length": frames_to_stack, "num_envs": num_envs}}
            )
            logger.info(
                f"从环境配置构造伪Hydra配置: seq_length={frames_to_stack}, num_envs={num_envs}"
            )
            return pseudo_cfg
        except Exception as e:
            logger.error(f"加载环境配置失败: {e}")

    # 最后尝试：检查是否有其他可能的配置文件
    possible_config_files = [
        "config.yaml",
        "hydra_config.yaml",
        ".hydra/config.yaml",
        ".hydra/hydra.yaml",
    ]

    for config_file in possible_config_files:
        config_path = os.path.join(model_dir, config_file)
        if os.path.exists(config_path):
            logger.info(f"尝试备用配置文件: {config_path}")
            try:
                backup_cfg = OmegaConf.load(config_path)
                if hasattr(backup_cfg, "env") and hasattr(backup_cfg.env, "seq_length"):
                    logger.info(f"成功从备用配置加载: {config_file}")
                    return backup_cfg
            except Exception as e:
                logger.debug(f"备用配置文件加载失败 {config_file}: {e}")

    logger.warning(f"未找到任何可用的训练配置文件在目录: {model_dir}")
    logger.warning("将使用默认环境配置，这可能导致评估时帧堆叠配置与训练时不一致")
    return None


def setup_environment_for_evaluation(
    env_name: str, model_path: str, num_envs: Optional[int] = None
):
    """
    为评估设置环境，确保与训练时配置一致

    Args:
        env_name: 环境名称
        model_path: 模型路径
        num_envs: 环境数量（可选，用于覆盖）

    Returns:
        tuple: (env_cfg, training_cfg_info)
    """
    # 1. 获取基础环境配置
    EnvCfg, env_cfg = get_environment_config(env_name)

    # 2. 尝试加载训练时配置
    training_cfg = load_training_config_from_model_dir(model_path)

    if training_cfg is not None:
        # 3. 应用训练时的配置覆盖
        env_cfg = apply_training_config_overrides(env_cfg, training_cfg)

        config_info = {
            "source": "training_config",
            "seq_length": training_cfg.env.seq_length,
            "frames_to_stack": env_cfg.num_frames_to_stack,
            "observation_space": env_cfg.observation_space,
            "base_observation_space": env_cfg.base_observation_space,
        }
    else:
        # 4. 使用默认配置
        logger.warning("使用默认环境配置，可能与训练时不一致！")
        config_info = {
            "source": "default_config",
            "seq_length": env_cfg.num_frames_to_stack,
            "frames_to_stack": env_cfg.num_frames_to_stack,
            "observation_space": env_cfg.observation_space,
            "base_observation_space": env_cfg.base_observation_space,
        }

    # 5. 覆盖环境数量（如果指定）
    if num_envs is not None:
        env_cfg.scene.num_envs = num_envs
        config_info["num_envs_override"] = num_envs

    return env_cfg, config_info


def validate_environment_consistency(
    env, expected_obs_dim: int, expected_action_dim: int, config_info: dict = None
):
    """
    验证环境配置的一致性，包括帧堆叠验证

    Args:
        env: 创建的环境实例
        expected_obs_dim: 期望的观察维度
        expected_action_dim: 期望的动作维度
        config_info: 配置信息字典（用于详细验证）

    Returns:
        bool: 是否一致
    """
    actual_obs_dim = env.observation_space.shape[0]
    actual_action_dim = env.action_space.shape[0]

    obs_consistent = actual_obs_dim == expected_obs_dim
    action_consistent = actual_action_dim == expected_action_dim

    logger.info("=" * 50)
    logger.info("环境一致性验证:")
    logger.info(
        f"  观察空间: 期望{expected_obs_dim}, 实际{actual_obs_dim} {'✅' if obs_consistent else '❌'}"
    )
    logger.info(
        f"  动作空间: 期望{expected_action_dim}, 实际{actual_action_dim} {'✅' if action_consistent else '❌'}"
    )

    # 详细的帧堆叠验证
    if config_info:
        logger.info("详细配置验证:")
        logger.info(f"  配置来源: {config_info.get('source', 'unknown')}")
        logger.info(f"  序列长度: {config_info.get('seq_length', 'unknown')}")
        logger.info(f"  帧堆叠数: {config_info.get('frames_to_stack', 'unknown')}")
        logger.info(
            f"  基础观察维度: {config_info.get('base_observation_space', 'unknown')}"
        )

        # 验证帧堆叠计算
        base_obs_dim = config_info.get("base_observation_space")
        frames_to_stack = config_info.get("frames_to_stack")
        if base_obs_dim and frames_to_stack:
            calculated_obs_dim = base_obs_dim * frames_to_stack
            calculation_correct = calculated_obs_dim == expected_obs_dim
            logger.info(
                f"  计算验证: {base_obs_dim}×{frames_to_stack}={calculated_obs_dim} {'✅' if calculation_correct else '❌'}"
            )

            if not calculation_correct:
                logger.error(
                    f"帧堆叠计算不正确！{base_obs_dim}×{frames_to_stack}≠{expected_obs_dim}"
                )

    # 错误分析和建议
    if not obs_consistent:
        logger.error(f"❌ 观察空间维度不匹配！这可能导致模型预测失败")
        logger.error(f"   建议检查:")
        logger.error(f"   1. 训练时的帧堆叠配置是否正确加载")
        logger.error(f"   2. 环境的num_frames_to_stack参数是否与训练时一致")
        logger.error(f"   3. 模型目录中是否存在effective_config.yaml文件")

    if not action_consistent:
        logger.error(f"❌ 动作空间维度不匹配！这可能导致动作执行失败")
        logger.error(f"   建议检查环境的action_space配置")

    overall_consistent = obs_consistent and action_consistent
    logger.info(f"总体一致性: {'✅ 通过' if overall_consistent else '❌ 失败'}")
    logger.info("=" * 50)

    return overall_consistent


def save_evaluation_config(env_cfg, output_dir: str, config_info: dict):
    """
    保存评估时使用的配置信息

    Args:
        env_cfg: 环境配置
        output_dir: 输出目录
        config_info: 配置信息
    """
    eval_config_path = os.path.join(output_dir, "evaluation_env_config.yaml")

    try:
        # 保存环境配置
        dump_yaml(eval_config_path, env_cfg)

        # 保存配置信息
        config_info_path = os.path.join(output_dir, "evaluation_config_info.yaml")
        dump_yaml(config_info_path, config_info)

        logger.info(f"评估配置已保存:")
        logger.info(f"  环境配置: {eval_config_path}")
        logger.info(f"  配置信息: {config_info_path}")

    except Exception as e:
        logger.error(f"保存评估配置失败: {e}")


def debug_environment_config(env_cfg, model_path: str, config_info: dict):
    """
    调试环境配置，提供详细的配置信息和一致性检查

    Args:
        env_cfg: 环境配置实例
        model_path: 模型路径
        config_info: 配置信息字典
    """
    logger.info("=" * 60)
    logger.info("🔍 环境配置调试报告")
    logger.info("=" * 60)

    # 基本信息
    logger.info(f"📁 模型路径: {model_path}")
    logger.info(f"📁 模型目录: {os.path.dirname(model_path)}")

    # 配置来源分析
    logger.info("\n📋 配置来源分析:")
    source = config_info.get("source", "unknown")
    logger.info(f"   配置来源: {source}")

    if source == "training_config":
        logger.info("   ✅ 配置来自训练时保存的文件")
    elif source == "default_config":
        logger.info("   ⚠️  使用默认配置，可能不一致")
        logger.info("   建议检查以下文件是否存在:")
        model_dir = os.path.dirname(model_path)
        config_files = [
            "effective_config.yaml",
            "env_config_applied.yaml",
            "config.yaml",
            ".hydra/config.yaml",
        ]
        for config_file in config_files:
            config_path = os.path.join(model_dir, config_file)
            exists = os.path.exists(config_path)
            logger.info(f"     {config_file}: {'✅ 存在' if exists else '❌ 不存在'}")

    # 观察空间配置
    logger.info("\n👁️  观察空间配置:")
    logger.info(
        f"   基础观察维度: {config_info.get('base_observation_space', 'unknown')}"
    )
    logger.info(f"   帧堆叠数量: {config_info.get('frames_to_stack', 'unknown')}")
    logger.info(f"   序列长度: {config_info.get('seq_length', 'unknown')}")
    logger.info(f"   总观察维度: {config_info.get('observation_space', 'unknown')}")

    # 计算验证
    base_obs = config_info.get("base_observation_space")
    frames = config_info.get("frames_to_stack")
    total_obs = config_info.get("observation_space")

    if base_obs and frames and total_obs:
        calculated = base_obs * frames
        is_correct = calculated == total_obs
        logger.info(f"\n🧮 计算验证:")
        logger.info(f"   {base_obs} × {frames} = {calculated}")
        logger.info(f"   配置的总维度: {total_obs}")
        logger.info(f"   计算正确性: {'✅ 正确' if is_correct else '❌ 错误'}")

        if not is_correct:
            logger.error(f"   ❌ 维度计算不匹配！请检查配置")

    # 环境配置详情
    logger.info(f"\n🏗️  环境配置详情:")
    logger.info(f"   环境类型: {type(env_cfg).__name__}")
    logger.info(f"   环境数量: {getattr(env_cfg.scene, 'num_envs', 'unknown')}")
    logger.info(f"   动作空间: {getattr(env_cfg, 'action_space', 'unknown')}")
    logger.info(f"   动作缩放: {getattr(env_cfg, 'action_scale', 'unknown')}")

    # 建议
    logger.info(f"\n💡 建议:")
    if source == "default_config":
        logger.info("   1. 确保模型训练时保存了配置文件")
        logger.info("   2. 检查训练脚本是否调用了save_evaluation_config")
        logger.info("   3. 验证模型目录路径是否正确")
    elif source == "training_config":
        logger.info("   1. 配置加载正常，应该与训练时一致")
        logger.info("   2. 如果仍有问题，检查环境实现是否正确使用配置")

    logger.info("=" * 60)


def create_config_comparison_report(
    training_config_path: str, eval_config_info: dict
) -> str:
    """
    创建训练配置与评估配置的对比报告

    Args:
        training_config_path: 训练配置文件路径
        eval_config_info: 评估配置信息

    Returns:
        str: 对比报告
    """
    report_lines = []
    report_lines.append("📊 训练-评估配置对比报告")
    report_lines.append("=" * 50)

    # 加载训练配置
    if os.path.exists(training_config_path):
        try:
            if training_config_path.endswith(".yaml"):
                training_cfg = OmegaConf.load(training_config_path)
            else:
                training_cfg = load_yaml(training_config_path)

            # 对比关键参数
            comparisons = [
                (
                    "序列长度",
                    getattr(training_cfg.env, "seq_length", "N/A"),
                    eval_config_info.get("seq_length", "N/A"),
                ),
                (
                    "帧堆叠数",
                    getattr(training_cfg.env, "num_frames_to_stack", "N/A"),
                    eval_config_info.get("frames_to_stack", "N/A"),
                ),
                (
                    "观察维度",
                    getattr(training_cfg.env, "observation_space", "N/A"),
                    eval_config_info.get("observation_space", "N/A"),
                ),
            ]

            for param_name, train_val, eval_val in comparisons:
                match = train_val == eval_val
                report_lines.append(f"{param_name}:")
                report_lines.append(f"  训练时: {train_val}")
                report_lines.append(f"  评估时: {eval_val}")
                report_lines.append(f"  一致性: {'✅ 一致' if match else '❌ 不一致'}")
                report_lines.append("")

        except Exception as e:
            report_lines.append(f"❌ 无法加载训练配置: {e}")
    else:
        report_lines.append(f"❌ 训练配置文件不存在: {training_config_path}")

    return "\n".join(report_lines)
