"""单手DexH13控制模型的DirectRLEnv实现 - 通用目标导向运动生成"""

import gymnasium as gym
import torch
from collections import deque
import numpy as np

# from tlpy.drivers.isaac_tora_driver import Isaac<PERSON>oraDriver
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.sim.spawners.from_files import GroundPlaneCfg, spawn_ground_plane
from isaaclab.utils import configclass
from isaaclab.utils.math import sample_uniform

# 添加DexH13配置导入
from px_janus_learnsim.robot.DexH13.DexH13_right import DexH13_right_CFG
from px_janus_learnsim.robot.DexH13.DexH13_left import DexH13_left_CFG
import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation, ArticulationCfg
from typing import Sequence
from px_janus_learnsim.config.paths import ASSET_PATH
from isaaclab.assets import (
    RigidObject,
    RigidObjectCfg,
)
from isaaclab.managers import SceneEntityCfg
from px_janus_learnsim.utils.math import (
    quat_to_rot6d,
    rot6d_to_rot_mat,
    quat_from_matrix,
)

from typing import Dict

# === 新增：导入训练时使用的物体中心坐标系转换模块 ===
try:
    from px_janus_learnsim.utils.object_centric_transforms import (
        ObjectCentricTransformer,
        ObjectPose,
        HandState,
    )

    OBJECT_CENTRIC_AVAILABLE = True
    print("✅ 环境成功导入训练时的物体中心坐标系转换模块")
except ImportError as e:
    OBJECT_CENTRIC_AVAILABLE = False
    print(f"⚠️ 无法导入物体中心坐标系转换模块: {e}")
    print("   继续使用世界坐标系模式")

# from tlpy.interfaces.robot import ToraJoints
from dataclasses import MISSING

OBJECT_MODEL_CONFIGS = {
    "bottle": {
        "usd_path": f"{ASSET_PATH}/yeyedenongchangfanqiejiang.usda",
        "scale": np.array([0.001, 0.001, 0.001]),
        "mass": 0.0001,
        "init_pos": (-0.455, 0.005, 10.18),
        "collision_enabled": False,
        "rigid_body_enabled": True,  # 添加刚体属性
        "kinematic_enabled": True,  # 添加运动学属性
    },
    "ketchup": {
        "usd_path": f"{ASSET_PATH}/005_tomato_soup_can.usd",
        "scale": np.array([1.0, 1.0, 1.0]),
        "mass": 0.001,
        "init_pos": (-0.5, 0.0, 0.1),
        "collision_enabled": False,
        "rigid_body_enabled": True,  # 添加刚体属性
        "kinematic_enabled": True,  # 添加运动学属性
    },
}


@configclass
class SingleHandDexH13DirectEnvCfg(DirectRLEnvCfg):
    """
    单手DexH13控制模型配置类

    设计理念：
    - 通用单手控制：一个模型同时适用于左右手
    - 目标导向：学习从当前状态到目标状态的运动映射
    - 数据高效：最大化利用所有手部运动数据
    """

    # 环境基础配置
    num_frames_to_stack: int = 1  # 默认值，实际使用时会被Hydra配置覆盖

    # 手部选择配置 - 新增参数
    hand_type: str = "right"  # "left" 或 "right"，选择控制哪只手

    # 单手控制模型观察空间 (Total Dimension = 34)
    # ===============================================
    # 修改后设计：直接输出模型期望的格式
    # - current_hand_state: 25维 (16关节 + 9位姿) - 当前手部状态
    # - object_state:       9维  (3位置 + 6旋转)   - 环境物体状态
    # 总计: 25 + 9 = 34维 (模型直接可用的格式)
    #
    # 额外在观察字典中提供：
    # - target_pose: 目标手部位置信息，供ExecutionBCActorPolicy使用

    # === 新增：动作处理配置 ===
    # 动作缩放配置 - 用于兼容外部缩放处理
    disable_builtin_action_scaling: bool = (
        False  # 禁用内置动作缩放（当使用外部统计缩放时）
    )

    # === 新增：坐标系转换配置 ===
    # 物体中心坐标系配置 - 确保训练和评估一致性
    use_object_centric: bool = True  # 默认启用物体中心坐标系转换（与训练时保持一致）

    base_observation_space: int = 34  # 修改：改为34维，直接输出模型期望格式

    decimation = 2
    episode_length_s = 5.0
    action_scale = 1.0  # [N]

    # 单手动作空间 (25维)
    # ==================
    # - hand_joints: 16维 (拇指4 + 食指4 + 中指4 + 无名指4)
    # - hand_pose:   9维  (3位置 + 6旋转)
    action_space = 25  # 单手动作：16关节 + 9位姿

    observation_space: int = (
        MISSING  # base_observation_space * num_frames_to_stack，会在运行时重新计算
    )

    # --- 单手控制模型观察空间详细说明 ---
    #
    # 设计优势：
    # 1. 目标导向学习：
    #    - 输入包含当前状态和目标状态
    #    - 学习状态到状态的运动映射
    #    - 支持任意起终点运动生成
    #
    # 2. 通用性设计：
    #    - 同一模型适用于左右手
    #    - 通过hand_type参数选择控制手部
    #    - 数据混合训练提升泛化能力
    #
    # 3. 效率提升：
    #    - 观察维度减半：110→59维
    #    - 动作维度减半：50→25维
    #    - 学习复杂度显著降低

    state_space = 0

    # simulation
    sim: SimulationCfg = SimulationCfg(dt=1 / 120, render_interval=decimation)

    # robot - 支持左右手评估的双重配置设计
    # ==========================================
    # 设计说明：虽然是单手控制，但保留左右手配置是为了：
    # 1. 评估时支持手部类型切换 (--hand_type left/right)
    # 2. 同一套代码可以评估不同手部的控制模型
    # 3. 便于后续扩展到左右手切换训练
    # 运行时会根据hand_type参数选择使用哪个配置

    dexh13_right_cfg: ArticulationCfg = DexH13_right_CFG.replace(
        prim_path="/World/envs/env_.*/dexh13_hand",  # 统一命名为dexh13_hand
        init_state=ArticulationCfg.InitialStateCfg(
            pos=(0.0, 0.0, 1.0),  # 右手初始位置
            rot=(0.0, 0.0, 0.0, 1.0),  # 默认旋转
        ),
    )

    dexh13_left_cfg: ArticulationCfg = DexH13_left_CFG.replace(
        prim_path="/World/envs/env_.*/dexh13_hand",  # 统一命名为dexh13_hand
        init_state=ArticulationCfg.InitialStateCfg(
            pos=(0.0, 0.0, 1.0),  # 左手初始位置
            rot=(0.0, 0.0, 0.0, 1.0),  # 默认旋转
        ),
    )

    # scene
    scene: InteractiveSceneCfg = InteractiveSceneCfg(
        num_envs=1, env_spacing=4.0, replicate_physics=True
    )

    table: RigidObjectCfg = RigidObjectCfg(
        prim_path="/World/envs/env_.*/table",
        spawn=sim_utils.UsdFileCfg(
            usd_path=f"{ASSET_PATH}/table_white.usd",
            scale=[0.1, 0.15, 0.1],  # 改为Python列表
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                kinematic_enabled=True,
                disable_gravity=True,
            ),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(
            pos=(0.0, -0.4, 0.0), rot=(0.7071, 0.7071, 0.0, 0.0)
        ),
    )

    object1: RigidObjectCfg = RigidObjectCfg(
        prim_path="/World/envs/env_.*/object1",
        spawn=sim_utils.UsdFileCfg(
            usd_path=f"{ASSET_PATH}/005_tomato_soup_can.usd",
            scale=[1.0, 1.0, 1.0],  # 改为Python列表
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                kinematic_enabled=False,
                disable_gravity=False,
                enable_gyroscopic_forces=True,
            ),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(
            # pos=(-0.2, -0.25, 1.0343), rot=(0.707, 0.707, 0.0, 0.0)
            pos=(-0.2, -0.25, 1.0343),
            rot=(0.0, 0.0, 0.0, 1.0),
        ),
    )

    # 目标状态配置
    # ===================
    # 用于设置目标手部状态，支持动态更新
    default_target_joint_positions: list = [0.0] * 16  # 默认目标关节角度
    default_target_position: list = [0.0, 0.0, 1.0]  # 默认目标位置
    default_target_orientation: list = [0.0, 0.0, 0.0, 1.0]  # 默认目标旋转(四元数)

    # === 新增：目标导向评估配置 ===
    # 评估模式配置
    evaluation_mode: bool = False  # 是否启用评估模式
    random_target_generation: bool = True  # 是否随机生成目标
    random_initial_pose: bool = True  # 是否随机初始位姿

    # === 新增：目标可视化配置 ===
    enable_target_visualization: bool = False  # 是否启用目标手可视化
    target_hand_color: list = [0.0, 0.8, 1.0]  # 目标手颜色（蓝绿色）
    target_hand_transparency: float = 0.6  # 目标手透明度
    target_hand_offset: list = [1.0, 0.0, 0.0]  # 目标手相对当前手的偏移位置

    # === 新增：机器人手颜色配置 ===
    robot_hand_color: list = [1.0, 0.0, 0.0]  # 机器人手颜色（红色）
    robot_hand_transparency: float = 1.0  # 机器人手透明度（完全不透明）

    # 随机范围配置（与训练数据一致）
    target_joint_random_range: list = [-0.5, 0.5]  # 关节角度随机范围（与训练数据一致）
    target_position_random_range: list = [
        [-0.2, 0.2],
        [-0.2, 0.2],
        [0.8, 1.2],
    ]  # x,y,z位置范围

    # 评估阈值配置
    position_tolerance: float = 0.05  # 位置达到阈值 (5cm)
    joint_tolerance: float = 0.1  # 关节角度达到阈值 (弧度)
    success_hold_time: int = 10  # 成功保持时间步数

    # 奖励权重配置
    reward_position_weight: float = 10.0  # 位置奖励权重
    reward_joint_weight: float = 5.0  # 关节奖励权重
    reward_success: float = 100.0  # 成功奖励
    reward_penalty: float = -1.0  # 每步惩罚

    # reset - 统一关节角度配置（与训练数据一致）
    max_joint_pos = 3.15  # 关节位置限制：±1.5弧度（给PD控制器足够的操作空间）
    initial_joint_pos_range = [-0.25, 0.25]  # 初始关节位置随机范围

    # reward scales
    rew_scale_alive = 1.0
    rew_scale_terminated = -2.0
    rew_scale_joint_pos = -1.0
    rew_scale_joint_vel = -0.01

    # === 新增：轨迹模式相关配置 ===
    enable_tracking_failure_detection: bool = False  # 是否启用跟踪失   败检测
    trajectory_tracking_pos_threshold: float = 0.15  # 位置误差阈值 (15cm)
    trajectory_tracking_joint_threshold: float = 0.8  # 关节误差阈值 (0.8 rad)
    trajectory_completion_reward: float = 50.0  # 轨迹完成奖励


class SingleHandDexH13DirectEnv(DirectRLEnv):
    """
    单手DexH13控制环境类

    实现目标导向的单手控制：
    - 支持左右手选择
    - 学习从当前状态到目标状态的运动映射
    - 统一的单手控制接口
    """

    cfg: SingleHandDexH13DirectEnvCfg

    def __init__(
        self,
        cfg: SingleHandDexH13DirectEnvCfg,
        render_mode: str | None = None,
        **kwargs,
    ):
        """Initialize the DexH13 single hand direct environment."""

        # 从配置中获取手部类型
        self.hand_type = cfg.hand_type
        # 轨迹模式相关变量
        self.trajectory_mode = False
        self.loaded_trajectory = None
        self.trajectory_step_index = 0

        # === 重要：在super().__init__之前设置所有需要的属性 ===

        # 设置评估模式相关变量
        self.evaluation_mode = cfg.evaluation_mode
        self.success_hold_counter = 0  # 成功保持计数器
        self.episode_success = False  # 当前episode是否成功
        self.initial_target_distance = None  # 初始目标距离（用于奖励计算）

        # 存储初始和目标状态（用于评估）
        self.initial_hand_state = None
        self.target_achieved = False

        # 设置目标可视化相关变量（必须在super().__init__之前）
        self.enable_target_visualization = cfg.enable_target_visualization
        self.target_hand = None  # 目标手对象
        if self.enable_target_visualization:
            print("🎯 目标可视化已启用")

        # === 新增：初始化训练时使用的物体中心坐标系转换器 ===
        self.use_object_centric = cfg.use_object_centric and OBJECT_CENTRIC_AVAILABLE
        if self.use_object_centric:
            self.object_centric_transformer = ObjectCentricTransformer(
                device="cuda" if torch.cuda.is_available() else "cpu"
            )
            print("✅ 使用训练时的物体中心坐标系转换器")
            print("   - 与SingleHandDataProcessor保持完全一致")
            print("   - 确保训练和评估的坐标系转换逻辑相同")
        else:
            self.object_centric_transformer = None
            if cfg.use_object_centric and not OBJECT_CENTRIC_AVAILABLE:
                print("⚠️ 物体中心坐标系转换器不可用，使用世界坐标系模式")
                print("   - 这可能导致与训练时的坐标系不一致")
            else:
                print("🌍 使用世界坐标系模式")

        # 初始化单手关节状态变量（在_setup_scene之后会被正确设置）
        self.dexh13_hand_joint_pos = None
        self.dexh13_hand_joint_vel = None
        # 不在这里初始化dexh13_hand，让_setup_scene负责创建它

        # 延迟初始化，等待_setup_scene完成
        self._hand_initialized = False

        # 计算观察空间大小
        observation_space_size = self._compute_observation_space_size(cfg)
        cfg.observation_space = observation_space_size

        # 调用父类初始化方法
        super().__init__(cfg, render_mode, **kwargs)

        self.action_scale = self.cfg.action_scale

        # 延迟初始化物体实体配置，现在在_setup_scene中处理
        # 不在__init__中初始化object1_entity_cfg，因为此时object1还不存在

        self.base_obs_dim = self.cfg.base_observation_space
        self.num_stack = self.cfg.num_frames_to_stack

        self.observation_space = gym.spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(self.cfg.observation_space,),
            dtype=np.float32,
        )
        print(f"[Info] 单手环境gym.space.Box创建: {self.observation_space.shape}")

        self._obs_history = [deque(maxlen=self.num_stack) for _ in range(self.num_envs)]

        self.action_space = gym.spaces.Box(
            low=-1.0, high=1.0, shape=(self.cfg.action_space,), dtype=np.float32
        )

        self.action_range = self.cfg.action_scale
        self.action_center = 0.0

        self.last_actions = torch.zeros(
            (self.num_envs, self.cfg.action_space), device=self.device
        )
        self.episode_length_s = self.cfg.episode_length_s

    def load_trajectory(self, trajectory_data):
        """加载轨迹数据用于复现"""
        self.trajectory_mode = True
        self.loaded_trajectory = trajectory_data
        self.trajectory_step_index = 0

        # 从轨迹数据推断序列长度
        self.seq_length = self.loaded_trajectory["actions"].shape[1]  # T维度
        print(f"🎬 轨迹数据加载完成: 序列长度 {self.seq_length}")

        # 立即设置初始状态 - 应用到所有环境
        env_ids = torch.arange(self.num_envs, device=self.device, dtype=torch.int32)
        self._apply_trajectory_initial_state(env_ids)

    def _check_trajectory_completion(self):
        """检查轨迹是否播放完毕"""
        completed = torch.zeros(self.num_envs, dtype=torch.bool, device=self.device)

        if self.trajectory_step_index >= self.seq_length:
            completed[:] = True  # 所有环境的轨迹都完成
            print(f"🎬 轨迹播放完毕: {self.seq_length} 步")

        return completed

    def _check_tracking_failure(self):
        """检查是否跟踪失败（可选功能）"""
        failed = torch.zeros(self.num_envs, dtype=torch.bool, device=self.device)

        if not hasattr(self, "current_target_pose"):
            return failed

        # 计算当前位置与目标位置的误差
        current_pose = self._get_current_hand_pose()  # [num_envs, 25]
        target_pose = self.current_target_pose  # [num_envs, 25]

        # 位置误差
        pos_error = torch.norm(current_pose[:, 16:19] - target_pose[:, 16:19], dim=1)
        # 关节误差
        joint_error = torch.norm(current_pose[:, :16] - target_pose[:, :16], dim=1)

        # 失败阈值
        pos_threshold = self.cfg.trajectory_tracking_pos_threshold  # 例如 0.2m
        joint_threshold = self.cfg.trajectory_tracking_joint_threshold  # 例如 1.0 rad

        failed = (pos_error > pos_threshold) | (joint_error > joint_threshold)

        return failed

    @staticmethod
    def _compute_observation_space_size(cfg: SingleHandDexH13DirectEnvCfg) -> int:
        """计算观察空间大小"""
        # 🔧 重要理解：seq_length不影响观察空间大小！
        # seq_length=15 的含义：
        # - 训练时：模型从单个34维观察预测15个动作的序列
        # - 评估时：环境只需提供34维观察，模型输出15个动作，选第一个执行
        # - 这是 sequence-to-sequence 学习，不是帧堆叠！

        base_obs_size = cfg.base_observation_space  # 34维：手部25维 + 物体9维

        # ✅ 观察空间始终是34维，不受num_frames_to_stack影响
        # num_frames_to_stack在这个架构中不适用，因为模型处理的是seq2seq而非帧堆叠
        obs_size = base_obs_size  # 固定34维

        print(
            f"[Info] 单手环境观察空间: {obs_size}维 (手部25 + 物体9, 不受seq_length影响)"
        )

        return obs_size

    def _setup_scene(self):
        """Setup the scene with the DexH13 single hand robot."""

        print(f"🤚 创建{self.hand_type}手DexH13控制环境")

        # 根据手部类型选择配置
        if self.hand_type == "right":
            base_robot_cfg = self.cfg.dexh13_right_cfg
        else:
            base_robot_cfg = self.cfg.dexh13_left_cfg

        # === 新增：为机器人手添加颜色配置 ===
        # 创建机器人手的visual_material配置
        robot_visual_material = sim_utils.PreviewSurfaceCfg(
            diffuse_color=tuple(self.cfg.robot_hand_color),  # RGB颜色（红色）
            metallic=0.2,  # 金属感
            roughness=0.6,  # 粗糙度
            opacity=self.cfg.robot_hand_transparency,  # 不透明度
            emissive_color=(0.0, 0.0, 0.0),  # 发光颜色
        )

        # 创建带颜色配置的spawn配置
        robot_spawn_cfg = sim_utils.UsdFileCfg(
            usd_path=base_robot_cfg.spawn.usd_path,
            visual_material=robot_visual_material,  # 设置机器人手颜色
            mass_props=base_robot_cfg.spawn.mass_props,
            rigid_props=base_robot_cfg.spawn.rigid_props,
            articulation_props=base_robot_cfg.spawn.articulation_props,
            # 🎨 纯视觉效果配置 - 禁用碰撞检测
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=False,  # 完全禁用碰撞
                contact_offset=0.0,  # 设置接触偏移为0
                rest_offset=0.0,  # 设置静止偏移为0
            ),
        )

        # 更新robot_cfg以包含颜色配置
        robot_cfg = base_robot_cfg.replace(spawn=robot_spawn_cfg)

        # 第一步：创建DexH13灵巧手（使用统一的prim路径）
        self.dexh13_hand = Articulation(robot_cfg)

        # 第二步：添加地面
        spawn_ground_plane(prim_path="/World/ground", cfg=GroundPlaneCfg())

        # 第三步：添加机器人到场景
        self.scene.articulations["dexh13_hand"] = self.dexh13_hand

        # === 新增：创建目标可视化手（如果启用） ===
        if self.enable_target_visualization:
            self._setup_target_hand()

        # 第四步：克隆环境到所有位置
        self.scene.clone_environments(copy_from_source=False)

        # 第五步：创建和添加其他物体（在克隆环境后创建）
        self.table = RigidObject(self.cfg.table)
        self.object1 = RigidObject(self.cfg.object1)

        # 将物体添加到场景
        self.scene.rigid_objects["table"] = self.table
        self.scene.rigid_objects["object1"] = self.object1

        # 第六步：添加灯光（灯光通常是全局的，不需要复制）
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

        # ===== 重要：现在场景完全初始化，可以安全访问data属性 =====

        # 设置动作缩放因子
        self.action_scale = self.cfg.action_scale

        # 初始化关节状态变量（现在dexh13_hand.data可用）
        if hasattr(self.dexh13_hand, "data"):
            self.dexh13_hand_joint_pos = self.dexh13_hand.data.joint_pos
            self.dexh13_hand_joint_vel = self.dexh13_hand.data.joint_vel

            # 初始化DexH13关节映射（移至此处以确保在首次step前完成）
            self._initialize_joint_mapping()
        else:
            print("⚠️ DexH13手部data属性未就绪，延迟初始化")
            # 标记为未初始化，在第一次step时再尝试
            self._joint_state_initialized = False
            self.dexh13_hand_entity_cfg = None

        # 初始化目标状态 - 使用默认值
        self.target_hand_joints = torch.zeros(
            16, device=self.device, dtype=torch.float32
        )
        self.target_hand_position = torch.tensor(
            [0.0, 0.0, 1.0], device=self.device, dtype=torch.float32
        )
        self.target_hand_rotation = torch.tensor(
            [0.0, 0.0, 0.0, 1.0], device=self.device, dtype=torch.float32
        )

        # 初始化评估相关变量
        if self.cfg.evaluation_mode:
            self.current_step_count = torch.zeros(
                self.num_envs, device=self.device, dtype=torch.int32
            )
            self.success_tracker = torch.zeros(
                self.num_envs, device=self.device, dtype=torch.bool
            )
            self.task_success = torch.zeros(
                self.num_envs, device=self.device, dtype=torch.bool
            )

            # 初始化目标生成的随机范围
            self.joint_random_range = (
                torch.tensor(
                    self.cfg.target_joint_random_range,
                    device=self.device,
                    dtype=torch.float32,
                )
                .unsqueeze(0)
                .repeat(self.num_envs, 1, 1)
            )  # [num_envs, num_joints, 2]

            self.position_random_range = (
                torch.tensor(
                    self.cfg.target_position_random_range,
                    device=self.device,
                    dtype=torch.float32,
                )
                .unsqueeze(0)
                .repeat(self.num_envs, 1, 1)
            )  # [num_envs, 3, 2]

        # 创建物体实体配置（延迟到data可用）
        if (
            hasattr(self, "table")
            and self.table is not None
            and hasattr(self.table, "data")
        ):
            self.table_entity_cfg = SceneEntityCfg(
                "table", body_names=list(self.table.data.body_names)
            )
            self.table_entity_cfg.resolve(self.scene)
            print("✅ 桌子实体配置创建成功")
        else:
            self.table_entity_cfg = None
            print("⚠️ 桌子实体配置延迟创建")

        if (
            hasattr(self, "object1")
            and self.object1 is not None
            and hasattr(self.object1, "data")
        ):
            self.object1_entity_cfg = SceneEntityCfg(
                "object1", body_names=list(self.object1.data.body_names)
            )
            self.object1_entity_cfg.resolve(self.scene)
            print("✅ 物体实体配置创建成功")
        else:
            self.object1_entity_cfg = None
            print("⚠️ 物体实体配置延迟创建")

        print(f"✅ 单手DexH13环境设置完成: {self.hand_type}手模式")
        print(f"   - 机器人手颜色: RGB{tuple(self.cfg.robot_hand_color)}")
        print(f"   - 机器人手透明度: {self.cfg.robot_hand_transparency}")

        # 调试信息：验证dexh13_hand对象状态
        print("🔍 调试信息 - dexh13_hand对象状态:")
        print(f"   - dexh13_hand对象: {self.dexh13_hand}")
        print(f"   - 对象类型: {type(self.dexh13_hand)}")
        if self.dexh13_hand is not None:
            print(f"   - 是否有data属性: {hasattr(self.dexh13_hand, 'data')}")
            if hasattr(self.dexh13_hand, "data"):
                print(f"   - data对象: {self.dexh13_hand.data}")
        print(f"   - 在场景中的键: {list(self.scene.articulations.keys())}")

    def _ensure_joint_state_initialized(self):
        """延迟初始化关节状态变量，确保物理视图已完全初始化"""
        if not self._joint_state_initialized:
            try:
                # 验证dexh13_hand对象是否有效
                if self.dexh13_hand is None:
                    print("❌ dexh13_hand对象为None，无法初始化关节状态")
                    return

                # 现在物理视图应该已经可用，可以安全访问num_joints
                total_joints = self.dexh13_hand.num_joints
                self.joint_pos = torch.zeros(
                    (self.num_envs, total_joints), device=self.device
                )
                self.joint_vel = torch.zeros(
                    (self.num_envs, total_joints), device=self.device
                )
                self._joint_state_initialized = True
                print(f"✅ 单手关节状态初始化完成: 总计{total_joints}个关节")
            except AttributeError as e:
                print(f"⚠️ 关节状态初始化失败，物理视图未就绪: {e}")
                # 使用默认关节数量作为后备方案 (DexH13每只手16个关节)
                total_joints = 16
                self.joint_pos = torch.zeros(
                    (self.num_envs, total_joints), device=self.device
                )
                self.joint_vel = torch.zeros(
                    (self.num_envs, total_joints), device=self.device
                )
                self._joint_state_initialized = True
                print(f"✅ 使用默认关节数量初始化: {total_joints}个关节")

    def _pre_physics_step(self, actions: torch.Tensor) -> None:
        # 检查是否禁用内置动作缩放（用于外部统计缩放）
        if self.cfg.disable_builtin_action_scaling:
            # 如果禁用内置缩放，直接使用输入的动作（已经经过外部处理）
            self.actions = actions.clone()
        else:
            # 从[-1,1]扩展到实际需要的范围 [-action_scale, action_scale]
            scaled_actions = actions * self.action_range + self.action_center
            self.actions = scaled_actions.clone()

        self.last_actions = self.actions.clone()

    def _initialize_joint_mapping(self):
        """
        高效初始化单手DexH13关节映射

        支持左右手，自动检测并建立关节索引映射
        添加详细的错误处理和验证
        """

        # 定义标准关节名称映射
        joint_name_mapping = {
            "right": [
                # 拇指 (4个关节)
                "mzcb",
                "mzjdxz",
                "mzzd",
                "mzydxz",
                # 食指 (4个关节)
                "szcb",
                "szjdxz",
                "szzdxz",
                "szydxz",
                # 中指 (4个关节)
                "zzcb",
                "zzjdxz",
                "zzzdxz",
                "zzydxz",
                # 无名指 (4个关节)
                "wmzcb",
                "wmzjdxz",
                "wmzzdxz",
                "wmzydxz",
            ],
            "left": [
                # 拇指 (4个关节) - 左手可能有不同命名
                "mzcb",
                "mzjdxz",
                "mzzdxz",
                "mzyd",
                # 食指 (4个关节)
                "szcb",
                "szjdxz",
                "szzdxz",
                "szydxz",
                # 中指 (4个关节)
                "zzcb",
                "zzjdxz",
                "zzzdxz",
                "zzydxz",
                # 无名指 (4个关节)
                "wmzcb",
                "wmzjdxz",
                "wmzzdxz",
                "wmzydxz",
            ],
        }

        if self.hand_type not in joint_name_mapping:
            raise ValueError(
                f"❌ 不支持的手部类型: {self.hand_type}，支持类型: {list(joint_name_mapping.keys())}"
            )

        target_joint_names = joint_name_mapping[self.hand_type]
        print(f"🔍 正在初始化{self.hand_type}手关节映射...")

        # 获取环境中实际的关节名称（用于调试）
        all_joint_names = self.dexh13_hand.data.joint_names
        print(f"📋 环境中发现{len(all_joint_names)}个关节")

        # 查找并验证关节索引
        found_indices = []
        missing_joints = []

        for i, joint_name in enumerate(target_joint_names):
            try:
                joint_ids, _ = self.dexh13_hand.find_joints(joint_name)
                if joint_ids is not None and len(joint_ids) > 0:
                    found_indices.append(joint_ids[0])
                    print(f"   ✅ {joint_name} -> 索引 {joint_ids[0]}")
                else:
                    missing_joints.append(joint_name)
                    print(f"   ❌ {joint_name} -> 未找到")
            except Exception as e:
                missing_joints.append(joint_name)
                print(f"   ⚠️ {joint_name} -> 查找失败: {e}")

        # 验证映射结果
        if len(found_indices) == 16:
            self.dexh13_hand_joint_indices = found_indices
            print(f"✅ {self.hand_type}手关节映射成功: 找到全部16个关节")
            print(f"📍 关节索引: {found_indices}")
        else:
            error_msg = (
                f"❌ {self.hand_type}手关节映射失败: 找到{len(found_indices)}/16个关节"
            )
            if missing_joints:
                error_msg += f"\n   缺失关节: {missing_joints}"
                error_msg += f"\n   可用关节: {list(all_joint_names)}"

            print(error_msg)

            # 设置默认映射以避免崩溃
            print(f"⚠️ 使用默认关节索引范围 [0:{len(target_joint_names)}]")
            self.dexh13_hand_joint_indices = list(range(len(target_joint_names)))

        # 存储关节名称供其他方法使用
        self.dexh13_hand_joint_names = target_joint_names

        print(f"🔗 {self.hand_type}手关节映射初始化完成")

    def _apply_action(self) -> None:
        """
        应用单手控制模型输出的25维动作到DexH13灵巧手

        单手25维动作空间分解：
        - hand_joints: [0:16]  手部关节 (拇指4 + 食指4 + 中指4 + 无名指4)
        - hand_pose:   [16:25] 手部位姿 (3位置 + 6旋转)

        相比双手50维动作，单手控制大大简化了处理逻辑
        """

        if self.trajectory_mode:
            self._apply_trajectory_step_data()

        # 确保关节映射已初始化
        if not hasattr(self, "dexh13_hand_joint_indices"):
            print("⚠️ 关节映射未初始化，执行紧急初始化")
            self._initialize_joint_mapping()

        num_envs = self.actions.shape[0]
        num_joints = len(self.dexh13_hand.data.joint_names)

        # 验证动作维度（单手25维）
        if self.actions.shape[1] != 25:
            print(f"❌ 单手动作维度不匹配: 期望25维，实际{self.actions.shape[1]}维")
            return

        # 创建完整的关节目标位置张量
        full_actions = torch.zeros(
            (num_envs, num_joints),
            device=self.actions.device,
            dtype=self.actions.dtype,
        )

        # 准备位姿数据张量（用于批量设置）
        hand_poses = torch.zeros(
            (num_envs, 7),  # [x,y,z,qw,qx,qy,qz]
            device=self.actions.device,
            dtype=self.actions.dtype,
        )

        # 简化的单手动作处理
        for env_idx in range(num_envs):
            action = self.actions[env_idx]  # 25维动作向量

            # === 1. 提取并处理手部关节动作 [0:16] ===
            hand_joint_actions = action[0:16]  # 16个关节动作

            # 验证关节索引数量
            if len(self.dexh13_hand_joint_indices) == len(hand_joint_actions):
                # ✅ 修复：使用环境配置的action_scale，确保与训练时一致
                # 动作已经在_pre_physics_step中被缩放过：actions * action_scale + action_center
                # 由于action_scale=1.0, action_center=0.0，所以self.actions = 原始actions
                # 这里直接使用手部关节动作，它们已经是合适的范围

                # 对于DexH13手部关节，合理的角度范围大约是±1.0弧度
                # 由于训练数据范围是[-1,1]，而max_joint_pos=1.0，直接使用即可

                # 获取当前关节位置用于调试
                current_joint_pos = self.dexh13_hand.data.joint_pos[
                    env_idx, self.dexh13_hand_joint_indices
                ]

                # 直接使用动作值（已经过action_scale处理）
                full_actions[env_idx, self.dexh13_hand_joint_indices] = (
                    hand_joint_actions
                )

                # if env_idx == 0:  # 只为第一个环境打印调试信息
                #     print(f"🎯 单手{self.hand_type}控制 - 关节动作:")
                #     print(f"   动作范围: [{hand_joint_actions.min():.3f}, {hand_joint_actions.max():.3f}]")
                #     print(f"   关节限制: ±{self.cfg.max_joint_pos}")
                #     print(f"   拇指: {hand_joint_actions[0:4].cpu().numpy()}")
                #     print(f"   食指: {hand_joint_actions[4:8].cpu().numpy()}")
                #     print(f"   中指: {hand_joint_actions[8:12].cpu().numpy()}")
                #     print(f"   无名指: {hand_joint_actions[12:16].cpu().numpy()}")
            else:
                print(
                    f"❌ {self.hand_type}手关节索引数量({len(self.dexh13_hand_joint_indices)})与动作数量({len(hand_joint_actions)})不匹配"
                )
                return

            # === 2. 提取并处理手部位姿动作 [16:25] ===
            hand_pose_actions = action[16:25]  # 9维：3位置 + 6旋转

            # 分离位置和旋转
            hand_pos = hand_pose_actions[0:3]  # xyz位置
            hand_rot_6d = hand_pose_actions[3:9]  # 6D旋转表示

            # === 3. 6D旋转转换为四元数 ===
            # 6D旋转表示：前3维为旋转矩阵第一列，后3维为第二列
            rot_matrix = torch.zeros(
                3, 3, device=self.actions.device, dtype=self.actions.dtype
            )

            # 设置旋转矩阵的前两列
            rot_matrix[:, 0] = hand_rot_6d[0:3]  # 第一列
            rot_matrix[:, 1] = hand_rot_6d[3:6]  # 第二列

            # 通过叉积计算第三列
            rot_matrix[:, 2] = torch.linalg.cross(rot_matrix[:, 0], rot_matrix[:, 1])

            # 正交化处理
            # 标准化第三列
            rot_matrix[:, 2] = rot_matrix[:, 2] / torch.norm(rot_matrix[:, 2])
            # 标准化第一列
            rot_matrix[:, 0] = rot_matrix[:, 0] / torch.norm(rot_matrix[:, 0])
            # 重新计算第二列确保正交性
            rot_matrix[:, 1] = torch.linalg.cross(rot_matrix[:, 2], rot_matrix[:, 0])

            # 使用Isaac Lab的函数将旋转矩阵转换为四元数 (wxyz格式)
            quat = quat_from_matrix(rot_matrix)  # 返回 [w,x,y,z] 格式

            # === 4. 组合7维位姿数据 ===
            # 🔧 修复：使用绝对位置而不是相对增量
            # 将手腕位置动作解释为绝对目标位置（与训练数据一致）
            target_pos = hand_pos  # 直接使用动作作为绝对目标位置

            # 组合位姿：[x,y,z,qw,qx,qy,qz]
            hand_poses[env_idx] = torch.cat([target_pos, quat])

            # if env_idx == 0:  # 调试信息
            #     print(f"🎯 单手{self.hand_type}控制 - 位姿动作:")
            #     print(f"   动作位置: {hand_pos.cpu().numpy()}")
            #     print(f"   目标位置: {target_pos.cpu().numpy()}")
            #     print(f"   目标旋转(wxyz): {quat.cpu().numpy()}")

        # === 5. 应用动作到仿真 ===
        # 应用关节动作
        self.dexh13_hand.set_joint_position_target(full_actions)

        # 应用位姿动作
        env_ids = torch.arange(num_envs, device=self.actions.device)  # 转换为tensor
        self.dexh13_hand.write_root_pose_to_sim(hand_poses, env_ids)

    def _get_observations(self) -> dict:
        # 调用基础观察计算函数
        current_base_obs, target_pos_obj, target_rot_6d_obj = (
            self._compute_base_observations()
        )

        # 处理观察历史和帧堆叠
        stacked_obs_list = []
        for env_idx in range(self.num_envs):
            obs_to_store = current_base_obs[env_idx].clone().detach()

            if len(self._obs_history[env_idx]) == 0:
                for _ in range(self.num_stack):
                    self._obs_history[env_idx].append(obs_to_store)
            else:
                self._obs_history[env_idx].append(obs_to_store)

            stacked_env_tensor = torch.cat(list(self._obs_history[env_idx]), dim=0)
            stacked_obs_list.append(stacked_env_tensor)

        final_stacked_obs = torch.stack(stacked_obs_list, dim=0)

        # 基础观察信息
        obs_dict = {"policy": final_stacked_obs}
        # print("\nfinal_stacked_obs: ", final_stacked_obs.shape)

        # 评估模式下添加评估信息
        if self.evaluation_mode:
            eval_info = self.get_evaluation_info()

            # 使用物体坐标系下的目标位置和旋转构建25维target pose
            if (
                self.use_object_centric
                and target_pos_obj is not None
                and target_rot_6d_obj is not None
                and target_pos_obj.shape[0] > 0
                and target_rot_6d_obj.shape[0] > 0
            ):
                target_full_state_25d_obj = torch.cat(
                    [
                        self.target_hand_joints,  # [16] 关节角度
                        target_pos_obj[0],  # [3] 物体坐标系下的目标位置
                        target_rot_6d_obj[0],  # [6] 物体坐标系下的目标旋转
                    ],
                    dim=0,
                )
                eval_info["target_full_state_25d"] = (
                    target_full_state_25d_obj.cpu().numpy().tolist()
                )

            obs_dict["evaluation_info"] = eval_info

        return obs_dict

    def _compute_trajectory_tracking_reward(self):
        """轨迹跟踪专用奖励"""
        rewards = torch.zeros(self.num_envs, device=self.device)

        # 跟踪精度奖励
        tracking_reward = self._compute_tracking_accuracy_reward()

        # 轨迹完成奖励
        if self.trajectory_step_index >= self.seq_length:
            completion_bonus = torch.full_like(
                rewards, self.cfg.trajectory_completion_reward
            )
            rewards += completion_bonus

        return rewards + tracking_reward

    def _get_rewards(self) -> torch.Tensor:

        if self.trajectory_mode:
            return self._compute_trajectory_tracking_reward()

        if self.evaluation_mode:
            # 评估模式：使用目标导向奖励
            return self.compute_target_oriented_reward()
        else:
            # 训练模式：使用原有奖励函数
            total_reward = compute_rewards(
                self.cfg.rew_scale_alive,
                self.cfg.rew_scale_terminated,
                self.cfg.rew_scale_joint_pos,
                self.cfg.rew_scale_joint_vel,
                self.joint_pos,
                self.joint_vel,
                self.reset_terminated,
            )
            return total_reward

    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        # 确保关节状态变量已初始化
        self._ensure_joint_state_initialized()

        # 获取单手关节状态进行边界检查
        joint_pos = self.dexh13_hand.data.joint_pos
        joint_vel = self.dexh13_hand.data.joint_vel

        # 合并单手关节数据用于奖励计算
        self.joint_pos = joint_pos.clone()
        self.joint_vel = joint_vel.clone()

        time_out = self.episode_length_buf >= self.max_episode_length - 1

        # 检查单手是否超出关节限制（只检查我们控制的手部关节）
        if (
            hasattr(self, "dexh13_hand_joint_indices")
            and len(self.dexh13_hand_joint_indices) > 0
        ):
            # 只检查我们控制的手部关节
            hand_joint_pos = joint_pos[:, self.dexh13_hand_joint_indices]
            out_of_bounds = torch.any(
                torch.abs(hand_joint_pos) > self.cfg.max_joint_pos, dim=1
            )
            # print out the hand joint pos that exceed the limit
            exceeded_indices = torch.where(out_of_bounds)[0]
            for env_idx in exceeded_indices:
                print(f"   🚫 环境 {env_idx} 关节超限: {hand_joint_pos[env_idx]}")
        else:
            # 如果没有索引映射，检查前16个关节（向后兼容）
            hand_joint_pos = (
                joint_pos[:, :16] if joint_pos.shape[1] >= 16 else joint_pos
            )
            out_of_bounds = torch.any(
                torch.abs(hand_joint_pos) > self.cfg.max_joint_pos, dim=1
            )

        # === 新增：任务完成的done条件（评估模式） ===

        trajectory_completed = torch.zeros(
            self.num_envs, dtype=torch.bool, device=self.device
        )
        tracking_failed = torch.zeros(
            self.num_envs, dtype=torch.bool, device=self.device
        )

        if self.trajectory_mode:
            # 1. 轨迹播放完毕
            trajectory_completed = self._check_trajectory_completion()

            # 2. 跟踪失败检查（可选）
            if self.cfg.enable_tracking_failure_detection:
                tracking_failed = self._check_tracking_failure()

        task_completed = torch.zeros(
            self.num_envs, dtype=torch.bool, device=self.device
        )

        if self.evaluation_mode:
            # 检查目标是否达到
            target_achieved, joint_distances, position_distances = (
                self.evaluate_target_achievement()
            )

            # 更新成功保持计数器（这里需要修复，应该为每个环境单独计数）
            for env_idx in range(self.num_envs):
                if target_achieved[env_idx]:
                    if not hasattr(self, "success_hold_counters"):
                        self.success_hold_counters = torch.zeros(
                            self.num_envs, dtype=torch.int32, device=self.device
                        )

                    self.success_hold_counters[env_idx] += 1

                    # 如果保持成功状态足够长时间，标记任务完成
                    if (
                        self.success_hold_counters[env_idx]
                        >= self.cfg.success_hold_time
                    ):
                        task_completed[env_idx] = True
                        self.episode_success = True
                        print(
                            f"   ✅ 环境 {env_idx} 任务完成! 保持成功状态 {self.success_hold_counters[env_idx]} 步"
                        )
                else:
                    # 重置计数器
                    if hasattr(self, "success_hold_counters"):
                        self.success_hold_counters[env_idx] = 0

        # 组合所有终结条件
        if self.trajectory_mode:
            terminated = (
                out_of_bounds | task_completed | trajectory_completed | tracking_failed
            )  # 关节超限 或 任务完成
        else:
            terminated = out_of_bounds | task_completed  # 关节超限 或 任务完成

        truncated = time_out  # 时间超时

        # === 新增：打印终结原因 ===
        # 检查每个环境的终结情况并打印详细信息
        for env_idx in range(self.num_envs):
            env_timeout = time_out[env_idx].item()
            env_out_of_bounds = out_of_bounds[env_idx].item()
            env_task_completed = task_completed[env_idx].item()
            env_trajectory_completed = trajectory_completed[env_idx].item()
            env_tracking_failed = tracking_failed[env_idx].item()

            if env_timeout or env_out_of_bounds or env_task_completed:
                print(f"\n🔚 环境 {env_idx} 终结原因:")
                print(
                    f"   步数: {self.episode_length_buf[env_idx].item()}/{self.max_episode_length}"
                )

                if env_task_completed:
                    print(
                        f"   🏆 任务完成: 成功达到目标并保持 {self.cfg.success_hold_time} 步"
                    )

                if env_timeout:
                    print(f"   ⏰ 时间超时: 达到最大步数 {self.max_episode_length}")
                if env_trajectory_completed:
                    print("   🏆 轨迹播放完成")
                if env_tracking_failed:
                    print("   ❌ 跟踪失败")
                if env_out_of_bounds:
                    # 找出超限的关节（只检查手部关节）
                    if (
                        hasattr(self, "dexh13_hand_joint_indices")
                        and len(self.dexh13_hand_joint_indices) > 0
                    ):
                        env_hand_joint_pos = joint_pos[
                            env_idx, self.dexh13_hand_joint_indices
                        ]
                        joint_indices_to_check = self.dexh13_hand_joint_indices
                    else:
                        # 向后兼容：检查前16个关节
                        max_joints = min(16, joint_pos.shape[1])
                        env_hand_joint_pos = joint_pos[env_idx, :max_joints]
                        joint_indices_to_check = list(range(max_joints))

                    print
                    joint_limits_exceeded = (
                        torch.abs(env_hand_joint_pos) > self.cfg.max_joint_pos
                    )
                    exceeded_local_indices = torch.where(joint_limits_exceeded)[0]

                    print(
                        f"   🚫 关节超限: {len(exceeded_local_indices)}个手部关节超出限制 (±{self.cfg.max_joint_pos})"
                    )

                    # 打印前5个超限关节的详细信息
                    for i, local_idx in enumerate(exceeded_local_indices[:5]):
                        global_joint_idx = joint_indices_to_check[local_idx.item()]
                        joint_value = env_hand_joint_pos[local_idx].item()
                        print(
                            f"      手部关节[{local_idx.item()}] (全局索引{global_joint_idx}): {joint_value:.4f} (限制: ±{self.cfg.max_joint_pos})"
                        )

                    if len(exceeded_local_indices) > 5:
                        print(
                            f"      ... 还有 {len(exceeded_local_indices)-5} 个关节超限"
                        )

                # 如果是评估模式，额外显示评估信息
                if hasattr(self, "evaluation_mode") and self.evaluation_mode:
                    if hasattr(self, "target_achieved") and self.target_achieved:
                        print("   ✅ 目标已达到")
                    else:
                        print("   ❌ 目标未达到")

        return terminated, truncated

    def _reset_idx(self, env_ids: Sequence[int] | None):
        if env_ids is None:
            env_ids = list(range(self.num_envs))
        super()._reset_idx(env_ids)

        # 验证关键对象是否有效
        if self.dexh13_hand is None:
            print("❌ _reset_idx: dexh13_hand对象为None，无法执行重置")
            return

        # 确保关节状态变量已初始化
        self._ensure_joint_state_initialized()

        # 重置单手
        joint_pos = self.dexh13_hand.data.default_joint_pos[env_ids]
        joint_pos += sample_uniform(
            self.cfg.initial_joint_pos_range[0],
            self.cfg.initial_joint_pos_range[1],
            (len(env_ids), self.dexh13_hand.num_joints),
            joint_pos.device,
        )
        joint_vel = self.dexh13_hand.data.default_joint_vel[env_ids]
        default_root_state = self.dexh13_hand.data.default_root_state[env_ids]
        default_root_state[:, :3] += self.scene.env_origins[env_ids]

        # 合并单手状态用于奖励计算
        self.joint_pos[env_ids] = joint_pos
        self.joint_vel[env_ids] = joint_vel

        # 应用单手状态到仿真
        self.dexh13_hand.write_root_pose_to_sim(default_root_state[:, :7], env_ids)
        self.dexh13_hand.write_root_velocity_to_sim(default_root_state[:, 7:], env_ids)
        self.dexh13_hand.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)

        # 重置其他物体
        if self.table is not None and hasattr(self.table, "data"):
            table_state = self.table.data.default_root_state[env_ids].clone()
            table_state[:, :3] += self.scene.env_origins[env_ids]
            self.table.write_root_state_to_sim(table_state, env_ids)
        else:
            print("⚠️ table对象无效，跳过table重置")

        if self.object1 is not None and hasattr(self.object1, "data"):
            object1_state = self.object1.data.default_root_state[env_ids].clone()
            object1_state[:, :3] += self.scene.env_origins[env_ids]
            self.object1.write_root_state_to_sim(object1_state, env_ids)
        else:
            print("⚠️ object1对象无效，跳过object1重置")

        # 🔧 观察历史处理：seq2seq架构不需要帧堆叠
        # 但保留观察历史逻辑以防某些代码路径需要
        initial_base_obs, _, _ = self._compute_base_observations()
        for env_idx in env_ids:
            if hasattr(self, "_obs_history") and env_idx in self._obs_history:
                self._obs_history[env_idx].clear()
                obs_to_fill = initial_base_obs[env_idx].clone().detach()
                # 注意：在seq2seq架构中，num_stack应该为1
                for _ in range(max(1, self.num_stack)):  # 至少保留1个观察
                    self._obs_history[env_idx].append(obs_to_fill)

        self.last_actions[env_ids] = 0.0

        if self.trajectory_mode and self.loaded_trajectory is not None:
            self._apply_trajectory_initial_state(env_ids)

        # === 新增：评估模式特殊处理 ===
        if self.evaluation_mode:
            # 重置评估状态
            self.success_hold_counter = 0
            self.episode_success = False
            self.target_achieved = False

            # 初始化/重置成功保持计数器
            if not hasattr(self, "success_hold_counters"):
                self.success_hold_counters = torch.zeros(
                    self.num_envs, dtype=torch.int32, device=self.device
                )
            else:
                self.success_hold_counters[env_ids] = 0

            # 生成随机目标
            if self.cfg.random_target_generation:
                self.generate_random_target(env_ids)

            # 设置随机初始位姿
            if self.cfg.random_initial_pose:
                self.set_random_initial_pose(env_ids)

            # 记录初始状态（用于评估指标计算）
            self.initial_hand_state = self.get_current_hand_state()

            print(f"🎯 评估模式重置完成 - 环境 {env_ids}")
            print(f"   - 随机目标: {self.cfg.random_target_generation}")
            print(f"   - 随机初始位姿: {self.cfg.random_initial_pose}")

    def _compute_base_observations(self) -> torch.Tensor:
        """
        计算目标导向的单手控制观察空间 (34维)

        观察空间组成：
        - current_hand_state: 25维 (16关节 + 9位姿) - 当前手部状态
        - object_state:       9维  (3位置 + 6旋转)   - 环境物体状态
        总计: 25 + 9 = 34维

        根据use_object_centric配置，使用训练时的ObjectCentricTransformer进行坐标系转换
        返回值：
        - base_obs: 34维观察空间
        - target_pos_obj: 3维物体位置
        - target_rot_6d_obj: 6维物体旋转
        """

        # 验证关键对象是否有效
        if self.dexh13_hand is None:
            print("❌ _compute_base_observations: dexh13_hand对象为None")
            # 返回零填充的观察向量
            return torch.zeros((self.num_envs, 34), device=self.device)

        # === 0. 获取物体状态信息（用于坐标系转换） ===

        if self.object1 is not None and hasattr(self.object1, "data"):
            obj1_pos = self.object1.data.root_pos_w  # [num_envs, 3]
            obj1_quat = self.object1.data.root_quat_w  # [num_envs, 4]
            obj1_rot_6d = quat_to_rot6d(obj1_quat)  # [num_envs, 6]
        else:
            print("⚠️ object1对象无效，使用零填充")
            obj1_pos = torch.zeros((self.num_envs, 3), device=self.device)
            obj1_quat = (
                torch.tensor([0.0, 0.0, 0.0, 1.0], device=self.device)
                .unsqueeze(0)
                .expand(self.num_envs, -1)
            )
            obj1_rot_6d = torch.zeros((self.num_envs, 6), device=self.device)

        # === 1. 获取当前手部状态 (25维) ===

        # 获取当前手部关节位置 (16维)
        if (
            hasattr(self, "dexh13_hand_joint_indices")
            and len(self.dexh13_hand_joint_indices) == 16
        ):
            current_joint_pos = self.dexh13_hand.data.joint_pos[
                ..., self.dexh13_hand_joint_indices
            ]
        else:
            print(f"⚠️ {self.hand_type}手关节索引未正确初始化，执行紧急初始化")
            self._initialize_joint_mapping()
            if (
                hasattr(self, "dexh13_hand_joint_indices")
                and len(self.dexh13_hand_joint_indices) == 16
            ):
                current_joint_pos = self.dexh13_hand.data.joint_pos[
                    ..., self.dexh13_hand_joint_indices
                ]
            else:
                print(f"❌ {self.hand_type}手关节索引初始化失败，使用零填充")
                current_joint_pos = torch.zeros((self.num_envs, 16), device=self.device)

        # 获取当前手部位姿 (9维: 3位置 + 6旋转)
        current_pos = self.dexh13_hand.data.root_pos_w  # [num_envs, 3]
        current_quat = self.dexh13_hand.data.root_quat_w  # [num_envs, 4]
        current_rot_6d = quat_to_rot6d(current_quat)  # [num_envs, 6]
        target_pos_world = self.target_hand_position.unsqueeze(0).expand(
            self.num_envs, -1
        )  # [num_envs, 3]
        # 将目标四元数转换为6D旋转表示
        target_quat = self.target_hand_rotation.unsqueeze(0).expand(
            self.num_envs, -1
        )  # [num_envs, 4]
        target_rot_6d = quat_to_rot6d(target_quat)  # [num_envs, 6]
        # === 2. 坐标系转换（使用训练时的ObjectCentricTransformer） === 物体坐标系下
        if self.use_object_centric and self.object_centric_transformer is not None:
            # 使用训练时的转换器，确保与SingleHandDataProcessor完全一致
            # 只转换当前手部位置，目标状态单独通过target_pose提供
            (
                current_pos_obj,
                current_rot_6d_obj,
                target_pos_obj,
                target_rot_6d_obj,
                object_state,
            ) = self._apply_training_coordinate_transform(
                current_pos,
                current_rot_6d,
                target_pos_world,
                target_rot_6d,
                obj1_pos,
                obj1_quat,
                obj1_rot_6d,
            )

            # 组合转换后的手部状态
            current_hand_state = torch.cat(
                [current_joint_pos, current_pos_obj, current_rot_6d_obj], dim=1
            )

        else:
            raise ValueError("无法进行坐标系转换")

        # === 3. 组合最终观察向量 ===

        base_obs = torch.cat([current_hand_state, object_state], dim=1)

        # === 4. 验证观察维度 ===

        expected_dim = 34
        if base_obs.shape[1] != expected_dim:
            print(f"❌ [观察维度错误] 计算{base_obs.shape[1]}维，期望{expected_dim}维")
            print(f"   当前状态: {current_hand_state.shape[1]}维")
            print(f"   物体状态: {object_state.shape[1]}维")
        else:
            # 简化调试输出
            if not hasattr(self, "_obs_debug_count"):
                self._obs_debug_count = 0
            self._obs_debug_count += 1

            if self._obs_debug_count == 1 or self._obs_debug_count % 1000 == 0:
                coord_system = (
                    "训练时物体中心坐标系" if self.use_object_centric else "世界坐标系"
                )
                print(
                    f"✅ [{self.hand_type}手观察|{coord_system}] {base_obs.shape[1]}维 = {current_hand_state.shape[1]}(当前) + {object_state.shape[1]}(物体)"
                )

        return base_obs, target_pos_obj, target_rot_6d_obj

    def _apply_training_coordinate_transform(
        self,
        current_pos: torch.Tensor,  # [num_envs, 3] 当前手部位置
        current_rot_6d: torch.Tensor,  # [num_envs, 6] 当前手部旋转
        target_pos: torch.Tensor,  # [num_envs, 3] 目标手部位置
        target_rot_6d: torch.Tensor,  # [num_envs, 6] 目标手部旋转
        obj_pos: torch.Tensor,  # [num_envs, 3] 物体位置
        obj_quat: torch.Tensor,  # [num_envs, 4] 物体四元数
        obj_rot_6d: torch.Tensor,  # [num_envs, 6] 物体6D旋转
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        使用训练时的ObjectCentricTransformer进行坐标系转换 - 优化版本

        这个方法与SingleHandDataProcessor中的转换逻辑完全一致，
        确保评估时的观察空间与训练时完全相同。

        🚀 性能优化：
        - 减少CPU/GPU转换次数
        - 批量处理tensor操作
        - 优化内存分配模式

        Args:
            current_pos: 当前手部位置（世界坐标系）
            current_rot_6d: 当前手部旋转（6D表示）
            target_pos: 目标手部位置（世界坐标系）
            target_rot_6d: 目标手部旋转（6D表示）
            obj_pos: 物体位置（世界坐标系）
            obj_quat: 物体四元数旋转（世界坐标系）
            obj_rot_6d: 物体6D旋转（世界坐标系）

        Returns:
            current_pos_obj: 当前手部位置（物体坐标系）
            current_rot_6d_obj: 当前手部旋转（6D表示）
            target_pos_obj: 目标手部位置（物体坐标系）
            target_rot_6d_obj: 目标手部旋转（6D表示）
            object_state: 物体状态（物体坐标系下，位置为原点）
        """

        # 🚀 优化：减少CPU/GPU转换，使用批量处理
        # 只在最后必要时转换为numpy，中间过程保持tensor

        # 预分配结果tensor，避免逐个append
        current_pos_obj_list = []
        target_pos_obj_list = []
        current_rot_6d_obj_list = []
        target_rot_6d_obj_list = []

        # 批量转换为numpy（一次性转换，而不是在循环中重复转换）
        obj_pos_np = obj_pos.cpu().numpy()  # [num_envs, 3]
        obj_quat_np = obj_quat.cpu().numpy()  # [num_envs, 4] [qw,qx,qy,qz]
        current_pos_np = current_pos.cpu().numpy()  # [num_envs, 3]
        target_pos_np = target_pos.cpu().numpy()  # [num_envs, 3]
        current_rot_6d_np = current_rot_6d.cpu().numpy()  # [num_envs, 6]
        target_rot_6d_np = target_rot_6d.cpu().numpy()  # [num_envs, 6]

        for env_idx in range(self.num_envs):
            try:
                # 创建ObjectPose对象（单个环境）
                object_pose = ObjectPose(
                    position=obj_pos_np[env_idx],  # [3]
                    orientation=obj_quat_np[env_idx],  # [4] [qw,qx,qy,qz]
                )

                # 设置物体姿态到转换器
                self.object_centric_transformer.set_object_pose(object_pose)

                # 🚀 优化：减少tensor创建和设备转换
                # 转换当前手部位置到物体坐标系
                # 注意：ObjectCentricTransformer需要四元数输入和输出
                # 先将6D旋转转换为四元数：6D旋转 → 旋转矩阵 → 四元数
                current_rot_6d_torch = torch.tensor(
                    current_rot_6d_np[env_idx], device=self.device
                ).unsqueeze(0)
                current_rot_mat = rot6d_to_rot_mat(current_rot_6d_torch)  # [1, 3, 3]
                current_quat_torch = quat_from_matrix(current_rot_mat)[0]  # [4]
                current_quat_np = current_quat_torch.cpu().numpy()  # [4] 四元数

                current_pos_obj_env, current_quat_obj_env = (
                    self.object_centric_transformer.world_to_object_frame(
                        current_pos_np[env_idx],  # [3]
                        current_quat_np,  # [4] 四元数
                        object_pose,
                    )
                )
                # 🚀 优化：减少不必要的tensor创建和GPU转换
                # 将四元数转换为6D旋转 - 复用tensor而不是每次创建新的
                current_quat_obj_torch = torch.tensor(
                    current_quat_obj_env, device=self.device
                ).unsqueeze(0)
                current_rot_6d_obj_env = (
                    quat_to_rot6d(current_quat_obj_torch)[0].cpu().numpy()
                )

                current_pos_obj_list.append(current_pos_obj_env)
                current_rot_6d_obj_list.append(current_rot_6d_obj_env)

                # 🚀 优化：转换目标手部位置到物体坐标系
                # 先将6D旋转转换为四元数：6D旋转 → 旋转矩阵 → 四元数
                target_rot_6d_torch = torch.tensor(
                    target_rot_6d_np[env_idx], device=self.device
                ).unsqueeze(0)
                target_rot_mat = rot6d_to_rot_mat(target_rot_6d_torch)  # [1, 3, 3]
                target_quat_torch = quat_from_matrix(target_rot_mat)[0]  # [4]
                target_quat_np = target_quat_torch.cpu().numpy()  # [4] 四元数

                target_pos_obj_env, target_quat_obj_env = (
                    self.object_centric_transformer.world_to_object_frame(
                        target_pos_np[env_idx],  # [3]
                        target_quat_np,  # [4] 四元数
                        object_pose,
                    )
                )
                # 🚀 优化：减少tensor创建次数
                # 将四元数转换为6D旋转
                target_quat_obj_torch = torch.tensor(
                    target_quat_obj_env, device=self.device
                ).unsqueeze(0)
                target_rot_6d_obj_env = (
                    quat_to_rot6d(target_quat_obj_torch)[0].cpu().numpy()
                )

                target_pos_obj_list.append(target_pos_obj_env)
                target_rot_6d_obj_list.append(target_rot_6d_obj_env)

            except Exception as e:
                print(f"⚠️ 环境{env_idx}坐标转换失败: {e}")
                raise e

        # 🚀 优化：批量转换回torch张量，减少设备转换次数
        # 使用from_numpy + to() 比 tensor() 更高效
        current_pos_obj = torch.from_numpy(np.array(current_pos_obj_list)).to(
            dtype=torch.float32, device=self.device
        )  # [num_envs, 3]

        target_pos_obj = torch.from_numpy(np.array(target_pos_obj_list)).to(
            dtype=torch.float32, device=self.device
        )  # [num_envs, 3]

        current_rot_6d_obj = torch.from_numpy(np.array(current_rot_6d_obj_list)).to(
            dtype=torch.float32, device=self.device
        )  # [num_envs, 6]

        target_rot_6d_obj = torch.from_numpy(np.array(target_rot_6d_obj_list)).to(
            dtype=torch.float32, device=self.device
        )  # [num_envs, 6]

        # 物体状态在物体坐标系下：位置为原点，旋转为单位旋转
        # 这与SingleHandDataProcessor中的处理完全一致
        # 创建单位旋转6D表示：[1,0,0,1,0,0] 对应单位矩阵
        unit_rotation_6d = torch.zeros_like(obj_rot_6d)
        unit_rotation_6d[:, 0] = 1.0  # r11 = 1
        unit_rotation_6d[:, 3] = 1.0  # r22 = 1
        # 其他分量保持0：r12=0, r21=0, r31=0, r32=0

        object_state = torch.cat(
            [
                torch.zeros_like(obj_pos),  # 物体位置 = [0,0,0]（物体坐标系原点）
                unit_rotation_6d,  # 物体旋转为单位旋转（与训练时一致）
            ],
            dim=1,
        )  # [num_envs, 9]

        return (
            current_pos_obj,
            current_rot_6d_obj,
            target_pos_obj,
            target_rot_6d_obj,
            object_state,
        )

    def _validate_coordinate_system_consistency(self):
        """
        验证坐标系转换的一致性

        确保评估环境的坐标系转换与训练时的SingleHandDataProcessor保持一致
        """
        print("🔍 验证坐标系转换一致性...")
        print(
            f"   坐标系模式: {'训练时物体中心坐标系' if self.use_object_centric else '世界坐标系'}"
        )

        if self.use_object_centric and self.object_centric_transformer is not None:
            print("   ✅ 物体中心坐标系已启用，与训练时完全一致")
            print("   - 使用相同的ObjectCentricTransformer")
            print("   - 当前手部位置: 相对于物体坐标系")
            print("   - 目标手部位置: 相对于物体坐标系")
            print("   - 物体位置: 原点[0,0,0]")
            print("   - 动作指令: 相对于物体坐标系")
        else:
            print("   ⚠️  世界坐标系模式，与训练时可能不一致")
            print("   - 当前手部位置: 世界坐标系绝对位置")
            print("   - 目标手部位置: 世界坐标系绝对位置")
            print("   - 物体位置: 世界坐标系实际位置")
            print("   - 可能导致评估性能下降")

        # 进行简单的数值验证
        if (
            self.object1 is not None
            and hasattr(self.object1, "data")
            and self.use_object_centric
            and self.object_centric_transformer is not None
        ):
            # 测试用的世界坐标系位置
            test_world_pos = np.array([0.1, 0.0, 0.0])  # 相对物体的偏移

            try:
                # 获取物体姿态
                obj_pos = self.object1.data.root_pos_w[0].cpu().numpy()  # 第一个环境
                obj_quat = self.object1.data.root_quat_w[0].cpu().numpy()  # 第一个环境

                # 创建物体姿态
                object_pose = ObjectPose(position=obj_pos, orientation=obj_quat)
                self.object_centric_transformer.set_object_pose(object_pose)

                # 测试坐标系转换
                test_world_absolute = obj_pos + test_world_pos  # 世界坐标系绝对位置
                test_obj_pos, _ = self.object_centric_transformer.world_to_object_frame(
                    test_world_absolute
                )

                # 逆转换验证
                recovered_world_pos, _ = (
                    self.object_centric_transformer.object_to_world_frame(test_obj_pos)
                )

                print("   🧪 坐标系转换数值验证:")
                print(f"      世界偏移: {test_world_pos}")
                print(f"      物体坐标: {test_obj_pos}")
                print(
                    f"      转换误差: {np.linalg.norm(test_world_absolute - recovered_world_pos):.6f}"
                )

                # 验证转换是否符合预期（偏移应该转换正确）
                expected_obj_pos = test_world_pos  # 在简单情况下应该相等
                if np.allclose(test_obj_pos, expected_obj_pos, atol=1e-3):
                    print("      ✅ 转换结果正确")
                else:
                    print("      ⚠️ 转换结果存在差异，但这可能由于物体旋转导致")

            except Exception as e:
                print(f"   ⚠️ 数值验证失败: {e}")

        return True

    def set_target_hand_state(
        self,
        target_joints: list = None,
        target_position: list = None,
        target_rotation: list = None,
    ):
        """
        设置目标手部状态

        Args:
            target_joints: 目标关节角度 (16维)
            target_position: 目标位置 (3维)
            target_rotation: 目标旋转四元数 (4维)
        """
        if target_joints is not None:
            self.target_hand_joints = torch.tensor(
                target_joints, device=self.device, dtype=torch.float32
            )
            print(f"🎯 更新目标关节状态: {len(target_joints)}维")

        if target_position is not None:
            self.target_hand_position = torch.tensor(
                target_position, device=self.device, dtype=torch.float32
            )
            print(f"🎯 更新目标位置: {target_position}")

        if target_rotation is not None:
            self.target_hand_rotation = torch.tensor(
                target_rotation, device=self.device, dtype=torch.float32
            )
            print(f"🎯 更新目标旋转: {target_rotation}")

        # 自动更新目标手可视化
        self._update_target_hand_visualization()

    def get_current_hand_state(self):
        """
        获取当前手部状态，用于轨迹片段提取

        Returns:
            dict: 包含当前关节角度和位姿的字典
        """
        # 验证关键对象是否有效
        if self.dexh13_hand is None:
            print("❌ get_current_hand_state: dexh13_hand对象为None")
            # 返回默认状态
            return {
                "joints": [0.0] * 16,
                "position": [0.0, 0.0, 1.0],
                "rotation": [0.0, 0.0, 0.0, 1.0],
            }

        # 获取当前关节位置
        if hasattr(self, "dexh13_hand_joint_indices"):
            current_joints = self.dexh13_hand.data.joint_pos[
                0, self.dexh13_hand_joint_indices
            ]
        else:
            current_joints = torch.zeros(16, device=self.device)

        # 获取当前位姿单手训练模式已启用
        current_pos = self.dexh13_hand.data.root_pos_w[0]
        current_quat = self.dexh13_hand.data.root_quat_w[0]

        return {
            "joints": current_joints.cpu().numpy().tolist(),
            "position": current_pos.cpu().numpy().tolist(),
            "rotation": current_quat.cpu().numpy().tolist(),
        }

    # === 新增：评估相关方法 ===

    def generate_random_target(self, env_ids: Sequence[int] = None):
        """
        生成随机目标状态

        Args:
            env_ids: 要设置目标的环境ID列表，None表示所有环境
        """
        if env_ids is None:
            env_ids = list(range(self.num_envs))

        if not self.cfg.random_target_generation:
            return

        print(f"🎯 为{len(env_ids)}个环境生成随机目标...")

        # 生成随机关节目标
        random_joints = (
            sample_uniform(
                self.cfg.target_joint_random_range[0],
                self.cfg.target_joint_random_range[1],
                (16,),
                self.device,
            )
            .cpu()
            .numpy()
            .tolist()
        )

        # 生成随机位置目标
        pos_ranges = self.cfg.target_position_random_range
        random_position = [
            np.random.uniform(pos_ranges[0][0], pos_ranges[0][1]),  # x
            np.random.uniform(pos_ranges[1][0], pos_ranges[1][1]),  # y
            np.random.uniform(pos_ranges[2][0], pos_ranges[2][1]),  # z
        ]

        # 生成随机旋转目标（暂时使用默认旋转）
        random_rotation = [0.0, 0.0, 0.0, 1.0]

        # 设置目标状态
        self.set_target_hand_state(
            target_joints=random_joints,
            target_position=random_position,
            target_rotation=random_rotation,
        )

        # 更新目标手可视化
        self._update_target_hand_visualization()

        print("✅ 随机目标生成完成")
        print(f"   关节目标范围: {self.cfg.target_joint_random_range}")
        print(f"   位置目标: {random_position}")

    def set_random_initial_pose(self, env_ids: Sequence[int] = None):
        """
        设置随机初始位姿

        Args:
            env_ids: 要设置初始位姿的环境ID列表
        """
        if env_ids is None:
            env_ids = list(range(self.num_envs))

        if not self.cfg.random_initial_pose:
            return

        print(f"🎲 为{len(env_ids)}个环境设置随机初始位姿...")

        # 生成随机关节位置
        joint_pos = self.dexh13_hand.data.default_joint_pos[env_ids].clone()
        random_joint_offset = sample_uniform(
            self.cfg.target_joint_random_range[0] * 0.5,  # 初始位姿的随机范围小一些
            self.cfg.target_joint_random_range[1] * 0.5,
            (len(env_ids), self.dexh13_hand.num_joints),
            joint_pos.device,
        )
        joint_pos += random_joint_offset

        # 生成随机根位置
        root_state = self.dexh13_hand.data.default_root_state[env_ids].clone()
        pos_ranges = self.cfg.target_position_random_range
        for i, env_id in enumerate(env_ids):
            random_pos_offset = torch.tensor(
                [
                    np.random.uniform(-0.1, 0.1),  # x轻微偏移
                    np.random.uniform(-0.1, 0.1),  # y轻微偏移
                    np.random.uniform(-0.05, 0.05),  # z轻微偏移
                ],
                device=self.device,
            )
            root_state[i, :3] += random_pos_offset

        # 应用随机初始状态
        joint_vel = self.dexh13_hand.data.default_joint_vel[env_ids]

        # 添加环境origin偏移
        root_state[:, :3] += self.scene.env_origins[env_ids]

        self.dexh13_hand.write_root_pose_to_sim(root_state[:, :7], env_ids)
        self.dexh13_hand.write_root_velocity_to_sim(root_state[:, 7:], env_ids)
        self.dexh13_hand.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)

        print("✅ 随机初始位姿设置完成")

    def evaluate_target_achievement(self) -> torch.Tensor:
        """
        评估目标达到情况

        Returns:
            success_mask: [num_envs] 布尔张量，表示每个环境是否达到目标
        """
        # 验证关键对象是否有效
        if self.dexh13_hand is None:
            print("❌ evaluate_target_achievement: dexh13_hand对象为None")
            # 返回失败结果
            target_achieved = torch.zeros(
                self.num_envs, dtype=torch.bool, device=self.device
            )
            joint_distances = torch.ones(self.num_envs, device=self.device) * 999.0
            position_distances = torch.ones(self.num_envs, device=self.device) * 999.0
            return target_achieved, joint_distances, position_distances

        # 获取当前手部状态
        current_joints = (
            self.dexh13_hand.data.joint_pos[..., self.dexh13_hand_joint_indices]
            if hasattr(self, "dexh13_hand_joint_indices")
            else torch.zeros((self.num_envs, 16), device=self.device)
        )
        current_pos = self.dexh13_hand.data.root_pos_w

        # 获取目标状态
        target_joints = self.target_hand_joints.unsqueeze(0).expand(self.num_envs, -1)
        target_pos = self.target_hand_position.unsqueeze(0).expand(self.num_envs, -1)

        # 计算关节距离
        joint_distances = torch.norm(current_joints - target_joints, dim=1)
        joint_achieved = joint_distances < self.cfg.joint_tolerance

        # 计算位置距离
        position_distances = torch.norm(current_pos - target_pos, dim=1)
        position_achieved = position_distances < self.cfg.position_tolerance

        # 目标达到 = 关节达到 AND 位置达到
        target_achieved = joint_achieved & position_achieved

        return target_achieved, joint_distances, position_distances

    def compute_target_oriented_reward(self) -> torch.Tensor:
        """
        计算目标导向的奖励

        Returns:
            rewards: [num_envs] 奖励张量
        """
        # 评估目标达到情况
        target_achieved, joint_distances, position_distances = (
            self.evaluate_target_achievement()
        )

        # 基础奖励计算
        # 1. 位置奖励（距离越近奖励越高）
        max_pos_distance = 1.0  # 最大可能的位置距离
        position_reward = (
            self.cfg.reward_position_weight
            * (max_pos_distance - position_distances)
            / max_pos_distance
        )

        # 2. 关节奖励（角度差越小奖励越高）
        max_joint_distance = np.pi  # 最大可能的关节角度差
        joint_reward = (
            self.cfg.reward_joint_weight
            * (max_joint_distance - joint_distances)
            / max_joint_distance
        )

        # 3. 基础奖励组合
        base_reward = position_reward + joint_reward + self.cfg.reward_penalty

        # 4. 成功奖励
        success_reward = torch.zeros_like(base_reward)

        # 更新成功保持计数器
        for env_idx in range(self.num_envs):
            if target_achieved[env_idx]:
                self.success_hold_counter += 1
                if self.success_hold_counter >= self.cfg.success_hold_time:
                    success_reward[env_idx] = self.cfg.reward_success
                    self.episode_success = True
            else:
                self.success_hold_counter = 0

        total_reward = base_reward + success_reward

        return total_reward

    def get_evaluation_info(self) -> Dict:
        """
        获取评估信息

        Returns:
            info: 包含评估指标的字典
        """
        target_achieved, joint_distances, position_distances = (
            self.evaluate_target_achievement()
        )

        # 计算当前手部状态
        current_state = self.get_current_hand_state()

        # 构建完整的25维目标状态：16关节 + 3位置 + 6旋转
        target_rotation_6d = quat_to_rot6d(
            self.target_hand_rotation.unsqueeze(0)
        ).squeeze(0)
        target_full_state_25d = torch.cat(
            [
                self.target_hand_joints,  # [16] 关节角度
                self.target_hand_position,  # [3] 位置
                target_rotation_6d,  # [6] 6D旋转
            ],
            dim=0,
        )  # [25] 完整目标状态

        info = {
            "target_achieved": target_achieved.cpu().numpy().tolist(),
            "joint_distances": joint_distances.cpu().numpy().tolist(),
            "position_distances": position_distances.cpu().numpy().tolist(),
            "success_hold_counter": self.success_hold_counter,
            "episode_success": self.episode_success,
            "current_hand_state": current_state,
            "target_hand_joints": self.target_hand_joints.cpu().numpy().tolist(),
            "target_hand_position": self.target_hand_position.cpu().numpy().tolist(),
            "target_hand_rotation": self.target_hand_rotation.cpu().numpy().tolist(),
            "target_full_state_25d": target_full_state_25d.cpu()
            .numpy()
            .tolist(),  # 新增：完整25维目标状态
        }

        return info

    def _setup_target_hand(self):
        """设置目标可视化手对象 - 使用Isaac Lab官方visual_material方法"""
        try:
            print(f"🎯 正在创建{self.hand_type}手目标可视化对象...")

            # 根据手部类型选择配置
            if self.hand_type == "right":
                base_hand_cfg = self.cfg.dexh13_right_cfg
                # 右手关节配置
                joint_config = {
                    "mzcb": 0.00,
                    "mzjdxz": 0.00,
                    "mzzd": 0.00,
                    "mzydxz": 0.00,  # 拇指
                    "szcb": 0.0,
                    "szjdxz": 0.0,
                    "szzdxz": 0.0,
                    "szydxz": 0.0,  # 食指
                    "zzcb": 0.0,
                    "zzjdxz": 0.0,
                    "zzzdxz": 0.0,
                    "zzydxz": 0.0,  # 中指
                    "wmzcb": 0.0,
                    "wmzjdxz": 0.0,
                    "wmzzdxz": 0.0,
                    "wmzydxz": 0.0,  # 无名指
                }
            else:
                base_hand_cfg = self.cfg.dexh13_left_cfg
                # 左手关节配置
                joint_config = {
                    "mzcb": 0.00,
                    "mzjdxz": 0.00,
                    "mzzdxz": 0.00,
                    "mzyd": 0.00,  # 拇指
                    "szcb": 0.0,
                    "szjdxz": 0.0,
                    "szzdxz": 0.0,
                    "szydxz": 0.0,  # 食指
                    "zzcb": 0.0,
                    "zzjdxz": 0.0,
                    "zzzdxz": 0.0,
                    "zzydxz": 0.0,  # 中指
                    "wmzcb": 0.0,
                    "wmzjdxz": 0.0,
                    "wmzzdxz": 0.0,
                    "wmzydxz": 0.0,  # 无名指
                }

            # 创建目标手的visual_material配置 - 使用Isaac Lab官方方法
            target_visual_material = sim_utils.PreviewSurfaceCfg(
                diffuse_color=tuple(self.cfg.target_hand_color),  # RGB颜色
                metallic=0.2,  # 金属感
                roughness=0.6,  # 粗糙度
                opacity=1.0 - self.cfg.target_hand_transparency,  # 不透明度
                emissive_color=(0.0, 0.0, 0.0),  # 发光颜色
            )

            # 创建spawn配置，继承原配置但添加visual_material
            target_spawn_cfg = sim_utils.UsdFileCfg(
                usd_path=base_hand_cfg.spawn.usd_path,
                activate_contact_sensors=False,  # 目标手不需要碰撞检测
                visual_material=target_visual_material,  # 设置目标手颜色
                mass_props=base_hand_cfg.spawn.mass_props,
                rigid_props=base_hand_cfg.spawn.rigid_props,
                articulation_props=base_hand_cfg.spawn.articulation_props,
                # 🎨 纯视觉效果配置 - 禁用碰撞检测
                collision_props=sim_utils.CollisionPropertiesCfg(
                    collision_enabled=False,  # 完全禁用碰撞
                    contact_offset=0.0,  # 设置接触偏移为0
                    rest_offset=0.0,  # 设置静止偏移为0
                ),
            )

            # 配置目标手
            target_hand_cfg = ArticulationCfg(
                prim_path="/World/envs/env_.*/target_hand",  # 为所有环境创建目标手
                spawn=target_spawn_cfg,
                init_state=ArticulationCfg.InitialStateCfg(
                    pos=(
                        self.cfg.target_hand_offset[0],
                        self.cfg.target_hand_offset[1],
                        self.cfg.target_hand_offset[2],
                    ),
                    rot=(0.0, 0.0, 0.0, 1.0),  # 默认旋转
                    joint_pos=joint_config,  # 使用预定义的关节配置
                    joint_vel={".*": 0.0},  # 所有关节初始速度为0
                ),
                actuators={},  # 目标手不需要执行器
            )

            # 创建目标手对象
            print(f"      📍 创建目标手对象...")
            print(f"         - 手部类型: {self.hand_type}")
            print(f"         - USD路径: {target_spawn_cfg.usd_path}")
            print(f"         - 初始位置: {target_hand_cfg.init_state.pos}")
            print(f"         - 目标颜色: RGB{tuple(self.cfg.target_hand_color)}")
            print(f"         - 透明度: {self.cfg.target_hand_transparency}")

            self.target_hand = Articulation(target_hand_cfg)
            self.scene.articulations["target_hand"] = self.target_hand

            print("      ✅ 目标可视化手创建成功")
            print(f"         - 使用Isaac Lab官方visual_material方法")
            print(f"         - 颜色已应用: RGB{tuple(self.cfg.target_hand_color)}")

        except Exception as e:
            print(f"      ❌ 目标手创建失败: {e}")
            import traceback

            traceback.print_exc()
            self.target_hand = None

    def _update_target_hand_visualization(self):
        """更新目标手的可视化位置 - 参考vis_hdf5.py的机器人状态更新方式"""
        if not self.enable_target_visualization or self.target_hand is None:
            return

        try:
            # 参考vis_hdf5.py中的位姿更新方式
            # 构建7维位姿数据 [x,y,z,qw,qx,qy,qz] - 参考vis_hdf5.py的hand_real_pos构建
            target_position = self.target_hand_position.cpu().numpy()
            target_rotation = self.target_hand_rotation.cpu().numpy()  # [qw,qx,qy,qz]

            # 组合成7维位姿
            target_pose_7d = np.concatenate([target_position, target_rotation])

            # 转换为tensor - 参考vis_hdf5.py的tensor创建方式
            target_pose_tensor = torch.tensor(
                np.array([target_pose_7d]),
                dtype=torch.float32,
                device=self.target_hand.device,
            )

            # 为所有环境设置相同的目标位置 - 参考vis_hdf5.py的多环境处理
            if self.num_envs > 1:
                target_pose_tensor = target_pose_tensor.repeat(self.num_envs, 1)

            # 更新目标手的根部位姿 - 参考vis_hdf5.py的write_root_pose_to_sim调用
            self.target_hand.write_root_pose_to_sim(root_pose=target_pose_tensor)

            # 参考vis_hdf5.py中的关节角度更新方式
            target_joints_array = self.target_hand_joints.cpu().numpy()

            # 参考vis_hdf5.py中的关节重排列方式：new_finger_angles = np.array(finger_angles).reshape(4, 4).T
            if len(target_joints_array) == 16:
                # 重新排列关节顺序以匹配DexH13的期望格式
                reshaped_joints = target_joints_array.reshape(4, 4).T
                trans_joints = (
                    np.concatenate(
                        [
                            reshaped_joints[:, 3:],  # 最后一个手指移到前面
                            reshaped_joints[:, :3],  # 前三个手指移到后面
                        ],
                        axis=1,
                    )
                    .reshape(16)
                    .tolist()
                )
            else:
                trans_joints = target_joints_array.tolist()

            # 转换为tensor - 参考vis_hdf5.py的tensor创建
            target_joints_tensor = torch.tensor(
                trans_joints, dtype=torch.float32, device=self.target_hand.device
            )

            # 为所有环境设置相同的目标关节角度
            if self.num_envs > 1:
                target_joints_tensor = target_joints_tensor.unsqueeze(0).repeat(
                    self.num_envs, 1
                )

            # 更新目标手的关节位置 - 参考vis_hdf5.py的set_joint_position_target调用
            self.target_hand.set_joint_position_target(target_joints_tensor)

        except Exception as e:
            print(f"      ⚠️ 目标手可视化更新失败: {e}")

    def _convert_6d_to_quaternion(self, rot_6d):
        """复用现有的6D旋转转换逻辑"""
        # 确保输入在正确设备上
        rot_6d = rot_6d.to(self.device, dtype=torch.float32)

        # 构建旋转矩阵
        rot_matrix = torch.zeros(3, 3, device=self.device, dtype=torch.float32)
        rot_matrix[:, 0] = rot_6d[0:3]  # 第一列
        rot_matrix[:, 1] = rot_6d[3:6]  # 第二列
        rot_matrix[:, 2] = torch.linalg.cross(rot_matrix[:, 0], rot_matrix[:, 1])

        # 正交化
        rot_matrix[:, 2] = rot_matrix[:, 2] / torch.norm(rot_matrix[:, 2])
        rot_matrix[:, 0] = rot_matrix[:, 0] / torch.norm(rot_matrix[:, 0])
        rot_matrix[:, 1] = torch.linalg.cross(rot_matrix[:, 2], rot_matrix[:, 0])

        # 转换为四元数
        quat = quat_from_matrix(rot_matrix)
        return quat

    def _apply_hand_state_to_env(self, joints, position, rot_6d, env_ids):
        """将手部状态应用到环境"""
        # 确保所有数据都在正确的设备上
        joints = joints.to(self.device, dtype=torch.float32)
        position = position.to(self.device, dtype=torch.float32)
        rot_6d = rot_6d.to(self.device, dtype=torch.float32)

        # 设置关节角度
        joint_pos = self.dexh13_hand.data.default_joint_pos[env_ids].clone()
        joint_pos[:, self.dexh13_hand_joint_indices] = joints

        # 设置手部位姿
        quat = self._convert_6d_to_quaternion(rot_6d)
        root_pose = torch.cat([position, quat]).unsqueeze(0)  # [1, 7]

        # 应用到仿真
        print("应用轨迹数据到手部状态到仿真")
        joint_vel = self.dexh13_hand.data.default_joint_vel[env_ids]
        self.dexh13_hand.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)
        self.dexh13_hand.write_root_pose_to_sim(root_pose, env_ids)

    def _apply_trajectory_initial_state(self, env_ids):
        """应用轨迹的初始状态"""
        if self.loaded_trajectory is None:
            return

        print(f"🎬 轨迹初始化: 设置第0步状态到环境 {env_ids}")

        # A. 设置策略手初始状态（轨迹第0步）
        # 直接从轨迹actions第0步获取初始手部状态更准确
        initial_action = self.loaded_trajectory["actions"][0, 0]  # [25]

        # 确保数据在正确的设备上
        if hasattr(initial_action, "to"):
            initial_action = initial_action.to(self.device, dtype=torch.float32)
        else:
            initial_action = torch.tensor(
                initial_action, device=self.device, dtype=torch.float32
            )

        initial_joints = initial_action[:16]  # 关节角度
        initial_pos = initial_action[16:19]  # 世界坐标位置
        initial_rot_6d = initial_action[19:25]  # 6D旋转

        # 应用到策略控制手
        self._apply_hand_state_to_env(
            initial_joints, initial_pos, initial_rot_6d, env_ids
        )

        # B. 设置目标手可视化（轨迹最后一步）
        final_target = self.loaded_trajectory["actions"][0, -1]  # 最后一步[25]

        # 确保数据在正确的设备上（与initial_action处理一致）
        if hasattr(final_target, "to"):
            final_target = final_target.to(self.device, dtype=torch.float32)
        else:
            final_target = torch.tensor(
                final_target, device=self.device, dtype=torch.float32
            )

        final_joints = final_target[:16]
        final_pos = final_target[16:19]
        final_rot_6d = final_target[19:25]

        # 转换6D旋转为四元数（复用现有逻辑）
        final_quat = self._convert_6d_to_quaternion(final_rot_6d)

        # 设置目标手状态
        self.target_hand_joints = final_joints.to(self.device, dtype=torch.float32)
        self.target_hand_position = final_pos.to(self.device, dtype=torch.float32)
        self.target_hand_rotation = final_quat.to(self.device, dtype=torch.float32)

        # 更新目标手可视化
        self._update_target_hand_visualization()

        # 重置轨迹索引
        self.trajectory_step_index = 0
        print(f"🎬 轨迹初始化完成: 序列长度 {self.seq_length}")

    def _apply_trajectory_step_data(self):
        """应用轨迹步数据（每step调用）"""
        if self.trajectory_step_index >= self.seq_length:
            return  # 轨迹播放完毕

        # 目标手在初始化后保持不变，只需要递增索引
        self.trajectory_step_index += 1

        if self.trajectory_step_index % 5 == 0:  # 每5步打印一次进度
            print(f"🎬 轨迹播放进度: {self.trajectory_step_index}/{self.seq_length}")

    def _compute_tracking_accuracy_reward(self):
        """计算跟踪精度奖励（轨迹模式专用）"""
        # 简单实现：基于目标达到情况给奖励
        if hasattr(self, "evaluate_target_achievement"):
            target_achieved, joint_distances, position_distances = (
                self.evaluate_target_achievement()
            )

            # 基于距离给予奖励
            pos_reward = 1.0 / (1.0 + position_distances)  # 距离越近奖励越高
            joint_reward = 1.0 / (1.0 + joint_distances)  # 关节差越小奖励越高

            return pos_reward + joint_reward
        else:
            return torch.zeros(self.num_envs, device=self.device)


@torch.jit.script
def compute_rewards(
    rew_scale_alive: float,
    rew_scale_terminated: float,
    rew_scale_joint_pos: float,
    rew_scale_joint_vel: float,
    joint_pos: torch.Tensor,
    joint_vel: torch.Tensor,
    reset_terminated: torch.Tensor,
):
    rew_alive = rew_scale_alive * (1.0 - reset_terminated.float())
    rew_termination = rew_scale_terminated * reset_terminated.float()
    rew_joint_pos = rew_scale_joint_pos * torch.sum(torch.square(joint_pos), dim=-1)
    rew_joint_vel = rew_scale_joint_vel * torch.sum(torch.abs(joint_vel), dim=-1)
    total_reward = rew_alive + rew_termination + rew_joint_pos + rew_joint_vel
    return total_reward


# 注册已移动到 env/env_registry.py 统一管理
