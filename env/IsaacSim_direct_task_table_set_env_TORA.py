"""Tora机器人的DirectRLEnv实现"""

import gymnasium as gym
import torch
from collections import deque
import numpy as np

# from tlpy.drivers.isaac_tora_driver import Isaac<PERSON>oraDriver
from isaaclab.envs import DirectRLEnv, DirectRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim import SimulationCfg
from isaaclab.sim.spawners.from_files import GroundPlaneCfg, spawn_ground_plane
from isaaclab.utils import configclass
from isaaclab.utils.math import sample_uniform
from px_janus_learnsim.robot.tora.tora import TORA_CFG
import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation, ArticulationCfg
from typing import Sequence
from isaaclab.assets import ArticulationCfg, AssetBaseCfg
from px_janus_learnsim.config.paths import ROBOT_MODELS_PATH, ASSET_PATH
from isaaclab.assets import (
    Articulation,
    ArticulationCfg,
    AssetBaseCfg,
    RigidObject,
    RigidObjectCfg,
)
from isaaclab.managers import SceneEntityCfg
from px_janus_learnsim.utils.math import quat_to_rot6d
from px_janus_learnsim.robot.controllers.pinocchio_wholecontrol import (
    Px_wholebody_solver,
)

# from tlpy.interfaces.robot import ToraJoints
from dataclasses import MISSING


@configclass
class ToraDirectEnvCfg(DirectRLEnvCfg):
    # env
    num_frames_to_stack: int = 1  # 默认值，实际使用时会被Hydra配置覆盖
    base_observation_space: int = 110
    decimation = 2
    episode_length_s = 5.0
    action_scale = 1.0  # [N]
    action_space = 50
    observation_space: int = (
        MISSING  # base_observation_space * num_frames_to_stack，会在运行时重新计算
    )
    # --- 假设的扁平化观察空间 (Total Dimension = 110) ---

    # 机器人本体感受信息 (Robot Proprioception - 50 dims)
    # ---------------------------------------------------
    # left_hand_joints:       16,  # 左手 16 个关节角度
    # left_hand_pose:          9,  # 左手 9 维位姿 (3位置 + 6旋转)
    # right_hand_joints:      16,  # 右手 16 个关节角度
    # right_hand_pose:         9,  # 右手 9 维位姿 (3位置 + 6旋转)

    # 物体状态信息 (Object States - 60 dims, for Max 5 Objects)
    # --------------------------------------------------------
    # 选择最多追踪 5 个物体是基于以下考虑：
    # 1. 人类认知限制: 研究表明人类的多目标追踪 (Mutil object tracking, M,O,T) 能力上限通常在 3-5 个物体。
    #    虽然 Subitizing (快速估数) 的上限更低 (约 1-4)，但 [M,O,T] 更贴近于持续关注和理解场景中多个动态元素的状态。
    #    设定上限为 5 是对这种有限认知资源的一种借鉴，避免信息过载。
    # 2. 任务相关性: 在典型的机器人操作任务中，通常只有少数几个物体是与当前交互直接相关的。
    # 3. 计算效率与模型复杂性: 为每个可能的物体分配状态维度会使观察空间变得非常大且稀疏。
    #    限制物体数量有助于控制模型输入的维度，使其更易于学习和处理。
    # 4. 固定输入要求: 强化学习算法和神经网络通常需要固定大小的输入向量。
    #    设置一个合理的上限（如 5）并结合填充/掩码机制，是在满足固定输入要求的同时，处理可变数量物体的常用方法。
    #
    # object_1_state:         12,  # 物体1: 3位置 + 6旋转 + 3尺寸
    # object_2_state:         12,  # 物体2: 3位置 + 6旋转 + 3尺寸
    # object_3_state:         12,  # 物体3: (如果不存在则填充0)
    # object_4_state:         12,  # 物体4: (如果不存在则填充0)
    # object_5_state:         12,  # 物体5: (如果不存在则填充0)

    # --- 总计: 50 + 60 = 110 维 ---
    state_space = 0

    # simulation
    sim: SimulationCfg = SimulationCfg(dt=1 / 120, render_interval=decimation)

    # robot
    robot_cfg: ArticulationCfg = TORA_CFG.replace(prim_path="/World/envs/env_.*/Robot")

    # scene
    scene: InteractiveSceneCfg = InteractiveSceneCfg(
        num_envs=1, env_spacing=4.0, replicate_physics=True
    )

    table: RigidObjectCfg = RigidObjectCfg(
        prim_path="/World/envs/env_.*/table",
        spawn=sim_utils.UsdFileCfg(
            usd_path=f"{ASSET_PATH}/table_white.usd",
            scale=[0.1, 0.15, 0.1],  # 改为Python列表
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                kinematic_enabled=True,
                disable_gravity=True,
            ),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(
            pos=(0.0, -0.4, 0.0), rot=(0.7071, 0.7071, 0.0, 0.0)
        ),
    )

    object1: RigidObjectCfg = RigidObjectCfg(
        prim_path="/World/envs/env_.*/object1",
        spawn=sim_utils.UsdFileCfg(
            usd_path=f"{ASSET_PATH}/ImageToStl.com_obj_000030.usd",
            scale=[0.001, 0.001, 0.001],  # 改为Python列表
            rigid_props=sim_utils.RigidBodyPropertiesCfg(
                kinematic_enabled=False,
                disable_gravity=False,
                enable_gyroscopic_forces=True,
            ),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(
            pos=(-0.2, -0.25, 1.0343), rot=(0.707, 0.707, 0.0, 0.0)
        ),
    )

    # reset
    max_joint_pos = 3.0
    initial_joint_pos_range = [-0.25, 0.25]

    # reward scales
    rew_scale_alive = 1.0
    rew_scale_terminated = -2.0
    rew_scale_joint_pos = -1.0
    rew_scale_joint_vel = -0.01


class ToraDirectEnv(DirectRLEnv):
    cfg: ToraDirectEnvCfg

    def __init__(self, cfg: ToraDirectEnvCfg, render_mode: str | None = None, **kwargs):
        super().__init__(cfg, render_mode, **kwargs)

        self.action_scale = self.cfg.action_scale
        if hasattr(self, "tora"):
            self.joint_pos = self.tora.data.joint_pos
            self.joint_vel = self.tora.data.joint_vel
            # self.sim_tora_driver = IsaacToraDriver(self.tora.data.joint_names)
            self.robot_entity_cfg = SceneEntityCfg(
                "tora",
                joint_names=list(self.tora.data.joint_names),
                body_names=list(self.tora.data.body_names),
            )
            self.robot_entity_cfg.resolve(self.scene)
        else:
            print("[Warning] self.tora not initialized yet in ToraDirectEnv.__init__")

        if hasattr(self, "object1"):
            self.object1_entity_cfg = SceneEntityCfg(
                "object1", body_names=list(self.object1.data.body_names)
            )
            self.object1_entity_cfg.resolve(self.scene)
        else:
            print(
                "[Warning] self.object1 not initialized yet in ToraDirectEnv.__init__"
            )

        self.base_obs_dim = self.cfg.base_observation_space
        self.num_stack = self.cfg.num_frames_to_stack

        self.observation_space = gym.spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(self.cfg.observation_space,),
            dtype=np.float32,
        )
        print(
            f"[Info] Environment gym.space.Box created with shape: {self.observation_space.shape}"
        )

        self._obs_history = [deque(maxlen=self.num_stack) for _ in range(self.num_envs)]

        self.action_space = gym.spaces.Box(
            low=-1.0, high=1.0, shape=(self.cfg.action_space,), dtype=np.float32
        )

        self.action_range = self.cfg.action_scale
        self.action_center = 0.0

        if hasattr(self, "robot_entity_cfg"):
            self.robot_entity_cfg.resolve(self.scene)
        if hasattr(self, "object1_entity_cfg"):
            self.object1_entity_cfg.resolve(self.scene)
        self.last_actions = torch.zeros(
            (self.num_envs, self.cfg.action_space), device=self.device
        )
        self.episode_length_s = self.cfg.episode_length_s

    def _setup_scene(self):
        # 第一步：创建基础场景
        # 创建机器人
        self.tora = Articulation(self.cfg.robot_cfg)

        # 添加地面
        spawn_ground_plane(prim_path="/World/ground", cfg=GroundPlaneCfg())

        # 添加机器人到场景
        self.scene.articulations["tora"] = self.tora

        # 第二步：克隆环境
        # 这一步会复制所有已经创建的物体（机器人和地面）
        self.scene.clone_environments(copy_from_source=False)

        # 第三步：创建和添加其他物体
        # 在克隆环境后创建桌子和物体，这样它们会在每个环境中都被正确创建
        self.table = RigidObject(self.cfg.table)
        self.object1 = RigidObject(self.cfg.object1)

        # 将物体添加到场景
        self.scene.rigid_objects["table"] = self.table
        self.scene.rigid_objects["object1"] = self.object1

        # 添加灯光（灯光通常是全局的，不需要复制）
        light_cfg = sim_utils.DomeLightCfg(intensity=2000.0, color=(0.75, 0.75, 0.75))
        light_cfg.func("/World/Light", light_cfg)

    def _pre_physics_step(self, actions: torch.Tensor) -> None:
        # 从[-1,1]扩展到实际需要的范围 [-action_scale, action_scale]
        scaled_actions = actions * self.action_range + self.action_center
        self.actions = scaled_actions.clone()
        self.last_actions = self.actions.clone()

    def _apply_action(self) -> None:
        """
        应用BC模型输出的50维动作到仿真环境

        动作空间分解 (50维):
        - 左手腕位置: [0:3]   (x, y, z)
        - 左手腕旋转: [3:9]   (6D旋转表示)
        - 右手腕位置: [9:12]  (x, y, z)
        - 右手腕旋转: [12:18] (6D旋转表示)
        - 左手关节:   [18:34] (16个关节角度)
        - 右手关节:   [34:50] (16个关节角度)
        """
        # 确保有wholebody solver
        if not hasattr(self, "wholebody_solver"):
            self.wholebody_solver = Px_wholebody_solver()
            print("✅ 初始化Px_wholebody_solver")

        num_envs = self.actions.shape[0]
        num_joints = len(self.tora.data.joint_names)

        # 创建完整的关节目标位置张量
        full_actions = torch.zeros(
            (num_envs, num_joints),
            device=self.actions.device,
            dtype=self.actions.dtype,
        )

        # 处理每个环境的动作
        for env_idx in range(num_envs):
            action = self.actions[env_idx]  # 取出第env_idx个环境的50维动作

            # 1. 解析动作空间
            left_wrist_pos = action[0:3].cpu().numpy()  # 左手腕位置
            left_wrist_rot6d = action[3:9].cpu().numpy()  # 左手腕6D旋转
            right_wrist_pos = action[9:12].cpu().numpy()  # 右手腕位置
            right_wrist_rot6d = action[12:18].cpu().numpy()  # 右手腕6D旋转
            left_hand_joints = action[18:34].cpu().numpy()  # 左手16个关节
            right_hand_joints = action[34:50].cpu().numpy()  # 右手16个关节

            # 2. 将6D旋转转换为四元数 (从rot6d到旋转矩阵再到四元数)
            from isaaclab.utils.math import rot6d_to_matrix, matrix_to_quat

            left_rot_matrix = rot6d_to_matrix(
                torch.tensor(left_wrist_rot6d, device=self.device).unsqueeze(0)
            )
            left_quat = (
                matrix_to_quat(left_rot_matrix).squeeze(0).cpu().numpy()
            )  # [x,y,z,w]

            right_rot_matrix = rot6d_to_matrix(
                torch.tensor(right_wrist_rot6d, device=self.device).unsqueeze(0)
            )
            right_quat = (
                matrix_to_quat(right_rot_matrix).squeeze(0).cpu().numpy()
            )  # [x,y,z,w]

            # 3. 构造目标末端位姿 [x,y,z,qx,qy,qz,qw]
            left_target_pose = np.concatenate([left_wrist_pos, left_quat])
            right_target_pose = np.concatenate([right_wrist_pos, right_quat])

            # 4. 使用wholebody solver解算全身关节位置
            try:
                # 获取当前关节状态作为初始值
                current_joint_pos = self.tora.data.joint_pos[env_idx].cpu().numpy()

                # 调用IK解算器
                solved_joint_pos = self.wholebody_solver.solve_ik(
                    left_target_pose=left_target_pose,
                    right_target_pose=right_target_pose,
                    current_joint_pos=current_joint_pos,
                )

                # 如果IK解算成功，使用解算结果
                if solved_joint_pos is not None:
                    full_actions[env_idx] = torch.tensor(
                        solved_joint_pos, device=self.device
                    )
                else:
                    print(f"⚠️ IK求解失败，使用关节空间控制作为后备方案")
                    # 后备方案：直接映射已知的关节
                    self._apply_joint_space_control(
                        env_idx, left_hand_joints, right_hand_joints, full_actions
                    )

            except Exception as e:
                print(f"⚠️ Wholebody solver调用失败: {e}，使用关节空间控制")
                # 后备方案：直接映射已知的关节
                self._apply_joint_space_control(
                    env_idx, left_hand_joints, right_hand_joints, full_actions
                )

        # 应用动作到仿真
        self.tora.set_joint_position_target(full_actions)

    def _apply_joint_space_control(
        self,
        env_idx: int,
        left_hand_joints: np.ndarray,
        right_hand_joints: np.ndarray,
        full_actions: torch.Tensor,
    ):
        """
        后备方案：直接控制手指关节，而不使用IK解算
        """
        # 左手关节名称映射 (与BC训练时的顺序保持一致)
        left_hand_joint_names = []
        for finger in ["thumb", "index", "mid", "ring"]:  # 按BC训练顺序
            for i in range(1, 5):
                left_hand_joint_names.append(f"L{finger}{i}_joint")

        # 右手关节名称映射 (与BC训练时的顺序保持一致)
        right_hand_joint_names = []
        for finger in ["thumb", "index", "mid", "ring"]:  # 按BC训练顺序
            for i in range(1, 5):
                right_hand_joint_names.append(f"{finger}{i}_joint")

        # 使用find_joints动态查找关节索引
        try:
            left_indices, left_names = self.tora.find_joints(left_hand_joint_names)
            right_indices, right_names = self.tora.find_joints(right_hand_joint_names)

            # 映射左手关节
            if left_indices is not None and len(left_indices) > 0:
                # 确保不超出索引范围
                valid_left_count = min(len(left_indices), len(left_hand_joints))
                if valid_left_count > 0:
                    left_joint_tensor = torch.tensor(
                        left_hand_joints[:valid_left_count],
                        device=self.device,
                        dtype=full_actions.dtype,
                    )
                    full_actions[env_idx, left_indices[:valid_left_count]] = (
                        left_joint_tensor
                    )
                    print(f"✅ 映射{valid_left_count}个左手关节")

            # 映射右手关节
            if right_indices is not None and len(right_indices) > 0:
                # 确保不超出索引范围
                valid_right_count = min(len(right_indices), len(right_hand_joints))
                if valid_right_count > 0:
                    right_joint_tensor = torch.tensor(
                        right_hand_joints[:valid_right_count],
                        device=self.device,
                        dtype=full_actions.dtype,
                    )
                    full_actions[env_idx, right_indices[:valid_right_count]] = (
                        right_joint_tensor
                    )
                    print(f"✅ 映射{valid_right_count}个右手关节")

        except Exception as e:
            print(f"❌ 关节映射失败: {e}")
            # 最后的后备方案：只填充前50个关节
            action_tensor = torch.tensor(
                np.concatenate([left_hand_joints, right_hand_joints])[:50],
                device=self.device,
                dtype=full_actions.dtype,
            )
            available_joints = min(50, full_actions.shape[1])
            full_actions[env_idx, :available_joints] = action_tensor[:available_joints]

    def _get_observations(self) -> dict:
        # 调用基础观察计算函数
        current_base_obs = self._compute_base_observations()

        # 处理观察历史和帧堆叠
        stacked_obs_list = []
        for env_idx in range(self.num_envs):
            obs_to_store = current_base_obs[env_idx].clone().detach()

            if len(self._obs_history[env_idx]) == 0:
                for _ in range(self.num_stack):
                    self._obs_history[env_idx].append(obs_to_store)
            else:
                self._obs_history[env_idx].append(obs_to_store)

            stacked_env_tensor = torch.cat(list(self._obs_history[env_idx]), dim=0)
            stacked_obs_list.append(stacked_env_tensor)

        final_stacked_obs = torch.stack(stacked_obs_list, dim=0)
        return {"policy": final_stacked_obs}

    def _get_rewards(self) -> torch.Tensor:
        total_reward = compute_rewards(
            self.cfg.rew_scale_alive,
            self.cfg.rew_scale_terminated,
            self.cfg.rew_scale_joint_pos,
            self.cfg.rew_scale_joint_vel,
            self.joint_pos,
            self.joint_vel,
            self.reset_terminated,
        )
        return total_reward

    def _get_dones(self) -> tuple[torch.Tensor, torch.Tensor]:
        self.joint_pos = self.tora.data.joint_pos
        self.joint_vel = self.tora.data.joint_vel

        time_out = self.episode_length_buf >= self.max_episode_length - 1
        out_of_bounds = torch.any(
            torch.abs(self.joint_pos) > self.cfg.max_joint_pos,
            dim=1,
        )
        out_of_bounds = torch.zeros_like(time_out, dtype=torch.bool, device=self.device)

        return out_of_bounds, time_out

    def _reset_idx(self, env_ids: Sequence[int] | None):
        if env_ids is None:
            env_ids = list(range(self.num_envs))
        super()._reset_idx(env_ids)

        joint_pos = self.tora.data.default_joint_pos[env_ids]
        joint_pos += sample_uniform(
            self.cfg.initial_joint_pos_range[0],
            self.cfg.initial_joint_pos_range[1],
            (len(env_ids), self.tora.num_joints),
            joint_pos.device,
        )
        joint_vel = self.tora.data.default_joint_vel[env_ids]
        default_root_state = self.tora.data.default_root_state[env_ids]
        default_root_state[:, :3] += self.scene.env_origins[env_ids]
        self.joint_pos[env_ids] = joint_pos
        self.joint_vel[env_ids] = joint_vel
        self.tora.write_root_pose_to_sim(default_root_state[:, :7], env_ids)
        self.tora.write_root_velocity_to_sim(default_root_state[:, 7:], env_ids)
        self.tora.write_joint_state_to_sim(joint_pos, joint_vel, None, env_ids)

        table_state = self.table.data.default_root_state[env_ids].clone()
        object1_state = self.object1.data.default_root_state[env_ids].clone()
        table_state[:, :3] += self.scene.env_origins[env_ids]
        object1_state[:, :3] += self.scene.env_origins[env_ids]
        self.table.write_root_state_to_sim(table_state, env_ids)
        self.object1.write_root_state_to_sim(object1_state, env_ids)

        initial_base_obs = self._compute_base_observations()

        for env_idx in env_ids:
            self._obs_history[env_idx].clear()
            obs_to_fill = initial_base_obs[env_idx].clone().detach()
            for _ in range(self.num_stack):
                self._obs_history[env_idx].append(obs_to_fill)

        self.last_actions[env_ids] = 0.0

    def _compute_base_observations(self) -> torch.Tensor:
        body_indices_larm, _ = self.tora.find_bodies("larm_7_Link")
        wrist_pos_l = self.tora.data.body_pos_w[..., body_indices_larm[0], :]
        wrist_rot_l = self.tora.data.body_quat_w[..., body_indices_larm[0], :]
        wrist_rot_6d_l = quat_to_rot6d(wrist_rot_l)

        body_indices_rarm, _ = self.tora.find_bodies("rarm_7_Link")
        if body_indices_rarm:
            wrist_pos_r = self.tora.data.body_pos_w[..., body_indices_rarm[0], :]
            wrist_rot_r = self.tora.data.body_quat_w[..., body_indices_rarm[0], :]
            wrist_rot_6d_r = quat_to_rot6d(wrist_rot_r)
        else:
            wrist_pos_r = torch.zeros_like(wrist_pos_l)
            wrist_rot_6d_r = torch.zeros_like(wrist_rot_6d_l)

        # 左手关节 (16个)
        left_hand_joint_names = []
        for finger in ["index", "mid", "ring", "thumb"]:
            for i in range(1, 5):
                left_hand_joint_names.append(f"L{finger}{i}_joint")

        # 右手关节 (16个)
        right_hand_joint_names = []
        for finger in ["index", "mid", "ring", "thumb"]:
            for i in range(1, 5):
                right_hand_joint_names.append(f"{finger}{i}_joint")

        left_indices, _ = self.tora.find_joints(left_hand_joint_names)
        right_indices, _ = self.tora.find_joints(right_hand_joint_names)
        if left_indices:
            left_hand_pos = self.tora.data.joint_pos[..., left_indices]
        else:
            left_hand_pos = torch.zeros((self.num_envs, 16), device=self.device)
        if right_indices:
            right_hand_pos = self.tora.data.joint_pos[..., right_indices]
        else:
            right_hand_pos = torch.zeros((self.num_envs, 16), device=self.device)

        robot_proprio = torch.cat(
            [
                wrist_pos_l,
                wrist_rot_6d_l,
                wrist_pos_r,
                wrist_rot_6d_r,
                left_hand_pos,
                right_hand_pos,
            ],
            dim=1,
        )

        object_states = []
        max_objects = 5
        tracked_objects = [self.object1]
        for i in range(max_objects):
            if i < len(tracked_objects):
                obj = tracked_objects[i]
                obj_pos = obj.data.root_pos_w
                obj_rot = obj.data.root_quat_w
                obj_rot_6d = quat_to_rot6d(obj_rot)
                obj_size = torch.tensor([0.1, 0.1, 0.1], device=self.device).expand(
                    self.num_envs, 3
                )
                obj_state = torch.cat([obj_pos, obj_rot_6d, obj_size], dim=1)
            else:
                obj_state = torch.zeros((self.num_envs, 12), device=self.device)
            object_states.append(obj_state)
        all_object_states = torch.cat(object_states, dim=1)

        base_obs = torch.cat([robot_proprio, all_object_states], dim=1)

        # if base_obs.shape[1] != self.base_obs_dim:
        #     print(f"[Error] _compute_base_observations dim ({base_obs.shape[1]}) != configured base_obs_dim ({self.base_obs_dim}) - Robot Proprio: {robot_proprio.shape[1]}, Object States: {all_object_states.shape[1]}")
        # else:
        #     print(f"[_compute_base_observations] Computed base_obs shape: {base_obs.shape}")

        return base_obs


@torch.jit.script
def compute_rewards(
    rew_scale_alive: float,
    rew_scale_terminated: float,
    rew_scale_joint_pos: float,
    rew_scale_joint_vel: float,
    joint_pos: torch.Tensor,
    joint_vel: torch.Tensor,
    reset_terminated: torch.Tensor,
):
    rew_alive = rew_scale_alive * (1.0 - reset_terminated.float())
    rew_termination = rew_scale_terminated * reset_terminated.float()
    rew_joint_pos = rew_scale_joint_pos * torch.sum(torch.square(joint_pos), dim=-1)
    rew_joint_vel = rew_scale_joint_vel * torch.sum(torch.abs(joint_vel), dim=-1)
    total_reward = rew_alive + rew_termination + rew_joint_pos + rew_joint_vel
    return total_reward


# 注册环境

gym.register(
    id="Isaac-TORA-Direct-v0",
    entry_point="IsaacSim_direct_task_table_set_env_TORA:ToraDirectEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": "IsaacSim_direct_task_table_set_env_TORA:ToraDirectEnvCfg",
        "rl_games_cfg_entry_point": "agents:rl_games_ppo_cfg.yaml",
        "rsl_rl_cfg_entry_point": "agents:rsl_rl_ppo_cfg.yaml",
        "skrl_cfg_entry_point": "agents:skrl_ppo_cfg.yaml",
        "skrl_sac_cfg_entry_point": "agents:skrl_sac_cfg.yaml",  # 添加这一行
        "sb3_cfg_entry_point": "agents:sb3_ppo_cfg.yaml",
    },
)
