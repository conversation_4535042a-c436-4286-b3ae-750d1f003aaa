#!/usr/bin/env python3
"""visualize_dataset_distribution.py

Standalone utility to explore and visualise the distribution of dataset features.

Features
--------
1. Supports CSV (pandas) and HDF5 (h5py) data sources.
2. Computes descriptive statistics (count / mean / std / min / quartiles / max) for all numeric
   columns or datasets and saves them to a JSON file.
3. Produces per-feature histograms and kernel density plots.
4. Aggregates the data over a sliding temporal window (default 25) and plots the mean value of
   each feature in time – useful to understand how observations / actions evolve across long
   trajectories.
5. Completely independent from the rest of the code base – only relies on common Python science
   stack (numpy / pandas / matplotlib / seaborn / h5py / tqdm).

Example
-------
python scripts/visualize_dataset_distribution.py \
       --dataset_path data/my_recording.csv \
       --output_dir distribution_report \
       --stride 25
"""

import argparse
import json
import os
import sys
from pathlib import Path
from typing import Dict, <PERSON>, Tuple, Union

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from concurrent.futures import ProcessPoolExecutor, as_completed

try:
    import h5py  # type: ignore
except ImportError:  # pragma: no cover – h5py is optional, warn gracefully
    h5py = None
    print("⚠️  h5py not found – HDF5 support will be disabled.", file=sys.stderr)

# -----------------------------------------------------------------------------
# Global helpers (defined early for multiprocessing pickling)
# -----------------------------------------------------------------------------


class OnlineStats:
    """Accumulate count / mean / std / min / max using Welford's algorithm."""

    __slots__ = ("n", "mean", "M2", "min", "max")

    def __init__(self):
        self.n = 0
        self.mean = 0.0
        self.M2 = 0.0
        self.min = np.inf
        self.max = -np.inf

    def update(self, arr: np.ndarray):
        arr = arr.astype(float).ravel()
        if arr.size == 0:
            return
        arr = arr[~np.isnan(arr)]
        if arr.size == 0:
            return
        n_b = arr.size
        mean_b = arr.mean()
        M2_b = ((arr - mean_b) ** 2).sum()
        self.min = float(min(self.min, np.nanmin(arr)))
        self.max = float(max(self.max, np.nanmax(arr)))
        delta = mean_b - self.mean
        total_n = self.n + n_b
        if total_n == 0:
            return
        self.mean = (self.n * self.mean + n_b * mean_b) / total_n
        self.M2 = self.M2 + M2_b + delta ** 2 * self.n * n_b / total_n
        self.n = total_n

    def finalize(self):
        if self.n < 2:
            std = float("nan")
        else:
            std = np.sqrt(self.M2 / (self.n - 1))
        return {
            "count": int(self.n),
            "mean": self.mean,
            "std": std,
            "min": self.min,
            "max": self.max,
        }


def _worker_process_file(fp_str: str, chunk_size: int, hdf5_chunk: int, sample_size: int):
    """Process a single CSV/HDF5 file and return statistics plus reservoir samples.

    This function is defined at module level to be picklable by multiprocessing.
    """

    from pathlib import Path
    import pandas as pd
    import numpy as np
    import h5py

    class _OS:
        __slots__ = ("n", "mean", "M2", "min", "max")

        def __init__(self):
            self.n = 0
            self.mean = 0.0
            self.M2 = 0.0
            self.min = np.inf
            self.max = -np.inf

        def update(self, data: np.ndarray):
            data = data.astype(float).ravel()
            if data.size == 0:
                return
            data = data[~np.isnan(data)]
            if data.size == 0:
                return
            n_b = data.size
            mean_b = data.mean()
            M2_b = ((data - mean_b) ** 2).sum()
            self.min = float(min(self.min, np.nanmin(data)))
            self.max = float(max(self.max, np.nanmax(data)))
            delta = mean_b - self.mean
            total_n = self.n + n_b
            if total_n == 0:
                return
            self.mean = (self.n * self.mean + n_b * mean_b) / total_n
            self.M2 = self.M2 + M2_b + delta ** 2 * self.n * n_b / total_n
            self.n = total_n

        def to_tuple(self):
            if self.n < 2:
                std = float("nan")
            else:
                std = np.sqrt(self.M2 / (self.n - 1))
            return (self.n, self.mean, std, self.min, self.max)

    fp = Path(fp_str)
    stats: dict[str, _OS] = {}
    reservoir: dict[str, list[float]] = {}
    seen: dict[str, int] = {}

    def _update(col: str, arr: np.ndarray):
        if col not in stats:
            stats[col] = _OS()
            reservoir[col] = []
            seen[col] = 0
        stats[col].update(arr)
        if sample_size > 0:
            arr = arr[~np.isnan(arr)]
            for v in arr:
                seen[col] += 1
                n_seen = seen[col]
                if len(reservoir[col]) < sample_size:
                    reservoir[col].append(float(v))
                else:
                    j = np.random.randint(0, n_seen)
                    if j < sample_size:
                        reservoir[col][j] = float(v)

    if fp.suffix.lower() == ".csv":
        for chunk in pd.read_csv(fp, chunksize=chunk_size):
            for col in chunk.select_dtypes(include=[np.number]).columns:
                _update(col, chunk[col].values)
    elif fp.suffix.lower() in {".h5", ".hdf5"}:
        def _walk(group, prefix=""):
            for k in group:
                item = group[k]
                name = f"{prefix}/{k}" if prefix else k
                if isinstance(item, h5py.Dataset):
                    if not np.issubdtype(item.dtype, np.number):
                        continue
                    dset = item
                    total = dset.shape[0] if dset.shape else 0
                    if total == 0:
                        _update(name, np.array(dset[()]).reshape(-1))
                        continue
                    for s in range(0, total, hdf5_chunk):
                        e = min(s + hdf5_chunk, total)
                        _update(name, dset[s:e])
                elif isinstance(item, h5py.Group):
                    _walk(item, name)

        with h5py.File(fp, "r") as h5f:
            _walk(h5f)

    simple_stats = {k: v.to_tuple() for k, v in stats.items()}
    return simple_stats, reservoir

# -----------------------------------------------------------------------------
# Data ingestion helpers
# -----------------------------------------------------------------------------


def _load_csv(path: Path) -> pd.DataFrame:
    """Load a CSV using pandas.

    Parameters
    ----------
    path : Path
        CSV file to be loaded.
    """
    return pd.read_csv(path)


def _walk_hdf5_group(group, prefix: str, collection: Dict[str, List]):
    """Recursively walk an h5py group, flattening numeric datasets into *collection*.

    Non-numeric datasets are ignored. Each numeric dataset becomes a column whose name is the
    path inside the HDF5 file – e.g. "group/subgroup/dset".
    """
    for key in group:
        item = group[key]
        name = f"{prefix}/{key}" if prefix else key
        if isinstance(item, h5py.Dataset):
            # Only take numeric datasets – skip strings or object types.
            if np.issubdtype(item.dtype, np.number):
                collection[name] = item[()].reshape(-1)  # Flatten.
        elif isinstance(item, h5py.Group):
            _walk_hdf5_group(item, name, collection)


def _load_hdf5(path: Path) -> pd.DataFrame:
    """Load numeric datasets from an HDF5 file into a dataframe.

    Each individual HDF5 dataset is flattened and becomes a column.
    """
    if h5py is None:
        raise RuntimeError("h5py is not installed – cannot load HDF5 data.")

    collection: Dict[str, List] = {}
    with h5py.File(path, "r") as f:
        _walk_hdf5_group(f, "", collection)

    if not collection:
        raise ValueError(f"No numeric data found in HDF5 file {path}.")

    # Align all columns by length – pad shorter arrays with NaNs.
    max_len = max(len(v) for v in collection.values())
    padded_data = {}
    for name, arr in collection.items():
        if len(arr) < max_len:
            # Always pad with float array to safely hold NaNs even if original dtype is int
            padded = np.full(max_len, np.nan, dtype=float)
            # Cast original array to float to avoid precision loss for most numeric types
            padded[: len(arr)] = arr.astype(float)
            arr = padded
        padded_data[name] = arr

    return pd.DataFrame(padded_data)


def load_dataset(path: Path) -> pd.DataFrame:
    """Load *path* into a pandas.DataFrame.

    Accepts:
    * *.csv* – via pandas
    * *.h5* / *.hdf5* – via h5py
    """
    if not path.exists():
        raise FileNotFoundError(f"Dataset path {path} does not exist")

    if path.is_dir():
        # Concatenate dataframes from all files inside directory.
        frames = []
        for file in sorted(path.iterdir()):
            if file.suffix.lower() == ".csv":
                frames.append(_load_csv(file))
            elif file.suffix.lower() in {".h5", ".hdf5"}:
                frames.append(_load_hdf5(file))
            # Ignore anything else.
        if not frames:
            raise ValueError(f"No CSV/HDF5 files found in directory {path}")
        return pd.concat(frames, axis=0, ignore_index=True)
    else:
        if path.suffix.lower() == ".csv":
            return _load_csv(path)
        elif path.suffix.lower() in {".h5", ".hdf5"}:
            return _load_hdf5(path)
        else:
            raise ValueError("Unsupported file type – only CSV and HDF5 are supported.")


# -----------------------------------------------------------------------------
# Plotting helpers
# -----------------------------------------------------------------------------


def _create_output_dir(path: Path):
    """Ensure that *path* exists (mkdir ‑p)."""
    path.mkdir(parents=True, exist_ok=True)


def plot_histograms(df: pd.DataFrame, out_dir: Path):
    """Generate per-feature histograms / KDEs for the entire dataframe."""
    num_cols = df.select_dtypes(include=[np.number]).columns
    if len(num_cols) == 0:
        print("❌ No numeric columns found – skipping histogram plots.")
        return

    hist_dir = out_dir / "histograms"
    _create_output_dir(hist_dir)

    for col in tqdm(num_cols, desc="Plotting histograms"):
        plt.figure(figsize=(6, 4))
        sns.histplot(df[col].dropna(), kde=True, bins=50, color="skyblue")
        plt.title(f"Distribution of {col}")
        plt.xlabel(col)
        plt.ylabel("Frequency")
        plt.tight_layout()
        plt.savefig(hist_dir / f"{col}.png")
        plt.close()


def plot_temporal_mean(df: pd.DataFrame, stride: int, out_dir: Path):
    """Aggregate the dataframe every *stride* rows and plot the mean per feature w.r.t time."""

    if stride <= 0:
        print("Stride must be > 0 – skipping temporal plots.")
        return

    num_cols = df.select_dtypes(include=[np.number]).columns
    if len(num_cols) == 0:
        print("❌ No numeric columns found – skipping temporal plots.")
        return

    # Trim length to a multiple of stride for clean grouping.
    trimmed_len = len(df) - (len(df) % stride)
    if trimmed_len == 0:
        print("Dataset too short for the requested stride – skipping temporal plots.")
        return

    data_mat = df[num_cols].iloc[:trimmed_len].values.reshape(-1, stride, len(num_cols))
    mean_over_stride = data_mat.mean(axis=1)  # shape: (num_chunks, num_cols)

    temporal_df = pd.DataFrame(mean_over_stride, columns=num_cols)
    temporal_df["chunk"] = np.arange(len(temporal_df)) * stride

    temp_dir = out_dir / "temporal"
    _create_output_dir(temp_dir)

    # Plot each feature separately (saves cluttering).
    for col in tqdm(num_cols, desc="Plotting temporal means"):
        plt.figure(figsize=(8, 4))
        sns.lineplot(x="chunk", y=col, data=temporal_df, marker="o")
        plt.title(f"Mean {col} every {stride} steps")
        plt.xlabel("Time step")
        plt.ylabel(col)
        plt.tight_layout()
        plt.savefig(temp_dir / f"{col}.png")
        plt.close()


# -----------------------------------------------------------------------------
# Command-line interface
# -----------------------------------------------------------------------------


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Dataset distribution visualiser")

    parser.add_argument(
        "--dataset_path",
        type=str,
        required=True,
        help="Path to a CSV/HDF5 file or a directory containing such files.",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="dataset_distribution_output",
        help="Directory where reports and figures will be written.",
    )
    parser.add_argument(
        "--stride",
        type=int,
        default=25,
        help="Temporal aggregation stride (e.g. 25 steps). Use 0 to disable temporal plots.",
    )
    parser.add_argument(
        "--streaming",
        action="store_true",
        help="Enable memory-friendly streaming mode. Processes files / data chunks sequentially instead of loading everything into RAM.",
    )
    parser.add_argument(
        "--chunk_size",
        type=int,
        default=200_000,
        help="Row chunk size when streaming CSV files (default: 200k).",
    )
    parser.add_argument(
        "--hdf5_chunk",
        type=int,
        default=50_000,
        help="Maximum number of rows read at once from each HDF5 dataset when in streaming mode (default: 50k).",
    )
    parser.add_argument(
        "--num_workers",
        type=int,
        default=1,
        help="Number of worker processes for parallel file processing (>=1). Only used in streaming mode.",
    )
    parser.add_argument(
        "--use_existing_stats",
        type=str,
        default=None,
        help="Path to an existing stats file (.json or .pkl). If provided and file exists, the script will load it instead of scanning the dataset.",
    )
    parser.add_argument(
        "--sample_size",
        type=int,
        default=50000,
        help="Maximum number of values kept per numeric column when streaming (reservoir sampling) to build histograms.",
    )

    return parser.parse_args()


# -----------------------------------------------------------------------------
# Main entry point
# -----------------------------------------------------------------------------


def main():
    args = parse_args()

    dataset_path = Path(args.dataset_path)
    output_dir = Path(args.output_dir)
    stride = args.stride

    _create_output_dir(output_dir)

    # --------------------------------------------------
    # 1️⃣ 直接使用已有统计文件（最快）
    # --------------------------------------------------

    if args.use_existing_stats is not None:
        stats_path_in = Path(args.use_existing_stats)
        if not stats_path_in.exists():
            print(f"❌ Existing stats file {stats_path_in} not found, falling back to scanning…")
        else:
            print(f"✅ Loading existing statistics from {stats_path_in}")
            if stats_path_in.suffix == ".json":
                with open(stats_path_in, "r", encoding="utf-8") as f:
                    final_stats = json.load(f)
            else:
                import pickle

                with open(stats_path_in, "rb") as f:
                    final_stats = pickle.load(f)

            out_stats_path = output_dir / "summary_statistics.json"
            with open(out_stats_path, "w", encoding="utf-8") as f:
                json.dump(final_stats, f, indent=2, ensure_ascii=False)
            print(f"📊 Statistics copied to {out_stats_path}. No dataset scanning performed.")
            return

    if args.streaming:
        # -------------------------------------------
        # 串行 or 并行文件级处理
        # -------------------------------------------

        futures = []
        with ProcessPoolExecutor(max_workers=args.num_workers) as ex:
            for fp in sorted(dataset_path.iterdir()):
                futures.append(
                    ex.submit(
                        _worker_process_file,
                        str(fp),
                        args.chunk_size,
                        args.hdf5_chunk,
                        args.sample_size,
                    )
                )

        # Prepare global collectors
        online_stats: Dict[str, OnlineStats] = {}
        reservoir_samples: Dict[str, List[float]] = {}
        total_seen: Dict[str, int] = {}

        for fut in tqdm(as_completed(futures), total=len(futures), desc="Merging results", unit="file"):
            stats_part, reservoir_part = fut.result()

            # merge stats
            for col, (n, mean, std, mn, mx) in stats_part.items():
                if col not in online_stats:
                    os = OnlineStats()
                    os.n = n
                    os.mean = mean
                    # Rebuild M2 from std
                    if n > 1 and not np.isnan(std):
                        os.M2 = (std ** 2) * (n - 1)
                    else:
                        os.M2 = 0.0
                    os.min = mn
                    os.max = mx
                    online_stats[col] = os
                else:
                    # Combine two OnlineStats using update on synthetic array? better implement merge
                    other = OnlineStats()
                    other.n = n
                    other.mean = mean
                    other.M2 = (std ** 2) * (n - 1) if n > 1 and not np.isnan(std) else 0.0
                    other.min = mn
                    other.max = mx

                    # merge into online_stats[col]
                    a = online_stats[col]
                    delta = other.mean - a.mean
                    tot_n = a.n + other.n
                    if tot_n == 0:
                        continue
                    a.mean = (a.n * a.mean + other.n * other.mean) / tot_n
                    a.M2 = a.M2 + other.M2 + (delta ** 2) * a.n * other.n / tot_n
                    a.min = min(a.min, other.min)
                    a.max = max(a.max, other.max)
                    a.n = tot_n

            if args.sample_size > 0:
                for col, samples in reservoir_part.items():
                    if col not in reservoir_samples:
                        reservoir_samples[col] = []
                        total_seen[col] = 0
                    for v in samples:
                        total_seen[col] += 1
                        if len(reservoir_samples[col]) < args.sample_size:
                            reservoir_samples[col].append(v)
                        else:
                            j = np.random.randint(0, total_seen[col])
                            if j < args.sample_size:
                                reservoir_samples[col][j] = v

        # After merging, continue below to export

    else:
        # ---------- Non-streaming (original behaviour) ----------
        print("🚀 Loading dataset into memory…")
        df = load_dataset(dataset_path)
        print(
            f"✅ Loaded dataset with shape {df.shape} and {df.select_dtypes(include=[np.number]).shape[1]} numeric features."
        )

        stats = df.describe().to_dict()
        stats_path = output_dir / "summary_statistics.json"
        with open(stats_path, "w", encoding="utf-8") as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        print(f"📊 Descriptive statistics saved to {stats_path}")

        print("📈 Generating histograms…")
        plot_histograms(df, output_dir)
        print("📈 Generating temporal mean plots…")
        if stride > 0:
            plot_temporal_mean(df, stride, output_dir)
        else:
            print("Skipping temporal plots (stride <= 0)")

        print(f"🎉 All reports written to {output_dir.resolve()}")


if __name__ == "__main__":
    main() 