"""
离线有监督学习示例脚本
====================

本脚本演示了使用 Janus 学习框架进行完整的离线有监督学习流程：
1. 数据准备：从 自定义数据集加载抓取任务数据
2. 模型构建：使用 自定义算法 创建策略模型
3. 模型训练：通过 LightningTrainer 进行有监督学习
4. 模型保存：训练完成后将模型权重保存到文件

技术栈：
- 学习框架：Janus Learning Framework
- 数据集接口：自定义数据加载 PaxiniHDF5
- 训练框架：PyTorch Lightning
- 策略模型：DummyPolicy

此示例脚本可作为使用 Janus 框架开发自定义离线学习应用的参考模板。
"""

import torch
import os
import yaml
import argparse
from pathlib import Path
from termcolor import colored
from datetime import datetime

from px_janus_learnsim.AgentDirector import AgentDirector
from px_janus_learnsim.dataprocess.dataprocess_manager import DataprocessManagerCfg
from px_janus_learnsim.learning.learning_manager import LearningManagerCfg
from px_janus_learnsim.ErrorManager import ErrorManagerCfg
from px_janus_learnsim.Clerk import ClerkDuties

from px_janus_learnsim.learning.constants import (
    AlgorithmType,
    FrameworkType,
    UtilityType,
)

1


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Tacrix-LeRobot训练脚本")
    parser.add_argument(
        "--dataset",
        type=str,
        default="/home/<USER>/workspace/px_LearningSim_Janus/data/poc_dataset",
        help="数据集",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="outputs/train/tacrix_diffusion",
        help="输出目录",
    )
    parser.add_argument("--steps", type=int, default=5000, help="训练步数")
    parser.add_argument("--batch_size", type=int, default=8, help="批处理大小")
    parser.add_argument("--lr", type=float, default=1e-4, help="学习率")
    parser.add_argument("--device", type=str, default="cuda", help="训练设备")
    parser.add_argument("--log_freq", type=int, default=10, help="日志频率")
    return parser.parse_args()


def main():
    # 解析命令行参数
    args = parse_args()

    # 创建输出目录
    output_directory = Path(args.output_dir)
    output_directory.mkdir(parents=True, exist_ok=True)

    # 训练参数
    device = torch.device(args.device)
    training_steps = args.steps
    dataset_id = args.dataset
    log_freq = args.log_freq

    # 初始化Agent和Managers
    agent_director = AgentDirector()
    agent = agent_director.create_agent(
        "TacrixLeRobotTrainer",
        {
            "dataprocess": DataprocessManagerCfg(
                clerks=["Pipeline"], loader_type="PaxiniHDF5", data_path=dataset_id
            ),
            "learning": LearningManagerCfg(
                frameworks=FrameworkType.LIGHTNING.value,
                algorithms=AlgorithmType.DUMMY.value,
                utilities=UtilityType.MODEL_IO.value,
            ),
            "error_manager": ErrorManagerCfg(),
        },
    )
    print(colored(f"开始训练: 数据集={dataset_id}, 步数={training_steps}", "cyan"))

    # 创建数据管道
    train_loader, val_loader, norm_stats, is_sim = (
        agent.dataprocess_manager.execute_action(
            "Pipeline",
            data={
                "command": "load_data",
                "database_dir": dataset_id,
                "camera_names": ["front_camera"],
                "tactile_names": ["tactile_sensor"],
                "batch_size_train": args.batch_size,
                "batch_size_val": args.batch_size // 2,
            },
        )
    )

    policy = agent.learning_manager.create_policy(
        params={"device": device, "hidden_dim": 256}
    )
    print("policy: ", policy)
    # 训练模型 - 简化的API
    # 确保policy在正确设备上
    policy = policy.to(device)

    # 训练模型 
    results = agent.learning_manager.train(
        policy=policy,
        train_loader=train_loader,
        val_loader=val_loader,
        framework=FrameworkType.LIGHTNING,
        lr=args.lr,
        max_epochs=5,
        device=torch.device("cuda" if torch.cuda.is_available() else "cpu"),
    ) 


    # print(results)


    trained_policy = policy
    
    agent.learning_manager.save_model(
        policy=trained_policy,
        save_path=str(output_directory / "final_model.pt"),  # 正确参数名
    )


if __name__ == "__main__":
    main()
