# Tacrix 抓取姿态生成训练配置文件
# ===============================

# 实验基本配置
experiment:
    task_name: GraspGeneration # 任务名称
    description: 抓取姿态生成训练实验 # 实验描述
    seed: 42 # 随机种子

# 模型参数
model:
    # 点云编码器配置
    encoder:
        type: pointnet # 编码器类型: pointnet, dgcnn, etc.
        input_channels: 3 # 输入通道数 (通常为xyz坐标)
        hidden_layers: [ 64, 128, 256 ] # 隐藏层维度
        output_dim: 512 # 输出特征维度

    # 策略网络配置
    policy:
        type: mlp # 策略网络类型: mlp, lstm, transformer
        hidden_dim: 512 # 隐藏层维度
        num_layers: 2 # 隐藏层数量
        activation: relu # 激活函数: relu, tanh, etc.
        action_dim: 7 # 动作维度 (位置[3] + 四元数[4])

# 训练参数
training:
    num_epochs: 100 # 训练轮数
    batch_size: 32 # 批处理大小
    learning_rate: 1e-4 # 学习率
    weight_decay: 1e-5 # 权重衰减
    clip_grad: 1.0 # 梯度裁剪值

    # 早停设置
    early_stopping:
        enabled: true # 是否启用早停
        patience: 10 # 容忍轮数
        min_delta: 0.01 # 最小改进值

# RL算法参数
rl:
    algorithm: ppo # RL算法: ppo, sac, etc.
    gamma: 0.99 # 折扣因子
    gae_lambda: 0.95 # GAE lambda参数
    clip_ratio: 0.2 # PPO裁剪参数
    entropy_coef: 0.01 # 熵正则化系数
    value_coef: 0.5 # 价值损失系数
    ppo_epochs: 10 # PPO更新轮数
    num_mini_batches: 4 # 小批量数量

# 仿真参数
simulation:
    sim_type: isaac # 仿真器类型: isaac, bullet, etc.
    update_rate: 1000 # 仿真更新率
    max_steps_per_episode: 100 # 每个回合最大步数
    render: true # 是否渲染可视化

# 数据参数
data:
    path: "/home/<USER>/workspace/px_dexgraspnet/grasp_generation/data/DexH13/graspdata" # 数据路径
    train_ratio: 0.8 # 训练集比例

    # 数据增强设置
    augmentation:
        enabled: true # 是否启用数据增强
        rotation: true # 随机旋转
        translation: true # 随机平移
        scaling: false # 随机缩放
        jitter: 0.01 # 点云抖动强度

    normalize: true # 是否归一化点云
    voxel_size: 0.01 # 体素下采样大小

# 机器人参数
robot:
    type: TORA # 机器人类型
    name: GraspRobot # 机器人名称
    timeout: 10.0 # 默认超时时间
    max_effort: 50.0 # 最大执行力矩

# 系统参数
system:
    gpu: "0" # GPU设备ID
    num_workers: 4 # 数据加载线程数

# 日志参数
logging:
    log_interval: 10 # 日志记录间隔(批次)
    checkpoint_interval: 5 # 检查点保存间隔(轮次)
    tensorboard: true # 是否使用Tensorboard
    save_videos: false # 是否保存视频
