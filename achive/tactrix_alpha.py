"""Template for Tacrix



"""

import torch
from px_janus_learnsim.robot.robot_manager import RobotManagerCfg
from px_janus_learnsim.simulator.simulator_manager import SimulatorManagerCfg
from px_janus_learnsim.ErrorManager import ErrorManagerCfg
from px_janus_learnsim.AgentDirector import AgentDirector
from px_janus_learnsim.dataprocess.dataprocess_manager import DataprocessManagerCfg
from px_janus_learnsim.task.task_manager import TaskManagerCfg
from px_janus_learnsim.learning.learning_manager import LearningManagerCfg
from px_janus_learnsim.Janus_data_class import Colors

import numpy as np

from px_janus_learnsim.config.paths import ROBOT_MODELS_PATH

# # 训练启动脚本示例
# from px_janus_learnsim.learning.algorithms import PPO
# from px_janus_learnsim.bridge.janus2gymnasium import JanusGymWrapper
# from px_janus_learnsim.sim.isaac.envs import IsaacCartpoleEnv


def generate_manager_configs():
    """生成管理器配置"""
    return {
        "robot": RobotManagerCfg(
            robot_type="TORA",
            robot_name="TestRobot",
            default_timeout=10.0,
            clerks=["JointControl", "EndEffectorControl", "ObservationRead"],
            model_path=ROBOT_MODELS_PATH["TORA"]["USD"],
        ),
        "simulator": SimulatorManagerCfg(
            simulator_name="ToraSimulator",
            update_rate=1000,
            clerks=[
                "OpenSimWindow",
                # "DefineOrigins", #预留接口
                # "DesignScene", #预留接口
                "RunSimulation",
                "SimInitialize",
                "InteractiveScene",
            ],
            simulator_type="isaac",
        ),
        "dataprocess": DataprocessManagerCfg(
            clerks=["CustomizedLoader"],
            data_path="/home/<USER>/workspace/px_dexgraspnet/grasp_generation/data/DexH13/graspdata20250218_3",
        ),
        "task": TaskManagerCfg(
            clerks=["ReachTask","GraspTask","ManipulationTask"],
        ),
        "learning": LearningManagerCfg(
            clerks=[
                "BCImitationLearner",
                "PoseEstimator",
                "PointCloudReconstructor",
                "IBS_GraspingPolicy",
            ],
        ),
        "error_manager": ErrorManagerCfg(),
    }


def configured_agent(agent_director):
    """配置 agent"""
    manager_configs = generate_manager_configs()
    agent = agent_director.create_agent("ToraAgent", manager_configs)
    # # 注册回调函数
    # agent.simulator_manager.register_callback(
    #     "DefineOrigins", callback_DefineOriginsClerk
    # )
    # agent.simulator_manager.register_callback("DesignScene", callback_DesignSceneClerk)
    # agent.simulator_manager.register_callback(
    #     "RunSimulation", callback_RunSimulationClerk
    # )

    return agent


def main():
    # 初始化Agent作为中央协调器
    agent_director = AgentDirector()
    agent = agent_director.create_agent("ToraAgent", generate_manager_configs())

    
    # 数据预处理阶段 --------------------------------------------------------
    agent.dataprocess_manager.execute_action("CustomizedLoader")


    # 加载示教数据（通过已注册的CustomizedLoader Clerk）
    training_data = agent.dataprocess_manager.execute_action(
        "CustomizedLoader",
        params={
            "batch_size": 128,
            "augment": True
        }
    )
    print(f"Loaded {len(training_data)} trajectories")


  # 仿真初始化阶段 --------------------------------------------------------
    sim_manager = agent.simulator_manager
    
    # 执行仿真初始化Clerk链
    sim_objects= sim_manager.execute_clerk_chain([
        "SimInitialize",    # 初始化物理引擎
        "OpenSimWindow",    # 创建可视化窗口
        "InteractiveScene"  # 构建交互式场景
    ])
    sim_manager.reset()
    # 获取场景对象
    scene, sim, app = sim_objects
    
    # 任务与学习集成阶段 ----------------------------------------------------
    task_manager = agent.task_manager
    learn_manager = agent.learning_manager
    
    # 初始化BC模仿学习（使用已注册的BCImitationLearner Clerk）
    learn_manager.activate_clerk("BCImitationLearner")
    policy = learn_manager.execute_action(
        "BCImitationLearner",
        inputs={
            "expert_data": training_data,
            "obs_normalizer": agent.dataprocess_manager.get_obs_stats()
        }
    )
    
    # 定义训练循环
    for epoch in range(100):
        # 任务管理器选择当前任务（如ReachTask）
        current_task = task_manager.execute_action("ReachTask")
        
        # 重置仿真环境
        sim.reset()
        obs = sim.get_observations()
        
        # 运行单次训练迭代
        total_reward = 0
        while not current_task.is_done(obs):
            # 通过学习策略生成动作
            action = policy.predict(obs)
            
            # 机器人执行动作（通过JointControl Clerk）
            robot_result = agent.robot_manager.execute_clerk(
                "JointControl",
                inputs={
                    "target_positions": action,
                    "max_effort": 50.0
                }
            )
            
            # 步进仿真
            sim.step()
            
            # 获取新观测和奖励
            new_obs = sim.get_observations()
            reward = current_task.compute_reward(obs, action, new_obs)
            total_reward += reward
            
            # 更新策略
            learn_manager.update_clerk(
                "BCImitationLearner",
                transition=(obs, action, reward, new_obs)
            )
            
            obs = new_obs
        
        print(f"Epoch {epoch}: Total Reward = {total_reward:.2f}")
    
    # 保存训练好的策略
    learn_manager.save_clerk_state("BCImitationLearner", "trained_policy.pth")
    
    # 关闭仿真
    sim.shutdown()

if __name__ == "__main__":
    main()