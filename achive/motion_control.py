"""Template for Tacrix"""

import torch
from px_janus_learnsim.robot.robot_manager import RobotManagerCfg
from px_janus_learnsim.simulator.simulator_manager import SimulatorManagerCfg
from px_janus_learnsim.ErrorManager import ErrorManagerCfg
from px_janus_learnsim.AgentDirector import Agent<PERSON>irector
from px_janus_learnsim.dataprocess.dataprocess_manager import DataprocessManagerCfg
from px_janus_learnsim.task.task_manager import TaskManagerCfg
from px_janus_learnsim.learning.learning_manager import LearningManagerCfg
from px_janus_learnsim.Janus_data_class import Colors
from px_janus_learnsim.Agent import ManagerBasedAgent
import numpy as np
import yaml
import os
from px_janus_learnsim.config.paths import ROBOT_MODELS_PATH
from px_janus_learnsim.Janus_data_class import ClerkExecuteParams

# # 训练启动脚本示例
# from px_janus_learnsim.learning.algorithms import PPO
# from px_janus_learnsim.bridge.janus2gymnasium import Jan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
# from px_janus_learnsim.sim.isaac.envs import <PERSON><PERSON>artpoleEnv


def generate_manager_configs():
    """生成管理器配置"""
    return {
        "robot": RobotManagerCfg(
            robot_type="TORA",
            robot_name="TestRobot",
            default_timeout=10.0,
            clerks=["JointControl", "EndEffectorControl", "ObservationRead"],
            model_path=ROBOT_MODELS_PATH["TORA"]["USD"],
        ),
        # "simulator": SimulatorManagerCfg(
        #     simulator_name="ToraSimulator",
        #     update_rate=1000,
        #     clerks=[
        #         "OpenSimWindow",
        #         # "DefineOrigins", #预留接口
        #         # "DesignScene", #预留接口
        #         "RunSimulation",
        #         "SimInitialize",
        #         "InteractiveScene",
        #     ],
        #     simulator_type="isaac",
        # ),
        "dataprocess": DataprocessManagerCfg(
            loader_type="PaxiniHDF5",
            clerks=["CustomizedLoader"],
            data_path="/home/<USER>/workspace/px_dexgraspnet/grasp_generation/data/DexH13/graspdata20250218_3",
        ),
        "task": TaskManagerCfg(
            task_type="GraspTask",
            clerks=["ReachTask", "GraspTask", "ManipulationTask"],
        ),
        "learning": LearningManagerCfg(
            learning_type="PoseEstimator",
            clerks=[
                "BCImitationLearner",
                "PoseEstimator",
                "PointCloudReconstructor",
                "IBS_GraspingPolicy",
            ],
        ),
        "error_manager": ErrorManagerCfg(),
    }


def configured_agent(agent_director: AgentDirector) -> ManagerBasedAgent:
    """配置 agent"""
    manager_configs = generate_manager_configs()
    agent = agent_director.create_agent("ToraAgent", manager_configs)
    # # 注册回调函数
    # agent.simulator_manager.register_callback(
    #     "DefineOrigins", callback_DefineOriginsClerk
    # )
    # agent.simulator_manager.register_callback("DesignScene", callback_DesignSceneClerk)
    # agent.simulator_manager.register_callback(
    #     "RunSimulation", callback_RunSimulationClerk
    # )

    return agent


# def main():

#     # 初始化 agent
#     agent_director = AgentDirector()
#     agent = configured_agent(agent_director)
#     """
#         目前可根据--num_envs生成指定数量,config_scene.yaml文件能修改场景->修改同场景不同机型待测中
#         但是这个应该是一个通用的东西->可以做一个parser.add_argument在外面生成指定数量
#         把这个进行拆分
#     """
#     scene, sim, app = agent.simulator_manager.interactive_scene(
#         num_origins=2, spacing=2
#     )  # num_origins仅仅只是保留接口而已


#     # policy=agent.learning_manager.policy()
#     robot=agent.robot_manager
#     cup=scene.get_entity("cup")
#     cup_pose=cup.get_pose()
#     def policy():
#         robot.joint.set("neck_yaw", 30)          # 转动头部
#         robot.cartesian.move("right_hand", cup_pose)  # 移动右手到杯子位置
#         robot.grasp.activate("right_hand")        # 执行抓握
#         robot.reset()

#     sim.reset()
#     print(f"{Colors.GREEN}[INFO]: Setup complete...{Colors.RESET}")
#     # 运行仿真
#     agent.simulator_manager.run_simulation(
#         motion_generator=policy,
#         sim=sim,
#         scene_entities=scene,  # 目前是把整个环境进行打包
#         simulation_app=app,
#         motion_pregenerator=None,
#         motion_compare=None,

#     )


def main2():
    agent_director = AgentDirector()
    agent = configured_agent(agent_director)
    robot = agent.robot_manager
    # manager.reset()
    from px_janus_learnsim.Janus_data_class import JointControlData

    # 测试内置动作执行
    print("\n测试内置动作执行:")

    joint_names = [
        "larm_1_joint",
        "larm_2_joint",
        "larm_3_joint",
        "larm_4_joint",
        "larm_5_joint",
        "larm_6_joint",
        "larm_7_joint",
        "rarm_1_joint",
        "rarm_2_joint",
        "rarm_3_joint",
        "rarm_4_joint",
        "rarm_5_joint",
        "rarm_6_joint",
        "rarm_7_joint",
        "Lindex1_joint",
        "Lindex2_joint",
        "Lindex3_joint",
        "Lmid1_joint",
        "Lmid2_joint",
        "Lmid3_joint",
        "Lring1_joint",
        "Lring2_joint",
        "Lring3_joint",
        "Lthumb1_joint",
        "Lthumb2_joint",
        "Lthumb3_joint",
        "Lthumb4_joint",
        "index1_joint",
        "index2_joint",
        "index3_joint",
        "mid1_joint",
        "mid2_joint",
        "mid3_joint",
        "ring1_joint",
        "ring2_joint",
        "ring3_joint",
        "thumb1_joint",
        "thumb2_joint",
        "thumb3_joint",
        "thumb4_joint",
        "rotate_joint",
        "waist_1_joint",
        "waist_2_joint",
        "neck_joint",
        "head_joint",
    ]
    # 基础姿势：所有关节归零
    base_positions = [0.0] * 45

    # 设置特定关节位置（示例）
    # 假设前3个关节是颈部关节
    base_positions[0:3] = [0.1, 0.2, 0.3]  # neck_yaw, neck_pitch, neck_roll

    # 设置手臂关节（假设索引4-15是右臂）
    base_positions[4:16] = [0.5] * 12  # 右臂展开

    # 设置腿部关节（假设索引30-41是腿部）
    base_positions[30:42] = [-0.3] * 12  # 微蹲姿势

    joint_data = JointControlData(
        joint_name=joint_names,  # 根据实际关节名称修改
        target_position=base_positions,  # 确保数量与关节数量匹配
        speed=0.5,
    )

    ClerkExecuteParams(
        callbacks={"JointControl": robot.execute_action},
        data=joint_data,
    )
    result = robot.execute_action("JointControl", joint_data)
    print(f"关节控制执行结果: {result}")

    # 测试回调动作执行（需要先注册回调）
    print("\n测试回调动作执行成功")
    # try:
    #     robot.execute_action("CustomAction", {"param": 1.0})
    # except Exception as e:
    #     print(f"预期中的错误处理: {str(e)}")


if __name__ == "__main__":
    main2()
