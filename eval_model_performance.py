#!/usr/bin/env python3
"""
轨迹数据分析脚本 - 无仿真环境依赖版本

功能：
1. 加载训练好的单手控制模型(支持ACT序列模型)
. 从数据集提取轨迹样本
3. 基于观察预测完整动作序列并与真实序列对比
4. 分析序列预测的时间步误差模式
5. 生成详细的分析报告和可视化

特点：
- 支持ACT等序列预测模型，一次预测整个动作序列
- 逐步对比预测序列与真实序列的各时间步误差
- 分析关节、位置、旋转等动作组件的预测精度

作者: AI Assistant
日期: 2024
"""

from eval.cli.main import main
from eval.utils.schedule_utils import ConstantSchedulePicklable

# 将类添加到全局命名空间，以便pickle能够找到它
globals()["ConstantSchedulePicklable"] = ConstantSchedulePicklable

# 🆕 流式数据集分析器 - 内存友好的处理方案


if __name__ == "__main__":
    main()

"""
🌊 默认流式处理使用说明：

现在脚本默认使用流式处理模式，内存友好且支持任意大小的数据集！

# 基本用法（推荐，使用默认流式处理）
python eval_model_performance.py \
    --model_path /path/to/model.pth \
    --dataset_path /path/to/dataset \
    --num_samples -1

# 自定义流式处理参数
python eval_model_performance.py \
    --model_path /path/to/model.pth \
    --dataset_path /path/to/dataset \
    --num_samples -1 \
    --chunk_size 30 \
    --checkpoint_interval 50 \
    --max_memory_usage 0.7

# 如果需要使用旧的批处理模式（不推荐，除非特殊需求）
python eval_model_performance.py \
    --model_path /path/to/model.pth \
    --dataset_path /path/to/dataset \
    --num_samples 100 \
    --disable_streaming

# 🆕 数据预处理配置（数据已预处理为物体中心坐标系且无归一化）
# 默认配置已适配预处理数据，无需额外参数
# 如需调试，可使用以下参数：

# 强制启用坐标系转换（向后兼容）
python eval_model_performance.py \
    --model_path /path/to/model.pth \
    --dataset_path /path/to/dataset \
    --num_samples -1 \
    --enable_object_centric

# 强制启用归一化（向后兼容）
python eval_model_performance.py \
    --model_path /path/to/model.pth \
    --dataset_path /path/to/dataset \
    --num_samples -1 \
    --enable_normalization

流式处理的优势（默认启用）：
✅ 内存使用恒定，不随数据集大小增长
✅ 支持任意大小的数据集（18K+样本无压力）
✅ 自动内存监控和动态调整
✅ 检查点保存，支持中断恢复
✅ 实时进度显示和内存状态监控
✅ 不会因为内存不足而死机
✅ 适配预处理数据，无需额外转换和归一化

现在你可以放心地使用 --num_samples -1 来处理整个数据集了！
"""
